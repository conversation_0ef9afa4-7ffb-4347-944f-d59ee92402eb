# General
.DS_Store
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Node.js
node_modules/
package-lock.json
yarn.lock

# React Native
android/.gradle/
android/app/build/
android/build/
ios/Pods/
ios/build/
*.xcworkspace
*.xcuserstate
*.xcuserdata/
*.lock
*.swp
*.iml
*.idea/
*.sublime-workspace
*.sublime-project

# .NET
bin/
obj/
*.user
*.suo
*.pdb
*.cache
*.log
*.vs/
.vscode/
*.db
*.sqlite
*.sqlite3

# Environment variables
.env
.env.*

# Build artifacts
dist/
build/
wwwroot/

# Coverage
coverage/
*.lcov

# Temporary files
*.tmp
*.temp
*.bak
*.old

# IDE-specific files
.vscode/
.idea/
*.code-workspace

# OS-specific files
*.swp
*.lock
*.bak
*.tmp
*.temp