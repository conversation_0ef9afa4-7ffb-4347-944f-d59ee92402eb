# Hisab-Kitab: Personal Finance Management App

<div align="center">
  <img src="frontend/HisabKitab/assets/icon.png" alt="Hisab-Kitab Logo" width="120" />
  <h3>Track, Manage, and Plan Your Finances</h3>
  <p>A mobile-first personal finance app with family sharing capabilities</p>
</div>

## 📱 About Hisab-Kitab

Hisab-Kitab is a comprehensive personal finance management application built with .NET 8 Web API backend and React Native frontend. The app follows Clean Architecture principles and offers a wide range of features to help users manage their finances effectively.

### Key Features

- **User Authentication**: Secure login and registration with JWT tokens
- **Family Management**: Create and manage family groups with role-based access
- **Accounts & Categories**: Track multiple accounts and categorize transactions
- **Transactions**: Record income and expenses with detailed information
- **Loans & EMIs**: Manage loans with different interest calculation methods
- **Savings Goals**: Set and track progress towards financial goals
- **Analytics & Reports**: Visualize spending patterns and financial health
- **Budget Governance**: Set spending limits and approval workflows
- **Receipt Scanning**: Capture and extract data from receipts using OCR
- **Offline-First Experience**: Use the app without internet connection
- **Multilingual Support**: Available in English and Nepali

## 📖 User Guide

### Getting Started

#### Registration and Login

1. **Register a new account**:
   - Open the app and tap "Register"
   - Fill in your details (username, email, password, name)
   - Submit the form to create your account

2. **Login to your account**:
   - Enter your username and password
   - Tap "Login" to access your dashboard

#### Dashboard Overview

The main dashboard provides a quick overview of your financial status:

- **Total Balance**: Sum of all your account balances
- **Recent Transactions**: Latest income and expenses
- **Quick Actions**: Add transactions, view accounts, etc.

### Managing Families

#### Creating a Family

1. Navigate to the Families tab
2. Tap "Create Family"
3. Enter family name and description
4. Tap "Create" to establish your family group

#### Inviting Family Members

1. Open the family details screen
2. Tap "Invite Member"
3. Share the generated invite code with family members
4. They can join using the code from their app

#### Family Roles

- **Family Admin**: Can manage family settings, members, and shared accounts
- **Family Member**: Can view and use shared accounts based on permissions

### Managing Accounts

#### Creating an Account

1. Go to the Accounts tab
2. Tap "Add Account"
3. Select account type (Cash, Bank, Credit Card, etc.)
4. Enter initial balance and other details
5. Choose whether it's personal or family account
6. Tap "Create" to add the account

#### Sharing Accounts

1. Open the account details
2. Tap "Share Account"
3. Select family members to share with
4. Set permissions (View, Edit, Full Access)
5. Tap "Share" to confirm

### Recording Transactions

#### Adding a Transaction

1. Tap the "+" button from the dashboard or transactions tab
2. Select transaction type (Income, Expense, Transfer)
3. Enter amount and select category
4. Add description and date
5. Select the account for the transaction
6. Tap "Save" to record the transaction

#### Transaction Categories

Transactions are organized into categories:
- **Income**: Salary, Investments, Gifts, etc.
- **Expense**: Food, Transportation, Housing, etc.
- **Transfer**: Moving money between accounts

### Setting Budgets

1. Go to the Categories tab
2. Select a category
3. Tap "Add Budget Limit"
4. Set monthly budget amount
5. Choose if it applies to you or the whole family
6. Save to activate budget tracking

### Loans Management

#### Creating a Loan

1. Navigate to the Loans tab
2. Tap "Add Loan"
3. Enter loan details (amount, interest rate, term)
4. Select interest type (Flat, Reducing Balance, Compound)
5. Set start date and payment schedule
6. Tap "Create" to add the loan

#### Tracking Loan Payments

1. Open a loan from the loans list
2. View the payment schedule and timeline
3. Tap "Add Payment" to record a payment
4. Enter payment amount and date
5. Save to update loan status

### Savings Goals

#### Creating a Savings Goal

1. Go to the Savings tab
2. Tap "Add Goal"
3. Enter goal name, target amount, and target date
4. Add an image (optional)
5. Set priority level
6. Tap "Create" to start tracking

#### Contributing to Goals

1. Open a savings goal
2. Tap "Add Contribution"
3. Enter contribution amount and date
4. Save to update progress

### Analytics and Reports

1. Navigate to the Analytics tab
2. View pre-generated reports:
   - Income vs. Expense
   - Category Breakdown
   - Monthly Trends
   - Net Worth
3. Tap on charts to see detailed information
4. Use filters to customize date ranges and accounts

### Offline Mode

The app works offline with automatic synchronization:

1. Use the app normally when offline
2. Changes are stored locally
3. When online, tap "Sync" or wait for automatic sync
4. Resolve any conflicts if prompted

### Settings and Preferences

Access the Settings tab to customize:

- **Profile**: Update personal information
- **Appearance**: Toggle dark/light mode
- **Language**: Switch between English and Nepali
- **Notifications**: Configure alerts and reminders
- **Security**: Change password and security settings

## 🛠️ Technical Information

### System Requirements

- **Mobile**: iOS 12+ or Android 7+
- **Backend**: .NET 8 with PostgreSQL database
- **API**: RESTful API with JWT authentication

### Architecture

- **Backend**: Clean Architecture with Domain-Driven Design
- **Frontend**: React Native with Expo framework
- **Database**: PostgreSQL with Entity Framework Core
- **Authentication**: JWT tokens with refresh mechanism

## 🚀 Installation and Setup

### Backend Setup

1. **Prerequisites**:
   - .NET 8 SDK
   - PostgreSQL database
   - Visual Studio 2022 or VS Code

2. **Database Configuration**:
   ```bash
   # Navigate to the backend directory
   cd backend/HisabKitab.API

   # Update connection string in appsettings.json
   # "DefaultConnection": "Host=localhost;Database=HisabKitab;Username=postgres;Password=yourpassword"

   # Apply migrations
   dotnet ef database update
   ```

3. **Running the Backend**:
   ```bash
   # Start the API server
   dotnet run

   # The API will be available at:
   # https://localhost:7290 and http://localhost:5046
   ```

4. **Exposing Local API for Mobile Testing**:
   ```bash
   # Install ngrok
   npm install -g ngrok

   # Expose the HTTP port
   ngrok http 5046

   # Copy the generated URL and update in frontend/HisabKitab/src/config.ts
   ```

### Frontend Setup

1. **Prerequisites**:
   - Node.js (v16+)
   - npm or yarn
   - Expo CLI

2. **Installation**:
   ```bash
   # Navigate to the frontend directory
   cd frontend/HisabKitab

   # Install dependencies
   npm install

   # Install additional packages
   npm install tesseract.js dexie @react-native-community/netinfo expo-image-manipulator date-fns
   ```

3. **Configuration**:
   - Update the API URL in `src/config.ts` with your ngrok URL or backend server address

4. **Running the App**:
   ```bash
   # Start the Expo development server
   npx expo start

   # Follow the instructions in the terminal to run on:
   # - iOS simulator
   # - Android emulator
   # - Physical device using Expo Go app
   ```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Backend Issues

1. **Database Connection Errors**:
   - Verify PostgreSQL is running
   - Check connection string in `appsettings.json`
   - Ensure database user has proper permissions

2. **Migration Errors**:
   ```bash
   # Reset migrations if needed
   dotnet ef database drop
   dotnet ef migrations remove
   dotnet ef migrations add InitialCreate
   dotnet ef database update
   ```

3. **API Not Accessible from Mobile**:
   - Ensure CORS is properly configured
   - Verify ngrok tunnel is active
   - Check firewall settings

#### Frontend Issues

1. **Build Errors**:
   ```bash
   # Clear npm cache and node_modules
   rm -rf node_modules
   npm cache clean --force
   npm install
   ```

2. **API Connection Issues**:
   - Verify API URL in `src/config.ts`
   - Check network connectivity
   - Ensure backend server is running

3. **Expo Issues**:
   ```bash
   # Clear Expo cache
   expo r -c
   ```

### Data Migration

If you need to migrate from AsyncStorage to Dexie.js:

1. Go to Settings > Advanced > Storage Migration
2. Tap "Start Migration"
3. Follow the on-screen instructions
4. Verify data after migration is complete

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Role-Based Access Control**: Different permissions for different user roles
- **Family-Level Permissions**: Granular control over shared resources
- **Encrypted Storage**: Sensitive data is stored securely
- **Automatic Logout**: Session expires after inactivity

## 🔮 Future Enhancements

- **Biometric Authentication**: Fingerprint and Face ID login
- **Advanced Analytics**: AI-powered financial insights
- **Investment Tracking**: Monitor stocks and investments
- **Bill Reminders**: Automated bill payment reminders
- **Financial Goals**: Enhanced goal setting and tracking

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 👥 Contributors

- Kushal Tamang - Project Lead & Developer
