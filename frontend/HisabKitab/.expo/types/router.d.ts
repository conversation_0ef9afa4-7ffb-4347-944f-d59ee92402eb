/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/typography`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/accounts` | `/accounts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/analytics` | `/analytics`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/approval` | `/approval`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/budget` | `/budget`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/categories` | `/categories`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/families` | `/families`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/loans` | `/loans`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/notifications` | `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/priorities` | `/priorities`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/receipts` | `/receipts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/savings` | `/savings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/sync` | `/sync`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/transactions` | `/transactions`; params?: Router.UnknownInputParams; } | { pathname: `/analytics/cashflow`; params?: Router.UnknownInputParams; } | { pathname: `/analytics/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/analytics/export`; params?: Router.UnknownInputParams; } | { pathname: `/analytics/monthly-summary`; params?: Router.UnknownInputParams; } | { pathname: `/analytics/networth`; params?: Router.UnknownInputParams; } | { pathname: `/approval/create`; params?: Router.UnknownInputParams; } | { pathname: `/approval/requests`; params?: Router.UnknownInputParams; } | { pathname: `/auth/login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/register`; params?: Router.UnknownInputParams; } | { pathname: `/budget/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/budget/limits`; params?: Router.UnknownInputParams; } | { pathname: `/budget/spending`; params?: Router.UnknownInputParams; } | { pathname: `/examples/components`; params?: Router.UnknownInputParams; } | { pathname: `/family/create`; params?: Router.UnknownInputParams; } | { pathname: `/family/join`; params?: Router.UnknownInputParams; } | { pathname: `/notifications/settings`; params?: Router.UnknownInputParams; } | { pathname: `/receipts`; params?: Router.UnknownInputParams; } | { pathname: `/reminders/create`; params?: Router.UnknownInputParams; } | { pathname: `/reminders`; params?: Router.UnknownInputParams; } | { pathname: `/savings/goals/create`; params?: Router.UnknownInputParams; } | { pathname: `/savings/wishlist/create`; params?: Router.UnknownInputParams; } | { pathname: `/settings/features`; params?: Router.UnknownInputParams; } | { pathname: `/sync/conflict-resolution`; params?: Router.UnknownInputParams; } | { pathname: `/sync`; params?: Router.UnknownInputParams; } | { pathname: `/sync/merge-editor`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/family/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/family/edit/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/receipts/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/reminders/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/reminders/edit/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/savings/goals/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/savings/goals/edit/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/savings/wishlist/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/savings/wishlist/edit/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/notifications`; params?: Router.UnknownOutputParams; } | { pathname: `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/typography`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/accounts` | `/accounts`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/analytics` | `/analytics`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/approval` | `/approval`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/budget` | `/budget`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/categories` | `/categories`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/families` | `/families`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/loans` | `/loans`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/notifications` | `/notifications`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/priorities` | `/priorities`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/receipts` | `/receipts`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/savings` | `/savings`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/sync` | `/sync`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/transactions` | `/transactions`; params?: Router.UnknownOutputParams; } | { pathname: `/analytics/cashflow`; params?: Router.UnknownOutputParams; } | { pathname: `/analytics/dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `/analytics/export`; params?: Router.UnknownOutputParams; } | { pathname: `/analytics/monthly-summary`; params?: Router.UnknownOutputParams; } | { pathname: `/analytics/networth`; params?: Router.UnknownOutputParams; } | { pathname: `/approval/create`; params?: Router.UnknownOutputParams; } | { pathname: `/approval/requests`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/login`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/register`; params?: Router.UnknownOutputParams; } | { pathname: `/budget/dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `/budget/limits`; params?: Router.UnknownOutputParams; } | { pathname: `/budget/spending`; params?: Router.UnknownOutputParams; } | { pathname: `/examples/components`; params?: Router.UnknownOutputParams; } | { pathname: `/family/create`; params?: Router.UnknownOutputParams; } | { pathname: `/family/join`; params?: Router.UnknownOutputParams; } | { pathname: `/notifications/settings`; params?: Router.UnknownOutputParams; } | { pathname: `/receipts`; params?: Router.UnknownOutputParams; } | { pathname: `/reminders/create`; params?: Router.UnknownOutputParams; } | { pathname: `/reminders`; params?: Router.UnknownOutputParams; } | { pathname: `/savings/goals/create`; params?: Router.UnknownOutputParams; } | { pathname: `/savings/wishlist/create`; params?: Router.UnknownOutputParams; } | { pathname: `/settings/features`; params?: Router.UnknownOutputParams; } | { pathname: `/sync/conflict-resolution`; params?: Router.UnknownOutputParams; } | { pathname: `/sync`; params?: Router.UnknownOutputParams; } | { pathname: `/sync/merge-editor`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } } | { pathname: `/family/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/family/edit/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/receipts/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/reminders/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/reminders/edit/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/savings/goals/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/savings/goals/edit/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/savings/wishlist/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/savings/wishlist/edit/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/notifications${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `/typography${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/accounts${`?${string}` | `#${string}` | ''}` | `/accounts${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/analytics${`?${string}` | `#${string}` | ''}` | `/analytics${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/approval${`?${string}` | `#${string}` | ''}` | `/approval${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/budget${`?${string}` | `#${string}` | ''}` | `/budget${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/categories${`?${string}` | `#${string}` | ''}` | `/categories${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/explore${`?${string}` | `#${string}` | ''}` | `/explore${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/families${`?${string}` | `#${string}` | ''}` | `/families${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/loans${`?${string}` | `#${string}` | ''}` | `/loans${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/notifications${`?${string}` | `#${string}` | ''}` | `/notifications${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/priorities${`?${string}` | `#${string}` | ''}` | `/priorities${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/receipts${`?${string}` | `#${string}` | ''}` | `/receipts${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/savings${`?${string}` | `#${string}` | ''}` | `/savings${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/settings${`?${string}` | `#${string}` | ''}` | `/settings${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/sync${`?${string}` | `#${string}` | ''}` | `/sync${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/transactions${`?${string}` | `#${string}` | ''}` | `/transactions${`?${string}` | `#${string}` | ''}` | `/analytics/cashflow${`?${string}` | `#${string}` | ''}` | `/analytics/dashboard${`?${string}` | `#${string}` | ''}` | `/analytics/export${`?${string}` | `#${string}` | ''}` | `/analytics/monthly-summary${`?${string}` | `#${string}` | ''}` | `/analytics/networth${`?${string}` | `#${string}` | ''}` | `/approval/create${`?${string}` | `#${string}` | ''}` | `/approval/requests${`?${string}` | `#${string}` | ''}` | `/auth/login${`?${string}` | `#${string}` | ''}` | `/auth/register${`?${string}` | `#${string}` | ''}` | `/budget/dashboard${`?${string}` | `#${string}` | ''}` | `/budget/limits${`?${string}` | `#${string}` | ''}` | `/budget/spending${`?${string}` | `#${string}` | ''}` | `/examples/components${`?${string}` | `#${string}` | ''}` | `/family/create${`?${string}` | `#${string}` | ''}` | `/family/join${`?${string}` | `#${string}` | ''}` | `/notifications/settings${`?${string}` | `#${string}` | ''}` | `/receipts${`?${string}` | `#${string}` | ''}` | `/reminders/create${`?${string}` | `#${string}` | ''}` | `/reminders${`?${string}` | `#${string}` | ''}` | `/savings/goals/create${`?${string}` | `#${string}` | ''}` | `/savings/wishlist/create${`?${string}` | `#${string}` | ''}` | `/settings/features${`?${string}` | `#${string}` | ''}` | `/sync/conflict-resolution${`?${string}` | `#${string}` | ''}` | `/sync${`?${string}` | `#${string}` | ''}` | `/sync/merge-editor${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/typography`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/accounts` | `/accounts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/analytics` | `/analytics`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/approval` | `/approval`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/budget` | `/budget`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/categories` | `/categories`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/families` | `/families`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/loans` | `/loans`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/notifications` | `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/priorities` | `/priorities`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/receipts` | `/receipts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/savings` | `/savings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/sync` | `/sync`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/transactions` | `/transactions`; params?: Router.UnknownInputParams; } | { pathname: `/analytics/cashflow`; params?: Router.UnknownInputParams; } | { pathname: `/analytics/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/analytics/export`; params?: Router.UnknownInputParams; } | { pathname: `/analytics/monthly-summary`; params?: Router.UnknownInputParams; } | { pathname: `/analytics/networth`; params?: Router.UnknownInputParams; } | { pathname: `/approval/create`; params?: Router.UnknownInputParams; } | { pathname: `/approval/requests`; params?: Router.UnknownInputParams; } | { pathname: `/auth/login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/register`; params?: Router.UnknownInputParams; } | { pathname: `/budget/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/budget/limits`; params?: Router.UnknownInputParams; } | { pathname: `/budget/spending`; params?: Router.UnknownInputParams; } | { pathname: `/examples/components`; params?: Router.UnknownInputParams; } | { pathname: `/family/create`; params?: Router.UnknownInputParams; } | { pathname: `/family/join`; params?: Router.UnknownInputParams; } | { pathname: `/notifications/settings`; params?: Router.UnknownInputParams; } | { pathname: `/receipts`; params?: Router.UnknownInputParams; } | { pathname: `/reminders/create`; params?: Router.UnknownInputParams; } | { pathname: `/reminders`; params?: Router.UnknownInputParams; } | { pathname: `/savings/goals/create`; params?: Router.UnknownInputParams; } | { pathname: `/savings/wishlist/create`; params?: Router.UnknownInputParams; } | { pathname: `/settings/features`; params?: Router.UnknownInputParams; } | { pathname: `/sync/conflict-resolution`; params?: Router.UnknownInputParams; } | { pathname: `/sync`; params?: Router.UnknownInputParams; } | { pathname: `/sync/merge-editor`; params?: Router.UnknownInputParams; } | `/+not-found` | `/family/${Router.SingleRoutePart<T>}` | `/family/edit/${Router.SingleRoutePart<T>}` | `/receipts/${Router.SingleRoutePart<T>}` | `/reminders/${Router.SingleRoutePart<T>}` | `/reminders/edit/${Router.SingleRoutePart<T>}` | `/savings/goals/${Router.SingleRoutePart<T>}` | `/savings/goals/edit/${Router.SingleRoutePart<T>}` | `/savings/wishlist/${Router.SingleRoutePart<T>}` | `/savings/wishlist/edit/${Router.SingleRoutePart<T>}` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/family/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/family/edit/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/receipts/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/reminders/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/reminders/edit/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/savings/goals/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/savings/goals/edit/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/savings/wishlist/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/savings/wishlist/edit/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
