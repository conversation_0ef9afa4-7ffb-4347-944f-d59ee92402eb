# Hisab-Kitab Storage Migration Guide

This document provides instructions for migrating data from AsyncStorage to IndexedDB using Dexie.js in the Hisab-Kitab app.

## Why Migrate?

AsyncStorage has several limitations:
- Limited storage capacity (5-10MB on most devices)
- No indexing capabilities for efficient queries
- No support for complex data structures
- Poor performance with large datasets

IndexedDB (via Dexie.js) offers:
- Much larger storage capacity (50MB+ on most devices)
- Powerful indexing for fast queries
- Support for complex data structures
- Better performance with large datasets
- Transaction support for data integrity

## Migration Process

The migration process is designed to be seamless for users:

1. When users open the app after the update, they'll see a migration screen
2. The app will automatically migrate all data from AsyncStorage to IndexedDB
3. Users can choose to clear the old AsyncStorage data after successful migration
4. The app will continue to use IndexedDB for all storage operations

## Required Packages

Make sure the following packages are installed:

```bash
npm install dexie @react-native-community/netinfo expo-image-manipulator date-fns
```

## Implementation Details

The migration implementation consists of:

### 1. Migration Utilities (`src/utils/migrationUtils.ts`)

This file contains functions for:
- Checking migration status
- Migrating data from AsyncStorage to IndexedDB
- Clearing AsyncStorage after successful migration

### 2. Dexie.js Database Implementation (`src/services/db-dexie.ts`)

This file contains:
- Database schema definition
- Table mappings
- CRUD operations for all data types

### 3. Migration Screen (`src/screens/MigrationScreen.tsx`)

This screen:
- Shows migration progress
- Displays details about migrated data
- Allows users to clear old storage after migration
- Handles error cases and retries

### 4. App Integration (`app/_layout.tsx`)

The app checks if migration is needed and shows the migration screen if necessary.

## Manual Migration

You can trigger the migration manually by running:

```bash
node scripts/migrate-storage.js
```

This will create a flag file that forces the migration screen to appear the next time the app is opened.

## Testing Migration

To test the migration:
1. Add some data to the app using the old AsyncStorage implementation
2. Run the migration script or update the app to include the migration code
3. Open the app and verify that the migration screen appears
4. After migration, verify that all data is accessible in the app
5. Check that the app continues to work correctly with the new storage system

## Troubleshooting

If users experience issues during migration:

1. **Data Loss**: The migration process keeps the original AsyncStorage data until explicitly cleared. If there's an issue, users can retry the migration.

2. **Migration Fails**: The migration screen includes a retry button. Common causes of failure include:
   - Insufficient storage space
   - Corrupted AsyncStorage data
   - App permissions issues

3. **App Crashes**: If the app crashes during migration, it will attempt to resume the migration the next time it's opened.

## Rollback Plan

If critical issues are discovered with the IndexedDB implementation:

1. Update the app to check for a rollback flag
2. If the flag is present, revert to using AsyncStorage
3. Copy data back from IndexedDB to AsyncStorage if needed

## Future Improvements

After the migration is complete, consider:

1. Optimizing database schema for better performance
2. Implementing more advanced indexing for complex queries
3. Adding data compression for large objects
4. Implementing a more sophisticated sync strategy with the backend

## Questions?

If you have questions about the migration process, please contact the development team.
