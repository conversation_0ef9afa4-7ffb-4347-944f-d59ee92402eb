# Using ngrok with <PERSON>ab-<PERSON>ab

This guide explains how to use ngrok to connect your React Native app to your local backend server.

## What is ngrok?

ngrok is a tool that creates a secure tunnel to your localhost and provides a public URL that can be accessed from anywhere, including your mobile device. This is especially useful for mobile app development when you need to connect to a local backend server.

## Setup Instructions

### 1. Install ngrok

First, you need to install ngrok if you haven't already. You can download it from [ngrok.com](https://ngrok.com/) or install it using npm:

```bash
npm install -g ngrok
```

### 2. Start your backend server

Make sure your .NET backend server is running on port 5046:

```bash
dotnet run
```

### 3. Start ngrok

Open a new terminal window and run:

```bash
ngrok http 5046
```

This will display something like:

```
Session Status                online
Account                       Your Account
Version                       3.x.x
Region                        United States (us)
Forwarding                    https://1234abcd.ngrok.io -> http://localhost:5046
```

Copy the forwarding URL (e.g., `https://1234abcd.ngrok.io`).

### 4. Update the API URL in your app

Open `src/services/api.ts` and update the API_URL:

```typescript
// Replace with your ngrok URL
let API_URL = 'https://1234abcd.ngrok.io/api';
```

### 5. Restart your React Native app

```bash
npx expo start --clear
```

## Important Notes

1. **ngrok URLs are temporary**: Each time you restart ngrok, you'll get a new URL. You'll need to update the API_URL in your app accordingly.

2. **ngrok free tier limitations**: The free tier of ngrok has some limitations, including session duration and connection limits. For development purposes, this should be sufficient.

3. **CORS configuration**: Make sure your backend has CORS configured to accept requests from any origin:

```csharp
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// In Configure method or app configuration:
app.UseCors("AllowAll");
```

## Troubleshooting

1. **Connection issues**: If you're still having connection issues, make sure your backend server is running and accessible through ngrok by visiting the ngrok URL in a browser.

2. **SSL certificate warnings**: ngrok uses HTTPS by default, which might cause SSL certificate warnings. In development, you can bypass these warnings.

3. **Request timeouts**: If requests are timing out, check your backend server logs for any errors.

## Alternative Approaches

If ngrok doesn't work for your setup, consider these alternatives:

1. **Localtunnel**: Similar to ngrok, but with different features and limitations.
2. **Deploy your backend**: Deploy your backend to a cloud service like Azure, AWS, or Heroku.
3. **Use a local IP**: If your device and computer are on the same network, you can use your computer's local IP address.
