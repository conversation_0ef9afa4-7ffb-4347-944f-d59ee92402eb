import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert, Image, Linking, Modal } from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../../src/contexts/ThemeContext';
import { useSavings } from '../../../src/contexts/SavingsContext';
import { Ionicons } from '@expo/vector-icons';
import Button from '../../../src/components/ui/Button';
import LoadingIndicator from '../../../src/components/ui/LoadingIndicator';
import Dropdown from '../../../src/components/ui/Dropdown';
import { formatCurrency, formatDate } from '../../../src/utils/formatters';

export default function WishlistItemDetailsScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const itemId = parseInt(id);

  const {
    getWishlistItem,
    deleteWishlistItem,
    savingsGoals,
    linkWishlistItemToGoal,
    unlinkWishlistItemFromGoal,
  } = useSavings();

  // State
  const [isDeleting, setIsDeleting] = useState(false);
  const [isLinking, setIsLinking] = useState(false);
  const [showLinkModal, setShowLinkModal] = useState(false);
  const [selectedGoalId, setSelectedGoalId] = useState<number | null>(null);

  // Get item data
  const item = getWishlistItem(itemId);

  // Get active savings goals for linking
  const activeGoals = savingsGoals.filter(goal => goal.status === 'Active');

  // Handle edit item
  const handleEditItem = () => {
    router.push({
      pathname: "/savings/wishlist/edit/[id]",
      params: { id: itemId }
    } as any);
  };

  // Handle delete item
  const handleDeleteItem = () => {
    Alert.alert(
      t('wishlist.deleteItem'),
      t('wishlist.deleteItemConfirmation'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            setIsDeleting(true);
            try {
              const success = await deleteWishlistItem(itemId);
              if (success) {
                router.back();
              }
            } catch (error) {
              console.error('Error deleting wishlist item:', error);
              Alert.alert(t('common.error'), t('wishlist.deleteItemError'));
            } finally {
              setIsDeleting(false);
            }
          },
        },
      ]
    );
  };

  // Handle open product URL
  const handleOpenProductUrl = async () => {
    if (item?.productUrl) {
      try {
        const canOpen = await Linking.canOpenURL(item.productUrl);
        if (canOpen) {
          await Linking.openURL(item.productUrl);
        } else {
          Alert.alert(t('common.error'), t('common.cannotOpenUrl'));
        }
      } catch (error) {
        console.error('Error opening URL:', error);
        Alert.alert(t('common.error'), t('common.cannotOpenUrl'));
      }
    }
  };

  // Handle link to goal
  const handleLinkToGoal = () => {
    if (item?.linkedSavingsGoalId) {
      setSelectedGoalId(item.linkedSavingsGoalId);
    }
    setShowLinkModal(true);
  };

  // Handle unlink from goal
  const handleUnlinkFromGoal = () => {
    Alert.alert(
      t('wishlist.unlinkFromGoal'),
      t('common.areYouSure'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.confirm'),
          onPress: async () => {
            setIsLinking(true);
            try {
              const success = await unlinkWishlistItemFromGoal(itemId);
              if (success) {
                Alert.alert(t('common.success'), t('wishlist.unlinkSuccess'));
              }
            } catch (error) {
              console.error('Error unlinking from goal:', error);
              Alert.alert(t('common.error'), t('wishlist.unlinkError'));
            } finally {
              setIsLinking(false);
            }
          },
        },
      ]
    );
  };

  // Handle submit link
  const handleSubmitLink = async () => {
    if (selectedGoalId) {
      setIsLinking(true);
      try {
        const success = await linkWishlistItemToGoal(itemId, selectedGoalId);
        if (success) {
          setShowLinkModal(false);
          Alert.alert(t('common.success'), t('wishlist.linkSuccess'));
        }
      } catch (error) {
        console.error('Error linking to goal:', error);
        Alert.alert(t('common.error'), t('wishlist.linkError'));
      } finally {
        setIsLinking(false);
      }
    }
  };

  // Render loading state
  if (!item) {
    return <LoadingIndicator fullScreen message={t('common.loading')} />;
  }

  // Prepare goal options for dropdown
  const goalOptions = activeGoals.map(goal => ({
    label: goal.title,
    value: goal.id,
  }));

  return (
    <>
      <Stack.Screen
        options={{
          title: t('wishlist.itemDetails'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
          headerShadowVisible: false,
        }}
      />

      <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Item header */}
          <View style={styles.header}>
            <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {item.title}
            </Text>

            <View style={styles.actions}>
              <Button
                title={t('common.edit')}
                onPress={handleEditItem}
                variant="outline"
                size="small"
                icon="pencil-outline"
                style={styles.actionButton}
              />

              <Button
                title={t('common.delete')}
                onPress={handleDeleteItem}
                variant="danger"
                size="small"
                icon="trash-outline"
                isLoading={isDeleting}
                style={styles.actionButton}
              />
            </View>
          </View>

          {/* Item image */}
          {item.imageUrl ? (
            <Image
              source={{ uri: item.imageUrl }}
              style={styles.image}
              resizeMode="cover"
            />
          ) : (
            <View
              style={[
                styles.imagePlaceholder,
                { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
              ]}
            >
              <Ionicons
                name="image-outline"
                size={48}
                color={isDarkMode ? '#30363D' : '#D0D7DE'}
              />
            </View>
          )}

          {/* Item description */}
          {item.description && (
            <Text style={[styles.description, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {item.description}
            </Text>
          )}

          {/* Price section */}
          <View style={[styles.section, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
            <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {t('wishlist.estimatedPrice')}
            </Text>

            <Text style={[styles.price, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {formatCurrency(item.estimatedPrice)}
            </Text>

            {item.productUrl && (
              <Button
                title={t('common.viewProduct')}
                onPress={handleOpenProductUrl}
                icon="open-outline"
                variant="secondary"
                style={styles.viewButton}
              />
            )}
          </View>

          {/* Details section */}
          <View style={[styles.section, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
            <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {t('common.details')}
            </Text>

            <View style={styles.detailsGrid}>
              <View style={styles.detailItem}>
                <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  {t('wishlist.addedDate')}
                </Text>
                <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                  {formatDate(item.addedDate)}
                </Text>
              </View>

              {item.targetPurchaseDate && (
                <View style={styles.detailItem}>
                  <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                    {t('wishlist.targetPurchaseDate')}
                  </Text>
                  <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                    {formatDate(item.targetPurchaseDate)}
                  </Text>
                </View>
              )}

              <View style={styles.detailItem}>
                <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  {t('wishlist.status')}
                </Text>
                <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                  {t(`wishlist.statusOptions.${item.status.toLowerCase()}`)}
                </Text>
              </View>

              <View style={styles.detailItem}>
                <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  {t('wishlist.isShared')}
                </Text>
                <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                  {item.isShared ? t('common.yes') : t('common.no')}
                </Text>
              </View>

              {item.priority && (
                <View style={styles.detailItem}>
                  <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                    {t('wishlist.priority')}
                  </Text>
                  <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                    {item.priority.name}
                  </Text>
                </View>
              )}
            </View>
          </View>

          {/* Linked savings goal section */}
          <View style={[styles.section, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                {t('wishlist.linkedSavingsGoal')}
              </Text>

              {item.linkedSavingsGoalId ? (
                <Button
                  title={t('wishlist.unlinkFromGoal')}
                  onPress={handleUnlinkFromGoal}
                  variant="outline"
                  size="small"
                  icon="unlink-outline"
                  isLoading={isLinking}
                />
              ) : (
                <Button
                  title={t('wishlist.linkToGoal')}
                  onPress={handleLinkToGoal}
                  variant="outline"
                  size="small"
                  icon="link-outline"
                  isLoading={isLinking}
                  disabled={activeGoals.length === 0}
                />
              )}
            </View>

            {item.linkedSavingsGoal ? (
              <View style={styles.linkedGoalContainer}>
                <Text style={[styles.linkedGoalTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                  {item.linkedSavingsGoal.title}
                </Text>

                <Text style={[styles.linkedGoalDetails, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  {formatCurrency(item.linkedSavingsGoal.currentAmount)} / {formatCurrency(item.linkedSavingsGoal.targetAmount)}
                  {' '}({Math.round(item.linkedSavingsGoal.progress)}%)
                </Text>

                <Button
                  title={t('common.viewDetails')}
                  onPress={() => router.push({
                    pathname: "/savings/goals/[id]",
                    params: { id: item.linkedSavingsGoalId }
                  } as any)}
                  variant="secondary"
                  size="small"
                  style={styles.viewDetailsButton}
                />
              </View>
            ) : (
              <Text style={[styles.noLinkText, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {activeGoals.length > 0
                  ? t('wishlist.noLinkedGoal')
                  : t('wishlist.noGoalsAvailable')}
              </Text>
            )}
          </View>
        </ScrollView>

        {/* Link modal */}
        <Modal
          visible={showLinkModal}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setShowLinkModal(false)}
        >
          <View style={styles.modalContainer}>
            <View
              style={[
                styles.modalContent,
                { backgroundColor: isDarkMode ? '#161B22' : '#FFFFFF' }
              ]}
            >
              <View style={styles.modalHeader}>
                <Text style={[styles.modalTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                  {t('wishlist.linkToGoal')}
                </Text>

                <Button
                  title=""
                  icon="close-outline"
                  onPress={() => setShowLinkModal(false)}
                  variant="secondary"
                  size="small"
                />
              </View>

              <View style={styles.modalBody}>
                <Dropdown
                  label={t('wishlist.selectGoal')}
                  options={goalOptions}
                  selectedValue={selectedGoalId}
                  onValueChange={(value) => setSelectedGoalId(value as number)}
                  placeholder={t('common.select')}
                  required
                />

                <Button
                  title={t('common.save')}
                  onPress={handleSubmitLink}
                  isLoading={isLinking}
                  disabled={!selectedGoalId}
                  fullWidth
                  style={styles.saveButton}
                />
              </View>
            </View>
          </View>
        </Modal>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    flex: 1,
  },
  actions: {
    flexDirection: 'row',
  },
  actionButton: {
    marginLeft: 8,
  },
  image: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 16,
  },
  imagePlaceholder: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  description: {
    fontSize: 16,
    marginBottom: 16,
  },
  section: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  price: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  viewButton: {
    alignSelf: 'flex-start',
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  detailItem: {
    width: '48%',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  linkedGoalContainer: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#30363D',
  },
  linkedGoalTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  linkedGoalDetails: {
    fontSize: 14,
    marginBottom: 8,
  },
  viewDetailsButton: {
    alignSelf: 'flex-start',
  },
  noLinkText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 16,
  },
  modalContent: {
    borderRadius: 8,
    padding: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalBody: {
    marginBottom: 16,
  },
  saveButton: {
    marginTop: 16,
  },
});
