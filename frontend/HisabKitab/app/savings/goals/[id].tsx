import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Al<PERSON>, Modal } from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../../src/contexts/ThemeContext';
import { useSavings } from '../../../src/contexts/SavingsContext';
import GoalProgressBar from '../../../src/components/savings/GoalProgressBar';
import ContributionList from '../../../src/components/savings/ContributionList';
import ForecastTimeline from '../../../src/components/savings/ForecastTimeline';
import WishlistItemCard from '../../../src/components/savings/WishlistItemCard';
import Button from '../../../src/components/ui/Button';
import LoadingIndicator from '../../../src/components/ui/LoadingIndicator';
import EmptyState from '../../../src/components/ui/EmptyState';
import ContributionForm from '../../../src/components/savings/ContributionForm';
import { formatDate } from '../../../src/utils/formatters';
import { SavingsContribution } from '../../../src/models/SavingsGoal';
import { getFrequencyTypeString } from '../../../src/utils/typeConversion';

export default function SavingsGoalDetailsScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const goalId = parseInt(id);

  const {
    getSavingsGoal,
    loadSavingsGoals,
    deleteSavingsGoal,
    getSavingsGoalForecast,
    addContribution,
    updateContribution,
    deleteContribution,
    wishlistItems,
  } = useSavings();

  // State
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isSubmittingContribution, setIsSubmittingContribution] = useState(false);
  const [forecast, setForecast] = useState<any>(null);
  const [showContributionModal, setShowContributionModal] = useState(false);
  const [editingContribution, setEditingContribution] = useState<SavingsContribution | null>(null);

  // Get goal data
  const goal = getSavingsGoal(goalId);

  // Get linked wishlist items
  const linkedWishlistItems = wishlistItems.filter(item => item.linkedSavingsGoalId === goalId);

  // Load data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        await loadSavingsGoals();
        const forecastData = await getSavingsGoalForecast(goalId);
        if (forecastData) {
          setForecast(forecastData);
        }
      } catch (error) {
        console.error('Error loading goal data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [goalId]);

  // Handle edit goal
  const handleEditGoal = () => {
    router.push({
      pathname: "/savings/goals/edit/[id]",
      params: { id: goalId }
    } as any);
  };

  // Handle delete goal
  const handleDeleteGoal = () => {
    Alert.alert(
      t('savings.deleteGoal'),
      t('savings.deleteGoalConfirmation'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            setIsDeleting(true);
            try {
              const success = await deleteSavingsGoal(goalId);
              if (success) {
                router.back();
              }
            } catch (error) {
              console.error('Error deleting goal:', error);
              Alert.alert(t('common.error'), t('savings.deleteGoalError'));
            } finally {
              setIsDeleting(false);
            }
          },
        },
      ]
    );
  };

  // Handle add contribution
  const handleAddContribution = () => {
    setEditingContribution(null);
    setShowContributionModal(true);
  };

  // Handle edit contribution
  const handleEditContribution = (contribution: SavingsContribution) => {
    setEditingContribution(contribution);
    setShowContributionModal(true);
  };

  // Handle delete contribution
  const handleDeleteContribution = (contribution: SavingsContribution) => {
    Alert.alert(
      t('savings.deleteContribution'),
      t('savings.deleteContributionConfirmation'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteContribution(contribution.id);
            } catch (error) {
              console.error('Error deleting contribution:', error);
              Alert.alert(t('common.error'), t('savings.deleteContributionError'));
            }
          },
        },
      ]
    );
  };

  // Handle submit contribution
  const handleSubmitContribution = async (values: any) => {
    setIsSubmittingContribution(true);
    try {
      if (editingContribution) {
        await updateContribution(values);
      } else {
        await addContribution(values);
      }
      setShowContributionModal(false);
    } catch (error) {
      console.error('Error saving contribution:', error);
      Alert.alert(t('common.error'), t('common.unexpectedError'));
    } finally {
      setIsSubmittingContribution(false);
    }
  };

  // Handle view wishlist item
  const handleViewWishlistItem = (itemId: number) => {
    router.push({
      pathname: "/savings/wishlist/[id]",
      params: { id: itemId }
    } as any);
  };

  // Render loading state
  if (isLoading || !goal) {
    return <LoadingIndicator fullScreen message={t('common.loading')} />;
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: t('savings.goalDetails'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
          headerShadowVisible: false,
        }}
      />

      <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Goal header */}
          <View style={styles.header}>
            <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {goal.title}
            </Text>

            <View style={styles.actions}>
              <Button
                title={t('common.edit')}
                onPress={handleEditGoal}
                variant="outline"
                size="small"
                icon="pencil-outline"
                style={styles.actionButton}
              />

              <Button
                title={t('common.delete')}
                onPress={handleDeleteGoal}
                variant="danger"
                size="small"
                icon="trash-outline"
                isLoading={isDeleting}
                style={styles.actionButton}
              />
            </View>
          </View>

          {/* Goal description */}
          {goal.description && (
            <Text style={[styles.description, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {goal.description}
            </Text>
          )}

          {/* Progress section */}
          <View style={[styles.section, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
            <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {t('savings.progress')}
            </Text>

            <GoalProgressBar
              currentAmount={goal.currentAmount}
              targetAmount={goal.targetAmount}
              isOnTrack={goal.isOnTrack}
              daysRemaining={goal.daysRemaining}
              showDetails={true}
            />
          </View>

          {/* Details section */}
          <View style={[styles.section, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
            <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {t('common.details')}
            </Text>

            <View style={styles.detailsGrid}>
              <View style={styles.detailItem}>
                <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  {t('savings.startDate')}
                </Text>
                <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                  {formatDate(goal.startDate)}
                </Text>
              </View>

              <View style={styles.detailItem}>
                <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  {t('savings.targetDate')}
                </Text>
                <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                  {formatDate(goal.targetDate)}
                </Text>
              </View>

              <View style={styles.detailItem}>
                <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  {t('savings.status')}
                </Text>
                <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                  {t(`savings.statusOptions.${goal.status.toLowerCase()}`)}
                </Text>
              </View>

              <View style={styles.detailItem}>
                <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  {t('savings.isShared')}
                </Text>
                <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                  {goal.isShared ? t('common.yes') : t('common.no')}
                </Text>
              </View>

              {goal.priority && (
                <View style={styles.detailItem}>
                  <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                    {t('savings.priority')}
                  </Text>
                  <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                    {goal.priority.name}
                  </Text>
                </View>
              )}

              <View style={styles.detailItem}>
                <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  {t('savings.autoContribute')}
                </Text>
                <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                  {goal.autoContribute ? t('common.yes') : t('common.no')}
                </Text>
              </View>

              {goal.autoContribute && goal.autoContributeAmount && goal.autoContributeFrequency && (
                <View style={[styles.detailItem, styles.fullWidth]}>
                  <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                    {t('savings.autoContributeAmount')}
                  </Text>
                  <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                    {goal.autoContributeAmount} ({t(`loans.frequencyTypes.${getFrequencyTypeString(goal.autoContributeFrequency)}`)})
                  </Text>
                </View>
              )}
            </View>
          </View>

          {/* Forecast section */}
          {forecast && (
            <View style={[styles.section, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
              <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                {t('savings.forecast')}
              </Text>

              <ForecastTimeline forecast={forecast} />
            </View>
          )}

          {/* Contributions section */}
          <View style={styles.listSection}>
            <ContributionList
              contributions={goal.contributions || []}
              onAddContribution={handleAddContribution}
              onEditContribution={handleEditContribution}
              onDeleteContribution={handleDeleteContribution}
            />
          </View>

          {/* Linked wishlist items section */}
          {linkedWishlistItems.length > 0 && (
            <View style={styles.listSection}>
              <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                {t('wishlist.linkedSavingsGoal')}
              </Text>

              {linkedWishlistItems.map(item => (
                <WishlistItemCard
                  key={item.id}
                  item={item}
                  onPress={() => handleViewWishlistItem(item.id)}
                  showActions={false}
                />
              ))}
            </View>
          )}
        </ScrollView>

        {/* Contribution modal */}
        <Modal
          visible={showContributionModal}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setShowContributionModal(false)}
        >
          <View style={styles.modalContainer}>
            <View
              style={[
                styles.modalContent,
                { backgroundColor: isDarkMode ? '#161B22' : '#FFFFFF' }
              ]}
            >
              <View style={styles.modalHeader}>
                <Text style={[styles.modalTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                  {editingContribution ? t('savings.editContribution') : t('savings.addContribution')}
                </Text>

                <Button
                  title=""
                  icon="close-outline"
                  onPress={() => setShowContributionModal(false)}
                  variant="secondary"
                  size="small"
                />
              </View>

              <ContributionForm
                initialValues={editingContribution || {}}
                onSubmit={handleSubmitContribution}
                isLoading={isSubmittingContribution}
                savingsGoalId={goalId}
                mode={editingContribution ? 'edit' : 'create'}
              />
            </View>
          </View>
        </Modal>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    flex: 1,
  },
  actions: {
    flexDirection: 'row',
  },
  actionButton: {
    marginLeft: 8,
  },
  description: {
    fontSize: 16,
    marginBottom: 16,
  },
  section: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  detailItem: {
    width: '48%',
    marginBottom: 12,
  },
  fullWidth: {
    width: '100%',
  },
  detailLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  listSection: {
    marginBottom: 16,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingTop: 16,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
});
