import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../../src/contexts/ThemeContext';
import { useSavings } from '../../../src/contexts/SavingsContext';
import SavingsGoalForm from '../../../src/components/savings/SavingsGoalForm';
import LoadingIndicator from '../../../src/components/ui/LoadingIndicator';
import apiService from '../../../src/services/api';
// Define interfaces locally to avoid import issues
interface Priority {
  id: number;
  name: string;
  description?: string;
  color?: string;
  order: number;
  isSystem: boolean;
  createdAt: string;
  updatedAt?: string;
}

interface Family {
  id: number;
  name: string;
  description?: string;
  createdByUserId: number;
  createdByUsername?: string;
  createdAt: string;
  updatedAt?: string;
  memberCount: number;
  isUserAdmin: boolean;
}

export default function CreateSavingsGoalScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const router = useRouter();
  const { createSavingsGoal } = useSavings();

  // State
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [priorities, setPriorities] = useState<Priority[]>([]);
  const [families, setFamilies] = useState<Family[]>([]);

  // Load priorities and families
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        // Load priorities
        const prioritiesResponse = await apiService.priorities.getAllPriorities();
        if (prioritiesResponse.status === 200 && prioritiesResponse.data) {
          setPriorities(prioritiesResponse.data);
        }

        // Load families
        const familiesResponse = await apiService.family.getFamilies();
        if (familiesResponse.status === 200 && familiesResponse.data) {
          setFamilies(familiesResponse.data);
        }
      } catch (error) {
        console.error('Error loading data:', error);
        Alert.alert(t('common.error'), t('common.unexpectedError'));
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    setIsSubmitting(true);
    try {
      const goal = await createSavingsGoal(values);
      if (goal) {
        router.push({
          pathname: "/savings/goals/[id]",
          params: { id: goal.id }
        } as any);
      }
    } catch (error) {
      console.error('Error creating savings goal:', error);
      Alert.alert(t('common.error'), t('common.unexpectedError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render loading state
  if (isLoading) {
    return <LoadingIndicator fullScreen message={t('common.loading')} />;
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: t('savings.createGoal'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
          headerShadowVisible: false,
        }}
      />

      <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <SavingsGoalForm
          onSubmit={handleSubmit}
          isLoading={isSubmitting}
          priorities={priorities}
          families={families}
          mode="create"
        />
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
