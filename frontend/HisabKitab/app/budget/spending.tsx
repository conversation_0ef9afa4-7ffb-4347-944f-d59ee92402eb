import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import { useBudgetControl } from '../../src/contexts/BudgetControlContext';
import { useAuth } from '../../src/contexts/AuthContext';
import { Stack, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

// Define the Family interface
interface Family {
  id: number;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  members?: FamilyMember[];
}

interface FamilyMember {
  userId: number;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  joinedAt: string;
}

// Mock useFamily hook
const useFamily = () => {
  // Using a constant instead of state to avoid unused setFamilies warning
  const families: Family[] = [
    {
      id: 1,
      name: 'My Family',
      description: 'Personal family group',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
      members: [
        {
          userId: 1,
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          role: 'Admin',
          joinedAt: new Date().toISOString()
        },
        {
          userId: 2,
          firstName: 'Jane',
          lastName: 'Doe',
          email: '<EMAIL>',
          role: 'Member',
          joinedAt: new Date().toISOString()
        }
      ]
    },
    {
      id: 2,
      name: 'Work Group',
      description: 'Office expenses',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
      members: [
        {
          userId: 1,
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          role: 'Admin',
          joinedAt: new Date().toISOString()
        }
      ]
    }
  ];
  const [selectedFamily, setSelectedFamily] = useState<Family | null>(null);

  return {
    families,
    selectedFamily,
    loading: false,
    error: null,
    isFamilyAdmin: false,
    fetchFamilies: async () => {},
    fetchFamily: async () => {},
    createFamily: async () => false,
    updateFamily: async () => false,
    deleteFamily: async () => false,
    setSelectedFamily,
    inviteMember: async () => false,
    removeMember: async () => false,
    leaveFamily: async () => false,
    changeMemberRole: async () => false
  };
};
import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';

// Import components
import SpendingLimitCard from '../../src/components/budget/SpendingLimitCard';
import FamilySelector from '../../src/components/family/FamilySelector';

export default function SpendingLimitsScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const router = useRouter();
  const { families, selectedFamily, setSelectedFamily } = useFamily();
  const { user, isPlatformAdmin, isFamilyAdmin } = useAuth();

  const {
    spendingLimits,
    loading,
    error,
    fetchSpendingLimits,
    fetchSpendingLimitRemaining,
    createSpendingLimit,
    updateSpendingLimit,
    deleteSpendingLimit,
  } = useBudgetControl();

  // Local state
  const [spendingRemaining, setSpendingRemaining] = useState<Record<number, number>>({});
  const [loadingRemaining, setLoadingRemaining] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedLimit, setSelectedLimit] = useState<any>(null);

  // Form state
  const [formMemberId, setFormMemberId] = useState<number | null>(null);
  const [formAmount, setFormAmount] = useState('');
  const [formPeriod, setFormPeriod] = useState('Monthly');
  const [formStartDate, setFormStartDate] = useState(new Date());
  const [formEndDate, setFormEndDate] = useState<Date | null>(null);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  // Load data on mount and when selected family changes
  useEffect(() => {
    if (selectedFamily) {
      fetchSpendingLimits(selectedFamily.id);
    }
  }, [selectedFamily]);

  // Load remaining amounts when spending limits change
  useEffect(() => {
    const loadRemainingAmounts = async () => {
      setLoadingRemaining(true);

      // Load spending remaining amounts
      const spendingRemainingData: Record<number, number> = {};
      for (const limit of spendingLimits) {
        const remaining = await fetchSpendingLimitRemaining(limit.id);
        if (remaining !== null) {
          spendingRemainingData[limit.id] = remaining;
        }
      }
      setSpendingRemaining(spendingRemainingData);

      setLoadingRemaining(false);
    };

    if (spendingLimits.length > 0) {
      loadRemainingAmounts();
    }
  }, [spendingLimits]);

  // Handle family change
  const handleFamilyChange = (familyId: number) => {
    const family = families.find((f: Family) => f.id === familyId);
    if (family) {
      setSelectedFamily(family);
    }
  };

  // Open create modal
  const openCreateModal = () => {
    setIsEditing(false);
    setSelectedLimit(null);
    setFormMemberId(null);
    setFormAmount('');
    setFormPeriod('Monthly');
    setFormStartDate(new Date());
    setFormEndDate(null);
    setIsModalVisible(true);
  };

  // Open edit modal
  const openEditModal = (limit: any) => {
    setIsEditing(true);
    setSelectedLimit(limit);
    setFormMemberId(limit.familyMemberUserId);
    setFormAmount(limit.amount.toString());
    setFormPeriod(limit.period);
    setFormStartDate(new Date(limit.startDate));
    setFormEndDate(limit.endDate ? new Date(limit.endDate) : null);
    setIsModalVisible(true);
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!selectedFamily) return;
    if (!formMemberId) {
      Alert.alert(t('common.error'), t('budget.selectMember'));
      return;
    }
    if (!formAmount || isNaN(parseFloat(formAmount)) || parseFloat(formAmount) <= 0) {
      Alert.alert(t('common.error'), t('budget.enterValidAmount'));
      return;
    }

    const data = {
      familyMemberUserId: formMemberId,
      amount: parseFloat(formAmount),
      period: formPeriod,
      startDate: format(formStartDate, 'yyyy-MM-dd'),
      endDate: formEndDate ? format(formEndDate, 'yyyy-MM-dd') : undefined,
      isActive: true,
    };

    let success;
    if (isEditing && selectedLimit) {
      success = await updateSpendingLimit(selectedLimit.id, data);
    } else {
      success = await createSpendingLimit(selectedFamily.id, data);
    }

    if (success) {
      setIsModalVisible(false);
      fetchSpendingLimits(selectedFamily.id);
    }
  };

  // Handle delete
  const handleDelete = async (id: number) => {
    Alert.alert(
      t('common.confirm'),
      t('budget.confirmDeleteSpendingLimit'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            if (selectedFamily) {
              const success = await deleteSpendingLimit(id);
              if (success) {
                fetchSpendingLimits(selectedFamily.id);
              }
            }
          },
        },
      ]
    );
  };

  // Check if user has admin rights
  const hasAdminRights = isPlatformAdmin || (selectedFamily && isFamilyAdmin);

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#F9FAFB' }]}>
      <Stack.Screen
        options={{
          title: t('budget.spendingLimits'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#111827',
          headerRight: () => (
            hasAdminRights && (
              <TouchableOpacity
                style={styles.addButton}
                onPress={openCreateModal}
              >
                <Ionicons
                  name="add"
                  size={24}
                  color={isDarkMode ? '#FFFFFF' : '#111827'}
                />
              </TouchableOpacity>
            )
          ),
        }}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.familySelectorContainer}>
          {families.length > 0 && (
            <FamilySelector
              families={families}
              selectedFamilyId={selectedFamily?.id}
              onFamilyChange={handleFamilyChange}
            />
          )}
        </View>

        {!selectedFamily ? (
          <View style={styles.noFamilyContainer}>
            <Text style={[styles.noFamilyText, { color: isDarkMode ? '#FFFFFF' : '#111827' }]}>
              {t('budget.selectFamily')}
            </Text>
          </View>
        ) : loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0366D6" />
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { color: isDarkMode ? '#F87171' : '#EF4444' }]}>
              {error}
            </Text>
          </View>
        ) : spendingLimits.length === 0 ? (
          <View style={[styles.emptyContainer, { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' }]}>
            <Text style={[styles.emptyText, { color: isDarkMode ? '#9CA3AF' : '#6B7280' }]}>
              {t('budget.noSpendingLimits')}
            </Text>
            {hasAdminRights && (
              <TouchableOpacity
                style={[styles.emptyButton, { backgroundColor: '#0366D6' }]}
                onPress={openCreateModal}
              >
                <Text style={styles.emptyButtonText}>
                  {t('budget.createSpendingLimit')}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        ) : (
          <View style={styles.limitsContainer}>
            {loadingRemaining ? (
              <ActivityIndicator size="small" color="#0366D6" />
            ) : (
              spendingLimits.map(limit => (
                <SpendingLimitCard
                  key={limit.id}
                  id={limit.id}
                  memberName={limit.familyMemberName}
                  amount={limit.amount}
                  spent={limit.amount - (spendingRemaining[limit.id] || 0)}
                  period={limit.period}
                  onEdit={hasAdminRights ? () => openEditModal(limit) : undefined}
                  onDelete={hasAdminRights ? () => handleDelete(limit.id) : undefined}
                />
              ))
            )}
          </View>
        )}
      </ScrollView>

      {/* Create/Edit Modal */}
      <Modal
        visible={isModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[
              styles.modalContent,
              { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text
                style={[
                  styles.modalTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#111827' },
                ]}
              >
                {isEditing ? t('budget.editSpendingLimit') : t('budget.createSpendingLimit')}
              </Text>
              <TouchableOpacity onPress={() => setIsModalVisible(false)}>
                <Ionicons
                  name="close"
                  size={24}
                  color={isDarkMode ? '#FFFFFF' : '#374151'}
                />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.formContainer}>
              <Text style={[styles.label, { color: isDarkMode ? '#D1D5DB' : '#4B5563' }]}>
                {t('budget.familyMember')}*
              </Text>
              <View
                style={[
                  styles.pickerContainer,
                  { backgroundColor: isDarkMode ? '#374151' : '#F3F4F6' },
                ]}
              >
                <ScrollView
                  showsVerticalScrollIndicator={false}
                  contentContainerStyle={styles.memberContainer}
                >
                  {selectedFamily?.members?.map((member: FamilyMember) => (
                    <TouchableOpacity
                      key={member.userId}
                      style={[
                        styles.memberButton,
                        formMemberId === member.userId && styles.selectedMember,
                        {
                          backgroundColor: isDarkMode
                            ? formMemberId === member.userId
                              ? '#0366D6'
                              : '#4B5563'
                            : formMemberId === member.userId
                            ? '#0366D6'
                            : '#E5E7EB',
                        },
                      ]}
                      onPress={() => setFormMemberId(member.userId)}
                    >
                      <Text
                        style={[
                          styles.memberText,
                          {
                            color: formMemberId === member.userId
                              ? '#FFFFFF'
                              : isDarkMode
                              ? '#D1D5DB'
                              : '#4B5563',
                          },
                        ]}
                      >
                        {member.firstName} {member.lastName}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              <Text style={[styles.label, { color: isDarkMode ? '#D1D5DB' : '#4B5563' }]}>
                {t('budget.amount')}*
              </Text>
              <TextInput
                style={[
                  styles.input,
                  {
                    backgroundColor: isDarkMode ? '#374151' : '#F3F4F6',
                    color: isDarkMode ? '#FFFFFF' : '#111827',
                  },
                ]}
                value={formAmount}
                onChangeText={setFormAmount}
                placeholder={t('budget.enterAmount')}
                placeholderTextColor={isDarkMode ? '#9CA3AF' : '#6B7280'}
                keyboardType="numeric"
              />

              <Text style={[styles.label, { color: isDarkMode ? '#D1D5DB' : '#4B5563' }]}>
                {t('budget.period')}*
              </Text>
              <View
                style={[
                  styles.pickerContainer,
                  { backgroundColor: isDarkMode ? '#374151' : '#F3F4F6' },
                ]}
              >
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.periodContainer}
                >
                  {['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Yearly'].map(period => (
                    <TouchableOpacity
                      key={period}
                      style={[
                        styles.periodButton,
                        formPeriod === period && styles.selectedPeriod,
                        {
                          backgroundColor: isDarkMode
                            ? formPeriod === period
                              ? '#0366D6'
                              : '#4B5563'
                            : formPeriod === period
                            ? '#0366D6'
                            : '#E5E7EB',
                        },
                      ]}
                      onPress={() => setFormPeriod(period)}
                    >
                      <Text
                        style={[
                          styles.periodText,
                          {
                            color: formPeriod === period
                              ? '#FFFFFF'
                              : isDarkMode
                              ? '#D1D5DB'
                              : '#4B5563',
                          },
                        ]}
                      >
                        {t(`budget.period.${period.toLowerCase()}`)}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              <Text style={[styles.label, { color: isDarkMode ? '#D1D5DB' : '#4B5563' }]}>
                {t('budget.startDate')}*
              </Text>
              <TouchableOpacity
                style={[
                  styles.dateButton,
                  { backgroundColor: isDarkMode ? '#374151' : '#F3F4F6' },
                ]}
                onPress={() => setShowStartDatePicker(true)}
              >
                <Text
                  style={[
                    styles.dateText,
                    { color: isDarkMode ? '#FFFFFF' : '#111827' },
                  ]}
                >
                  {format(formStartDate, 'MMM d, yyyy')}
                </Text>
                <Ionicons
                  name="calendar-outline"
                  size={20}
                  color={isDarkMode ? '#D1D5DB' : '#6B7280'}
                />
              </TouchableOpacity>

              <Text style={[styles.label, { color: isDarkMode ? '#D1D5DB' : '#4B5563' }]}>
                {t('budget.endDate')} ({t('common.optional')})
              </Text>
              <TouchableOpacity
                style={[
                  styles.dateButton,
                  { backgroundColor: isDarkMode ? '#374151' : '#F3F4F6' },
                ]}
                onPress={() => setShowEndDatePicker(true)}
              >
                <Text
                  style={[
                    styles.dateText,
                    { color: isDarkMode ? '#FFFFFF' : '#111827' },
                  ]}
                >
                  {formEndDate ? format(formEndDate, 'MMM d, yyyy') : t('budget.noEndDate')}
                </Text>
                <Ionicons
                  name="calendar-outline"
                  size={20}
                  color={isDarkMode ? '#D1D5DB' : '#6B7280'}
                />
              </TouchableOpacity>

              {showStartDatePicker && (
                <DateTimePicker
                  value={formStartDate}
                  mode="date"
                  display="default"
                  onChange={(event, selectedDate) => {
                    setShowStartDatePicker(false);
                    if (selectedDate) {
                      setFormStartDate(selectedDate);
                    }
                  }}
                />
              )}

              {showEndDatePicker && (
                <DateTimePicker
                  value={formEndDate || new Date()}
                  mode="date"
                  display="default"
                  onChange={(event, selectedDate) => {
                    setShowEndDatePicker(false);
                    setFormEndDate(selectedDate || null);
                  }}
                />
              )}
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[
                  styles.cancelButton,
                  { borderColor: isDarkMode ? '#4B5563' : '#D1D5DB' },
                ]}
                onPress={() => setIsModalVisible(false)}
              >
                <Text
                  style={[
                    styles.cancelButtonText,
                    { color: isDarkMode ? '#FFFFFF' : '#374151' },
                  ]}
                >
                  {t('common.cancel')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.submitButton, { backgroundColor: '#0366D6' }]}
                onPress={handleSubmit}
              >
                <Text style={styles.submitButtonText}>
                  {isEditing ? t('common.update') : t('common.create')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  familySelectorContainer: {
    marginBottom: 16,
  },
  limitsContainer: {
    marginBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
  noFamilyContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noFamilyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 24,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
  },
  emptyButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  emptyButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  addButton: {
    padding: 8,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 12,
    padding: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  formContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
  },
  input: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginBottom: 16,
  },
  pickerContainer: {
    borderRadius: 8,
    marginBottom: 16,
    maxHeight: 150,
  },
  memberContainer: {
    padding: 8,
  },
  memberButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedMember: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  memberText: {
    fontSize: 14,
  },
  periodContainer: {
    padding: 8,
  },
  periodButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginRight: 8,
  },
  selectedPeriod: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  periodText: {
    fontSize: 14,
  },
  dateButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginBottom: 16,
  },
  dateText: {
    fontSize: 14,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 14,
  },
  submitButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  submitButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '500',
  },
});
