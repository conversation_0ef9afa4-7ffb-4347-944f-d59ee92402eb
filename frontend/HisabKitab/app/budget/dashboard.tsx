import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import { useBudgetControl } from '../../src/contexts/BudgetControlContext';
import { useAuth } from '../../src/contexts/AuthContext';
import { Stack, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

// Define the Family interface
interface Family {
  id: number;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  members?: FamilyMember[];
}

interface FamilyMember {
  userId: number;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  joinedAt: string;
}

// Mock useFamily hook
const useFamily = () => {
  // Using a constant instead of state to avoid unused setFamilies warning
  const families: Family[] = [
    {
      id: 1,
      name: 'My Family',
      description: 'Personal family group',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
      members: [
        {
          userId: 1,
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          role: 'Admin',
          joinedAt: new Date().toISOString()
        }
      ]
    },
    {
      id: 2,
      name: 'Work Group',
      description: 'Office expenses',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true
    }
  ];
  const [selectedFamily, setSelectedFamily] = useState<Family | null>(null);

  return {
    families,
    selectedFamily,
    loading: false,
    error: null,
    isFamilyAdmin: true,
    fetchFamilies: async () => {},
    fetchFamily: async () => {},
    createFamily: async () => false,
    updateFamily: async () => false,
    deleteFamily: async () => false,
    setSelectedFamily,
    inviteMember: async () => false,
    removeMember: async () => false,
    leaveFamily: async () => false,
    changeMemberRole: async () => false
  };
};

// Import components
import MetricCard from '../../src/components/analytics/MetricCard';
import BudgetLimitCard from '../../src/components/budget/BudgetLimitCard';
import SpendingLimitCard from '../../src/components/budget/SpendingLimitCard';
import FamilySelector from '../../src/components/family/FamilySelector';

export default function BudgetDashboardScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const router = useRouter();
  const { families, selectedFamily, setSelectedFamily } = useFamily();
  const { isPlatformAdmin, isFamilyAdmin } = useAuth(); // Removed unused 'user' variable

  const {
    budgetLimits,
    spendingLimits,
    loading,
    error,
    fetchBudgetLimits,
    fetchSpendingLimits,
    fetchBudgetRemaining,
    fetchSpendingLimitRemaining,
  } = useBudgetControl();

  // Local state for remaining amounts
  const [budgetRemaining, setBudgetRemaining] = useState<Record<number, number>>({});
  const [spendingRemaining, setSpendingRemaining] = useState<Record<number, number>>({});
  const [loadingRemaining, setLoadingRemaining] = useState(false);

  // Load data on mount and when selected family changes
  useEffect(() => {
    if (selectedFamily) {
      fetchBudgetLimits(selectedFamily.id);
      fetchSpendingLimits(selectedFamily.id);
    }
  }, [selectedFamily]);

  // Load remaining amounts when budget limits or spending limits change
  useEffect(() => {
    const loadRemainingAmounts = async () => {
      setLoadingRemaining(true);

      // Load budget remaining amounts
      const budgetRemainingData: Record<number, number> = {};
      for (const limit of budgetLimits) {
        const remaining = await fetchBudgetRemaining(limit.id);
        if (remaining !== null) {
          budgetRemainingData[limit.id] = remaining;
        }
      }
      setBudgetRemaining(budgetRemainingData);

      // Load spending remaining amounts
      const spendingRemainingData: Record<number, number> = {};
      for (const limit of spendingLimits) {
        const remaining = await fetchSpendingLimitRemaining(limit.id);
        if (remaining !== null) {
          spendingRemainingData[limit.id] = remaining;
        }
      }
      setSpendingRemaining(spendingRemainingData);

      setLoadingRemaining(false);
    };

    if (budgetLimits.length > 0 || spendingLimits.length > 0) {
      loadRemainingAmounts();
    }
  }, [budgetLimits, spendingLimits]);

  // Handle family change
  const handleFamilyChange = (familyId: number) => {
    const family = families.find(f => f.id === familyId);
    if (family) {
      setSelectedFamily(family);
    }
  };

  // Navigate to budget limits screen
  const navigateToBudgetLimits = () => {
    router.push('/budget/limits' as any);
  };

  // Navigate to spending limits screen
  const navigateToSpendingLimits = () => {
    router.push('/budget/spending' as any);
  };

  // Calculate total budget and spending
  const calculateTotals = () => {
    const totalBudget = budgetLimits.reduce((sum, limit) => sum + limit.amount, 0);
    const totalSpending = budgetLimits.reduce((sum, limit) => {
      const remaining = budgetRemaining[limit.id] || 0;
      return sum + (limit.amount - remaining);
    }, 0);
    const totalRemaining = budgetLimits.reduce((sum, limit) => {
      const remaining = budgetRemaining[limit.id] || 0;
      return sum + remaining;
    }, 0);

    return { totalBudget, totalSpending, totalRemaining };
  };

  const { totalBudget, totalSpending, totalRemaining } = calculateTotals();

  // Check if user has admin rights
  const hasAdminRights = isPlatformAdmin || (selectedFamily && isFamilyAdmin);

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#F9FAFB' }]}>
      <Stack.Screen
        options={{
          title: t('budget.dashboard'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#111827',
        }}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.familySelectorContainer}>
          {families.length > 0 && (
            <FamilySelector
              families={families}
              selectedFamilyId={selectedFamily?.id}
              onFamilyChange={handleFamilyChange}
            />
          )}
        </View>

        {!selectedFamily ? (
          <View style={styles.noFamilyContainer}>
            <Text style={[styles.noFamilyText, { color: isDarkMode ? '#FFFFFF' : '#111827' }]}>
              {t('budget.selectFamily')}
            </Text>
          </View>
        ) : loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0366D6" />
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { color: isDarkMode ? '#F87171' : '#EF4444' }]}>
              {error}
            </Text>
          </View>
        ) : (
          <>
            <View style={styles.metricsContainer}>
              <MetricCard
                title={t('budget.totalBudget')}
                value={totalBudget.toFixed(2)}
                icon="wallet"
                iconColor="#0366D6"
              />
              <MetricCard
                title={t('budget.totalSpent')}
                value={totalSpending.toFixed(2)}
                icon="cash"
                iconColor="#EF4444"
              />
              <MetricCard
                title={t('budget.totalRemaining')}
                value={totalRemaining.toFixed(2)}
                icon="trending-up"
                iconColor="#10B981"
              />
            </View>

            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#111827' }]}>
                {t('budget.budgetLimits')}
              </Text>
              {hasAdminRights && (
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={navigateToBudgetLimits}
                >
                  <Ionicons
                    name="add-circle"
                    size={24}
                    color={isDarkMode ? '#58A6FF' : '#0366D6'}
                  />
                </TouchableOpacity>
              )}
            </View>

            {budgetLimits.length === 0 ? (
              <View style={[styles.emptyContainer, { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' }]}>
                <Text style={[styles.emptyText, { color: isDarkMode ? '#9CA3AF' : '#6B7280' }]}>
                  {t('budget.noBudgetLimits')}
                </Text>
                {hasAdminRights && (
                  <TouchableOpacity
                    style={[styles.emptyButton, { backgroundColor: '#0366D6' }]}
                    onPress={navigateToBudgetLimits}
                  >
                    <Text style={styles.emptyButtonText}>
                      {t('budget.createBudgetLimit')}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            ) : (
              <View style={styles.limitsContainer}>
                {loadingRemaining ? (
                  <ActivityIndicator size="small" color="#0366D6" />
                ) : (
                  budgetLimits.map(limit => (
                    <BudgetLimitCard
                      key={limit.id}
                      id={limit.id}
                      categoryName={limit.categoryName}
                      amount={limit.amount}
                      spent={limit.amount - (budgetRemaining[limit.id] || 0)}
                      period={limit.period}
                      onPress={() => navigateToBudgetLimits()}
                    />
                  ))
                )}
              </View>
            )}

            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#111827' }]}>
                {t('budget.spendingLimits')}
              </Text>
              {hasAdminRights && (
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={navigateToSpendingLimits}
                >
                  <Ionicons
                    name="add-circle"
                    size={24}
                    color={isDarkMode ? '#58A6FF' : '#0366D6'}
                  />
                </TouchableOpacity>
              )}
            </View>

            {spendingLimits.length === 0 ? (
              <View style={[styles.emptyContainer, { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' }]}>
                <Text style={[styles.emptyText, { color: isDarkMode ? '#9CA3AF' : '#6B7280' }]}>
                  {t('budget.noSpendingLimits')}
                </Text>
                {hasAdminRights && (
                  <TouchableOpacity
                    style={[styles.emptyButton, { backgroundColor: '#0366D6' }]}
                    onPress={navigateToSpendingLimits}
                  >
                    <Text style={styles.emptyButtonText}>
                      {t('budget.createSpendingLimit')}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            ) : (
              <View style={styles.limitsContainer}>
                {loadingRemaining ? (
                  <ActivityIndicator size="small" color="#0366D6" />
                ) : (
                  spendingLimits.map(limit => (
                    <SpendingLimitCard
                      key={limit.id}
                      id={limit.id}
                      memberName={limit.familyMemberName}
                      amount={limit.amount}
                      spent={limit.amount - (spendingRemaining[limit.id] || 0)}
                      period={limit.period}
                      onPress={() => navigateToSpendingLimits()}
                    />
                  ))
                )}
              </View>
            )}
          </>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  familySelectorContainer: {
    marginBottom: 16,
  },
  metricsContainer: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  addButton: {
    padding: 4,
  },
  limitsContainer: {
    marginBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
  noFamilyContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noFamilyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 24,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
  },
  emptyButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  emptyButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '500',
  },
});
