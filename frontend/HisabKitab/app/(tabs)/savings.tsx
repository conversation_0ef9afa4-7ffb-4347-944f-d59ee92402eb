import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Text } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { useSavings } from '../../src/contexts/SavingsContext';
import SavingsGoalCard from '../../src/components/savings/SavingsGoalCard';
import WishlistItemCard from '../../src/components/savings/WishlistItemCard';
import EmptyState from '../../src/components/ui/EmptyState';
import LoadingIndicator from '../../src/components/ui/LoadingIndicator';
import Button from '../../src/components/ui/Button';

export default function SavingsScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const router = useRouter();
  const {
    savingsGoals,
    isLoadingSavingsGoals,
    wishlistItems,
    isLoadingWishlistItems,
  } = useSavings();

  // State for active tab
  const [activeTab, setActiveTab] = useState<'goals' | 'wishlist'>('goals');

  // Filter active goals and sort by progress
  const activeGoals = savingsGoals
    .filter(goal => goal.status === 'Active')
    .sort((a, b) => b.progress - a.progress);

  // Filter active wishlist items and sort by priority
  const activeWishlistItems = wishlistItems
    .filter(item => item.status === 'Pending')
    .sort((a, b) => {
      // Sort by priority if available
      if (a.priority && b.priority) {
        const priorityOrder = { 'Urgent': 0, 'High': 1, 'Normal': 2, 'Low': 3 };
        return priorityOrder[a.priority.name as keyof typeof priorityOrder] -
               priorityOrder[b.priority.name as keyof typeof priorityOrder];
      }
      // If one has priority and the other doesn't
      if (a.priority && !b.priority) return -1;
      if (!a.priority && b.priority) return 1;
      // If neither has priority, sort by estimated price
      return b.estimatedPrice - a.estimatedPrice;
    });

  // Navigate to goal details
  const handleGoalPress = (goalId: number) => {
    router.push({
      pathname: "/(tabs)/savings/goals/[id]",
      params: { id: goalId }
    } as any);
  };

  // Navigate to wishlist item details
  const handleWishlistItemPress = (itemId: number) => {
    router.push({
      pathname: "/(tabs)/savings/wishlist/[id]",
      params: { id: itemId }
    } as any);
  };

  // Render loading state
  if (isLoadingSavingsGoals || isLoadingWishlistItems) {
    return <LoadingIndicator fullScreen message={t('common.loading')} />;
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: t('savings.savings'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
          headerShadowVisible: false,
        }}
      />

      <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        {/* Tab selector */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[
              styles.tab,
              activeTab === 'goals' && styles.activeTab,
              { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
            ]}
            onPress={() => setActiveTab('goals')}
          >
            <Ionicons
              name="trending-up-outline"
              size={20}
              color={activeTab === 'goals' ? (isDarkMode ? '#58A6FF' : '#0366D6') : (isDarkMode ? '#8B949E' : '#6E7781')}
              style={styles.tabIcon}
            />
            <Text
              style={[
                styles.tabText,
                {
                  color: activeTab === 'goals'
                    ? (isDarkMode ? '#58A6FF' : '#0366D6')
                    : (isDarkMode ? '#8B949E' : '#6E7781')
                }
              ]}
            >
              {t('savings.goals')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tab,
              activeTab === 'wishlist' && styles.activeTab,
              { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
            ]}
            onPress={() => setActiveTab('wishlist')}
          >
            <Ionicons
              name="gift-outline"
              size={20}
              color={activeTab === 'wishlist' ? (isDarkMode ? '#58A6FF' : '#0366D6') : (isDarkMode ? '#8B949E' : '#6E7781')}
              style={styles.tabIcon}
            />
            <Text
              style={[
                styles.tabText,
                {
                  color: activeTab === 'wishlist'
                    ? (isDarkMode ? '#58A6FF' : '#0366D6')
                    : (isDarkMode ? '#8B949E' : '#6E7781')
                }
              ]}
            >
              {t('wishlist.wishlist')}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Content based on active tab */}
        {activeTab === 'goals' ? (
          <View style={styles.contentContainer}>
            {/* Add goal button */}
            <Button
              title={t('savings.addGoal')}
              onPress={() => router.push({ pathname: "/(tabs)/savings/goals/create" } as any)}
              icon="add-circle-outline"
              variant="primary"
              style={styles.addButton}
            />

            {/* Goals list */}
            <ScrollView
              contentContainerStyle={styles.scrollContent}
              showsVerticalScrollIndicator={false}
            >
              {activeGoals.length > 0 ? (
                activeGoals.map(goal => (
                  <SavingsGoalCard
                    key={goal.id}
                    goal={goal}
                    onPress={() => handleGoalPress(goal.id)}
                    showActions={false}
                  />
                ))
              ) : (
                <EmptyState
                  icon="trending-up-outline"
                  title={t('savings.noGoals')}
                  message={t('savings.noGoalsMessage')}
                  buttonTitle={t('savings.addGoal')}
                  onButtonPress={() => router.push({ pathname: "/(tabs)/savings/goals/create" } as any)}
                />
              )}
            </ScrollView>
          </View>
        ) : (
          <View style={styles.contentContainer}>
            {/* Add wishlist item button */}
            <Button
              title={t('wishlist.addItem')}
              onPress={() => router.push({ pathname: "/(tabs)/savings/wishlist/create" } as any)}
              icon="add-circle-outline"
              variant="primary"
              style={styles.addButton}
            />

            {/* Wishlist items list */}
            <ScrollView
              contentContainerStyle={styles.scrollContent}
              showsVerticalScrollIndicator={false}
            >
              {activeWishlistItems.length > 0 ? (
                activeWishlistItems.map(item => (
                  <WishlistItemCard
                    key={item.id}
                    item={item}
                    onPress={() => handleWishlistItemPress(item.id)}
                    showActions={false}
                  />
                ))
              ) : (
                <EmptyState
                  icon="gift-outline"
                  title={t('wishlist.noItems')}
                  message={t('wishlist.noItemsMessage')}
                  buttonTitle={t('wishlist.addItem')}
                  onButtonPress={() => router.push({ pathname: "/(tabs)/savings/wishlist/create" } as any)}
                />
              )}
            </ScrollView>
          </View>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#58A6FF',
  },
  tabIcon: {
    marginRight: 8,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  addButton: {
    marginVertical: 16,
  },
  scrollContent: {
    paddingBottom: 24,
  },
});
