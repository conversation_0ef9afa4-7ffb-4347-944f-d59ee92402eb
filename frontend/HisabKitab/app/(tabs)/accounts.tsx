import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';

// Import screens
import AccountsScreen from '../../src/screens/accounts/AccountsScreen';
import AccountDetailsScreen from '../../src/screens/accounts/AccountDetailsScreen';
import CreateAccountScreen from '../../src/screens/accounts/CreateAccountScreen';
import EditAccountScreen from '../../src/screens/accounts/EditAccountScreen';
import ShareAccountScreen from '../../src/screens/accounts/ShareAccountScreen';

const Stack = createStackNavigator();

export default function TabAccountsScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: isDarkMode ? '#1D3D47' : '#A1CEDC',
        },
        headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="AccountsList"
        component={AccountsScreen}
        options={{ title: t('accounts.title') }}
      />
      <Stack.Screen
        name="AccountDetails"
        component={AccountDetailsScreen}
        options={{ title: t('accounts.accountDetails') }}
      />
      <Stack.Screen
        name="CreateAccount"
        component={CreateAccountScreen}
        options={{ title: t('accounts.createAccount') }}
      />
      <Stack.Screen
        name="EditAccount"
        component={EditAccountScreen}
        options={{ title: t('accounts.editAccount') }}
      />
      <Stack.Screen
        name="ShareAccount"
        component={ShareAccountScreen}
        options={{ title: t('accounts.shareAccount') }}
      />
    </Stack.Navigator>
  );
}
