import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import { Text, View } from 'react-native';

// Placeholder component for screens that are not yet implemented
const PlaceholderScreen = ({ route }: any) => {
  const { isDarkMode } = useTheme();
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }}>
      <Text style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}>
        {route.name} Screen (Coming Soon)
      </Text>
    </View>
  );
};

// Import screens
import LoansScreen from '../../src/screens/loans/LoansScreen';

const Stack = createStackNavigator();

export default function TabLoansScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: isDarkMode ? '#1D3D47' : '#A1CEDC',
        },
        headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="LoansList"
        component={LoansScreen}
        options={{ title: t('loans.loans') }}
      />
      <Stack.Screen
        name="LoanDetails"
        component={PlaceholderScreen}
        options={{ title: t('loans.loanDetails') }}
      />
      <Stack.Screen
        name="CreateLoan"
        component={PlaceholderScreen}
        options={{ title: t('loans.addLoan') }}
      />
      <Stack.Screen
        name="EditLoan"
        component={PlaceholderScreen}
        options={{ title: t('loans.editLoan') }}
      />
      <Stack.Screen
        name="LoanTimeline"
        component={PlaceholderScreen}
        options={{ title: t('loans.timeline') }}
      />
      <Stack.Screen
        name="AddPayment"
        component={PlaceholderScreen}
        options={{ title: t('loans.addPayment') }}
      />
      <Stack.Screen
        name="EditPayment"
        component={PlaceholderScreen}
        options={{ title: t('loans.editPayment') }}
      />
      <Stack.Screen
        name="AddReminder"
        component={PlaceholderScreen}
        options={{ title: t('loans.addReminder') }}
      />
      <Stack.Screen
        name="EditReminder"
        component={PlaceholderScreen}
        options={{ title: t('loans.editReminder') }}
      />
      <Stack.Screen
        name="EMICalculator"
        component={PlaceholderScreen}
        options={{ title: t('loans.emiCalculator') }}
      />
    </Stack.Navigator>
  );
}
