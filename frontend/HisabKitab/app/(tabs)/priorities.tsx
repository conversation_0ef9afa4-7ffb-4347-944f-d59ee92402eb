import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';

// Import screens
import PrioritiesScreen from '../../src/screens/priorities/PrioritiesScreen';
import CreatePriorityScreen from '../../src/screens/priorities/CreatePriorityScreen';
import EditPriorityScreen from '../../src/screens/priorities/EditPriorityScreen';

const Stack = createStackNavigator();

export default function TabPrioritiesScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: isDarkMode ? '#1D3D47' : '#A1CEDC',
        },
        headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="PrioritiesList"
        component={PrioritiesScreen}
        options={{ title: t('priorities.priorities') }}
      />
      <Stack.Screen
        name="CreatePriority"
        component={CreatePriorityScreen}
        options={{ title: t('priorities.createPriority') }}
      />
      <Stack.Screen
        name="EditPriority"
        component={EditPriorityScreen}
        options={{ title: t('priorities.editPriority') }}
      />
    </Stack.Navigator>
  );
}
