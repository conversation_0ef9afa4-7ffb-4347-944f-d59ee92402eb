import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function TabAnalyticsScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  // Navigation functions
  const navigateToDashboard = () => {
    router.push('/analytics/dashboard' as any);
  };

  const navigateToNetWorth = () => {
    router.push('/analytics/networth' as any);
  };

  const navigateToCashflow = () => {
    router.push('/analytics/cashflow' as any);
  };

  const navigateToExport = () => {
    router.push('/analytics/export' as any);
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#F9FAFB' }]}>
      <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#111827' }]}>
        {t('analytics.analytics')}
      </Text>

      <View style={styles.menuContainer}>
        <TouchableOpacity
          style={[styles.menuItem, { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' }]}
          onPress={navigateToDashboard}
        >
          <Ionicons name="stats-chart" size={24} color="#0366D6" />
          <Text style={[styles.menuText, { color: isDarkMode ? '#FFFFFF' : '#111827' }]}>
            {t('analytics.dashboard')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.menuItem, { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' }]}
          onPress={navigateToNetWorth}
        >
          <Ionicons name="wallet" size={24} color="#8B5CF6" />
          <Text style={[styles.menuText, { color: isDarkMode ? '#FFFFFF' : '#111827' }]}>
            {t('analytics.netWorth')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.menuItem, { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' }]}
          onPress={navigateToCashflow}
        >
          <Ionicons name="trending-up" size={24} color="#F59E0B" />
          <Text style={[styles.menuText, { color: isDarkMode ? '#FFFFFF' : '#111827' }]}>
            {t('analytics.cashflow')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.menuItem, { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' }]}
          onPress={navigateToExport}
        >
          <Ionicons name="download" size={24} color="#10B981" />
          <Text style={[styles.menuText, { color: isDarkMode ? '#FFFFFF' : '#111827' }]}>
            {t('analytics.export')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
  },
  menuContainer: {
    flex: 1,
    gap: 16,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  menuText: {
    fontSize: 18,
    fontWeight: '500',
    marginLeft: 16,
  },
});
