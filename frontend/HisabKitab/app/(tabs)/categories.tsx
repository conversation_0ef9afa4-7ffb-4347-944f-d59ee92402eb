import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';

// Import screens
import CategoriesScreen from '../../src/screens/categories/CategoriesScreen';
import CategoryDetailsScreen from '../../src/screens/categories/CategoryDetailsScreen';
import CreateCategoryScreen from '../../src/screens/categories/CreateCategoryScreen';
import EditCategoryScreen from '../../src/screens/categories/EditCategoryScreen';
import AddBudgetLimitScreen from '../../src/screens/categories/AddBudgetLimitScreen';
import EditBudgetLimitScreen from '../../src/screens/categories/EditBudgetLimitScreen';
import BudgetLimitsScreen from '../../src/screens/categories/BudgetLimitsScreen';

const Stack = createStackNavigator();

export default function TabCategoriesScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: isDarkMode ? '#1D3D47' : '#A1CEDC',
        },
        headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="CategoriesList"
        component={CategoriesScreen}
        options={{ title: t('categories.categories') }}
      />
      <Stack.Screen
        name="CategoryDetails"
        component={CategoryDetailsScreen}
        options={{ title: t('categories.categoryDetails') }}
      />
      <Stack.Screen
        name="CreateCategory"
        component={CreateCategoryScreen}
        options={{ title: t('categories.createCategory') }}
      />
      <Stack.Screen
        name="EditCategory"
        component={EditCategoryScreen}
        options={{ title: t('categories.editCategory') }}
      />
      <Stack.Screen
        name="AddBudgetLimit"
        component={AddBudgetLimitScreen}
        options={{ title: t('categories.addBudgetLimit') }}
      />
      <Stack.Screen
        name="EditBudgetLimit"
        component={EditBudgetLimitScreen}
        options={{ title: t('categories.editBudgetLimit') }}
      />
      <Stack.Screen
        name="BudgetLimits"
        component={BudgetLimitsScreen}
        options={{ title: t('categories.budgetLimits') }}
      />
    </Stack.Navigator>
  );
}
