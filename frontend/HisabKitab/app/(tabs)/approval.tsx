import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function TabApprovalScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  // Navigation functions
  const navigateToRequests = () => {
    router.push('/approval/requests' as any);
  };

  const navigateToCreate = () => {
    router.push('/approval/create' as any);
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#F9FAFB' }]}>
      <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#111827' }]}>
        {t('approval.approval')}
      </Text>

      <View style={styles.menuContainer}>
        <TouchableOpacity
          style={[styles.menuItem, { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' }]}
          onPress={navigateToRequests}
        >
          <Ionicons name="list" size={24} color="#0366D6" />
          <Text style={[styles.menuText, { color: isDarkMode ? '#FFFFFF' : '#111827' }]}>
            {t('approval.requests')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.menuItem, { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' }]}
          onPress={navigateToCreate}
        >
          <Ionicons name="add-circle" size={24} color="#10B981" />
          <Text style={[styles.menuText, { color: isDarkMode ? '#FFFFFF' : '#111827' }]}>
            {t('approval.createRequest')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
  },
  menuContainer: {
    flex: 1,
    gap: 16,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  menuText: {
    fontSize: 18,
    fontWeight: '500',
    marginLeft: 16,
  },
});
