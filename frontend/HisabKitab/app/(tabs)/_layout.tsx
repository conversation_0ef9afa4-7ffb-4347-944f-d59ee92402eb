import { Tabs } from 'expo-router';
import React from 'react';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import { useNotifications } from '../../src/contexts/NotificationContext';
import { View } from 'react-native';
import NotificationBadge from '../../src/components/notifications/NotificationBadge';
import ProtectedRoute from '../../src/components/ProtectedRoute';

export default function TabLayout() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { unreadCount } = useNotifications();

  // Notification badge component for tab bar
  const NotificationIcon = ({ color, size }: { color: string; size: number }) => (
    <View>
      <Ionicons name="notifications-outline" size={size} color={color} />
      <NotificationBadge count={unreadCount} size={16} />
    </View>
  );

  return (
    <ProtectedRoute>
      <Tabs
        screenOptions={{
          tabBarActiveTintColor: isDarkMode ? '#58A6FF' : '#0366D6',
          tabBarInactiveTintColor: isDarkMode ? '#8B949E' : '#6E7781',
          tabBarStyle: {
            backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
            borderTopColor: isDarkMode ? '#30363D' : '#D0D7DE',
          },
          // Hide the tab layout headers to avoid duplicate headers
          headerShown: false,
        }}>
      <Tabs.Screen
        name="index"
        options={{
          title: t('common.appName'),
          tabBarIcon: ({ color, size }) => <Ionicons name="home-outline" size={size} color={color} />,
        }}
      />
      <Tabs.Screen
        name="accounts"
        options={{
          title: t('accounts.accounts'),
          tabBarIcon: ({ color, size }) => <Ionicons name="wallet-outline" size={size} color={color} />,
        }}
      />
      <Tabs.Screen
        name="transactions"
        options={{
          title: t('transactions.transactions'),
          tabBarIcon: ({ color, size }) => <Ionicons name="swap-horizontal-outline" size={size} color={color} />,
        }}
      />
      <Tabs.Screen
        name="loans"
        options={{
          title: t('loans.loans'),
          tabBarIcon: ({ color, size }) => <Ionicons name="cash-outline" size={size} color={color} />,
        }}
      />
      <Tabs.Screen
        name="savings"
        options={{
          title: t('savings.savings'),
          tabBarIcon: ({ color, size }) => <Ionicons name="trending-up-outline" size={size} color={color} />,
        }}
      />
      <Tabs.Screen
        name="analytics"
        options={{
          title: t('analytics.analytics'),
          tabBarIcon: ({ color, size }) => <Ionicons name="stats-chart-outline" size={size} color={color} />,
        }}
      />
      <Tabs.Screen
        name="budget"
        options={{
          title: t('budget.budget'),
          tabBarIcon: ({ color, size }) => <Ionicons name="calculator-outline" size={size} color={color} />,
        }}
      />
      <Tabs.Screen
        name="approval"
        options={{
          title: t('approval.approval'),
          tabBarIcon: ({ color, size }) => <Ionicons name="checkmark-circle-outline" size={size} color={color} />,
        }}
      />
      <Tabs.Screen
        name="families"
        options={{
          title: t('family.families'),
          tabBarIcon: ({ color, size }) => <Ionicons name="people-outline" size={size} color={color} />,
        }}
      />
      <Tabs.Screen
        name="categories"
        options={{
          title: t('categories.categories'),
          tabBarIcon: ({ color, size }) => <Ionicons name="list-outline" size={size} color={color} />,
        }}
      />
      <Tabs.Screen
        name="priorities"
        options={{
          title: t('priorities.priorities'),
          tabBarIcon: ({ color, size }) => <Ionicons name="flag-outline" size={size} color={color} />,
        }}
      />
      <Tabs.Screen
        name="receipts"
        options={{
          title: t('receipts.receipts'),
          tabBarIcon: ({ color, size }) => <Ionicons name="receipt-outline" size={size} color={color} />,
        }}
      />
      <Tabs.Screen
        name="sync"
        options={{
          title: t('sync.sync'),
          tabBarIcon: ({ color, size }) => <Ionicons name="sync-outline" size={size} color={color} />,
        }}
      />
      <Tabs.Screen
        name="notifications"
        options={{
          title: t('notifications.title'),
          tabBarIcon: ({ color, size }) => <NotificationIcon color={color} size={size} />,
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: t('settings.settings'),
          tabBarIcon: ({ color, size }) => <Ionicons name="settings-outline" size={size} color={color} />,
        }}
      />
    </Tabs>
    </ProtectedRoute>
  );
}
