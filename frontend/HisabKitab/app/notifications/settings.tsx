import React from 'react';
import { Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import NotificationSettingsScreen from '../../src/screens/notifications/NotificationSettingsScreen';
import { useFeatureFlag } from '../../src/contexts/FeatureFlagContext';
import FeatureAware from '../../src/components/common/FeatureAware';
import { View, Text, StyleSheet } from 'react-native';

export default function NotificationSettingsLayout() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  return (
    <>
      <Stack.Screen
        options={{
          title: t('notifications.notificationSettings'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
          headerShadowVisible: false,
        }}
      />
      <FeatureAware
        featureName="NotificationSettings"
        fallback={
          <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
            <Text style={[styles.message, { color: isDarkMode ? '#C9D1D9' : '#24292E' }]}>
              {t('features.notAvailable', { feature: 'NotificationSettings' })}
            </Text>
          </View>
        }
      >
        <NotificationSettingsScreen />
      </FeatureAware>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
  },
});
