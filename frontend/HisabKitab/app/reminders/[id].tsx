import React from 'react';
import { Stack, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import ReminderDetailsScreen from '../../src/screens/reminders/ReminderDetailsScreen';

export default function ReminderDetailsLayout() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { id } = useLocalSearchParams();

  return (
    <>
      <Stack.Screen
        options={{
          title: t('reminders.reminderDetails'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
          headerShadowVisible: false,
        }}
      />
      <ReminderDetailsScreen />
    </>
  );
}
