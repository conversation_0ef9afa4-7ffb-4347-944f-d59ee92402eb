import React from 'react';
import { Stack, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../../src/contexts/ThemeContext';
import CreateReminderScreen from '../../../src/screens/reminders/CreateReminderScreen';

export default function EditReminderLayout() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { id } = useLocalSearchParams();

  return (
    <>
      <Stack.Screen
        options={{
          title: t('reminders.editReminder'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
          headerShadowVisible: false,
        }}
      />
      <CreateReminderScreen />
    </>
  );
}
