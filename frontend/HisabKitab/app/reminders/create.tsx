import React from 'react';
import { Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import CreateReminderScreen from '../../src/screens/reminders/CreateReminderScreen';

export default function CreateReminderLayout() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  return (
    <>
      <Stack.Screen
        options={{
          title: t('reminders.createReminder'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
          headerShadowVisible: false,
        }}
      />
      <CreateReminderScreen />
    </>
  );
}
