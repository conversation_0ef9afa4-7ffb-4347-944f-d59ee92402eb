import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';
import { useEffect, useState } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';

// Import providers
import { ThemeProvider } from '../src/contexts/ThemeContext';
import { LanguageProvider } from '../src/contexts/LanguageContext';
import { AuthProvider } from '../src/contexts/AuthContext';
import { AccountProvider } from '../src/contexts/AccountContext';
import { CategoryProvider } from '../src/contexts/CategoryContext';
import { LoanProvider } from '../src/contexts/LoanContext';
import { NotificationProvider } from '../src/contexts/NotificationContext';
import { SavingsProvider } from '../src/contexts/SavingsContext';
import { AnalyticsProvider } from '../src/contexts/AnalyticsContext';
import { BudgetControlProvider } from '../src/contexts/BudgetControlContext';
import { ApprovalProvider } from '../src/contexts/ApprovalContext';
import { ToastProvider } from '../src/contexts/ToastContext';
import { OCRProvider } from '../src/contexts/OCRContext';
import { SyncProvider } from '../src/contexts/SyncContext';
import { FeatureFlagProvider } from '../src/contexts/FeatureFlagContext';

// Import font loader
import { loadFonts } from '../src/utils/fontLoader';

// Import migration utilities
import { checkMigrationStatus } from '../src/utils/migrationUtils';
import MigrationScreen from '../src/screens/MigrationScreen';

// Import i18n
import i18n from '../src/utils/i18n';

// Import SSL bypass for development
import '../src/utils/ssl-bypass';

export default function RootLayout() {
  // Temporarily disable custom font loading until assets are available
  const [loaded] = useFonts({
    // TODO: Add custom fonts when assets are available
    // For now, using system fonts
  });

  // Set loaded to true since we're not loading any fonts
  const fontsLoaded = true;
  const [i18nInitialized, setI18nInitialized] = useState(false);
  const [migrationChecked, setMigrationChecked] = useState(false);
  const [migrationNeeded, setMigrationNeeded] = useState(false);

  // Initialize i18n
  useEffect(() => {
    const initI18n = async () => {
      try {
        // Wait for i18n to initialize
        if (i18n.isInitialized) {
          // Force English language
          await i18n.changeLanguage('en');
          setI18nInitialized(true);
        } else {
          i18n.on('initialized', async () => {
            // Force English language
            await i18n.changeLanguage('en');
            setI18nInitialized(true);
          });
        }
      } catch (error) {
        console.error('Error initializing i18n:', error);
        // Set initialized anyway to avoid blocking the app
        setI18nInitialized(true);
      }
    };

    initI18n();
  }, []);

  // Check migration status
  useEffect(() => {
    const checkMigration = async () => {
      try {
        if (i18nInitialized) {
          const migrationCompleted = await checkMigrationStatus();
          setMigrationNeeded(!migrationCompleted);
          setMigrationChecked(true);
        }
      } catch (error) {
        console.error('Error checking migration status:', error);
        // Assume migration is not needed if there's an error
        setMigrationNeeded(false);
        setMigrationChecked(true);
      }
    };

    checkMigration();
  }, [i18nInitialized]);

  if (!fontsLoaded || !i18nInitialized || !migrationChecked) {
    // Show loading indicator while fonts, i18n, and migration check are loading
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3AAFA9" />
      </View>
    );
  }

  // Show migration screen if migration is needed
  if (migrationNeeded) {
    return (
      <ThemeProvider>
        <LanguageProvider>
          <ToastProvider>
            <MigrationScreen
              onMigrationComplete={() => setMigrationNeeded(false)}
            />
          </ToastProvider>
        </LanguageProvider>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider>
      <LanguageProvider>
        <ToastProvider>
          <AuthProvider>
            <FeatureFlagProvider>
              <NotificationProvider>
                <AccountProvider>
                  <CategoryProvider>
                    <LoanProvider>
                      <SavingsProvider>
                        <AnalyticsProvider>
                          <BudgetControlProvider>
                            <ApprovalProvider>
                              <OCRProvider>
                                <SyncProvider>
                                  <Stack>
                                    <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                                    <Stack.Screen name="auth/login" options={{ headerShown: false }} />
                                    <Stack.Screen name="auth/register" options={{ headerShown: false }} />
                                    <Stack.Screen name="profile" options={{ headerShown: false }} />
                                    <Stack.Screen name="family/create" options={{ headerShown: false }} />
                                    <Stack.Screen name="family/join" options={{ headerShown: false }} />
                                    <Stack.Screen name="family/[id]" options={{ headerShown: false }} />
                                    <Stack.Screen name="family/edit/[id]" options={{ headerShown: false }} />
                                    <Stack.Screen name="notifications" options={{ headerShown: false }} />
                                    <Stack.Screen name="receipts" options={{ headerShown: false }} />
                                    <Stack.Screen name="receipts/[id]" options={{ headerShown: false }} />
                                    <Stack.Screen name="sync" options={{ headerShown: false }} />
                                    <Stack.Screen name="sync/conflict-resolution" options={{ headerShown: false }} />
                                    <Stack.Screen name="sync/merge-editor" options={{ headerShown: false }} />
                                    <Stack.Screen name="savings/goals" options={{ headerShown: false }} />
                                    <Stack.Screen name="savings/goals/[id]" options={{ headerShown: false }} />
                                    <Stack.Screen name="savings/goals/create" options={{ headerShown: false }} />
                                    <Stack.Screen name="savings/goals/edit/[id]" options={{ headerShown: false }} />
                                    <Stack.Screen name="savings/wishlist" options={{ headerShown: false }} />
                                    <Stack.Screen name="savings/wishlist/[id]" options={{ headerShown: false }} />
                                    <Stack.Screen name="savings/wishlist/create" options={{ headerShown: false }} />
                                    <Stack.Screen name="savings/wishlist/edit/[id]" options={{ headerShown: false }} />
                                    <Stack.Screen name="analytics/dashboard" options={{ headerShown: false }} />
                                    <Stack.Screen name="analytics/networth" options={{ headerShown: false }} />
                                    <Stack.Screen name="analytics/cashflow" options={{ headerShown: false }} />
                                    <Stack.Screen name="analytics/export" options={{ headerShown: false }} />
                                    <Stack.Screen name="analytics/monthly-summary" options={{ headerShown: false }} />
                                    <Stack.Screen name="budget/dashboard" options={{ headerShown: false }} />
                                    <Stack.Screen name="budget/limits" options={{ headerShown: false }} />
                                    <Stack.Screen name="budget/spending" options={{ headerShown: false }} />
                                    <Stack.Screen name="approval/requests" options={{ headerShown: false }} />
                                    <Stack.Screen name="approval/create" options={{ headerShown: false }} />
                                    <Stack.Screen name="reminders" options={{ headerShown: false }} />
                                    <Stack.Screen name="reminders/[id]" options={{ headerShown: false }} />
                                    <Stack.Screen name="reminders/create" options={{ headerShown: false }} />
                                    <Stack.Screen name="reminders/edit/[id]" options={{ headerShown: false }} />
                                    <Stack.Screen name="settings/features" options={{ headerShown: false }} />
                                    <Stack.Screen name="+not-found" />
                                  </Stack>
                                  <StatusBar style="auto" />
                                </SyncProvider>
                              </OCRProvider>
                            </ApprovalProvider>
                          </BudgetControlProvider>
                        </AnalyticsProvider>
                      </SavingsProvider>
                    </LoanProvider>
                  </CategoryProvider>
                </AccountProvider>
              </NotificationProvider>
            </FeatureFlagProvider>
          </AuthProvider>
        </ToastProvider>
      </LanguageProvider>
    </ThemeProvider>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0B0C10', // Deep Charcoal Blue background
  },
});
