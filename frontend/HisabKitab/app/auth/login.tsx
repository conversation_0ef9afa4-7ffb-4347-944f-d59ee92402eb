import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../src/contexts/AuthContext';
import { useTheme } from '../../src/contexts/ThemeContext';
import { useLanguage } from '../../src/contexts/LanguageContext';
import { router } from 'expo-router';

const LoginScreen = () => {
  const { t } = useTranslation();
  const { login } = useAuth();
  const { isDarkMode } = useTheme();
  const { language, setLanguage } = useLanguage();

  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async () => {
    if (!username || !password) {
      Alert.alert(t('common.error'), t('auth.requiredFields'));
      return;
    }

    setIsLoading(true);

    try {
      const result = await login(username, password);

      if (result.success) {
        // Login successful, navigate to home page
        router.replace('/(tabs)');
      } else {
        Alert.alert(t('common.error'), result.error || t('auth.loginFailed'));
      }
    } catch (error) {
      console.error('Login error:', error);
      Alert.alert(t('common.error'), t('auth.loginFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
        {t('common.appName')}
      </Text>

      {/* Language Toggle */}
      <View style={[styles.languageToggle, { marginTop: 10 }]}>
        <TouchableOpacity
          style={[
            styles.languageButton,
            {
              backgroundColor: language === 'en' ? '#0366D6' : (isDarkMode ? '#161B22' : '#F6F8FA'),
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
            }
          ]}
          onPress={() => setLanguage('en')}
        >
          <Text
            style={[
              styles.languageButtonText,
              { color: language === 'en' ? '#FFFFFF' : (isDarkMode ? '#FFFFFF' : '#000000') }
            ]}
          >
            English
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.languageButton,
            {
              backgroundColor: language === 'ne' ? '#0366D6' : (isDarkMode ? '#161B22' : '#F6F8FA'),
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
            }
          ]}
          onPress={() => setLanguage('ne')}
        >
          <Text
            style={[
              styles.languageButtonText,
              { color: language === 'ne' ? '#FFFFFF' : (isDarkMode ? '#FFFFFF' : '#000000') }
            ]}
          >
            नेपाली
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.formContainer}>
        <TextInput
          style={[
            styles.input,
            {
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              color: isDarkMode ? '#FFFFFF' : '#000000',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
            }
          ]}
          placeholder={t('auth.username')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          value={username}
          onChangeText={setUsername}
          autoCapitalize="none"
        />

        <TextInput
          style={[
            styles.input,
            {
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              color: isDarkMode ? '#FFFFFF' : '#000000',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
            }
          ]}
          placeholder={t('auth.password')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          value={password}
          onChangeText={setPassword}
          secureTextEntry
        />

        <TouchableOpacity
          style={[styles.button, { opacity: isLoading ? 0.7 : 1 }]}
          onPress={handleLogin}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#FFFFFF" />
          ) : (
            <Text style={styles.buttonText}>{t('auth.login')}</Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.registerLink}
          onPress={() => router.push('/auth/register')}
        >
          <Text style={[styles.registerText, { color: isDarkMode ? '#58A6FF' : '#0366D6' }]}>
            {t('auth.noAccount')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
    alignSelf: 'center',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 16,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  button: {
    height: 50,
    backgroundColor: '#0366D6',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  registerLink: {
    marginTop: 16,
    alignItems: 'center',
  },
  registerText: {
    fontSize: 16,
  },
  languageToggle: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
  },
  languageButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
    borderWidth: 1,
    marginHorizontal: 5,
    minWidth: 80,
    alignItems: 'center',
  },
  languageButtonText: {
    fontWeight: 'bold',
  },
});

export default LoginScreen;
