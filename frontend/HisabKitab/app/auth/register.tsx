import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, ActivityIndicator, ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../src/contexts/AuthContext';
import { useTheme } from '../../src/contexts/ThemeContext';
import { router } from 'expo-router';

const RegisterScreen = () => {
  const { t } = useTranslation();
  const { register } = useAuth();
  const { isDarkMode } = useTheme();
  
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const handleRegister = async () => {
    // Validate inputs
    if (!username || !email || !password || !confirmPassword || !firstName || !lastName) {
      Alert.alert(t('common.error'), t('auth.requiredFields'));
      return;
    }
    
    if (password !== confirmPassword) {
      Alert.alert(t('common.error'), t('auth.passwordMismatch'));
      return;
    }
    
    setIsLoading(true);
    
    try {
      const result = await register({
        username,
        email,
        password,
        firstName,
        lastName,
        phoneNumber,
        language: 'en',
      });
      
      if (result.success) {
        Alert.alert(t('common.success'), t('auth.registerSuccess'), [
          { text: t('common.ok'), onPress: () => router.push('/auth/login') }
        ]);
      } else {
        Alert.alert(t('common.error'), result.error || t('auth.registerFailed'));
      }
    } catch (error) {
      console.error('Register error:', error);
      Alert.alert(t('common.error'), t('auth.registerFailed'));
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}
      contentContainerStyle={styles.contentContainer}
    >
      <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
        {t('auth.register')}
      </Text>
      
      <View style={styles.formContainer}>
        <TextInput
          style={[
            styles.input,
            { 
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              color: isDarkMode ? '#FFFFFF' : '#000000',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
            }
          ]}
          placeholder={t('auth.username')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          value={username}
          onChangeText={setUsername}
          autoCapitalize="none"
        />
        
        <TextInput
          style={[
            styles.input,
            { 
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              color: isDarkMode ? '#FFFFFF' : '#000000',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
            }
          ]}
          placeholder={t('auth.email')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          value={email}
          onChangeText={setEmail}
          autoCapitalize="none"
          keyboardType="email-address"
        />
        
        <TextInput
          style={[
            styles.input,
            { 
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              color: isDarkMode ? '#FFFFFF' : '#000000',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
            }
          ]}
          placeholder={t('auth.firstName')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          value={firstName}
          onChangeText={setFirstName}
        />
        
        <TextInput
          style={[
            styles.input,
            { 
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              color: isDarkMode ? '#FFFFFF' : '#000000',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
            }
          ]}
          placeholder={t('auth.lastName')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          value={lastName}
          onChangeText={setLastName}
        />
        
        <TextInput
          style={[
            styles.input,
            { 
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              color: isDarkMode ? '#FFFFFF' : '#000000',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
            }
          ]}
          placeholder={t('auth.phoneNumber')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          value={phoneNumber}
          onChangeText={setPhoneNumber}
          keyboardType="phone-pad"
        />
        
        <TextInput
          style={[
            styles.input,
            { 
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              color: isDarkMode ? '#FFFFFF' : '#000000',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
            }
          ]}
          placeholder={t('auth.password')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          value={password}
          onChangeText={setPassword}
          secureTextEntry
        />
        
        <TextInput
          style={[
            styles.input,
            { 
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              color: isDarkMode ? '#FFFFFF' : '#000000',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
            }
          ]}
          placeholder={t('auth.confirmPassword')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          value={confirmPassword}
          onChangeText={setConfirmPassword}
          secureTextEntry
        />
        
        <TouchableOpacity
          style={[styles.button, { opacity: isLoading ? 0.7 : 1 }]}
          onPress={handleRegister}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#FFFFFF" />
          ) : (
            <Text style={styles.buttonText}>{t('auth.register')}</Text>
          )}
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.loginLink}
          onPress={() => router.push('/auth/login')}
        >
          <Text style={[styles.loginText, { color: isDarkMode ? '#58A6FF' : '#0366D6' }]}>
            {t('auth.haveAccount')}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    paddingTop: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 40,
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
    alignSelf: 'center',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 16,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  button: {
    height: 50,
    backgroundColor: '#0366D6',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loginLink: {
    marginTop: 20,
    alignItems: 'center',
    marginBottom: 40,
  },
  loginText: {
    fontSize: 16,
  },
});

export default RegisterScreen;
