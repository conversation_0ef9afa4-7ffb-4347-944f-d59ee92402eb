import React, { useEffect, useState } from 'react';
import { useLocalSearchParams, useRouter } from 'expo-router';
import EditFamilyScreen from '../../../src/screens/EditFamilyScreen';
import { ActivityIndicator, View, StyleSheet } from 'react-native';
import { useTheme } from '../../../src/contexts/ThemeContext';
import dbService, { Family } from '../../../src/services/db';

export default function EditFamilyRoute() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const familyId = parseInt(id || '0', 10);
  const { isDarkMode } = useTheme();
  const router = useRouter();
  
  const [family, setFamily] = useState<Family | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const loadFamily = async () => {
      try {
        const familyData = await dbService.getFamily(familyId);
        if (familyData) {
          setFamily(familyData);
        } else {
          // Family not found, navigate back
          router.back();
        }
      } catch (error) {
        console.error('Error loading family:', error);
        router.back();
      } finally {
        setLoading(false);
      }
    };
    
    loadFamily();
  }, [familyId]);
  
  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
      </View>
    );
  }
  
  if (!family) {
    return null;
  }
  
  return <EditFamilyScreen route={{ params: { family } }} />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
