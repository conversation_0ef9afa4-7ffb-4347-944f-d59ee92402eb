import React from 'react';
import { Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import MonthlySummaryScreen from '../../src/screens/analytics/MonthlySummaryScreen';

export default function MonthlySummaryLayout() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  return (
    <>
      <Stack.Screen
        options={{
          title: t('analytics.monthlySummary'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
          headerShadowVisible: false,
        }}
      />
      <MonthlySummaryScreen />
    </>
  );
}
