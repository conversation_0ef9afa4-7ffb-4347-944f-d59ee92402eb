import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import { useAnalytics } from '../../src/contexts/AnalyticsContext';
import { useFamily, Family } from '../../src/contexts/FamilyContext';
import { Stack } from 'expo-router';
import { format } from 'date-fns';

// Import components
import DateRangePicker from '../../src/components/analytics/DateRangePicker';
import PeriodSelector from '../../src/components/analytics/PeriodSelector';
import MetricCard from '../../src/components/analytics/MetricCard';
import ChartContainer from '../../src/components/analytics/ChartContainer';
import FamilySelector from '../../src/components/family/FamilySelector';

export default function NetWorthScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { families, selectedFamily, setSelectedFamily } = useFamily();

  const {
    dateRange,
    period,
    netWorth,
    netWorthTrend,
    loading,
    error,
    setDateRange,
    setPeriod,
    setSelectedFamilyId,
    fetchNetWorth,
    fetchNetWorthTrend,
  } = useAnalytics();

  // Load data on mount and when parameters change
  useEffect(() => {
    fetchNetWorth();
    fetchNetWorthTrend();
  }, [dateRange, period, selectedFamily]);

  // Handle date range change
  const handleDateRangeChange = (startDate: string, endDate: string) => {
    setDateRange({ startDate, endDate });
  };

  // Handle period change
  const handlePeriodChange = (newPeriod: string) => {
    setPeriod(newPeriod);
  };

  // Handle family change
  const handleFamilyChange = (familyId: number) => {
    const family = families.find((f: Family) => f.id === familyId);
    if (family) {
      setSelectedFamily(family);
      setSelectedFamilyId(familyId);
    }
  };

  // Prepare chart data for net worth trend
  const prepareNetWorthTrendData = () => {
    if (!netWorthTrend || netWorthTrend.length === 0) {
      return {
        labels: [],
        datasets: [
          {
            data: [],
            color: () => '#0366D6',
            strokeWidth: 2,
          },
        ],
      };
    }

    // Extract dates and values
    const labels = netWorthTrend.map(item => {
      const date = new Date(item.date);
      return format(date, period === 'Daily' ? 'dd' : period === 'Monthly' ? 'MMM' : 'MMM yy');
    });

    // Extract net worth values
    const netWorthData = netWorthTrend.map(item => item.value || 0);

    return {
      labels,
      datasets: [
        {
          data: netWorthData,
          color: () => '#0366D6',
          strokeWidth: 2,
        },
      ],
    };
  };

  // Prepare chart data for assets vs liabilities
  const prepareAssetsLiabilitiesData = () => {
    if (!netWorth) {
      return {
        labels: [t('analytics.assets'), t('analytics.liabilities')],
        datasets: [
          {
            data: [0, 0],
            color: (opacity = 1) => [
              `rgba(16, 185, 129, ${opacity})`,
              `rgba(239, 68, 68, ${opacity})`,
            ],
          },
        ],
      };
    }

    return {
      labels: [t('analytics.assets'), t('analytics.liabilities')],
      datasets: [
        {
          data: [
            Math.max(0, netWorth.totalAssets),
            Math.max(0, netWorth.totalLiabilities),
          ],
          color: (opacity = 1) => [
            `rgba(16, 185, 129, ${opacity})`,
            `rgba(239, 68, 68, ${opacity})`,
          ],
        },
      ],
    };
  };

  // Prepare chart data for asset breakdown
  const prepareAssetBreakdownData = () => {
    if (!netWorth || !netWorth.assetBreakdown || netWorth.assetBreakdown.length === 0) {
      return [];
    }

    // Assign colors
    const colors = ['#0366D6', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899'];

    return netWorth.assetBreakdown.map((asset, index) => ({
      name: asset.accountName,
      value: asset.balance,
      color: colors[index % colors.length],
      legendFontColor: isDarkMode ? '#FFFFFF' : '#374151',
      legendFontSize: 12,
    }));
  };

  // Prepare chart data for liability breakdown
  const prepareLiabilityBreakdownData = () => {
    if (!netWorth || !netWorth.liabilityBreakdown || netWorth.liabilityBreakdown.length === 0) {
      return [];
    }

    // Assign colors
    const colors = ['#EF4444', '#F59E0B', '#EC4899', '#8B5CF6', '#0366D6'];

    return netWorth.liabilityBreakdown.map((liability, index) => ({
      name: liability.accountName,
      value: liability.balance,
      color: colors[index % colors.length],
      legendFontColor: isDarkMode ? '#FFFFFF' : '#374151',
      legendFontSize: 12,
    }));
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#F9FAFB' }]}>
      <Stack.Screen
        options={{
          title: t('analytics.netWorth'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#111827',
        }}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.filtersContainer}>
          {families.length > 0 && (
            <FamilySelector
              families={families}
              selectedFamilyId={selectedFamily?.id}
              onFamilyChange={handleFamilyChange}
            />
          )}
          <DateRangePicker
            startDate={dateRange.startDate}
            endDate={dateRange.endDate}
            onDateRangeChange={handleDateRangeChange}
          />
          <PeriodSelector
            selectedPeriod={period}
            onPeriodChange={handlePeriodChange}
          />
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0366D6" />
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { color: isDarkMode ? '#F87171' : '#EF4444' }]}>
              {error}
            </Text>
          </View>
        ) : (
          <>
            <View style={styles.metricsContainer}>
              <MetricCard
                title={t('analytics.netWorth')}
                value={netWorth?.netWorth?.toFixed(2) || '0.00'}
                icon="wallet"
                iconColor="#0366D6"
              />
              <MetricCard
                title={t('analytics.totalAssets')}
                value={netWorth?.totalAssets?.toFixed(2) || '0.00'}
                icon="trending-up"
                iconColor="#10B981"
              />
              <MetricCard
                title={t('analytics.totalLiabilities')}
                value={netWorth?.totalLiabilities?.toFixed(2) || '0.00'}
                icon="trending-down"
                iconColor="#EF4444"
              />
            </View>

            <ChartContainer
              title={t('analytics.netWorthTrend')}
              subtitle={t('analytics.overTime')}
              type="line"
              data={prepareNetWorthTrendData()}
            />

            <ChartContainer
              title={t('analytics.assetsVsLiabilities')}
              type="bar"
              data={prepareAssetsLiabilitiesData()}
              height={200}
            />

            {netWorth?.assetBreakdown && netWorth.assetBreakdown.length > 0 && (
              <ChartContainer
                title={t('analytics.assetBreakdown')}
                type="pie"
                data={prepareAssetBreakdownData()}
                height={200}
              />
            )}

            {netWorth?.liabilityBreakdown && netWorth.liabilityBreakdown.length > 0 && (
              <ChartContainer
                title={t('analytics.liabilityBreakdown')}
                type="pie"
                data={prepareLiabilityBreakdownData()}
                height={200}
              />
            )}
          </>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  filtersContainer: {
    marginBottom: 16,
  },
  metricsContainer: {
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
});
