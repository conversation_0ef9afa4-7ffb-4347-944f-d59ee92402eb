import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import { useAnalytics } from '../../src/contexts/AnalyticsContext';
import { Stack, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';

// Import components
import DateRangePicker from '../../src/components/analytics/DateRangePicker';
import PeriodSelector from '../../src/components/analytics/PeriodSelector';
import MetricCard from '../../src/components/analytics/MetricCard';
import ChartContainer from '../../src/components/analytics/ChartContainer';

// Define a simple interface for families
interface Family {
  id: number;
  name: string;
}

// Add this interface to fix the TrendData type issue
interface TrendDataWithIncomeExpense {
  date: string;
  income: number;
  expense: number;
}

// Mock family selector component
const FamilySelector: React.FC<{
  families: Family[];
  selectedFamilyId?: number;
  onFamilyChange: (familyId: number) => void;
}> = ({ families, selectedFamilyId, onFamilyChange }) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  return (
    <View style={{ marginBottom: 16 }}>
      <Text style={{ color: isDarkMode ? '#D1D5DB' : '#4B5563', marginBottom: 8 }}>
        {t('family.selectFamily')}
      </Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {families.map((family) => (
          <TouchableOpacity
            key={family.id}
            style={{
              backgroundColor: selectedFamilyId === family.id
                ? '#0366D6'
                : isDarkMode ? '#1F2937' : '#F3F4F6',
              padding: 8,
              borderRadius: 8,
              marginRight: 8,
            }}
            onPress={() => onFamilyChange(family.id)}
          >
            <Text style={{
              color: selectedFamilyId === family.id
                ? '#FFFFFF'
                : isDarkMode ? '#D1D5DB' : '#4B5563'
            }}>
              {family.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

export default function AnalyticsDashboardScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const router = useRouter();

  // Mock family data
  const [families] = useState<Family[]>([
    { id: 1, name: 'My Family' },
    { id: 2, name: 'Work Group' }
  ]);
  const [selectedFamily, setSelectedFamily] = useState<Family | null>(null);

  const {
    dateRange,
    period,
    metrics,
    categoryBreakdown,
    trendData,
    loading,
    error,
    setDateRange,
    setPeriod,
    setSelectedFamilyId,
    fetchMetrics,
    fetchCategoryBreakdown,
    fetchTrendData,
  } = useAnalytics();

  // Load data on mount and when parameters change
  useEffect(() => {
    fetchMetrics();
    fetchCategoryBreakdown();
    fetchTrendData('income-expense');
  }, [dateRange, period, selectedFamily]);

  // Handle date range change
  const handleDateRangeChange = (startDate: string, endDate: string) => {
    setDateRange({ startDate, endDate });
  };

  // Handle period change
  const handlePeriodChange = (newPeriod: string) => {
    setPeriod(newPeriod);
  };

  // Handle family change
  const handleFamilyChange = (familyId: number) => {
    const family = families.find(f => f.id === familyId);
    if (family) {
      setSelectedFamily(family);
      setSelectedFamilyId(familyId);
    }
  };

  // Prepare chart data for income vs expense trend
  const prepareIncomeExpenseTrendData = () => {
    if (!trendData || trendData.length === 0) {
      return {
        labels: [],
        datasets: [
          {
            data: [],
            color: () => '#10B981',
            strokeWidth: 2,
          },
          {
            data: [],
            color: () => '#EF4444',
            strokeWidth: 2,
          },
        ],
        legend: [t('analytics.income'), t('analytics.expense')],
      };
    }

    // Extract dates and values
    const labels = trendData.map(item => {
      const date = new Date(item.date);
      return format(date, period === 'Daily' ? 'dd' : period === 'Monthly' ? 'MMM' : 'MMM yy');
    });

    // Extract income and expense values
    // Cast trendData to our interface with income and expense properties
    const typedTrendData = trendData as unknown as TrendDataWithIncomeExpense[];
    const incomeData = typedTrendData.map(item => item.income || 0);
    const expenseData = typedTrendData.map(item => item.expense || 0);

    return {
      labels,
      datasets: [
        {
          data: incomeData,
          color: () => '#10B981',
          strokeWidth: 2,
        },
        {
          data: expenseData,
          color: () => '#EF4444',
          strokeWidth: 2,
        },
      ],
      legend: [t('analytics.income'), t('analytics.expense')],
    };
  };

  // Prepare chart data for category breakdown
  const prepareCategoryBreakdownData = () => {
    if (!categoryBreakdown || categoryBreakdown.length === 0) {
      return [];
    }

    // Get top 5 expense categories
    const expenseCategories = categoryBreakdown
      .filter(item => item.type === 'Expense')
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 5);

    // Assign colors
    const colors = ['#0366D6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

    return expenseCategories.map((category, index) => ({
      name: category.categoryName,
      value: category.amount,
      color: colors[index % colors.length],
      legendFontColor: isDarkMode ? '#FFFFFF' : '#374151',
      legendFontSize: 12,
    }));
  };

  // Navigate to Net Worth screen
  const navigateToNetWorth = () => {
    router.push('/analytics/networth' as any);
  };

  // Navigate to Cashflow screen
  const navigateToCashflow = () => {
    router.push('/analytics/cashflow' as any);
  };

  // Navigate to Export screen
  const navigateToExport = () => {
    router.push('/analytics/export' as any);
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#F9FAFB' }]}>
      <Stack.Screen
        options={{
          title: t('analytics.dashboard'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#111827',
          headerRight: () => (
            <TouchableOpacity
              style={styles.exportButton}
              onPress={navigateToExport}
            >
              <Ionicons
                name="download-outline"
                size={24}
                color={isDarkMode ? '#FFFFFF' : '#111827'}
              />
            </TouchableOpacity>
          ),
        }}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.filtersContainer}>
          {families.length > 0 && (
            <FamilySelector
              families={families}
              selectedFamilyId={selectedFamily?.id}
              onFamilyChange={handleFamilyChange}
            />
          )}
          <DateRangePicker
            startDate={dateRange.startDate}
            endDate={dateRange.endDate}
            onDateRangeChange={handleDateRangeChange}
          />
          <PeriodSelector
            selectedPeriod={period}
            onPeriodChange={handlePeriodChange}
          />
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0366D6" />
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { color: isDarkMode ? '#F87171' : '#EF4444' }]}>
              {error}
            </Text>
          </View>
        ) : (
          <>
            <View style={styles.metricsContainer}>
              <MetricCard
                title={t('analytics.totalIncome')}
                value={metrics?.totalIncome?.toFixed(2) || '0.00'}
                icon="arrow-up-circle"
                iconColor="#10B981"
              />
              <MetricCard
                title={t('analytics.totalExpense')}
                value={metrics?.totalExpense?.toFixed(2) || '0.00'}
                icon="arrow-down-circle"
                iconColor="#EF4444"
              />
              <MetricCard
                title={t('analytics.netSavings')}
                value={metrics?.netSavings?.toFixed(2) || '0.00'}
                icon="save"
                iconColor="#0366D6"
                trend={metrics?.savingsRate}
              />
              <MetricCard
                title={t('analytics.netWorth')}
                value="View Details"
                icon="wallet"
                iconColor="#8B5CF6"
                onPress={navigateToNetWorth}
              />
              <MetricCard
                title={t('analytics.cashflow')}
                value="View Details"
                icon="trending-up"
                iconColor="#F59E0B"
                onPress={navigateToCashflow}
              />
            </View>

            <ChartContainer
              title={t('analytics.incomeVsExpense')}
              subtitle={t('analytics.overTime')}
              type="line"
              data={prepareIncomeExpenseTrendData()}
            />

            <ChartContainer
              title={t('analytics.topExpenseCategories')}
              type="pie"
              data={prepareCategoryBreakdownData()}
              height={200}
            />
          </>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  filtersContainer: {
    marginBottom: 16,
  },
  metricsContainer: {
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
  exportButton: {
    padding: 8,
  },
});
