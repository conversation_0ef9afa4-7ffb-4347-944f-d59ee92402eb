import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import { useAnalytics } from '../../src/contexts/AnalyticsContext';
import { Stack } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';

// Define the Family interface
interface Family {
  id: number;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  members?: any[];
}

// Mock the useFamily hook until module resolution is fixed
const useFamily = () => {
  // Using a constant instead of state to avoid unused setFamilies warning
  const families: Family[] = [
    {
      id: 1,
      name: 'My Family',
      description: 'Personal family group',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true
    },
    {
      id: 2,
      name: 'Work Group',
      description: 'Office expenses',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true
    }
  ];
  const [selectedFamily, setSelectedFamily] = useState<Family | null>(null);

  return {
    families,
    selectedFamily,
    loading: false,
    error: null,
    isFamilyAdmin: false,
    fetchFamilies: async () => {},
    fetchFamily: async () => {},
    createFamily: async () => false,
    updateFamily: async () => false,
    deleteFamily: async () => false,
    setSelectedFamily,
    inviteMember: async () => false,
    removeMember: async () => false,
    leaveFamily: async () => false,
    changeMemberRole: async () => false
  };
};

// Import components
import DateRangePicker from '../../src/components/analytics/DateRangePicker';
import PeriodSelector from '../../src/components/analytics/PeriodSelector';
import MetricCard from '../../src/components/analytics/MetricCard';
import ChartContainer from '../../src/components/analytics/ChartContainer';
import FamilySelector from '../../src/components/family/FamilySelector';

export default function CashflowScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { families, selectedFamily, setSelectedFamily } = useFamily();

  const {
    dateRange,
    period,
    cashflow,
    loading,
    error,
    setDateRange,
    setPeriod,
    setSelectedFamilyId,
    fetchCashflow,
  } = useAnalytics();

  // Load data on mount and when parameters change
  useEffect(() => {
    fetchCashflow();
  }, [dateRange, period, selectedFamily]);

  // Handle date range change
  const handleDateRangeChange = (startDate: string, endDate: string) => {
    setDateRange({ startDate, endDate });
  };

  // Handle period change
  const handlePeriodChange = (newPeriod: string) => {
    setPeriod(newPeriod);
  };

  // Handle family change
  const handleFamilyChange = (familyId: number) => {
    const family = families.find((f: Family) => f.id === familyId);
    if (family) {
      setSelectedFamily(family);
      setSelectedFamilyId(familyId);
    }
  };

  // Prepare chart data for monthly cashflow
  const prepareMonthlyCashflowData = () => {
    if (!cashflow || !cashflow.monthlyData || cashflow.monthlyData.length === 0) {
      return {
        labels: [],
        datasets: [
          {
            data: [],
            color: () => '#10B981',
            strokeWidth: 2,
          },
          {
            data: [],
            color: () => '#EF4444',
            strokeWidth: 2,
          },
        ],
        legend: [t('analytics.income'), t('analytics.expense')],
      };
    }

    // Extract months and values
    const labels = cashflow.monthlyData.map(item => {
      const date = new Date(item.month);
      return format(date, 'MMM');
    });

    // Extract income and expense values
    const incomeData = cashflow.monthlyData.map(item => item.income || 0);
    const expenseData = cashflow.monthlyData.map(item => item.expense || 0);

    return {
      labels,
      datasets: [
        {
          data: incomeData,
          color: () => '#10B981',
          strokeWidth: 2,
        },
        {
          data: expenseData,
          color: () => '#EF4444',
          strokeWidth: 2,
        },
      ],
      legend: [t('analytics.income'), t('analytics.expense')],
    };
  };

  // Prepare chart data for net cashflow
  const prepareNetCashflowData = () => {
    if (!cashflow || !cashflow.monthlyData || cashflow.monthlyData.length === 0) {
      return {
        labels: [],
        datasets: [
          {
            data: [],
            color: (opacity = 1) => {
              return `rgba(3, 102, 214, ${opacity})`;
            },
            strokeWidth: 2,
          },
        ],
      };
    }

    // Extract months and values
    const labels = cashflow.monthlyData.map(item => {
      const date = new Date(item.month);
      return format(date, 'MMM');
    });

    // Extract net values
    const netData = cashflow.monthlyData.map(item => item.net || 0);

    return {
      labels,
      datasets: [
        {
          data: netData,
          color: (opacity = 1, index: number) => {
            // Use green for positive values, red for negative
            const value = netData[index];
            return value >= 0
              ? `rgba(16, 185, 129, ${opacity})`
              : `rgba(239, 68, 68, ${opacity})`;
          },
          strokeWidth: 2,
        },
      ],
    };
  };

  // Prepare chart data for income vs expense
  const prepareIncomeExpenseData = () => {
    if (!cashflow) {
      return {
        labels: [t('analytics.income'), t('analytics.expense')],
        datasets: [
          {
            data: [0, 0],
            color: (opacity = 1) => [
              `rgba(16, 185, 129, ${opacity})`,
              `rgba(239, 68, 68, ${opacity})`,
            ],
          },
        ],
      };
    }

    return {
      labels: [t('analytics.income'), t('analytics.expense')],
      datasets: [
        {
          data: [
            Math.max(0, cashflow.totalIncome),
            Math.max(0, cashflow.totalExpense),
          ],
          color: (opacity = 1) => [
            `rgba(16, 185, 129, ${opacity})`,
            `rgba(239, 68, 68, ${opacity})`,
          ],
        },
      ],
    };
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#F9FAFB' }]}>
      <Stack.Screen
        options={{
          title: t('analytics.cashflow'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#111827',
        }}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.filtersContainer}>
          {families.length > 0 && (
            <FamilySelector
              families={families}
              selectedFamilyId={selectedFamily?.id}
              onFamilyChange={handleFamilyChange}
            />
          )}
          <DateRangePicker
            startDate={dateRange.startDate}
            endDate={dateRange.endDate}
            onDateRangeChange={handleDateRangeChange}
          />
          <PeriodSelector
            selectedPeriod={period}
            onPeriodChange={handlePeriodChange}
          />
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0366D6" />
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { color: isDarkMode ? '#F87171' : '#EF4444' }]}>
              {error}
            </Text>
          </View>
        ) : (
          <>
            <View style={styles.metricsContainer}>
              <MetricCard
                title={t('analytics.totalIncome')}
                value={cashflow?.totalIncome?.toFixed(2) || '0.00'}
                icon="arrow-up-circle"
                iconColor="#10B981"
              />
              <MetricCard
                title={t('analytics.totalExpense')}
                value={cashflow?.totalExpense?.toFixed(2) || '0.00'}
                icon="arrow-down-circle"
                iconColor="#EF4444"
              />
              <MetricCard
                title={t('analytics.netCashflow')}
                value={cashflow?.netCashflow?.toFixed(2) || '0.00'}
                icon="cash"
                iconColor="#0366D6"
              />
            </View>

            <ChartContainer
              title={t('analytics.monthlyCashflow')}
              subtitle={t('analytics.incomeVsExpense')}
              type="line"
              data={prepareMonthlyCashflowData()}
            />

            <ChartContainer
              title={t('analytics.netCashflowTrend')}
              type="bar"
              data={prepareNetCashflowData()}
            />

            <ChartContainer
              title={t('analytics.incomeVsExpense')}
              subtitle={t('analytics.summary')}
              type="bar"
              data={prepareIncomeExpenseData()}
              height={200}
            />
          </>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  filtersContainer: {
    marginBottom: 16,
  },
  metricsContainer: {
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
});
