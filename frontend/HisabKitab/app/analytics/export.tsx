import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import { useAnalytics } from '../../src/contexts/AnalyticsContext';
import { Stack } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

// Define the Family interface
interface Family {
  id: number;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  members?: any[];
}

// Mock the useFamily hook until module resolution is fixed
const useFamily = () => {
  // Using a constant instead of state to avoid unused setFamilies warning
  const families: Family[] = [
    {
      id: 1,
      name: 'My Family',
      description: 'Personal family group',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true
    },
    {
      id: 2,
      name: 'Work Group',
      description: 'Office expenses',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true
    }
  ];
  const [selectedFamily, setSelectedFamily] = useState<Family | null>(null);

  return {
    families,
    selectedFamily,
    loading: false,
    error: null,
    isFamilyAdmin: false,
    fetchFamilies: async () => {},
    fetchFamily: async () => {},
    createFamily: async () => false,
    updateFamily: async () => false,
    deleteFamily: async () => false,
    setSelectedFamily,
    inviteMember: async () => false,
    removeMember: async () => false,
    leaveFamily: async () => false,
    changeMemberRole: async () => false
  };
};

// Import components
import DateRangePicker from '../../src/components/analytics/DateRangePicker';
import FamilySelector from '../../src/components/family/FamilySelector';

export default function ExportScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { families, selectedFamily, setSelectedFamily } = useFamily();

  const {
    dateRange,
    loading,
    error,
    setDateRange,
    setSelectedFamilyId,
    exportData,
  } = useAnalytics();

  // Local state
  const [selectedFormat, setSelectedFormat] = useState('PDF');
  const [selectedType, setSelectedType] = useState('transactions');

  // Handle date range change
  const handleDateRangeChange = (startDate: string, endDate: string) => {
    setDateRange({ startDate, endDate });
  };

  // Handle family change
  const handleFamilyChange = (familyId: number) => {
    const family = families.find((f: Family) => f.id === familyId);
    if (family) {
      setSelectedFamily(family);
      setSelectedFamilyId(familyId);
    }
  };

  // Handle export
  const handleExport = async () => {
    const success = await exportData(selectedFormat, selectedType);

    if (success) {
      Alert.alert(
        t('analytics.exportSuccess'),
        t('analytics.exportSuccessMessage'),
        [{ text: t('common.ok') }]
      );
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#F9FAFB' }]}>
      <Stack.Screen
        options={{
          title: t('analytics.export'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#111827',
        }}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#111827' }]}>
            {t('analytics.exportOptions')}
          </Text>

          <Text style={[styles.label, { color: isDarkMode ? '#D1D5DB' : '#4B5563' }]}>
            {t('analytics.selectFamily')}
          </Text>
          {families.length > 0 && (
            <FamilySelector
              families={families}
              selectedFamilyId={selectedFamily?.id}
              onFamilyChange={handleFamilyChange}
            />
          )}

          <Text style={[styles.label, { color: isDarkMode ? '#D1D5DB' : '#4B5563' }]}>
            {t('analytics.selectDateRange')}
          </Text>
          <DateRangePicker
            startDate={dateRange.startDate}
            endDate={dateRange.endDate}
            onDateRangeChange={handleDateRangeChange}
          />

          <Text style={[styles.label, { color: isDarkMode ? '#D1D5DB' : '#4B5563' }]}>
            {t('analytics.selectFormat')}
          </Text>
          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={[
                styles.optionButton,
                selectedFormat === 'PDF' && styles.selectedOption,
                {
                  backgroundColor: isDarkMode
                    ? selectedFormat === 'PDF'
                      ? '#0366D6'
                      : '#1F2937'
                    : selectedFormat === 'PDF'
                    ? '#0366D6'
                    : '#F3F4F6',
                },
              ]}
              onPress={() => setSelectedFormat('PDF')}
            >
              <Ionicons
                name="document-text"
                size={20}
                color={
                  selectedFormat === 'PDF'
                    ? '#FFFFFF'
                    : isDarkMode
                    ? '#D1D5DB'
                    : '#4B5563'
                }
              />
              <Text
                style={[
                  styles.optionText,
                  {
                    color: selectedFormat === 'PDF'
                      ? '#FFFFFF'
                      : isDarkMode
                      ? '#D1D5DB'
                      : '#4B5563',
                  },
                ]}
              >
                PDF
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                selectedFormat === 'CSV' && styles.selectedOption,
                {
                  backgroundColor: isDarkMode
                    ? selectedFormat === 'CSV'
                      ? '#0366D6'
                      : '#1F2937'
                    : selectedFormat === 'CSV'
                    ? '#0366D6'
                    : '#F3F4F6',
                },
              ]}
              onPress={() => setSelectedFormat('CSV')}
            >
              <Ionicons
                name="document"
                size={20}
                color={
                  selectedFormat === 'CSV'
                    ? '#FFFFFF'
                    : isDarkMode
                    ? '#D1D5DB'
                    : '#4B5563'
                }
              />
              <Text
                style={[
                  styles.optionText,
                  {
                    color: selectedFormat === 'CSV'
                      ? '#FFFFFF'
                      : isDarkMode
                      ? '#D1D5DB'
                      : '#4B5563',
                  },
                ]}
              >
                CSV
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                selectedFormat === 'EXCEL' && styles.selectedOption,
                {
                  backgroundColor: isDarkMode
                    ? selectedFormat === 'EXCEL'
                      ? '#0366D6'
                      : '#1F2937'
                    : selectedFormat === 'EXCEL'
                    ? '#0366D6'
                    : '#F3F4F6',
                },
              ]}
              onPress={() => setSelectedFormat('EXCEL')}
            >
              <Ionicons
                name="grid"
                size={20}
                color={
                  selectedFormat === 'EXCEL'
                    ? '#FFFFFF'
                    : isDarkMode
                    ? '#D1D5DB'
                    : '#4B5563'
                }
              />
              <Text
                style={[
                  styles.optionText,
                  {
                    color: selectedFormat === 'EXCEL'
                      ? '#FFFFFF'
                      : isDarkMode
                      ? '#D1D5DB'
                      : '#4B5563',
                  },
                ]}
              >
                Excel
              </Text>
            </TouchableOpacity>
          </View>

          <Text style={[styles.label, { color: isDarkMode ? '#D1D5DB' : '#4B5563' }]}>
            {t('analytics.selectDataType')}
          </Text>
          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={[
                styles.optionButton,
                selectedType === 'transactions' && styles.selectedOption,
                {
                  backgroundColor: isDarkMode
                    ? selectedType === 'transactions'
                      ? '#0366D6'
                      : '#1F2937'
                    : selectedType === 'transactions'
                    ? '#0366D6'
                    : '#F3F4F6',
                },
              ]}
              onPress={() => setSelectedType('transactions')}
            >
              <Ionicons
                name="swap-horizontal"
                size={20}
                color={
                  selectedType === 'transactions'
                    ? '#FFFFFF'
                    : isDarkMode
                    ? '#D1D5DB'
                    : '#4B5563'
                }
              />
              <Text
                style={[
                  styles.optionText,
                  {
                    color: selectedType === 'transactions'
                      ? '#FFFFFF'
                      : isDarkMode
                      ? '#D1D5DB'
                      : '#4B5563',
                  },
                ]}
              >
                {t('analytics.transactions')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                selectedType === 'summary' && styles.selectedOption,
                {
                  backgroundColor: isDarkMode
                    ? selectedType === 'summary'
                      ? '#0366D6'
                      : '#1F2937'
                    : selectedType === 'summary'
                    ? '#0366D6'
                    : '#F3F4F6',
                },
              ]}
              onPress={() => setSelectedType('summary')}
            >
              <Ionicons
                name="stats-chart"
                size={20}
                color={
                  selectedType === 'summary'
                    ? '#FFFFFF'
                    : isDarkMode
                    ? '#D1D5DB'
                    : '#4B5563'
                }
              />
              <Text
                style={[
                  styles.optionText,
                  {
                    color: selectedType === 'summary'
                      ? '#FFFFFF'
                      : isDarkMode
                      ? '#D1D5DB'
                      : '#4B5563',
                  },
                ]}
              >
                {t('analytics.summary')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                selectedType === 'categories' && styles.selectedOption,
                {
                  backgroundColor: isDarkMode
                    ? selectedType === 'categories'
                      ? '#0366D6'
                      : '#1F2937'
                    : selectedType === 'categories'
                    ? '#0366D6'
                    : '#F3F4F6',
                },
              ]}
              onPress={() => setSelectedType('categories')}
            >
              <Ionicons
                name="pie-chart"
                size={20}
                color={
                  selectedType === 'categories'
                    ? '#FFFFFF'
                    : isDarkMode
                    ? '#D1D5DB'
                    : '#4B5563'
                }
              />
              <Text
                style={[
                  styles.optionText,
                  {
                    color: selectedType === 'categories'
                      ? '#FFFFFF'
                      : isDarkMode
                      ? '#D1D5DB'
                      : '#4B5563',
                  },
                ]}
              >
                {t('analytics.categories')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {error && (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { color: isDarkMode ? '#F87171' : '#EF4444' }]}>
              {error}
            </Text>
          </View>
        )}

        <TouchableOpacity
          style={[
            styles.exportButton,
            { backgroundColor: loading ? '#9CA3AF' : '#0366D6' },
          ]}
          onPress={handleExport}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <>
              <Ionicons name="download-outline" size={20} color="#FFFFFF" />
              <Text style={styles.exportButtonText}>
                {t('analytics.exportData')}
              </Text>
            </>
          )}
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedOption: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  optionText: {
    fontSize: 14,
    marginLeft: 8,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  exportButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginLeft: 8,
  },
});
