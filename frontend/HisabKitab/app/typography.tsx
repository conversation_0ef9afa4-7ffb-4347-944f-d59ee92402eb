import React from 'react';
import { Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import TypographyShowcaseScreen from '../src/screens/TypographyShowcaseScreen';

export default function TypographyScreen() {
  const { t } = useTranslation();

  return (
    <>
      <Stack.Screen
        options={{
          title: t('typography.showcase'),
          headerShown: true,
        }}
      />
      <TypographyShowcaseScreen />
    </>
  );
}
