import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import { useApproval } from '../../src/contexts/ApprovalContext';
import { useAuth } from '../../src/contexts/AuthContext';
import { Stack, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

// Define the Family interface
interface Family {
  id: number;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  members?: FamilyMember[];
}

interface FamilyMember {
  userId: number;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  joinedAt: string;
}

// Mock useFamily hook
const useFamily = () => {
  // Using a constant instead of state to avoid unused setFamilies warning
  const families: Family[] = [
    {
      id: 1,
      name: 'My Family',
      description: 'Personal family group',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
      members: [
        {
          userId: 1,
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          role: 'Admin',
          joinedAt: new Date().toISOString()
        }
      ]
    },
    {
      id: 2,
      name: 'Work Group',
      description: 'Office expenses',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true
    }
  ];
  const [selectedFamily, setSelectedFamily] = useState<Family | null>(null);

  return {
    families,
    selectedFamily,
    loading: false,
    error: null,
    isFamilyAdmin: true,
    fetchFamilies: async () => {},
    fetchFamily: async () => {},
    createFamily: async () => false,
    updateFamily: async () => false,
    deleteFamily: async () => false,
    setSelectedFamily,
    inviteMember: async () => false,
    removeMember: async () => false,
    leaveFamily: async () => false,
    changeMemberRole: async () => false
  };
};

// Import components
import ApprovalRequestCard from '../../src/components/approval/ApprovalRequestCard';
import FamilySelector from '../../src/components/family/FamilySelector';

export default function ApprovalRequestsScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const router = useRouter();
  const { families, selectedFamily, setSelectedFamily } = useFamily();
  const { user, isPlatformAdmin, isFamilyAdmin } = useAuth();

  const {
    approvalRequests,
    loading,
    error,
    fetchApprovalRequests,
    fetchFamilyApprovalRequests,
    approveRequest,
    rejectRequest,
  } = useApproval();

  // Local state
  const [viewMode, setViewMode] = useState<'requester' | 'approver'>('requester');
  const [statusFilter, setStatusFilter] = useState<string | undefined>(undefined);
  const [isRejectModalVisible, setIsRejectModalVisible] = useState(false);
  const [selectedRequestId, setSelectedRequestId] = useState<number | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');

  // Load data on mount and when parameters change
  useEffect(() => {
    if (selectedFamily && viewMode === 'approver') {
      fetchFamilyApprovalRequests(selectedFamily.id, statusFilter);
    } else {
      fetchApprovalRequests(viewMode === 'requester', statusFilter);
    }
  }, [selectedFamily, viewMode, statusFilter]);

  // Handle family change
  const handleFamilyChange = (familyId: number) => {
    const family = families.find((f: Family) => f.id === familyId);
    if (family) {
      setSelectedFamily(family);
    }
  };

  // Handle view mode change
  const handleViewModeChange = (mode: 'requester' | 'approver') => {
    setViewMode(mode);
  };

  // Handle status filter change
  const handleStatusFilterChange = (status?: string) => {
    setStatusFilter(status);
  };

  // Handle approve request
  const handleApprove = async (id: number) => {
    Alert.alert(
      t('approval.confirmApprove'),
      t('approval.confirmApproveMessage'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('approval.approve'),
          onPress: async () => {
            const success = await approveRequest(id);
            if (success && selectedFamily && viewMode === 'approver') {
              fetchFamilyApprovalRequests(selectedFamily.id, statusFilter);
            } else if (success) {
              fetchApprovalRequests(viewMode === 'requester', statusFilter);
            }
          },
        },
      ]
    );
  };

  // Open reject modal
  const openRejectModal = (id: number) => {
    setSelectedRequestId(id);
    setRejectionReason('');
    setIsRejectModalVisible(true);
  };

  // Handle reject request
  const handleReject = async () => {
    if (!selectedRequestId) return;
    if (!rejectionReason.trim()) {
      Alert.alert(t('common.error'), t('approval.enterRejectionReason'));
      return;
    }

    const success = await rejectRequest(selectedRequestId, rejectionReason);
    if (success) {
      setIsRejectModalVisible(false);
      if (selectedFamily && viewMode === 'approver') {
        fetchFamilyApprovalRequests(selectedFamily.id, statusFilter);
      } else {
        fetchApprovalRequests(viewMode === 'requester', statusFilter);
      }
    }
  };

  // Navigate to create approval request screen
  const navigateToCreateRequest = () => {
    router.push('/approval/create' as any);
  };

  // Check if user has admin rights
  const hasAdminRights = isPlatformAdmin || (selectedFamily && isFamilyAdmin);

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#F9FAFB' }]}>
      <Stack.Screen
        options={{
          title: t('approval.requests'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#111827',
          headerRight: () => (
            <TouchableOpacity
              style={styles.addButton}
              onPress={navigateToCreateRequest}
            >
              <Ionicons
                name="add"
                size={24}
                color={isDarkMode ? '#FFFFFF' : '#111827'}
              />
            </TouchableOpacity>
          ),
        }}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.filtersContainer}>
          {viewMode === 'approver' && families.length > 0 && (
            <FamilySelector
              families={families}
              selectedFamilyId={selectedFamily?.id}
              onFamilyChange={handleFamilyChange}
            />
          )}

          <View style={styles.viewModeContainer}>
            <TouchableOpacity
              style={[
                styles.viewModeButton,
                viewMode === 'requester' && styles.selectedViewMode,
                {
                  backgroundColor: isDarkMode
                    ? viewMode === 'requester'
                      ? '#0366D6'
                      : '#1F2937'
                    : viewMode === 'requester'
                    ? '#0366D6'
                    : '#F3F4F6',
                },
              ]}
              onPress={() => handleViewModeChange('requester')}
            >
              <Text
                style={[
                  styles.viewModeText,
                  {
                    color: viewMode === 'requester'
                      ? '#FFFFFF'
                      : isDarkMode
                      ? '#D1D5DB'
                      : '#4B5563',
                  },
                ]}
              >
                {t('approval.myRequests')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.viewModeButton,
                viewMode === 'approver' && styles.selectedViewMode,
                {
                  backgroundColor: isDarkMode
                    ? viewMode === 'approver'
                      ? '#0366D6'
                      : '#1F2937'
                    : viewMode === 'approver'
                    ? '#0366D6'
                    : '#F3F4F6',
                },
              ]}
              onPress={() => handleViewModeChange('approver')}
            >
              <Text
                style={[
                  styles.viewModeText,
                  {
                    color: viewMode === 'approver'
                      ? '#FFFFFF'
                      : isDarkMode
                      ? '#D1D5DB'
                      : '#4B5563',
                  },
                ]}
              >
                {t('approval.toApprove')}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.statusFilterContainer}>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.statusButtons}
            >
              <TouchableOpacity
                style={[
                  styles.statusButton,
                  statusFilter === undefined && styles.selectedStatus,
                  {
                    backgroundColor: isDarkMode
                      ? statusFilter === undefined
                        ? '#0366D6'
                        : '#1F2937'
                      : statusFilter === undefined
                      ? '#0366D6'
                      : '#F3F4F6',
                  },
                ]}
                onPress={() => handleStatusFilterChange(undefined)}
              >
                <Text
                  style={[
                    styles.statusText,
                    {
                      color: statusFilter === undefined
                        ? '#FFFFFF'
                        : isDarkMode
                        ? '#D1D5DB'
                        : '#4B5563',
                    },
                  ]}
                >
                  {t('approval.all')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.statusButton,
                  statusFilter === 'Pending' && styles.selectedStatus,
                  {
                    backgroundColor: isDarkMode
                      ? statusFilter === 'Pending'
                        ? '#0366D6'
                        : '#1F2937'
                      : statusFilter === 'Pending'
                      ? '#0366D6'
                      : '#F3F4F6',
                  },
                ]}
                onPress={() => handleStatusFilterChange('Pending')}
              >
                <Text
                  style={[
                    styles.statusText,
                    {
                      color: statusFilter === 'Pending'
                        ? '#FFFFFF'
                        : isDarkMode
                        ? '#D1D5DB'
                        : '#4B5563',
                    },
                  ]}
                >
                  {t('approval.status.pending')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.statusButton,
                  statusFilter === 'Approved' && styles.selectedStatus,
                  {
                    backgroundColor: isDarkMode
                      ? statusFilter === 'Approved'
                        ? '#0366D6'
                        : '#1F2937'
                      : statusFilter === 'Approved'
                      ? '#0366D6'
                      : '#F3F4F6',
                  },
                ]}
                onPress={() => handleStatusFilterChange('Approved')}
              >
                <Text
                  style={[
                    styles.statusText,
                    {
                      color: statusFilter === 'Approved'
                        ? '#FFFFFF'
                        : isDarkMode
                        ? '#D1D5DB'
                        : '#4B5563',
                    },
                  ]}
                >
                  {t('approval.status.approved')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.statusButton,
                  statusFilter === 'Rejected' && styles.selectedStatus,
                  {
                    backgroundColor: isDarkMode
                      ? statusFilter === 'Rejected'
                        ? '#0366D6'
                        : '#1F2937'
                      : statusFilter === 'Rejected'
                      ? '#0366D6'
                      : '#F3F4F6',
                  },
                ]}
                onPress={() => handleStatusFilterChange('Rejected')}
              >
                <Text
                  style={[
                    styles.statusText,
                    {
                      color: statusFilter === 'Rejected'
                        ? '#FFFFFF'
                        : isDarkMode
                        ? '#D1D5DB'
                        : '#4B5563',
                    },
                  ]}
                >
                  {t('approval.status.rejected')}
                </Text>
              </TouchableOpacity>
            </ScrollView>
          </View>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0366D6" />
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { color: isDarkMode ? '#F87171' : '#EF4444' }]}>
              {error}
            </Text>
          </View>
        ) : approvalRequests.length === 0 ? (
          <View style={[styles.emptyContainer, { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' }]}>
            <Text style={[styles.emptyText, { color: isDarkMode ? '#9CA3AF' : '#6B7280' }]}>
              {viewMode === 'requester'
                ? t('approval.noRequests')
                : t('approval.noRequestsToApprove')}
            </Text>
            {viewMode === 'requester' && (
              <TouchableOpacity
                style={[styles.emptyButton, { backgroundColor: '#0366D6' }]}
                onPress={navigateToCreateRequest}
              >
                <Text style={styles.emptyButtonText}>
                  {t('approval.createRequest')}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        ) : (
          <View style={styles.requestsContainer}>
            {approvalRequests.map(request => (
              <ApprovalRequestCard
                key={request.id}
                id={request.id}
                requesterName={request.requesterName}
                amount={request.amount}
                description={request.description}
                categoryName={request.categoryName}
                status={request.status}
                requestDate={request.requestDate}
                responseDate={request.responseDate}
                rejectionReason={request.rejectionReason}
                onApprove={
                  viewMode === 'approver' && request.status === 'Pending'
                    ? () => handleApprove(request.id)
                    : undefined
                }
                onReject={
                  viewMode === 'approver' && request.status === 'Pending'
                    ? () => openRejectModal(request.id)
                    : undefined
                }
              />
            ))}
          </View>
        )}
      </ScrollView>

      {/* Reject Modal */}
      <Modal
        visible={isRejectModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setIsRejectModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[
              styles.modalContent,
              { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text
                style={[
                  styles.modalTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#111827' },
                ]}
              >
                {t('approval.rejectRequest')}
              </Text>
              <TouchableOpacity onPress={() => setIsRejectModalVisible(false)}>
                <Ionicons
                  name="close"
                  size={24}
                  color={isDarkMode ? '#FFFFFF' : '#374151'}
                />
              </TouchableOpacity>
            </View>

            <Text style={[styles.label, { color: isDarkMode ? '#D1D5DB' : '#4B5563' }]}>
              {t('approval.rejectionReason')}*
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: isDarkMode ? '#374151' : '#F3F4F6',
                  color: isDarkMode ? '#FFFFFF' : '#111827',
                  textAlignVertical: 'top',
                },
              ]}
              value={rejectionReason}
              onChangeText={setRejectionReason}
              placeholder={t('approval.enterRejectionReason')}
              placeholderTextColor={isDarkMode ? '#9CA3AF' : '#6B7280'}
              multiline
              numberOfLines={4}
            />

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[
                  styles.cancelButton,
                  { borderColor: isDarkMode ? '#4B5563' : '#D1D5DB' },
                ]}
                onPress={() => setIsRejectModalVisible(false)}
              >
                <Text
                  style={[
                    styles.cancelButtonText,
                    { color: isDarkMode ? '#FFFFFF' : '#374151' },
                  ]}
                >
                  {t('common.cancel')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.rejectButton, { backgroundColor: '#EF4444' }]}
                onPress={handleReject}
              >
                <Text style={styles.rejectButtonText}>
                  {t('approval.reject')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  filtersContainer: {
    marginBottom: 16,
  },
  viewModeContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  viewModeButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 8,
  },
  selectedViewMode: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  viewModeText: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusFilterContainer: {
    marginBottom: 12,
  },
  statusButtons: {
    paddingVertical: 4,
  },
  statusButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  selectedStatus: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  statusText: {
    fontSize: 14,
  },
  requestsContainer: {
    marginBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
  emptyContainer: {
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 24,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
  },
  emptyButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  emptyButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  addButton: {
    padding: 8,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    borderRadius: 12,
    padding: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
  },
  input: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginBottom: 16,
    minHeight: 100,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 14,
  },
  rejectButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  rejectButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '500',
  },
});
