import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  TextInput,
  Alert
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import { useApproval } from '../../src/contexts/ApprovalContext';
import { useCategories } from '../../src/contexts/CategoryContext';
import { Stack, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

// Define the Family interface
interface Family {
  id: number;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  members?: any[];
}

// Mock useFamily hook
const useFamily = () => {
  // Using a constant instead of state to avoid unused setFamilies warning
  const families: Family[] = [
    {
      id: 1,
      name: 'My Family',
      description: 'Personal family group',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true
    },
    {
      id: 2,
      name: 'Work Group',
      description: 'Office expenses',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true
    }
  ];
  const [selectedFamily, setSelectedFamily] = useState<Family | null>(null);

  return {
    families,
    selectedFamily,
    loading: false,
    error: null,
    isFamilyAdmin: false,
    fetchFamilies: async () => {},
    fetchFamily: async () => {},
    createFamily: async () => false,
    updateFamily: async () => false,
    deleteFamily: async () => false,
    setSelectedFamily,
    inviteMember: async () => false,
    removeMember: async () => false,
    leaveFamily: async () => false,
    changeMemberRole: async () => false
  };
};

// Import components
import FamilySelector from '../../src/components/family/FamilySelector';

export default function CreateApprovalRequestScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const router = useRouter();
  const { families, selectedFamily, setSelectedFamily } = useFamily();
  const { categories, fetchCategories } = useCategories();

  const {
    loading,
    error,
    createApprovalRequest,
    checkApprovalRequired,
  } = useApproval();

  // Form state
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [categoryId, setCategoryId] = useState<number | null>(null);
  const [isCheckingApproval, setIsCheckingApproval] = useState(false);
  const [approvalRequired, setApprovalRequired] = useState<boolean | null>(null);
  const [thresholdAmount, setThresholdAmount] = useState<number | null>(null);

  // Load categories on mount
  useEffect(() => {
    fetchCategories();
  }, []);

  // Handle family change
  const handleFamilyChange = (familyId: number) => {
    const family = families.find((f: Family) => f.id === familyId);
    if (family) {
      setSelectedFamily(family);
      // Reset approval check when family changes
      setApprovalRequired(null);
      setThresholdAmount(null);
    }
  };

  // Check if approval is required
  const handleCheckApproval = async () => {
    if (!selectedFamily || !categoryId || !amount || isNaN(parseFloat(amount))) {
      return;
    }

    setIsCheckingApproval(true);
    const amountValue = parseFloat(amount);
    const result = await checkApprovalRequired(selectedFamily.id, amountValue, categoryId);

    if (result) {
      setApprovalRequired(result.isApprovalRequired);
      setThresholdAmount(result.thresholdAmount || null);
    }

    setIsCheckingApproval(false);
  };

  // Handle amount change
  const handleAmountChange = (value: string) => {
    setAmount(value);
    // Reset approval check when amount changes
    setApprovalRequired(null);
    setThresholdAmount(null);
  };

  // Handle category change
  const handleCategoryChange = (id: number) => {
    setCategoryId(id);
    // Reset approval check when category changes
    setApprovalRequired(null);
    setThresholdAmount(null);
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!selectedFamily) {
      Alert.alert(t('common.error'), t('approval.selectFamily'));
      return;
    }

    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      Alert.alert(t('common.error'), t('approval.enterValidAmount'));
      return;
    }

    if (!description.trim()) {
      Alert.alert(t('common.error'), t('approval.enterDescription'));
      return;
    }

    if (!categoryId) {
      Alert.alert(t('common.error'), t('approval.selectCategory'));
      return;
    }

    // Check if approval is required if not already checked
    if (approvalRequired === null) {
      await handleCheckApproval();
    }

    // If approval is not required, confirm with user
    if (approvalRequired === false) {
      Alert.alert(
        t('approval.approvalNotRequired'),
        t('approval.approvalNotRequiredMessage'),
        [
          {
            text: t('common.cancel'),
            style: 'cancel',
          },
          {
            text: t('approval.requestAnyway'),
            onPress: submitRequest,
          },
        ]
      );
    } else {
      submitRequest();
    }
  };

  // Submit the request
  const submitRequest = async () => {
    if (!selectedFamily || !categoryId) return;

    const data = {
      familyId: selectedFamily.id,
      amount: parseFloat(amount),
      description,
      categoryId,
    };

    const success = await createApprovalRequest(data);
    if (success) {
      Alert.alert(
        t('approval.requestCreated'),
        t('approval.requestCreatedMessage'),
        [
          {
            text: t('common.ok'),
            onPress: () => router.push('/approval/requests' as any),
          },
        ]
      );
    }
  };

  // Filter expense categories
  const expenseCategories = categories.filter(c => c.type === 'Expense');

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#F9FAFB' }]}>
      <Stack.Screen
        options={{
          title: t('approval.createRequest'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#111827',
        }}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.formContainer}>
          <Text style={[styles.label, { color: isDarkMode ? '#D1D5DB' : '#4B5563' }]}>
            {t('approval.selectFamily')}*
          </Text>
          {families.length > 0 && (
            <FamilySelector
              families={families}
              selectedFamilyId={selectedFamily?.id || 0}
              onFamilyChange={handleFamilyChange}
            />
          )}

          <Text style={[styles.label, { color: isDarkMode ? '#D1D5DB' : '#4B5563' }]}>
            {t('approval.amount')}*
          </Text>
          <View style={styles.amountContainer}>
            <TextInput
              style={[
                styles.amountInput,
                {
                  backgroundColor: isDarkMode ? '#374151' : '#F3F4F6',
                  color: isDarkMode ? '#FFFFFF' : '#111827',
                },
              ]}
              value={amount}
              onChangeText={handleAmountChange}
              placeholder={t('approval.enterAmount')}
              placeholderTextColor={isDarkMode ? '#9CA3AF' : '#6B7280'}
              keyboardType="numeric"
            />
            <TouchableOpacity
              style={[
                styles.checkButton,
                { backgroundColor: '#0366D6' },
              ]}
              onPress={handleCheckApproval}
              disabled={!selectedFamily || !categoryId || !amount || isNaN(parseFloat(amount)) || isCheckingApproval}
            >
              {isCheckingApproval ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.checkButtonText}>
                  {t('approval.check')}
                </Text>
              )}
            </TouchableOpacity>
          </View>

          {approvalRequired !== null && (
            <View
              style={[
                styles.approvalInfoContainer,
                {
                  backgroundColor: approvalRequired
                    ? 'rgba(239, 68, 68, 0.1)'
                    : 'rgba(16, 185, 129, 0.1)',
                },
              ]}
            >
              <Ionicons
                name={approvalRequired ? 'alert-circle' : 'checkmark-circle'}
                size={20}
                color={approvalRequired ? '#EF4444' : '#10B981'}
              />
              <Text
                style={[
                  styles.approvalInfoText,
                  { color: approvalRequired ? '#EF4444' : '#10B981' },
                ]}
              >
                {approvalRequired
                  ? t('approval.approvalRequired', { threshold: thresholdAmount?.toFixed(2) || '0.00' })
                  : t('approval.approvalNotRequired')}
              </Text>
            </View>
          )}

          <Text style={[styles.label, { color: isDarkMode ? '#D1D5DB' : '#4B5563' }]}>
            {t('approval.description')}*
          </Text>
          <TextInput
            style={[
              styles.descriptionInput,
              {
                backgroundColor: isDarkMode ? '#374151' : '#F3F4F6',
                color: isDarkMode ? '#FFFFFF' : '#111827',
                textAlignVertical: 'top',
              },
            ]}
            value={description}
            onChangeText={setDescription}
            placeholder={t('approval.enterDescription')}
            placeholderTextColor={isDarkMode ? '#9CA3AF' : '#6B7280'}
            multiline
            numberOfLines={4}
          />

          <Text style={[styles.label, { color: isDarkMode ? '#D1D5DB' : '#4B5563' }]}>
            {t('approval.category')}*
          </Text>
          <View
            style={[
              styles.categoriesContainer,
              { backgroundColor: isDarkMode ? '#374151' : '#F3F4F6' },
            ]}
          >
            <ScrollView
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.categoriesContent}
            >
              {expenseCategories.map(category => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.categoryButton,
                    categoryId === category.id && styles.selectedCategory,
                    {
                      backgroundColor: isDarkMode
                        ? categoryId === category.id
                          ? '#0366D6'
                          : '#4B5563'
                        : categoryId === category.id
                        ? '#0366D6'
                        : '#E5E7EB',
                    },
                  ]}
                  onPress={() => handleCategoryChange(category.id || 0)}
                >
                  <Text
                    style={[
                      styles.categoryText,
                      {
                        color: categoryId === category.id
                          ? '#FFFFFF'
                          : isDarkMode
                          ? '#D1D5DB'
                          : '#4B5563',
                      },
                    ]}
                  >
                    {category.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {error && (
            <View style={styles.errorContainer}>
              <Text style={[styles.errorText, { color: isDarkMode ? '#F87171' : '#EF4444' }]}>
                {error}
              </Text>
            </View>
          )}

          <TouchableOpacity
            style={[
              styles.submitButton,
              { backgroundColor: loading ? '#9CA3AF' : '#0366D6' },
            ]}
            onPress={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Text style={styles.submitButtonText}>
                {t('approval.submitRequest')}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  formContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
  },
  amountContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  amountInput: {
    flex: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginRight: 8,
  },
  checkButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  approvalInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  approvalInfoText: {
    fontSize: 14,
    marginLeft: 8,
  },
  descriptionInput: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginBottom: 16,
    minHeight: 100,
  },
  categoriesContainer: {
    borderRadius: 8,
    marginBottom: 24,
    maxHeight: 200,
  },
  categoriesContent: {
    padding: 12,
  },
  categoryButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedCategory: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  categoryText: {
    fontSize: 14,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
  submitButton: {
    paddingVertical: 12,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  submitButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
});
