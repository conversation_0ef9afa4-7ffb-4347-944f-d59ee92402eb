import React from 'react';
import { Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import FeatureManagementScreen from '../../src/screens/settings/FeatureManagementScreen';

export default function FeaturesLayout() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  return (
    <>
      <Stack.Screen
        options={{
          title: t('features.title'),
          headerStyle: {
            backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
          headerShadowVisible: false,
        }}
      />
      <FeatureManagementScreen />
    </>
  );
}
