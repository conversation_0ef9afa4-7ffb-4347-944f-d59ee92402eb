import React from 'react';
import { Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/contexts/ThemeContext';
import ProtectedRoute from '../../src/components/ProtectedRoute';

export default function SyncLayout() {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  return (
    <ProtectedRoute>
      <Stack
        screenOptions={{
          headerStyle: {
            backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
          },
          headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
          headerShadowVisible: false,
        }}
      >
        <Stack.Screen
          name="index"
          options={{
            title: t('sync.syncStatus'),
          }}
        />
        <Stack.Screen
          name="conflict-resolution"
          options={{
            title: t('sync.conflictResolution'),
          }}
        />
        <Stack.Screen
          name="merge-editor"
          options={{
            title: t('sync.mergeEditor'),
          }}
        />
      </Stack>
    </ProtectedRoute>
  );
}
