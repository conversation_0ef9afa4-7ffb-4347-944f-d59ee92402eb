/**
 * Migration script to move data from AsyncStorage to IndexedDB
 * 
 * This script can be run from the command line to manually trigger the migration:
 * node scripts/migrate-storage.js
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// ASCII art for the migration script
const asciiArt = `
╔═══════════════════════════════════════════════════════════╗
║                                                           ║
║   ██╗  ██╗██╗███████╗ █████╗ ██████╗     ██╗  ██╗██╗      ║
║   ██║  ██║██║██╔════╝██╔══██╗██╔══██╗    ██║ ██╔╝██║      ║
║   ███████║██║███████╗███████║██████╔╝    █████╔╝ ██║      ║
║   ██╔══██║██║╚════██║██╔══██║██╔══██╗    ██╔═██╗ ██║      ║
║   ██║  ██║██║███████║██║  ██║██████╔╝    ██║  ██╗██║      ║
║   ╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═╝╚═════╝     ╚═╝  ╚═╝╚═╝      ║
║                                                           ║
║                 STORAGE MIGRATION TOOL                    ║
║                                                           ║
╚═══════════════════════════════════════════════════════════╝
`;

// Print ASCII art
console.log(asciiArt);

// Print migration information
console.log('This script will help you prepare for the migration from AsyncStorage to IndexedDB.');
console.log('The actual migration will happen in the app when the user opens it next time.');
console.log('This script will create a migration flag file to trigger the migration.\n');

// Ask for confirmation
rl.question('Do you want to proceed? (y/n): ', (answer) => {
  if (answer.toLowerCase() !== 'y') {
    console.log('Migration cancelled.');
    rl.close();
    return;
  }
  
  // Create migration flag file
  try {
    const flagFilePath = path.join(__dirname, '..', 'migration-flag.json');
    const flagData = {
      migrationNeeded: true,
      timestamp: new Date().toISOString()
    };
    
    fs.writeFileSync(flagFilePath, JSON.stringify(flagData, null, 2));
    
    console.log('\n✅ Migration flag file created successfully!');
    console.log(`Flag file path: ${flagFilePath}`);
    console.log('\nThe next time users open the app, they will see the migration screen.');
    console.log('Make sure to include the following packages in your project:');
    console.log('  - dexie');
    console.log('  - @react-native-community/netinfo');
    console.log('\nYou can install them with:');
    console.log('  npm install dexie @react-native-community/netinfo\n');
    
    rl.close();
  } catch (error) {
    console.error('\n❌ Error creating migration flag file:', error.message);
    rl.close();
  }
});
