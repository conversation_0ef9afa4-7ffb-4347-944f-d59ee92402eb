{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/material-top-tabs": "^7.2.13", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "@reduxjs/toolkit": "^2.8.1", "axios": "^1.9.0", "date-fns": "^4.1.0", "dexie": "^4.0.11", "expo": "~53.0.9", "expo-blur": "~14.1.4", "expo-camera": "^16.1.6", "expo-constants": "~17.1.6", "expo-device": "^7.1.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-image-manipulator": "^13.1.7", "expo-image-picker": "^16.1.4", "expo-linking": "~7.1.5", "expo-media-library": "^17.1.6", "expo-notifications": "^0.31.2", "expo-router": "~5.0.7", "expo-sharing": "^13.1.5", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "i18next": "^25.1.2", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.5.1", "react-native": "0.79.2", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-tab-view": "^4.1.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "react-redux": "^9.2.0", "redux": "^5.0.1", "tesseract.js": "^6.0.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}