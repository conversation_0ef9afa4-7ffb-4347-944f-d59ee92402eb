{"expo": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "orientation": "portrait", "scheme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static"}, "plugins": ["expo-router", "expo-dev-client", ["expo-splash-screen", {"imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}}}