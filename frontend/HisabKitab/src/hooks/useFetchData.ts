import { useState, useCallback, useEffect } from 'react';
import { TFunction } from 'i18next';
import { handleApiError } from '../utils/errorHandling';

/**
 * Custom hook for fetching data with loading, error, and refresh states
 * @param fetchFunction The function to fetch data
 * @param t The translation function
 * @param errorKey The translation key for error messages
 * @param dependencies Dependencies array for the fetch function
 * @returns Object with data, loading state, error state, and refresh function
 */
export function useFetchData<T>(
  fetchFunction: () => Promise<{ status: number; data?: T; error?: string }>,
  t: TFunction,
  errorKey: string,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async (showLoadingIndicator = true) => {
    try {
      if (showLoadingIndicator) {
        setIsLoading(true);
      }

      const response = await fetchFunction();

      if (response.status === 200 && response.data) {
        setData(response.data);
        setError(null);
      } else {
        setError(response.error || t(errorKey));
        handleApiError(response, t, errorKey);
      }
    } catch (err) {
      setError(t(errorKey));
      handleApiError(err, t, errorKey);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [fetchFunction, t, errorKey]);

  const refresh = useCallback(() => {
    setIsRefreshing(true);
    fetchData(false);
  }, [fetchData]);

  useEffect(() => {
    let isMounted = true;

    const load = async () => {
      try {
        setIsLoading(true);
        const response = await fetchFunction();

        if (!isMounted) return;

        if (response.status === 200 && response.data) {
          setData(response.data);
          setError(null);
        } else {
          if (!isMounted) return;
          setError(response.error || t(errorKey));
          handleApiError(response, t, errorKey);
        }
      } catch (err) {
        if (!isMounted) return;
        setError(t(errorKey));
        handleApiError(err, t, errorKey);
      } finally {
        if (isMounted) {
          setIsLoading(false);
          setIsRefreshing(false);
        }
      }
    };

    load();

    return () => {
      isMounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [...dependencies]);

  return { data, isLoading, isRefreshing, error, refresh, fetchData };
}

/**
 * Custom hook for fetching a single item with loading and error states
 * @param fetchFunction The function to fetch the item
 * @param t The translation function
 * @param errorKey The translation key for error messages
 * @param dependencies Dependencies array for the fetch function
 * @returns Object with item, loading state, and error state
 */
export function useFetchItem<T>(
  fetchFunction: () => Promise<{ status: number; data?: T; error?: string }>,
  t: TFunction,
  errorKey: string,
  dependencies: any[] = []
) {
  const [item, setItem] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;

    const fetchItem = async () => {
      try {
        setIsLoading(true);
        const response = await fetchFunction();

        if (!isMounted) return;

        if (response.status === 200 && response.data) {
          setItem(response.data);
          setError(null);
        } else {
          if (!isMounted) return;
          setError(response.error || t(errorKey));
          handleApiError(response, t, errorKey);
        }
      } catch (err) {
        if (!isMounted) return;
        setError(t(errorKey));
        handleApiError(err, t, errorKey);
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchItem();

    return () => {
      isMounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [...dependencies]);

  return { item, isLoading, error };
}
