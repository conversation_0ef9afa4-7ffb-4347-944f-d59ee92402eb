import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAuth } from './AuthContext';
import apiService from '../services/api';
import { Notification } from '../models/Notification';
import { useNetInfo } from '@react-native-community/netinfo';

// Define types for new notification features
export type NotificationType = 'Info' | 'Success' | 'Warning' | 'Error' | 'Transaction' | 'Loan' | 'Budget' | 'Goal' | 'MonthlySummary';

export interface ScheduledNotification {
  id: number;
  title: string;
  message: string;
  type: NotificationType;
  scheduledFor: string;
  relatedEntityType?: string;
  relatedEntityId?: number;
  actionUrl?: string;
  isActive: boolean;
  isSent: boolean;
  sentAt?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface RecurringNotification {
  id: number;
  title: string;
  message: string;
  type: NotificationType;
  recurrenceType: string;
  recurrenceInterval: number;
  startDate: string;
  endDate?: string;
  recurrenceData?: string;
  relatedEntityType?: string;
  relatedEntityId?: number;
  actionUrl?: string;
  isActive: boolean;
  lastRunAt: string;
  nextRunAt: string;
  createdAt: string;
  updatedAt?: string;
}

export interface UserDevice {
  id: number;
  deviceId: string;
  deviceName: string;
  deviceType: string;
  pushToken: string;
  isActive: boolean;
  lastActiveAt: string;
  createdAt: string;
  updatedAt?: string;
}

export interface CategoryAmount {
  category: string;
  amount: number;
}

export interface DailyTrend {
  date: string;
  income: number;
  expense: number;
}

export interface MonthlySummary {
  id: number;
  month: number;
  year: number;
  totalIncome: number;
  totalExpense: number;
  netSavings: number;
  savingsRate: number;
  transactionCount: number;
  averageExpense: number;
  largestExpense: number;
  topExpenseCategory: string;
  topIncomeSource: string;
  expensesByCategory: CategoryAmount[];
  dailyTrend: DailyTrend[];
  createdAt: string;
  updatedAt?: string;
}

interface NotificationContextProps {
  // Basic notifications
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  fetchNotifications: () => Promise<void>;
  markAsRead: (id: number) => Promise<boolean>;
  markAllAsRead: () => Promise<boolean>;
  deleteNotification: (id: number) => Promise<boolean>;

  // Scheduled notifications
  scheduledNotifications: ScheduledNotification[];
  fetchScheduledNotifications: () => Promise<void>;
  scheduleNotification: (notification: {
    title: string;
    message: string;
    scheduledFor: string;
    type?: NotificationType;
    relatedEntityType?: string;
    relatedEntityId?: number;
    actionUrl?: string;
  }) => Promise<void>;
  updateScheduledNotification: (id: number, updates: {
    title?: string;
    message?: string;
    scheduledFor?: string;
    isActive?: boolean;
  }) => Promise<void>;
  deleteScheduledNotification: (id: number) => Promise<void>;

  // Recurring notifications
  recurringNotifications: RecurringNotification[];
  fetchRecurringNotifications: () => Promise<void>;
  createRecurringNotification: (notification: {
    title: string;
    message: string;
    recurrenceType: string;
    recurrenceInterval: number;
    startDate: string;
    endDate?: string;
    type?: NotificationType;
    relatedEntityType?: string;
    relatedEntityId?: number;
    actionUrl?: string;
    recurrenceData?: string;
  }) => Promise<void>;
  updateRecurringNotification: (id: number, updates: {
    title?: string;
    message?: string;
    recurrenceType?: string;
    recurrenceInterval?: number;
    startDate?: string;
    endDate?: string;
    isActive?: boolean;
    recurrenceData?: string;
  }) => Promise<void>;
  deleteRecurringNotification: (id: number) => Promise<void>;

  // Device management
  registerDevice: (deviceInfo: {
    deviceId: string;
    pushToken: string;
    deviceName?: string;
    deviceType?: string;
  }) => Promise<void>;
  updateDeviceToken: (deviceId: string, pushToken: string) => Promise<void>;
  unregisterDevice: (deviceId: string) => Promise<void>;

  // Monthly summary
  getMonthlySummary: (month: number, year: number) => Promise<MonthlySummary | null>;
}

const NotificationContext = createContext<NotificationContextProps | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

export const NotificationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { t } = useTranslation();
  const { isAuthenticated } = useAuth();
  const netInfo = useNetInfo();

  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [scheduledNotifications, setScheduledNotifications] = useState<ScheduledNotification[]>([]);
  const [recurringNotifications, setRecurringNotifications] = useState<RecurringNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch notifications when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      // Fetch all notification types
      fetchNotifications();
      fetchScheduledNotifications();
      fetchRecurringNotifications();

      // Set up polling for new notifications (every 1 minute)
      const intervalId = setInterval(() => {
        fetchNotifications(false); // Silent refresh (no loading indicator)
      }, 60000);

      return () => clearInterval(intervalId);
    }
  }, [isAuthenticated]);

  // Fetch notifications from API
  const fetchNotifications = async (showLoading = true) => {
    if (showLoading) {
      setIsLoading(true);
    }
    setError(null);

    try {
      const response = await apiService.notifications.getNotifications();

      if (response.data) {
        setNotifications(response.data);
        setUnreadCount(response.data.filter(n => !n.isRead).length);
      } else if (response.error) {
        setError(response.error);
        if (showLoading) {
          Alert.alert(t('common.error'), response.error);
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      if (showLoading) {
        Alert.alert(t('common.error'), errorMessage);
      }
    } finally {
      if (showLoading) {
        setIsLoading(false);
      }
    }
  };

  // Mark notification as read
  const markAsRead = async (id: number) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.notifications.markAsRead(id);

      if (response.data) {
        // Update local state
        setNotifications(prevNotifications =>
          prevNotifications.map(notification =>
            notification.id === id
              ? { ...notification, isRead: true, readAt: new Date().toISOString() }
              : notification
          )
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
        return true;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return false;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.notifications.markAllAsRead();

      if (response.data) {
        // Update local state
        setNotifications(prevNotifications =>
          prevNotifications.map(notification => ({
            ...notification,
            isRead: true,
            readAt: notification.readAt || new Date().toISOString()
          }))
        );
        setUnreadCount(0);
        return true;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return false;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Delete notification
  const deleteNotification = async (id: number) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.notifications.deleteNotification(id);

      if (response.data) {
        // Update local state
        const deletedNotification = notifications.find(n => n.id === id);
        setNotifications(prevNotifications =>
          prevNotifications.filter(notification => notification.id !== id)
        );

        // Update unread count if needed
        if (deletedNotification && !deletedNotification.isRead) {
          setUnreadCount(prev => Math.max(0, prev - 1));
        }

        return true;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return false;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch scheduled notifications
  const fetchScheduledNotifications = async () => {
    if (!isAuthenticated || !netInfo.isConnected) {
      Alert.alert(t('common.error'), t('common.offlineError'));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.notifications.getScheduledNotifications();

      if (response.data) {
        setScheduledNotifications(response.data);
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch recurring notifications
  const fetchRecurringNotifications = async () => {
    if (!isAuthenticated || !netInfo.isConnected) {
      Alert.alert(t('common.error'), t('common.offlineError'));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.notifications.getRecurringNotifications();

      if (response.data) {
        setRecurringNotifications(response.data);
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Schedule a notification
  const scheduleNotification = async (notification: {
    title: string;
    message: string;
    scheduledFor: string;
    type?: NotificationType;
    relatedEntityType?: string;
    relatedEntityId?: number;
    actionUrl?: string;
  }) => {
    if (!isAuthenticated || !netInfo.isConnected) {
      Alert.alert(t('common.error'), t('common.offlineError'));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.notifications.scheduleNotification(notification);

      if (response.data) {
        // Update state with new scheduled notification
        setScheduledNotifications(prev => [...prev, response.data]);
        Alert.alert(t('common.success'), t('notifications.scheduleSuccess'));
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Update a scheduled notification
  const updateScheduledNotification = async (id: number, updates: {
    title?: string;
    message?: string;
    scheduledFor?: string;
    isActive?: boolean;
  }) => {
    if (!isAuthenticated || !netInfo.isConnected) {
      Alert.alert(t('common.error'), t('common.offlineError'));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.notifications.updateScheduledNotification(id, updates);

      if (response.data) {
        // Update state with updated scheduled notification
        setScheduledNotifications(prev =>
          prev.map(n => n.id === id ? response.data : n)
        );
        Alert.alert(t('common.success'), t('notifications.updateScheduleSuccess'));
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Delete a scheduled notification
  const deleteScheduledNotification = async (id: number) => {
    if (!isAuthenticated || !netInfo.isConnected) {
      Alert.alert(t('common.error'), t('common.offlineError'));
      return;
    }

    try {
      const response = await apiService.notifications.deleteScheduledNotification(id);

      if (response.status === 200) {
        // Update state
        setScheduledNotifications(prev => prev.filter(n => n.id !== id));
        Alert.alert(t('common.success'), t('notifications.deleteScheduleSuccess'));
      } else if (response.error) {
        Alert.alert(t('common.error'), response.error);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      Alert.alert(t('common.error'), errorMessage);
    }
  };

  // Create a recurring notification
  const createRecurringNotification = async (notification: {
    title: string;
    message: string;
    recurrenceType: string;
    recurrenceInterval: number;
    startDate: string;
    endDate?: string;
    type?: NotificationType;
    relatedEntityType?: string;
    relatedEntityId?: number;
    actionUrl?: string;
    recurrenceData?: string;
  }) => {
    if (!isAuthenticated || !netInfo.isConnected) {
      Alert.alert(t('common.error'), t('common.offlineError'));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.notifications.createRecurringNotification(notification);

      if (response.data) {
        // Update state with new recurring notification
        setRecurringNotifications(prev => [...prev, response.data]);
        Alert.alert(t('common.success'), t('notifications.createRecurringSuccess'));
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Update a recurring notification
  const updateRecurringNotification = async (id: number, updates: {
    title?: string;
    message?: string;
    recurrenceType?: string;
    recurrenceInterval?: number;
    startDate?: string;
    endDate?: string;
    isActive?: boolean;
    recurrenceData?: string;
  }) => {
    if (!isAuthenticated || !netInfo.isConnected) {
      Alert.alert(t('common.error'), t('common.offlineError'));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.notifications.updateRecurringNotification(id, updates);

      if (response.data) {
        // Update state with updated recurring notification
        setRecurringNotifications(prev =>
          prev.map(n => n.id === id ? response.data : n)
        );
        Alert.alert(t('common.success'), t('notifications.updateRecurringSuccess'));
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Delete a recurring notification
  const deleteRecurringNotification = async (id: number) => {
    if (!isAuthenticated || !netInfo.isConnected) {
      Alert.alert(t('common.error'), t('common.offlineError'));
      return;
    }

    try {
      const response = await apiService.notifications.deleteRecurringNotification(id);

      if (response.status === 200) {
        // Update state
        setRecurringNotifications(prev => prev.filter(n => n.id !== id));
        Alert.alert(t('common.success'), t('notifications.deleteRecurringSuccess'));
      } else if (response.error) {
        Alert.alert(t('common.error'), response.error);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      Alert.alert(t('common.error'), errorMessage);
    }
  };

  // Register device for push notifications
  const registerDevice = async (deviceInfo: {
    deviceId: string;
    pushToken: string;
    deviceName?: string;
    deviceType?: string;
  }) => {
    if (!isAuthenticated || !netInfo.isConnected) return;

    try {
      await apiService.notifications.registerDevice(deviceInfo);
    } catch (err) {
      console.error('Error registering device:', err);
    }
  };

  // Update device token
  const updateDeviceToken = async (deviceId: string, pushToken: string) => {
    if (!isAuthenticated || !netInfo.isConnected) return;

    try {
      await apiService.notifications.updateDeviceToken(deviceId, { pushToken });
    } catch (err) {
      console.error('Error updating device token:', err);
    }
  };

  // Unregister device
  const unregisterDevice = async (deviceId: string) => {
    if (!isAuthenticated || !netInfo.isConnected) return;

    try {
      await apiService.notifications.unregisterDevice(deviceId);
    } catch (err) {
      console.error('Error unregistering device:', err);
    }
  };

  // Get monthly summary
  const getMonthlySummary = async (month: number, year: number): Promise<MonthlySummary | null> => {
    if (!isAuthenticated || !netInfo.isConnected) {
      Alert.alert(t('common.error'), t('common.offlineError'));
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.notifications.getMonthlySummary(month, year);

      if (response.data) {
        return response.data;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    notifications,
    scheduledNotifications,
    recurringNotifications,
    unreadCount,
    isLoading,
    error,
    fetchNotifications,
    fetchScheduledNotifications,
    fetchRecurringNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    scheduleNotification,
    updateScheduledNotification,
    deleteScheduledNotification,
    createRecurringNotification,
    updateRecurringNotification,
    deleteRecurringNotification,
    registerDevice,
    updateDeviceToken,
    unregisterDevice,
    getMonthlySummary,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};
