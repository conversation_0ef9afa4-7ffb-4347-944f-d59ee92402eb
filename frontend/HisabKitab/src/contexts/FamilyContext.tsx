import React, { createContext, useContext, useState, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { useToast } from './ToastContext';

// Define types for family data
export interface FamilyMember {
  userId: number;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  joinedAt: string;
}

export interface Family {
  id: number;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  members?: FamilyMember[];
}

export interface CreateFamilyRequest {
  name: string;
  description?: string;
}

export interface UpdateFamilyRequest {
  name: string;
  description?: string;
}

export interface InviteMemberRequest {
  email: string;
  role: string;
}

// Define the context interface
interface FamilyContextType {
  // State
  families: Family[];
  selectedFamily: Family | null;
  loading: boolean;
  error: string | null;
  isFamilyAdmin: boolean;

  // Actions
  fetchFamilies: () => Promise<void>;
  fetchFamily: (id: number) => Promise<void>;
  createFamily: (data: CreateFamilyRequest) => Promise<boolean>;
  updateFamily: (id: number, data: UpdateFamilyRequest) => Promise<boolean>;
  deleteFamily: (id: number) => Promise<boolean>;
  setSelectedFamily: (family: Family | null) => void;
  inviteMember: (familyId: number, data: InviteMemberRequest) => Promise<boolean>;
  removeMember: (familyId: number, userId: number) => Promise<boolean>;
  leaveFamily: (familyId: number) => Promise<boolean>;
  changeMemberRole: (familyId: number, userId: number, role: string) => Promise<boolean>;
}

// Create the context
const FamilyContext = createContext<FamilyContextType | undefined>(undefined);

// Provider component
export const FamilyProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const toast = useToast();

  // State
  const [families, setFamilies] = useState<Family[]>([
    {
      id: 1,
      name: 'My Family',
      description: 'Personal family group',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
      members: [
        {
          userId: 1,
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          role: 'Admin',
          joinedAt: new Date().toISOString()
        },
        {
          userId: 2,
          firstName: 'Jane',
          lastName: 'Doe',
          email: '<EMAIL>',
          role: 'Member',
          joinedAt: new Date().toISOString()
        }
      ]
    },
    {
      id: 2,
      name: 'Work Group',
      description: 'Office expenses',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
      members: [
        {
          userId: 1,
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          role: 'Admin',
          joinedAt: new Date().toISOString()
        }
      ]
    }
  ]);
  const [selectedFamily, setSelectedFamily] = useState<Family | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check if user is admin of selected family
  const isFamilyAdmin = selectedFamily?.members?.some(
    member => member.userId === user?.id && member.role === 'Admin'
  ) || false;

  // Fetch families
  const fetchFamilies = async (): Promise<void> => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      // Mock API call - in a real app, this would be an API call
      // const response = await apiService.family.getFamilies();

      // Simulate API response
      setTimeout(() => {
        // setFamilies(response.data);
        setLoading(false);
      }, 500);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch families';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      setLoading(false);
    }
  };

  // Fetch a single family
  const fetchFamily = async (id: number): Promise<void> => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      // Mock API call
      const family = families.find(f => f.id === id) || null;

      if (family) {
        setSelectedFamily(family);
      } else {
        setError('Family not found');
        toast.show('Family not found', { type: 'error' });
      }

      setLoading(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch family';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      setLoading(false);
    }
  };

  // Create a family
  const createFamily = async (data: CreateFamilyRequest): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      // Mock API call
      const newFamily: Family = {
        id: families.length + 1,
        name: data.name,
        description: data.description,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isActive: true,
        members: [
          {
            userId: user?.id || 1,
            firstName: user?.firstName || 'John',
            lastName: user?.lastName || 'Doe',
            email: user?.email || '<EMAIL>',
            role: 'Admin',
            joinedAt: new Date().toISOString()
          }
        ]
      };

      setFamilies([...families, newFamily]);
      toast.show('Family created successfully', { type: 'success' });
      setLoading(false);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create family';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      setLoading(false);
      return false;
    }
  };

  // Update a family
  const updateFamily = async (id: number, data: UpdateFamilyRequest): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      // Mock API call
      const updatedFamilies = families.map(family =>
        family.id === id
          ? { ...family, ...data, updatedAt: new Date().toISOString() }
          : family
      );

      setFamilies(updatedFamilies);

      if (selectedFamily && selectedFamily.id === id) {
        setSelectedFamily({ ...selectedFamily, ...data, updatedAt: new Date().toISOString() });
      }

      toast.show('Family updated successfully', { type: 'success' });
      setLoading(false);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update family';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      setLoading(false);
      return false;
    }
  };

  // Delete a family
  const deleteFamily = async (id: number): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      // Mock API call
      const updatedFamilies = families.filter(family => family.id !== id);

      setFamilies(updatedFamilies);

      if (selectedFamily && selectedFamily.id === id) {
        setSelectedFamily(null);
      }

      toast.show('Family deleted successfully', { type: 'success' });
      setLoading(false);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete family';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      setLoading(false);
      return false;
    }
  };

  // Invite a member
  const inviteMember = async (familyId: number, data: InviteMemberRequest): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      // Mock API call - using familyId and data to avoid unused parameter warnings
      console.log(`Inviting member to family ${familyId} with email ${data.email} and role ${data.role}`);
      toast.show('Invitation sent successfully', { type: 'success' });
      setLoading(false);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to invite member';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      setLoading(false);
      return false;
    }
  };

  // Remove a member
  const removeMember = async (familyId: number, userId: number): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      // Mock API call
      const updatedFamilies = families.map(family => {
        if (family.id === familyId && family.members) {
          return {
            ...family,
            members: family.members.filter(member => member.userId !== userId),
            updatedAt: new Date().toISOString()
          };
        }
        return family;
      });

      setFamilies(updatedFamilies);

      if (selectedFamily && selectedFamily.id === familyId && selectedFamily.members) {
        setSelectedFamily({
          ...selectedFamily,
          members: selectedFamily.members.filter(member => member.userId !== userId),
          updatedAt: new Date().toISOString()
        });
      }

      toast.show('Member removed successfully', { type: 'success' });
      setLoading(false);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove member';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      setLoading(false);
      return false;
    }
  };

  // Leave a family
  const leaveFamily = async (familyId: number): Promise<boolean> => {
    if (!isAuthenticated || !user || !user.id) return false;

    return removeMember(familyId, user.id);
  };

  // Change member role
  const changeMemberRole = async (familyId: number, userId: number, role: string): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      // Mock API call
      const updatedFamilies = families.map(family => {
        if (family.id === familyId && family.members) {
          return {
            ...family,
            members: family.members.map(member =>
              member.userId === userId
                ? { ...member, role, updatedAt: new Date().toISOString() }
                : member
            ),
            updatedAt: new Date().toISOString()
          };
        }
        return family;
      });

      setFamilies(updatedFamilies);

      if (selectedFamily && selectedFamily.id === familyId && selectedFamily.members) {
        setSelectedFamily({
          ...selectedFamily,
          members: selectedFamily.members.map(member =>
            member.userId === userId
              ? { ...member, role, updatedAt: new Date().toISOString() }
              : member
          ),
          updatedAt: new Date().toISOString()
        });
      }

      toast.show('Member role updated successfully', { type: 'success' });
      setLoading(false);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to change member role';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      setLoading(false);
      return false;
    }
  };

  // Context value
  const value: FamilyContextType = {
    families,
    selectedFamily,
    loading,
    error,
    isFamilyAdmin,
    fetchFamilies,
    fetchFamily,
    createFamily,
    updateFamily,
    deleteFamily,
    setSelectedFamily,
    inviteMember,
    removeMember,
    leaveFamily,
    changeMemberRole
  };

  return (
    <FamilyContext.Provider value={value}>
      {children}
    </FamilyContext.Provider>
  );
};

// Custom hook to use the family context
export const useFamily = () => {
  const context = useContext(FamilyContext);
  if (context === undefined) {
    throw new Error('useFamily must be used within a FamilyProvider');
  }
  return context;
};
