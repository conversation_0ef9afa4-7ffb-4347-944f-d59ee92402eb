import React, { createContext, useContext, useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, Animated, TouchableOpacity } from 'react-native';
import { useTheme } from './ThemeContext';
import { Ionicons } from '@expo/vector-icons';

interface Toast {
  id: string;
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
  duration?: number;
}

interface ToastContextType {
  show: (message: string, options?: { type?: 'success' | 'error' | 'info' | 'warning'; duration?: number }) => string;
  hide: (id: string) => void;
  hideAll: () => void;
}

const ToastContext = createContext<ToastContextType>({
  show: () => '',
  hide: () => {},
  hideAll: () => {},
});

export const useToast = () => useContext(ToastContext);

interface ToastProviderProps {
  children: React.ReactNode;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [toasts, setToasts] = useState<Toast[]>([]);
  const { isDarkMode } = useTheme();

  // Show a toast notification
  const show = (
    message: string,
    options: { type?: 'success' | 'error' | 'info' | 'warning'; duration?: number } = {}
  ): string => {
    const id = Math.random().toString(36).substring(2, 9);
    const newToast: Toast = {
      id,
      message,
      type: options.type || 'info',
      duration: options.duration || 3000,
    };

    setToasts((prevToasts) => [...prevToasts, newToast]);
    
    // Auto-hide after duration
    if (newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        hide(id);
      }, newToast.duration);
    }

    return id;
  };

  // Hide a specific toast
  const hide = (id: string) => {
    setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id));
  };

  // Hide all toasts
  const hideAll = () => {
    setToasts([]);
  };

  return (
    <ToastContext.Provider value={{ show, hide, hideAll }}>
      {children}
      <View style={styles.toastContainer}>
        {toasts.map((toast) => (
          <ToastItem
            key={toast.id}
            toast={toast}
            onDismiss={() => hide(toast.id)}
            isDarkMode={isDarkMode}
          />
        ))}
      </View>
    </ToastContext.Provider>
  );
};

interface ToastItemProps {
  toast: Toast;
  onDismiss: () => void;
  isDarkMode: boolean;
}

const ToastItem: React.FC<ToastItemProps> = ({ toast, onDismiss, isDarkMode }) => {
  const opacity = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(20)).current;

  useEffect(() => {
    // Animate in
    Animated.parallel([
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    // Animate out before dismissal if duration is set
    if (toast.duration && toast.duration > 0) {
      const timer = setTimeout(() => {
        Animated.parallel([
          Animated.timing(opacity, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(translateY, {
            toValue: 20,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start();
      }, toast.duration - 300);

      return () => clearTimeout(timer);
    }
  }, []);

  // Get icon and color based on toast type
  const getToastStyles = () => {
    switch (toast.type) {
      case 'success':
        return {
          icon: 'checkmark-circle',
          backgroundColor: isDarkMode ? '#0D3320' : '#E8F5E9',
          textColor: isDarkMode ? '#81C784' : '#2E7D32',
        };
      case 'error':
        return {
          icon: 'alert-circle',
          backgroundColor: isDarkMode ? '#2D0709' : '#FFEBEE',
          textColor: isDarkMode ? '#F48FB1' : '#B71C1C',
        };
      case 'warning':
        return {
          icon: 'warning',
          backgroundColor: isDarkMode ? '#332D09' : '#FFF8E1',
          textColor: isDarkMode ? '#FFD54F' : '#F57F17',
        };
      case 'info':
      default:
        return {
          icon: 'information-circle',
          backgroundColor: isDarkMode ? '#0D2A3D' : '#E1F5FE',
          textColor: isDarkMode ? '#4FC3F7' : '#0277BD',
        };
    }
  };

  const { icon, backgroundColor, textColor } = getToastStyles();

  return (
    <Animated.View
      style={[
        styles.toast,
        { backgroundColor, opacity, transform: [{ translateY }] },
      ]}
    >
      <Ionicons name={icon as any} size={20} color={textColor} style={styles.icon} />
      <Text style={[styles.message, { color: textColor }]}>{toast.message}</Text>
      <TouchableOpacity onPress={onDismiss}>
        <Ionicons name="close" size={20} color={textColor} />
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  toastContainer: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 9999,
  },
  toast: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginVertical: 8,
    width: '90%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  icon: {
    marginRight: 8,
  },
  message: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
  },
});

export default ToastProvider;
