import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import themeConfig from '../styles/theme';

// Theme types
export type ThemeType = 'light' | 'dark' | 'system';

// Interface for theme context
interface ThemeContextType {
  theme: ThemeType;
  isDarkMode: boolean;
  setTheme: (theme: ThemeType) => void;
  colors: any;
  spacing: any;
  borderRadius: any;
  typography: any;
  elevation: any;
  animation: any;
  screenStyles: any;
}

// Create theme context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme provider props
interface ThemeProviderProps {
  children: ReactNode;
}

// Theme provider component
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const systemColorScheme = useColorScheme();
  const [theme, setThemeState] = useState<ThemeType>('system');

  // Compute if dark mode is active
  const isDarkMode = theme === 'system'
    ? systemColorScheme === 'dark'
    : theme === 'dark';

  // Load theme from storage on mount
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem('theme');
        if (savedTheme) {
          setThemeState(savedTheme as ThemeType);
        }
      } catch (error) {
        console.error('Error loading theme:', error);
      }
    };

    loadTheme();
  }, []);

  // Set theme and save to storage
  const setTheme = async (newTheme: ThemeType) => {
    try {
      await AsyncStorage.setItem('theme', newTheme);
      setThemeState(newTheme);
    } catch (error) {
      console.error('Error saving theme:', error);
    }
  };

  // Get theme colors based on mode
  const getThemeColors = () => {
    return {
      ...themeConfig.colors,
      background: isDarkMode ? themeConfig.colors.background.dark : themeConfig.colors.background.light,
      text: isDarkMode ? themeConfig.colors.text.dark : themeConfig.colors.text.light,
      card: isDarkMode ? themeConfig.colors.card.dark : themeConfig.colors.card.light,
      cardBorder: isDarkMode ? themeConfig.colors.card.border.dark : themeConfig.colors.card.border.light,
    };
  };

  // Context value
  const value = {
    theme,
    isDarkMode,
    setTheme,
    colors: getThemeColors(),
    spacing: themeConfig.spacing,
    borderRadius: themeConfig.borderRadius,
    typography: themeConfig.typography,
    elevation: themeConfig.elevation,
    animation: themeConfig.animation,
    screenStyles: themeConfig.screenStyles,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};

// Hook to use theme context
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeContext;
