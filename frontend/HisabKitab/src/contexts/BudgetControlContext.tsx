import React, { createContext, useContext, useState, ReactNode } from 'react';
import apiService from '../services/api';
import { useAuth } from './AuthContext';
import { useToast } from './ToastContext';
import { useFamily } from './FamilyContext';

// Define types for budget control data
export interface BudgetLimit {
  id: number;
  familyId: number;
  categoryId: number;
  categoryName: string;
  amount: number;
  period: string;
  startDate: string;
  endDate?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  remaining?: number;
}

export interface SpendingLimit {
  id: number;
  familyId: number;
  familyMemberUserId: number;
  familyMemberName: string;
  amount: number;
  period: string;
  startDate: string;
  endDate?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  remaining?: number;
}

export interface CreateBudgetLimitRequest {
  familyId: number;
  categoryId: number;
  amount: number;
  period: string;
  startDate: string;
  endDate?: string;
}

export interface UpdateBudgetLimitRequest {
  amount: number;
  period: string;
  startDate: string;
  endDate?: string;
  isActive: boolean;
}

export interface CreateSpendingLimitRequest {
  familyMemberUserId: number;
  amount: number;
  period: string;
  startDate: string;
  endDate?: string;
}

export interface UpdateSpendingLimitRequest {
  amount: number;
  period: string;
  startDate: string;
  endDate?: string;
  isActive: boolean;
}

// Define the context interface
interface BudgetControlContextType {
  // State
  budgetLimits: BudgetLimit[];
  spendingLimits: SpendingLimit[];
  selectedBudgetLimit: BudgetLimit | null;
  selectedSpendingLimit: SpendingLimit | null;
  loading: boolean;
  error: string | null;

  // Actions
  fetchBudgetLimits: (familyId?: number) => Promise<void>;
  fetchBudgetLimit: (id: number) => Promise<void>;
  createBudgetLimit: (data: CreateBudgetLimitRequest) => Promise<boolean>;
  updateBudgetLimit: (id: number, data: UpdateBudgetLimitRequest) => Promise<boolean>;
  deleteBudgetLimit: (id: number) => Promise<boolean>;
  fetchBudgetRemaining: (id: number) => Promise<number | null>;

  fetchSpendingLimits: (familyId: number) => Promise<void>;
  fetchSpendingLimit: (id: number) => Promise<void>;
  createSpendingLimit: (familyId: number, data: CreateSpendingLimitRequest) => Promise<boolean>;
  updateSpendingLimit: (id: number, data: UpdateSpendingLimitRequest) => Promise<boolean>;
  deleteSpendingLimit: (id: number) => Promise<boolean>;
  fetchSpendingLimitRemaining: (id: number) => Promise<number | null>;
}

// Create the context
const BudgetControlContext = createContext<BudgetControlContextType | undefined>(undefined);

// Provider component
export const BudgetControlProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const { selectedFamily } = useFamily();
  const toast = useToast();

  // State
  const [budgetLimits, setBudgetLimits] = useState<BudgetLimit[]>([]);
  const [spendingLimits, setSpendingLimits] = useState<SpendingLimit[]>([]);
  const [selectedBudgetLimit, setSelectedBudgetLimit] = useState<BudgetLimit | null>(null);
  const [selectedSpendingLimit, setSelectedSpendingLimit] = useState<SpendingLimit | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch budget limits
  const fetchBudgetLimits = async (familyId?: number) => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      let response;

      if (familyId) {
        response = await apiService.budgetControl.getFamilyBudgetLimits(familyId);
      } else {
        response = await apiService.budgetControl.getBudgetLimits();
      }

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
      } else if (response.data) {
        setBudgetLimits(response.data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch budget limits';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Fetch a single budget limit
  const fetchBudgetLimit = async (id: number) => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.budgetControl.getBudgetLimit(id);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
      } else if (response.data) {
        setSelectedBudgetLimit(response.data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch budget limit';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Create a budget limit
  const createBudgetLimit = async (data: CreateBudgetLimitRequest): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.budgetControl.createBudgetLimit(data);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
        return false;
      } else {
        toast.show('Budget limit created successfully', { type: 'success' });
        await fetchBudgetLimits(data.familyId);
        return true;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create budget limit';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Update a budget limit
  const updateBudgetLimit = async (id: number, data: UpdateBudgetLimitRequest): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.budgetControl.updateBudgetLimit(id, data);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
        return false;
      } else {
        toast.show('Budget limit updated successfully', { type: 'success' });

        // Refresh the list if we have a selected family
        if (selectedFamily) {
          await fetchBudgetLimits(selectedFamily.id);
        } else {
          await fetchBudgetLimits();
        }

        return true;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update budget limit';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Delete a budget limit
  const deleteBudgetLimit = async (id: number): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.budgetControl.deleteBudgetLimit(id);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
        return false;
      } else {
        toast.show('Budget limit deleted successfully', { type: 'success' });

        // Refresh the list if we have a selected family
        if (selectedFamily) {
          await fetchBudgetLimits(selectedFamily.id);
        } else {
          await fetchBudgetLimits();
        }

        return true;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete budget limit';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Fetch budget remaining
  const fetchBudgetRemaining = async (id: number): Promise<number | null> => {
    if (!isAuthenticated) return null;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.budgetControl.getBudgetRemaining(id);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
        return null;
      } else if (response.data) {
        return response.data.remaining;
      }

      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch budget remaining';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Fetch spending limits
  const fetchSpendingLimits = async (familyId: number) => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.budgetControl.getSpendingLimits(familyId);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
      } else if (response.data) {
        setSpendingLimits(response.data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch spending limits';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Fetch a single spending limit
  const fetchSpendingLimit = async (id: number) => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.budgetControl.getSpendingLimit(id);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
      } else if (response.data) {
        setSelectedSpendingLimit(response.data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch spending limit';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Create a spending limit
  const createSpendingLimit = async (familyId: number, data: CreateSpendingLimitRequest): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.budgetControl.createSpendingLimit(familyId, data);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
        return false;
      } else {
        toast.show('Spending limit created successfully', { type: 'success' });
        await fetchSpendingLimits(familyId);
        return true;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create spending limit';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Update a spending limit
  const updateSpendingLimit = async (id: number, data: UpdateSpendingLimitRequest): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.budgetControl.updateSpendingLimit(id, data);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
        return false;
      } else {
        toast.show('Spending limit updated successfully', { type: 'success' });

        // Refresh the list if we have a selected family
        if (selectedFamily) {
          await fetchSpendingLimits(selectedFamily.id);
        }

        return true;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update spending limit';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Delete a spending limit
  const deleteSpendingLimit = async (id: number): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.budgetControl.deleteSpendingLimit(id);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
        return false;
      } else {
        toast.show('Spending limit deleted successfully', { type: 'success' });

        // Refresh the list if we have a selected family
        if (selectedFamily) {
          await fetchSpendingLimits(selectedFamily.id);
        }

        return true;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete spending limit';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Fetch spending limit remaining
  const fetchSpendingLimitRemaining = async (id: number): Promise<number | null> => {
    if (!isAuthenticated) return null;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.budgetControl.getSpendingLimitRemaining(id);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
        return null;
      } else if (response.data) {
        return response.data.remaining;
      }

      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch spending limit remaining';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Context value
  const value: BudgetControlContextType = {
    budgetLimits,
    spendingLimits,
    selectedBudgetLimit,
    selectedSpendingLimit,
    loading,
    error,
    fetchBudgetLimits,
    fetchBudgetLimit,
    createBudgetLimit,
    updateBudgetLimit,
    deleteBudgetLimit,
    fetchBudgetRemaining,
    fetchSpendingLimits,
    fetchSpendingLimit,
    createSpendingLimit,
    updateSpendingLimit,
    deleteSpendingLimit,
    fetchSpendingLimitRemaining
  };

  return (
    <BudgetControlContext.Provider value={value}>
      {children}
    </BudgetControlContext.Provider>
  );
};

// Custom hook to use the budget control context
export const useBudgetControl = () => {
  const context = useContext(BudgetControlContext);
  if (context === undefined) {
    throw new Error('useBudgetControl must be used within a BudgetControlProvider');
  }
  return context;
};
