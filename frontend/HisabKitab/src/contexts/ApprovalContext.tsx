import React, { createContext, useContext, useState, ReactNode } from 'react';
import apiService from '../services/api';
import { useAuth } from './AuthContext';
import { useToast } from '../contexts/ToastContext';

import { useFamily } from './FamilyContext';

// Define types for approval data
export interface ApprovalRequest {
  id: number;
  familyId: number;
  familyName: string;
  requesterId: number;
  requesterName: string;
  approverId?: number;
  approverName?: string;
  amount: number;
  description: string;
  categoryId: number;
  categoryName: string;
  status: string;
  requestDate: string;
  responseDate?: string;
  rejectionReason?: string;
}

export interface CreateApprovalRequest {
  familyId: number;
  amount: number;
  description: string;
  categoryId: number;
}

export interface ApprovalResponse {
  transactionId?: number;
}

export interface RejectResponse {
  rejectionReason: string;
}

export interface ApprovalRequiredCheck {
  isApprovalRequired: boolean;
  thresholdAmount?: number;
}

// Define the context interface
interface ApprovalContextType {
  // State
  approvalRequests: ApprovalRequest[];
  selectedRequest: ApprovalRequest | null;
  loading: boolean;
  error: string | null;

  // Actions
  fetchApprovalRequests: (asRequester?: boolean, status?: string) => Promise<void>;
  fetchFamilyApprovalRequests: (familyId: number, status?: string) => Promise<void>;
  fetchApprovalRequest: (id: number) => Promise<void>;
  createApprovalRequest: (data: CreateApprovalRequest) => Promise<boolean>;
  approveRequest: (id: number) => Promise<boolean>;
  rejectRequest: (id: number, rejectionReason: string) => Promise<boolean>;
  checkApprovalRequired: (familyId: number, amount: number, categoryId: number) => Promise<ApprovalRequiredCheck | null>;
}

// Create the context
const ApprovalContext = createContext<ApprovalContextType | undefined>(undefined);

// Provider component
export const ApprovalProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  // Using useFamily but not extracting selectedFamily to avoid unused variable warning
  const { } = useFamily();
  const toast = useToast();

  // State
  const [approvalRequests, setApprovalRequests] = useState<ApprovalRequest[]>([]);
  const [selectedRequest, setSelectedRequest] = useState<ApprovalRequest | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch approval requests
  const fetchApprovalRequests = async (asRequester: boolean = true, status?: string) => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.approval.getApprovalRequests(asRequester, status);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
      } else if (response.data) {
        setApprovalRequests(response.data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch approval requests';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Fetch family approval requests
  const fetchFamilyApprovalRequests = async (familyId: number, status?: string) => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.approval.getFamilyApprovalRequests(familyId, status);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
      } else if (response.data) {
        setApprovalRequests(response.data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch family approval requests';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Fetch a single approval request
  const fetchApprovalRequest = async (id: number) => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.approval.getApprovalRequest(id);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
      } else if (response.data) {
        setSelectedRequest(response.data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch approval request';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Create an approval request
  const createApprovalRequest = async (data: CreateApprovalRequest): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.approval.createApprovalRequest(data);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
        return false;
      } else {
        toast.show('Approval request created successfully', { type: 'success' });
        await fetchApprovalRequests(true);
        return true;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create approval request';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Approve a request
  const approveRequest = async (id: number): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.approval.approveRequest(id, {});

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
        return false;
      } else {
        toast.show('Request approved successfully', { type: 'success' });

        // Refresh the list
        await fetchApprovalRequests(false);

        return true;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to approve request';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Reject a request
  const rejectRequest = async (id: number, rejectionReason: string): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.approval.rejectRequest(id, { rejectionReason });

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
        return false;
      } else {
        toast.show('Request rejected successfully', { type: 'success' });

        // Refresh the list
        await fetchApprovalRequests(false);

        return true;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reject request';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Check if approval is required
  const checkApprovalRequired = async (familyId: number, amount: number, categoryId: number): Promise<ApprovalRequiredCheck | null> => {
    if (!isAuthenticated) return null;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.approval.checkApprovalRequired(familyId, amount, categoryId);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'error' });
        return null;
      } else if (response.data) {
        return response.data;
      }

      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to check if approval is required';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'error' });
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Context value
  const value: ApprovalContextType = {
    approvalRequests,
    selectedRequest,
    loading,
    error,
    fetchApprovalRequests,
    fetchFamilyApprovalRequests,
    fetchApprovalRequest,
    createApprovalRequest,
    approveRequest,
    rejectRequest,
    checkApprovalRequired
  };

  return (
    <ApprovalContext.Provider value={value}>
      {children}
    </ApprovalContext.Provider>
  );
};

// Custom hook to use the approval context
export const useApproval = () => {
  const context = useContext(ApprovalContext);
  if (context === undefined) {
    throw new Error('useApproval must be used within an ApprovalProvider');
  }
  return context;
};
