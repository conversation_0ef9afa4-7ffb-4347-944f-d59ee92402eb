import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAuth } from './AuthContext';
import apiService from '../services/api';
import dbService from '../services/db';

// Feature flag interface
export interface FeatureFlag {
  id: number;
  name: string;
  description: string;
  isEnabled: boolean;
  enabledFor: string; // JSON string: "all", "beta", or array of user IDs
  lastUpdatedAt: string | Date;
}

// Feature flag context interface
interface FeatureFlagContextProps {
  features: FeatureFlag[];
  isLoading: boolean;
  error: string | null;
  fetchFeatures: () => Promise<void>;
  isFeatureEnabled: (featureName: string) => boolean;
  updateFeature: (id: number, isEnabled: boolean, enabledFor?: string) => Promise<boolean>;
}

// Create context
const FeatureFlagContext = createContext<FeatureFlagContextProps | undefined>(undefined);

// Use feature flag hook
export const useFeatureFlag = () => {
  const context = useContext(FeatureFlagContext);
  if (!context) {
    throw new Error('useFeatureFlag must be used within a FeatureFlagProvider');
  }
  return context;
};

// Feature flag provider
export const FeatureFlagProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { t } = useTranslation();
  const { isAuthenticated, user, hasRole } = useAuth();
  
  const [features, setFeatures] = useState<FeatureFlag[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch features on mount if authenticated
  useEffect(() => {
    if (isAuthenticated) {
      fetchFeatures();
    }
  }, [isAuthenticated]);
  
  // Fetch features from API
  const fetchFeatures = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Try to get features from local DB first
      const localFeatures = await dbService.getFeatureFlags();
      
      if (localFeatures && localFeatures.length > 0) {
        setFeatures(localFeatures);
      }
      
      // Then fetch from API
      const response = await apiService.features.getFeatures();
      
      if (response.data) {
        setFeatures(response.data);
        
        // Save to local DB
        await dbService.saveFeatureFlags(response.data);
      } else if (response.error) {
        setError(response.error);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      console.error('Error fetching features:', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Check if a feature is enabled
  const isFeatureEnabled = (featureName: string): boolean => {
    // If no features loaded yet, return false
    if (features.length === 0) {
      return false;
    }
    
    // Find the feature
    const feature = features.find((f) => f.name === featureName);
    
    // If feature doesn't exist, return false
    if (!feature) {
      return false;
    }
    
    // If feature is not enabled, return false
    if (!feature.isEnabled) {
      return false;
    }
    
    // Check enabledFor
    try {
      const enabledFor = JSON.parse(feature.enabledFor);
      
      // If enabled for all, return true
      if (enabledFor === 'all') {
        return true;
      }
      
      // If enabled for beta and user is platform admin or family admin, return true
      if (enabledFor === 'beta' && (hasRole('PlatformAdmin') || hasRole('FamilyAdmin'))) {
        return true;
      }
      
      // If enabled for specific users and user is in the list, return true
      if (Array.isArray(enabledFor) && user && enabledFor.includes(user.id)) {
        return true;
      }
      
      // Otherwise, return false
      return false;
    } catch (error) {
      // If enabledFor is not valid JSON, assume it's enabled for all
      return true;
    }
  };
  
  // Update feature
  const updateFeature = async (
    id: number,
    isEnabled: boolean,
    enabledFor?: string
  ): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await apiService.features.updateFeature(id, isEnabled, enabledFor);
      
      if (response.data) {
        // Update local state
        setFeatures((prevFeatures) =>
          prevFeatures.map((feature) =>
            feature.id === id
              ? { ...feature, isEnabled, enabledFor: enabledFor || feature.enabledFor }
              : feature
          )
        );
        
        // Update local DB
        await dbService.updateFeatureFlag(id, isEnabled, enabledFor);
        
        return true;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
        return false;
      }
      
      return false;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  };
  
  // Context value
  const value = {
    features,
    isLoading,
    error,
    fetchFeatures,
    isFeatureEnabled,
    updateFeature,
  };
  
  return (
    <FeatureFlagContext.Provider value={value}>
      {children}
    </FeatureFlagContext.Provider>
  );
};

export default FeatureFlagContext;
