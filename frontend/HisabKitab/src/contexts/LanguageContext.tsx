import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { loadLanguagePreference, saveLanguagePreference } from '../utils/i18n';
import { useTranslation } from 'react-i18next';

// Language types
export type LanguageType = 'en' | 'ne';

// Interface for language context
interface LanguageContextType {
  language: LanguageType;
  setLanguage: (language: LanguageType) => Promise<void>;
  availableLanguages: { code: LanguageType; name: string }[];
  reloadTranslations: () => void;
}

// Create language context
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Language provider props
interface LanguageProviderProps {
  children: ReactNode;
}

// Available languages
const availableLanguages = [
  { code: 'en' as LanguageType, name: 'English' },
  { code: 'ne' as LanguageType, name: 'नेपाली' },
];

// Language provider component
export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const { i18n } = useTranslation();
  const [language, setLanguageState] = useState<LanguageType>('en');

  // Load language preference on mount
  useEffect(() => {
    const loadLanguage = async () => {
      try {
        await loadLanguagePreference();
        // Force a re-render with the loaded language
        setLanguageState(i18n.language as LanguageType);
        console.log('Language loaded:', i18n.language);
      } catch (error) {
        console.error('Error loading language:', error);
      }
    };

    loadLanguage();
  }, []);

  // Set language and save preference
  const setLanguage = async (newLanguage: LanguageType) => {
    await saveLanguagePreference(newLanguage);
    setLanguageState(newLanguage);
  };

  // Force reload translations
  const reloadTranslations = () => {
    // This will trigger a re-render of components using translations
    const currentLang = i18n.language as LanguageType;
    i18n.reloadResources();

    // Log current translations for debugging
    console.log('Current translations:', i18n.store.data);

    // Toggle language and back to force refresh
    setLanguageState(currentLang === 'en' ? 'ne' : 'en');
    setTimeout(() => {
      setLanguageState(currentLang);
    }, 100);
  };

  // Force reload translations on mount
  useEffect(() => {
    reloadTranslations();
  }, []);

  // Context value
  const value = {
    language,
    setLanguage,
    availableLanguages,
    reloadTranslations,
  };

  return <LanguageContext.Provider value={value}>{children}</LanguageContext.Provider>;
};

// Hook to use language context
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export default LanguageContext;
