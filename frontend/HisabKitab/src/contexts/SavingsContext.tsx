import React, { createContext, useContext, useState, useEffect } from 'react';
import { SavingsGoal, SavingsContribution, CreateSavingsGoalRequest, UpdateSavingsGoalRequest, CreateContributionRequest, UpdateContributionRequest, SavingsGoalForecast } from '../models/SavingsGoal';
import { WishlistItem, CreateWishlistItemRequest, UpdateWishlistItemRequest } from '../models/WishlistItem';
import { useAuth } from './AuthContext';
import apiService from '../services/api';
import { showErrorMessage } from '../utils/errorHandling';
import { useTranslation } from 'react-i18next';
import { ensureSavingsGoalStatus, ensureWishlistItemStatus } from '../utils/typeConversion';

interface SavingsContextType {
  // Savings Goals
  savingsGoals: SavingsGoal[];
  isLoadingSavingsGoals: boolean;
  savingsGoalsError: string | null;
  loadSavingsGoals: () => Promise<void>;
  getSavingsGoal: (id: number) => SavingsGoal | undefined;
  createSavingsGoal: (goal: CreateSavingsGoalRequest) => Promise<SavingsGoal | null>;
  updateSavingsGoal: (goal: UpdateSavingsGoalRequest) => Promise<SavingsGoal | null>;
  deleteSavingsGoal: (id: number) => Promise<boolean>;

  // Contributions
  addContribution: (contribution: CreateContributionRequest) => Promise<SavingsContribution | null>;
  updateContribution: (contribution: UpdateContributionRequest) => Promise<SavingsContribution | null>;
  deleteContribution: (id: number) => Promise<boolean>;

  // Forecasting
  getSavingsGoalForecast: (goalId: number) => Promise<SavingsGoalForecast | null>;

  // Wishlist Items
  wishlistItems: WishlistItem[];
  isLoadingWishlistItems: boolean;
  wishlistItemsError: string | null;
  loadWishlistItems: () => Promise<void>;
  getWishlistItem: (id: number) => WishlistItem | undefined;
  createWishlistItem: (item: CreateWishlistItemRequest) => Promise<WishlistItem | null>;
  updateWishlistItem: (item: UpdateWishlistItemRequest) => Promise<WishlistItem | null>;
  deleteWishlistItem: (id: number) => Promise<boolean>;

  // Link/Unlink
  linkWishlistItemToGoal: (wishlistItemId: number, goalId: number) => Promise<boolean>;
  unlinkWishlistItemFromGoal: (wishlistItemId: number) => Promise<boolean>;
}

const SavingsContext = createContext<SavingsContextType | undefined>(undefined);

export const SavingsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [savingsGoals, setSavingsGoals] = useState<SavingsGoal[]>([]);
  const [isLoadingSavingsGoals, setIsLoadingSavingsGoals] = useState(true);
  const [savingsGoalsError, setSavingsGoalsError] = useState<string | null>(null);

  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);
  const [isLoadingWishlistItems, setIsLoadingWishlistItems] = useState(true);
  const [wishlistItemsError, setWishlistItemsError] = useState<string | null>(null);

  const { user, isAuthenticated } = useAuth();
  const { t } = useTranslation();

  // Load savings goals
  const loadSavingsGoals = async () => {
    if (!isAuthenticated || !user) {
      setSavingsGoals([]);
      setIsLoadingSavingsGoals(false);
      return;
    }

    setIsLoadingSavingsGoals(true);
    setSavingsGoalsError(null);

    try {
      const response = await apiService.savings.getAllSavingsGoals();

      if (response.status === 200 && response.data) {
        // Calculate additional fields
        const goalsWithCalculatedFields = response.data.map((goal: SavingsGoal) => {
          // Ensure status is the correct string value
          const status = ensureSavingsGoalStatus(goal.status);

          const progress = goal.targetAmount > 0 ? (goal.currentAmount / goal.targetAmount) * 100 : 0;
          const remainingAmount = goal.targetAmount - goal.currentAmount;

          // Calculate days remaining
          const targetDate = new Date(goal.targetDate);
          const today = new Date();
          const daysRemaining = Math.max(0, Math.ceil((targetDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)));

          // Determine if on track
          const elapsedDays = Math.ceil((today.getTime() - new Date(goal.startDate).getTime()) / (1000 * 60 * 60 * 24));
          const totalDays = Math.ceil((targetDate.getTime() - new Date(goal.startDate).getTime()) / (1000 * 60 * 60 * 24));
          const expectedProgress = totalDays > 0 ? (elapsedDays / totalDays) * 100 : 0;
          const isOnTrack = progress >= expectedProgress;

          return {
            ...goal,
            status,
            progress,
            remainingAmount,
            daysRemaining,
            isOnTrack
          } as SavingsGoal;
        });

        setSavingsGoals(goalsWithCalculatedFields);
      } else {
        setSavingsGoalsError(response.error || t('common.unexpectedError'));
      }
    } catch (error) {
      console.error('Error loading savings goals:', error);
      setSavingsGoalsError(t('common.networkError'));
    } finally {
      setIsLoadingSavingsGoals(false);
    }
  };

  // Load wishlist items
  const loadWishlistItems = async () => {
    if (!isAuthenticated || !user) {
      setWishlistItems([]);
      setIsLoadingWishlistItems(false);
      return;
    }

    setIsLoadingWishlistItems(true);
    setWishlistItemsError(null);

    try {
      const response = await apiService.savings.getAllWishlistItems();

      if (response.status === 200 && response.data) {
        // Ensure status is the correct string value for each wishlist item
        const itemsWithCorrectStatus = response.data.map((item: WishlistItem) => ({
          ...item,
          status: ensureWishlistItemStatus(item.status)
        } as WishlistItem));
        setWishlistItems(itemsWithCorrectStatus);
      } else {
        setWishlistItemsError(response.error || t('common.unexpectedError'));
      }
    } catch (error) {
      console.error('Error loading wishlist items:', error);
      setWishlistItemsError(t('common.networkError'));
    } finally {
      setIsLoadingWishlistItems(false);
    }
  };

  // Load data when authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      loadSavingsGoals();
      loadWishlistItems();
    }
  }, [isAuthenticated, user]);

  // Get a specific savings goal
  const getSavingsGoal = (id: number) => {
    return savingsGoals.find(goal => goal.id === id);
  };

  // Create a new savings goal
  const createSavingsGoal = async (goal: CreateSavingsGoalRequest) => {
    try {
      const response = await apiService.savings.createSavingsGoal(goal);

      if (response.status === 201 && response.data) {
        // Add calculated fields
        const newGoal: SavingsGoal = {
          ...response.data,
          progress: 0,
          remainingAmount: response.data.targetAmount,
          daysRemaining: Math.ceil((new Date(response.data.targetDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)),
          isOnTrack: true
        };

        setSavingsGoals(prev => [...prev, newGoal]);
        return newGoal;
      } else {
        showErrorMessage(response.error || t('savings.createGoalError'));
        return null;
      }
    } catch (error) {
      console.error('Error creating savings goal:', error);
      showErrorMessage(t('common.networkError'));
      return null;
    }
  };

  // Update an existing savings goal
  const updateSavingsGoal = async (goal: UpdateSavingsGoalRequest) => {
    try {
      const response = await apiService.savings.updateSavingsGoal(goal.id, goal);

      if (response.status === 200 && response.data) {
        // Calculate additional fields
        const updatedGoal: SavingsGoal = {
          ...response.data,
          progress: response.data.targetAmount > 0 ? (response.data.currentAmount / response.data.targetAmount) * 100 : 0,
          remainingAmount: response.data.targetAmount - response.data.currentAmount,
          daysRemaining: Math.ceil((new Date(response.data.targetDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)),
          isOnTrack: true // This would need more complex calculation
        };

        setSavingsGoals(prev => prev.map(g => g.id === goal.id ? updatedGoal : g));
        return updatedGoal;
      } else {
        showErrorMessage(response.error || t('savings.updateGoalError'));
        return null;
      }
    } catch (error) {
      console.error('Error updating savings goal:', error);
      showErrorMessage(t('common.networkError'));
      return null;
    }
  };

  // Delete a savings goal
  const deleteSavingsGoal = async (id: number) => {
    try {
      const response = await apiService.savings.deleteSavingsGoal(id);

      if (response.status === 200) {
        setSavingsGoals(prev => prev.filter(goal => goal.id !== id));
        return true;
      } else {
        showErrorMessage(response.error || t('savings.deleteGoalError'));
        return false;
      }
    } catch (error) {
      console.error('Error deleting savings goal:', error);
      showErrorMessage(t('common.networkError'));
      return false;
    }
  };

  // Add a contribution to a savings goal
  const addContribution = async (contribution: CreateContributionRequest) => {
    try {
      const response = await apiService.savings.addContribution(contribution);

      if (response.status === 201 && response.data) {
        // Update the savings goal with the new contribution
        await loadSavingsGoals();
        return response.data;
      } else {
        showErrorMessage(response.error || t('savings.addContributionError'));
        return null;
      }
    } catch (error) {
      console.error('Error adding contribution:', error);
      showErrorMessage(t('common.networkError'));
      return null;
    }
  };

  // Update a contribution
  const updateContribution = async (contribution: UpdateContributionRequest) => {
    try {
      const response = await apiService.savings.updateContribution(contribution.id, contribution);

      if (response.status === 200 && response.data) {
        // Update the savings goal with the updated contribution
        await loadSavingsGoals();
        return response.data;
      } else {
        showErrorMessage(response.error || t('savings.updateContributionError'));
        return null;
      }
    } catch (error) {
      console.error('Error updating contribution:', error);
      showErrorMessage(t('common.networkError'));
      return null;
    }
  };

  // Delete a contribution
  const deleteContribution = async (id: number) => {
    try {
      const response = await apiService.savings.deleteContribution(id);

      if (response.status === 200) {
        // Update the savings goal after deleting the contribution
        await loadSavingsGoals();
        return true;
      } else {
        showErrorMessage(response.error || t('savings.deleteContributionError'));
        return false;
      }
    } catch (error) {
      console.error('Error deleting contribution:', error);
      showErrorMessage(t('common.networkError'));
      return false;
    }
  };

  // Get forecast for a savings goal
  const getSavingsGoalForecast = async (goalId: number) => {
    try {
      const response = await apiService.savings.getSavingsGoalForecast(goalId);

      if (response.status === 200 && response.data) {
        return response.data;
      } else {
        showErrorMessage(response.error || t('savings.forecastError'));
        return null;
      }
    } catch (error) {
      console.error('Error getting savings goal forecast:', error);
      showErrorMessage(t('common.networkError'));
      return null;
    }
  };

  // Get a specific wishlist item
  const getWishlistItem = (id: number) => {
    return wishlistItems.find(item => item.id === id);
  };

  // Create a new wishlist item
  const createWishlistItem = async (item: CreateWishlistItemRequest) => {
    try {
      const response = await apiService.savings.createWishlistItem(item);

      if (response.status === 201 && response.data) {
        setWishlistItems(prev => [...prev, response.data as WishlistItem]);
        return response.data;
      } else {
        showErrorMessage(response.error || t('wishlist.createItemError'));
        return null;
      }
    } catch (error) {
      console.error('Error creating wishlist item:', error);
      showErrorMessage(t('common.networkError'));
      return null;
    }
  };

  // Update an existing wishlist item
  const updateWishlistItem = async (item: UpdateWishlistItemRequest) => {
    try {
      const response = await apiService.savings.updateWishlistItem(item.id, item);

      if (response.status === 200 && response.data) {
        setWishlistItems(prev => prev.map(i => i.id === item.id ? (response.data as WishlistItem) : i));
        return response.data;
      } else {
        showErrorMessage(response.error || t('wishlist.updateItemError'));
        return null;
      }
    } catch (error) {
      console.error('Error updating wishlist item:', error);
      showErrorMessage(t('common.networkError'));
      return null;
    }
  };

  // Delete a wishlist item
  const deleteWishlistItem = async (id: number) => {
    try {
      const response = await apiService.savings.deleteWishlistItem(id);

      if (response.status === 200) {
        setWishlistItems(prev => prev.filter(item => item.id !== id));
        return true;
      } else {
        showErrorMessage(response.error || t('wishlist.deleteItemError'));
        return false;
      }
    } catch (error) {
      console.error('Error deleting wishlist item:', error);
      showErrorMessage(t('common.networkError'));
      return false;
    }
  };

  // Link a wishlist item to a savings goal
  const linkWishlistItemToGoal = async (wishlistItemId: number, goalId: number) => {
    try {
      const response = await apiService.savings.linkWishlistItemToGoal(wishlistItemId, goalId);

      if (response.status === 200) {
        // Reload both savings goals and wishlist items to reflect the changes
        await Promise.all([loadSavingsGoals(), loadWishlistItems()]);
        return true;
      } else {
        showErrorMessage(response.error || t('wishlist.linkError'));
        return false;
      }
    } catch (error) {
      console.error('Error linking wishlist item to goal:', error);
      showErrorMessage(t('common.networkError'));
      return false;
    }
  };

  // Unlink a wishlist item from a savings goal
  const unlinkWishlistItemFromGoal = async (wishlistItemId: number) => {
    try {
      const response = await apiService.savings.unlinkWishlistItemFromGoal(wishlistItemId);

      if (response.status === 200) {
        // Reload both savings goals and wishlist items to reflect the changes
        await Promise.all([loadSavingsGoals(), loadWishlistItems()]);
        return true;
      } else {
        showErrorMessage(response.error || t('wishlist.unlinkError'));
        return false;
      }
    } catch (error) {
      console.error('Error unlinking wishlist item from goal:', error);
      showErrorMessage(t('common.networkError'));
      return false;
    }
  };

  const value = {
    // Savings Goals
    savingsGoals,
    isLoadingSavingsGoals,
    savingsGoalsError,
    loadSavingsGoals,
    getSavingsGoal,
    createSavingsGoal,
    updateSavingsGoal,
    deleteSavingsGoal,

    // Contributions
    addContribution,
    updateContribution,
    deleteContribution,

    // Forecasting
    getSavingsGoalForecast,

    // Wishlist Items
    wishlistItems,
    isLoadingWishlistItems,
    wishlistItemsError,
    loadWishlistItems,
    getWishlistItem,
    createWishlistItem,
    updateWishlistItem,
    deleteWishlistItem,

    // Link/Unlink
    linkWishlistItemToGoal,
    unlinkWishlistItemFromGoal,
  };

  return <SavingsContext.Provider value={value}>{children}</SavingsContext.Provider>;
};

// Hook to use savings context
export const useSavings = () => {
  const context = useContext(SavingsContext);
  if (context === undefined) {
    throw new Error('useSavings must be used within a SavingsProvider');
  }
  return context;
};

export default SavingsContext;
