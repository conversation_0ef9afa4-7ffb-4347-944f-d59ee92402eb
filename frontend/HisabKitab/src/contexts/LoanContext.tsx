import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import apiService from '../services/api';
import { useAuth } from './AuthContext';
import { Loan, LoanPayment, LoanReminder, InterestType, FrequencyType } from '../models/Loan';

// Interface for loan context
interface LoanContextType {
  loans: Loan[];
  isLoading: boolean;
  error: string | null;
  refreshLoans: () => Promise<void>;
  getLoanById: (id: number) => Loan | undefined;
  createLoan: (loanData: any) => Promise<Loan | null>;
  updateLoan: (loanData: any) => Promise<Loan | null>;
  deleteLoan: (id: number) => Promise<boolean>;
  getLoanTimeline: (id: number) => Promise<any>;
  calculateEMI: (calculationData: any) => Promise<number | null>;

  // Payment operations
  getPaymentsByLoanId: (loanId: number) => Promise<LoanPayment[]>;
  getPaymentById: (id: number) => Promise<LoanPayment | null>;
  createPayment: (paymentData: any) => Promise<LoanPayment | null>;
  updatePayment: (paymentData: any) => Promise<LoanPayment | null>;
  deletePayment: (id: number) => Promise<boolean>;

  // Reminder operations
  getRemindersByLoanId: (loanId: number) => Promise<LoanReminder[]>;
  getReminderById: (id: number) => Promise<LoanReminder | null>;
  getActiveReminders: () => Promise<LoanReminder[]>;
  createReminder: (reminderData: any) => Promise<LoanReminder | null>;
  updateReminder: (reminderData: any) => Promise<LoanReminder | null>;
  deleteReminder: (id: number) => Promise<boolean>;
  markReminderAsSent: (id: number) => Promise<boolean>;

  // Helper functions
  getInterestTypeOptions: () => { label: string; value: InterestType }[];
  getFrequencyTypeOptions: () => { label: string; value: FrequencyType }[];
  formatLoanStatus: (status: string) => string;
}

// Create loan context
const LoanContext = createContext<LoanContextType | undefined>(undefined);

// Loan provider props
interface LoanProviderProps {
  children: ReactNode;
}

// Loan provider component
export const LoanProvider: React.FC<LoanProviderProps> = ({ children }) => {
  const [loans, setLoans] = useState<Loan[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated } = useAuth();
  const { t } = useTranslation();

  // Load loans on mount and when auth state changes
  useEffect(() => {
    if (isAuthenticated) {
      refreshLoans();
    } else {
      setLoans([]);
    }
  }, [isAuthenticated]);

  // Refresh loans
  const refreshLoans = async () => {
    if (!isAuthenticated) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.getLoans();

      if (response.data) {
        setLoans(response.data);
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Get loan by ID
  const getLoanById = (id: number) => {
    return loans.find(loan => loan.id === id);
  };

  // Create loan
  const createLoan = async (loanData: any) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.createLoan(loanData);

      if (response.data) {
        // Refresh loans to get the updated list
        await refreshLoans();
        return response.data;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Update loan
  const updateLoan = async (loanData: any) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.updateLoan(loanData.id, loanData);

      if (response.data) {
        // Refresh loans to get the updated list
        await refreshLoans();
        return response.data;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Delete loan
  const deleteLoan = async (id: number) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.deleteLoan(id);

      if (response.status === 204) {
        // Refresh loans to get the updated list
        await refreshLoans();
        return true;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return false;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Get loan timeline
  const getLoanTimeline = async (id: number) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.getLoanTimeline(id);

      if (response.data) {
        return response.data;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate EMI
  const calculateEMI = async (calculationData: any) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.calculateEMI(calculationData);

      if (response.data) {
        return response.data.emi;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Get payments by loan ID
  const getPaymentsByLoanId = async (loanId: number) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.getPayments(loanId);

      if (response.data) {
        return response.data;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return [];
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  // Get payment by ID
  const getPaymentById = async (id: number) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.getPayment(id);

      if (response.data) {
        return response.data;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Create payment
  const createPayment = async (paymentData: any) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.createPayment(paymentData);

      if (response.data) {
        // Refresh loans to get the updated list
        await refreshLoans();
        return response.data;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Update payment
  const updatePayment = async (paymentData: any) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.updatePayment(paymentData.id, paymentData);

      if (response.data) {
        // Refresh loans to get the updated list
        await refreshLoans();
        return response.data;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Delete payment
  const deletePayment = async (id: number) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.deletePayment(id);

      if (response.status === 204) {
        // Refresh loans to get the updated list
        await refreshLoans();
        return true;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return false;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Get reminders by loan ID
  const getRemindersByLoanId = async (loanId: number) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.getReminders(loanId);

      if (response.data) {
        return response.data;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return [];
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  // Get reminder by ID
  const getReminderById = async (id: number) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.getReminder(id);

      if (response.data) {
        return response.data;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Get active reminders
  const getActiveReminders = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.getActiveReminders();

      if (response.data) {
        return response.data;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return [];
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  // Create reminder
  const createReminder = async (reminderData: any) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.createReminder(reminderData);

      if (response.data) {
        return response.data;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Update reminder
  const updateReminder = async (reminderData: any) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.updateReminder(reminderData.id, reminderData);

      if (response.data) {
        return response.data;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Delete reminder
  const deleteReminder = async (id: number) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.deleteReminder(id);

      if (response.status === 204) {
        return true;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return false;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Mark reminder as sent
  const markReminderAsSent = async (id: number) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.loans.markReminderAsSent(id);

      if (response.status === 200) {
        return true;
      } else if (response.error) {
        setError(response.error);
        Alert.alert(t('common.error'), response.error);
      }

      return false;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      Alert.alert(t('common.error'), errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Helper functions
  const getInterestTypeOptions = () => {
    return [
      { label: t('loans.interestTypes.flat'), value: InterestType.Flat },
      { label: t('loans.interestTypes.reducingBalance'), value: InterestType.ReducingBalance },
      { label: t('loans.interestTypes.compound'), value: InterestType.Compound },
    ];
  };

  const getFrequencyTypeOptions = () => {
    return [
      { label: t('loans.frequencyTypes.daily'), value: FrequencyType.Daily },
      { label: t('loans.frequencyTypes.weekly'), value: FrequencyType.Weekly },
      { label: t('loans.frequencyTypes.monthly'), value: FrequencyType.Monthly },
      { label: t('loans.frequencyTypes.quarterly'), value: FrequencyType.Quarterly },
      { label: t('loans.frequencyTypes.yearly'), value: FrequencyType.Yearly },
    ];
  };

  const formatLoanStatus = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return t('loans.status.active');
      case 'completed':
        return t('loans.status.completed');
      case 'defaulted':
        return t('loans.status.defaulted');
      default:
        return status;
    }
  };

  // Context value
  const value = {
    loans,
    isLoading,
    error,
    refreshLoans,
    getLoanById,
    createLoan,
    updateLoan,
    deleteLoan,
    getLoanTimeline,
    calculateEMI,
    getPaymentsByLoanId,
    getPaymentById,
    createPayment,
    updatePayment,
    deletePayment,
    getRemindersByLoanId,
    getReminderById,
    getActiveReminders,
    createReminder,
    updateReminder,
    deleteReminder,
    markReminderAsSent,
    getInterestTypeOptions,
    getFrequencyTypeOptions,
    formatLoanStatus,
  };

  return <LoanContext.Provider value={value}>{children}</LoanContext.Provider>;
};

// Hook to use loan context
export const useLoans = () => {
  const context = useContext(LoanContext);
  if (context === undefined) {
    throw new Error('useLoans must be used within a LoanProvider');
  }
  return context;
};

export default LoanContext;
