import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Alert, Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import { createWorker, Worker, RecognizeResult } from 'tesseract.js';
import { useTranslation } from 'react-i18next';
import { useTheme } from './ThemeContext';
import apiService from '../services/api';
import { ReceiptData } from '../models/Receipt';
import NetInfo from '@react-native-community/netinfo';

// Define the OCR language type
export type OCRLanguage = 'eng' | 'nep';

// Define the OCR language status
interface OCRLanguageStatus {
  isDownloaded: boolean;
  isDownloading: boolean;
  downloadProgress: number;
}

// Define the OCR context type
interface OCRContextType {
  isProcessing: boolean;
  progress: number;
  recognizedText: string;
  extractedData: ReceiptData | null;
  languageStatus: Record<OCRLanguage, OCRLanguageStatus>;
  downloadLanguage: (language: OCRLanguage) => Promise<boolean>;
  processImage: (imageUri: string, language?: OCRLanguage) => Promise<ReceiptData | null>;
  processImageOnServer: (imageUri: string, language?: OCRLanguage) => Promise<ReceiptData | null>;
  cancelProcessing: () => void;
  clearResults: () => void;
  isNetworkConnected: boolean;
}

// Create the OCR context
const OCRContext = createContext<OCRContextType | undefined>(undefined);

// OCR Provider props
interface OCRProviderProps {
  children: ReactNode;
}

// OCR Provider component
export const OCRProvider: React.FC<OCRProviderProps> = ({ children }) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  // State
  const [worker, setWorker] = useState<Worker | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [recognizedText, setRecognizedText] = useState('');
  const [extractedData, setExtractedData] = useState<ReceiptData | null>(null);
  const [isNetworkConnected, setIsNetworkConnected] = useState(true);
  const [languageStatus, setLanguageStatus] = useState<Record<OCRLanguage, OCRLanguageStatus>>({
    eng: { isDownloaded: false, isDownloading: false, downloadProgress: 0 },
    nep: { isDownloaded: false, isDownloading: false, downloadProgress: 0 }
  });

  // Check if language data exists
  const checkLanguageData = async (language: OCRLanguage): Promise<boolean> => {
    if (Platform.OS === 'web') {
      // On web, Tesseract.js handles language downloads automatically
      return true;
    }

    try {
      // Check if language data exists in app documents directory
      const langDataPath = `${FileSystem.documentDirectory}tesseract/tessdata/${language}.traineddata`;
      const info = await FileSystem.getInfoAsync(langDataPath);

      // Update language status
      setLanguageStatus(prev => ({
        ...prev,
        [language]: {
          ...prev[language],
          isDownloaded: info.exists
        }
      }));

      return info.exists;
    } catch (error) {
      console.error(`Error checking language data for ${language}:`, error);
      return false;
    }
  };

  // Monitor network status
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsNetworkConnected(state.isConnected || false);
    });

    // Initial check
    NetInfo.fetch().then(state => {
      setIsNetworkConnected(state.isConnected || false);
    });

    // Check language data
    checkLanguageData('eng');
    checkLanguageData('nep');

    return () => {
      unsubscribe();
    };
  }, []);

  // Initialize worker
  useEffect(() => {
    return () => {
      // Clean up worker when component unmounts
      if (worker) {
        worker.terminate();
      }
    };
  }, [worker]);

  // Download language data
  const downloadLanguage = async (language: OCRLanguage): Promise<boolean> => {
    if (Platform.OS === 'web') {
      // On web, Tesseract.js handles language downloads automatically
      return true;
    }

    // Check if already downloaded
    const isDownloaded = await checkLanguageData(language);
    if (isDownloaded) {
      return true;
    }

    // Check network connection
    if (!isNetworkConnected) {
      Alert.alert(
        t('common.error'),
        t('receipts.networkRequired')
      );
      return false;
    }

    try {
      // Update language status
      setLanguageStatus(prev => ({
        ...prev,
        [language]: {
          ...prev[language],
          isDownloading: true,
          downloadProgress: 0
        }
      }));

      // Create directories if they don't exist
      const tessDataDir = `${FileSystem.documentDirectory}tesseract/tessdata`;
      await FileSystem.makeDirectoryAsync(tessDataDir, { intermediates: true });

      // Download language data
      const langDataUrl = `https://github.com/tesseract-ocr/tessdata_best/raw/main/${language}.traineddata`;
      const langDataPath = `${tessDataDir}/${language}.traineddata`;

      const downloadResumable = FileSystem.createDownloadResumable(
        langDataUrl,
        langDataPath,
        {},
        (downloadProgress) => {
          const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
          setLanguageStatus(prev => ({
            ...prev,
            [language]: {
              ...prev[language],
              downloadProgress: progress
            }
          }));
        }
      );

      const result = await downloadResumable.downloadAsync();

      if (result && result.status === 200) {
        // Update language status
        setLanguageStatus(prev => ({
          ...prev,
          [language]: {
            isDownloaded: true,
            isDownloading: false,
            downloadProgress: 1
          }
        }));

        return true;
      } else {
        throw new Error('Download failed');
      }
    } catch (error) {
      console.error(`Error downloading language data for ${language}:`, error);

      // Update language status
      setLanguageStatus(prev => ({
        ...prev,
        [language]: {
          ...prev[language],
          isDownloading: false
        }
      }));

      Alert.alert(
        t('common.error'),
        t('receipts.languageDownloadError')
      );

      return false;
    }
  };

  // Process image with Tesseract.js
  const processImage = async (imageUri: string, language: OCRLanguage = 'eng'): Promise<ReceiptData | null> => {
    try {
      // Check if language data is available
      const isLanguageAvailable = await checkLanguageData(language);

      if (!isLanguageAvailable) {
        // Try to download language data
        const downloadSuccess = await downloadLanguage(language);

        if (!downloadSuccess) {
          Alert.alert(
            t('common.error'),
            t('receipts.languageNotAvailable')
          );
          return null;
        }
      }

      setIsProcessing(true);
      setProgress(0);
      setRecognizedText('');
      setExtractedData(null);

      // Create a new worker if needed
      let currentWorker = worker;
      if (!currentWorker) {
        const newWorker = await createWorker(language, undefined, {
          logger: (progress: any) => {
            if (progress.status === 'recognizing text') {
              setProgress(progress.progress);
            }
          },
        });

        // Initialize worker
        await newWorker.reinitialize(language);
        setWorker(newWorker);
        currentWorker = newWorker;
      }

      // Recognize text
      const result = await currentWorker.recognize(imageUri);
      setRecognizedText(result.data.text);

      // Parse the recognized text
      const parsedData = parseReceiptText(result.data.text);
      setExtractedData(parsedData);

      setIsProcessing(false);
      return parsedData;
    } catch (error) {
      console.error('Error processing image with OCR:', error);
      Alert.alert(
        t('common.error'),
        t('receipts.ocrError')
      );
      setIsProcessing(false);
      return null;
    }
  };

  // Process image on server
  const processImageOnServer = async (imageUri: string, language: OCRLanguage = 'eng'): Promise<ReceiptData | null> => {
    try {
      // Check network connection
      if (!isNetworkConnected) {
        Alert.alert(
          t('common.error'),
          t('receipts.networkRequired')
        );
        return null;
      }

      setIsProcessing(true);
      setProgress(0);
      setRecognizedText('');
      setExtractedData(null);

      // Convert image to base64
      const base64 = await FileSystem.readAsStringAsync(imageUri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Send to server for processing
      const response = await apiService.receipts.processReceipt({
        imageBase64: base64,
        language,
      });

      if (response.status === 200 && response.data) {
        // The server response should include the raw OCR text
        // If it doesn't exist in the response, use an empty string
        const rawText = response.data.rawText || '';
        setRecognizedText(rawText);
        setExtractedData(response.data);
        setIsProcessing(false);
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to process receipt on server');
      }
    } catch (error) {
      console.error('Error processing image on server:', error);
      Alert.alert(
        t('common.error'),
        t('receipts.serverOcrError')
      );
      setIsProcessing(false);
      return null;
    }
  };

  // Parse receipt text to extract structured data
  const parseReceiptText = (text: string): ReceiptData => {
    // Simple parsing logic - this would be more sophisticated in a real app
    const lines = text.split('\n').filter(line => line.trim() !== '');

    // Try to find total amount
    const amountRegex = /total:?\s*(?:rs\.?|npr\.?|₹)?\s*(\d+(?:[.,]\d+)?)/i;
    const amountMatch = text.match(amountRegex);
    const totalAmount = amountMatch ? parseFloat(amountMatch[1].replace(',', '.')) : 0;

    // Try to find date
    const dateRegex = /date:?\s*(\d{1,2}[-/\.]\d{1,2}[-/\.]\d{2,4})/i;
    const dateMatch = text.match(dateRegex);
    const dateStr = dateMatch ? dateMatch[1] : '';

    // Try to find merchant name (usually in the first few lines)
    const merchantName = lines.length > 0 ? lines[0] : '';

    // Extract potential items
    const items: { name: string; amount: number }[] = [];
    const itemRegex = /(.+?)\s+(?:rs\.?|npr\.?|₹)?\s*(\d+(?:[.,]\d+)?)\s*$/i;

    for (let i = 1; i < lines.length - 1; i++) {
      const itemMatch = lines[i].match(itemRegex);
      if (itemMatch) {
        items.push({
          name: itemMatch[1].trim(),
          amount: parseFloat(itemMatch[2].replace(',', '.')),
        });
      }
    }

    return {
      merchantName,
      date: dateStr,
      totalAmount,
      items,
      rawText: text,
    };
  };

  // Cancel processing
  const cancelProcessing = () => {
    if (worker) {
      worker.terminate();
      setWorker(null);
    }
    setIsProcessing(false);
  };

  // Clear results
  const clearResults = () => {
    setRecognizedText('');
    setExtractedData(null);
  };

  // Context value
  const contextValue: OCRContextType = {
    isProcessing,
    progress,
    recognizedText,
    extractedData,
    languageStatus,
    downloadLanguage,
    processImage,
    processImageOnServer,
    cancelProcessing,
    clearResults,
    isNetworkConnected,
  };

  return (
    <OCRContext.Provider value={contextValue}>
      {children}
    </OCRContext.Provider>
  );
};

// Custom hook to use the OCR context
export const useOCR = (): OCRContextType => {
  const context = useContext(OCRContext);
  if (context === undefined) {
    throw new Error('useOCR must be used within an OCRProvider');
  }
  return context;
};
