import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import NetInfo from '@react-native-community/netinfo';
import dbService from '../services/db';
import apiService from '../services/api';
import { useAuth } from './AuthContext';
import { SyncQueue } from '../services/db';

// Define the sync status type
export type SyncStatus = 'idle' | 'syncing' | 'error' | 'success' | 'offline';

// Define the conflict type
export interface SyncConflict {
  id: number;
  entityType: string;
  entityId: number;
  localData: any;
  remoteData: any;
  resolved: boolean;
  resolution?: 'local' | 'remote' | 'merge';
  createdAt: Date;
}

// Define the sync context type
interface SyncContextType {
  syncStatus: SyncStatus;
  lastSyncTime: Date | null;
  pendingChanges: number;
  conflicts: SyncConflict[];
  isOnline: boolean;
  syncNow: () => Promise<boolean>;
  resolveConflict: (conflictId: number, resolution: 'local' | 'remote' | 'merge', mergedData?: any) => Promise<boolean>;
  addToSyncQueue: (item: Omit<SyncQueue, 'id'>) => Promise<number>;
  clearSyncQueue: () => Promise<void>;
}

// Create the sync context
const SyncContext = createContext<SyncContextType | undefined>(undefined);

// Sync Provider props
interface SyncProviderProps {
  children: ReactNode;
  syncInterval?: number; // in milliseconds, default 5 minutes
}

// Sync Provider component
export const SyncProvider: React.FC<SyncProviderProps> = ({
  children,
  syncInterval = 5 * 60 * 1000 // 5 minutes
}) => {
  const { t } = useTranslation();
  const { isAuthenticated } = useAuth();

  // State
  const [syncStatus, setSyncStatus] = useState<SyncStatus>('idle');
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  const [pendingChanges, setPendingChanges] = useState<number>(0);
  const [conflicts, setConflicts] = useState<SyncConflict[]>([]);
  const [isOnline, setIsOnline] = useState<boolean>(true);
  const [syncTimer, setSyncTimer] = useState<NodeJS.Timeout | null>(null);

  // Check network status
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsOnline(state.isConnected || false);

      // If we just came online and have pending changes, sync
      if (state.isConnected && pendingChanges > 0) {
        syncNow();
      }
    });

    // Initial check
    NetInfo.fetch().then(state => {
      setIsOnline(state.isConnected || false);
    });

    return () => {
      unsubscribe();
    };
  }, [pendingChanges]);

  // Load pending changes count
  useEffect(() => {
    if (isAuthenticated) {
      loadPendingChanges();
      loadLastSyncTime();
      loadConflicts();
    }
  }, [isAuthenticated]);

  // Set up sync timer
  useEffect(() => {
    if (isAuthenticated && isOnline) {
      // Use window.setInterval to get the correct return type
      const timer = window.setInterval(() => {
        if (pendingChanges > 0) {
          syncNow();
        }
      }, syncInterval);

      // Store the timer ID
      setSyncTimer(timer as unknown as NodeJS.Timeout);

      return () => {
        if (timer) window.clearInterval(timer);
      };
    } else if (syncTimer) {
      clearInterval(Number(syncTimer));
      setSyncTimer(null);
    }
  }, [isAuthenticated, isOnline, pendingChanges, syncInterval]);

  // Load pending changes count
  const loadPendingChanges = async () => {
    try {
      const queue = await dbService.getSyncQueue();
      setPendingChanges(queue.length);
    } catch (error) {
      console.error('Error loading pending changes:', error);
    }
  };

  // Load last sync time
  const loadLastSyncTime = async () => {
    try {
      const time = await dbService.getLastSyncTime();
      if (time) {
        setLastSyncTime(new Date(time));
      }
    } catch (error) {
      console.error('Error loading last sync time:', error);
    }
  };

  // Load conflicts
  const loadConflicts = async () => {
    try {
      const conflictList = await dbService.getConflicts();
      setConflicts(conflictList);
    } catch (error) {
      console.error('Error loading conflicts:', error);
    }
  };

  // Sync now
  const syncNow = async (): Promise<boolean> => {
    if (!isAuthenticated || !isOnline) {
      setSyncStatus(isOnline ? 'idle' : 'offline');
      return false;
    }

    try {
      setSyncStatus('syncing');

      // Get sync queue
      let queue = await dbService.getSyncQueue();

      if (queue.length === 0) {
        setSyncStatus('success');
        setLastSyncTime(new Date());
        await dbService.setLastSyncTime(new Date());
        return true;
      }

      // Prioritize the queue
      queue = prioritizeSyncQueue(queue);

      // Process queue in batches
      const batchSize = 10;
      const batches = [];

      for (let i = 0; i < queue.length; i += batchSize) {
        batches.push(queue.slice(i, i + batchSize));
      }

      let success = true;

      for (const batch of batches) {
        const result = await processSyncBatch(batch);
        if (!result) {
          success = false;
          break;
        }
      }

      // Update state
      await loadPendingChanges();
      await loadConflicts();

      setSyncStatus(success ? 'success' : 'error');

      if (success) {
        setLastSyncTime(new Date());
        await dbService.setLastSyncTime(new Date());
      }

      return success;
    } catch (error) {
      console.error('Error syncing:', error);
      setSyncStatus('error');
      return false;
    }
  };

  // Prioritize sync queue items
  const prioritizeSyncQueue = (queue: SyncQueue[]): SyncQueue[] => {
    // Define priority order for entity types
    const entityTypePriority: Record<string, number> = {
      'Transaction': 1,
      'Account': 2,
      'Category': 3,
      'Family': 4,
      'User': 5,
      'Loan': 6,
      'SavingsGoal': 7,
      'WishlistItem': 8,
      'Receipt': 9,
      'BudgetLimit': 10,
      'Notification': 11,
      'FeatureFlag': 12
    };

    // Define priority order for actions
    const actionPriority: Record<string, number> = {
      'Create': 1,
      'Update': 2,
      'Delete': 3
    };

    // Sort queue by priority
    return [...queue].sort((a, b) => {
      // First, sort by entity type priority
      const entityTypeA = entityTypePriority[a.entityType] || 999;
      const entityTypeB = entityTypePriority[b.entityType] || 999;

      if (entityTypeA !== entityTypeB) {
        return entityTypeA - entityTypeB;
      }

      // Then, sort by action priority
      const actionA = actionPriority[a.action] || 999;
      const actionB = actionPriority[b.action] || 999;

      if (actionA !== actionB) {
        return actionA - actionB;
      }

      // Finally, sort by creation time (oldest first)
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
    });
  };

  // Process sync batch
  const processSyncBatch = async (batch: SyncQueue[]): Promise<boolean> => {
    try {
      // Send batch to server
      const response = await apiService.sync.syncBatch(batch);

      if (response.status === 200 && response.data) {
        // Process server response
        const { processed, conflicts: serverConflicts } = response.data;

        // Remove processed items from queue
        for (const item of processed) {
          await dbService.removeSyncQueueItem(item.id);
        }

        // Handle conflicts
        if (serverConflicts && serverConflicts.length > 0) {
          for (const conflict of serverConflicts) {
            await dbService.saveConflict({
              entityType: conflict.entityType,
              entityId: conflict.entityId,
              localData: conflict.localData,
              remoteData: conflict.remoteData,
              resolved: false,
              createdAt: new Date(),
            });
          }

          // Show conflict notification
          Alert.alert(
            t('sync.conflictsDetected'),
            t('sync.conflictsMessage'),
            [
              {
                text: t('sync.viewConflicts'),
                onPress: () => {
                  // Navigate to conflicts screen
                  // This would be implemented in the app navigation
                },
              },
              {
                text: t('common.later'),
                style: 'cancel',
              },
            ]
          );
        }

        return true;
      } else {
        throw new Error(response.error || 'Sync failed');
      }
    } catch (error: unknown) {
      console.error('Error processing sync batch:', error);

      // Mark items as failed
      for (const item of batch) {
        // Ensure item.id is a number
        const itemId = item.id !== undefined ? item.id : -1;

        await dbService.updateSyncQueueItem(itemId, {
          status: 'Failed',
          retryCount: (item.retryCount || 0) + 1,
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        });
      }

      return false;
    }
  };

  // Resolve conflict
  const resolveConflict = async (
    conflictId: number,
    resolution: 'local' | 'remote' | 'merge',
    mergedData?: any
  ): Promise<boolean> => {
    try {
      const conflict = conflicts.find(c => c.id === conflictId);

      if (!conflict) {
        return false;
      }

      // Apply resolution
      if (resolution === 'local') {
        // Keep local data
        await dbService.resolveConflict(conflictId, resolution);

        // Add to sync queue to push local data to server
        await dbService.addToSyncQueue({
          entityType: conflict.entityType,
          entityId: conflict.entityId,
          action: 'Update',
          entityData: JSON.stringify(conflict.localData),
          status: 'Pending',
          retryCount: 0,
          createdAt: new Date(),
        });
      } else if (resolution === 'remote') {
        // Use remote data
        await dbService.resolveConflict(conflictId, resolution);

        // Update local data with remote data
        switch (conflict.entityType) {
          case 'Transaction':
            await dbService.saveTransaction(conflict.remoteData);
            break;
          case 'Account':
            await dbService.saveAccount(conflict.remoteData);
            break;
          case 'Category':
            await dbService.saveCategory(conflict.remoteData);
            break;
          case 'Receipt':
            await dbService.saveReceipt(conflict.remoteData);
            break;
          // Add other entity types as needed
        }
      } else if (resolution === 'merge' && mergedData) {
        // Use merged data
        await dbService.resolveConflict(conflictId, resolution);

        // Update local data with merged data
        switch (conflict.entityType) {
          case 'Transaction':
            await dbService.saveTransaction(mergedData);
            break;
          case 'Account':
            await dbService.saveAccount(mergedData);
            break;
          case 'Category':
            await dbService.saveCategory(mergedData);
            break;
          case 'Receipt':
            await dbService.saveReceipt(mergedData);
            break;
          // Add other entity types as needed
        }

        // Add to sync queue to push merged data to server
        await dbService.addToSyncQueue({
          entityType: conflict.entityType,
          entityId: conflict.entityId,
          action: 'Update',
          entityData: JSON.stringify(mergedData),
          status: 'Pending',
          retryCount: 0,
          createdAt: new Date(),
        });
      }

      // Refresh conflicts list
      await loadConflicts();
      await loadPendingChanges();

      return true;
    } catch (error) {
      console.error('Error resolving conflict:', error);
      return false;
    }
  };

  // Add to sync queue
  const addToSyncQueue = async (item: Omit<SyncQueue, 'id'>): Promise<number> => {
    try {
      const id = await dbService.addToSyncQueue(item);
      await loadPendingChanges();
      return id;
    } catch (error) {
      console.error('Error adding to sync queue:', error);
      return -1;
    }
  };

  // Clear sync queue
  const clearSyncQueue = async (): Promise<void> => {
    try {
      await dbService.clearSyncQueue();
      await loadPendingChanges();
    } catch (error) {
      console.error('Error clearing sync queue:', error);
    }
  };

  // Context value
  const contextValue: SyncContextType = {
    syncStatus,
    lastSyncTime,
    pendingChanges,
    conflicts,
    isOnline,
    syncNow,
    resolveConflict,
    addToSyncQueue,
    clearSyncQueue,
  };

  return (
    <SyncContext.Provider value={contextValue}>
      {children}
    </SyncContext.Provider>
  );
};

// Custom hook to use the sync context
export const useSync = (): SyncContextType => {
  const context = useContext(SyncContext);
  if (context === undefined) {
    throw new Error('useSync must be used within a SyncProvider');
  }
  return context;
};
