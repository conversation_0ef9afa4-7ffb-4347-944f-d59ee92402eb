import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';
import apiService from '../services/api';
import { useAuth } from './AuthContext';

// Mock Family interface and hook
interface Family {
  id: number;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  members?: any[];
}

// Mock useFamily hook
const useFamily = () => {
  return {
    families: [],
    selectedFamily: null as Family | null,
    loading: false,
    error: null,
    isFamilyAdmin: false,
    fetchFamilies: async () => {},
    fetchFamily: async () => {},
    createFamily: async () => false,
    updateFamily: async () => false,
    deleteFamily: async () => false,
    setSelectedFamily: () => {},
    inviteMember: async () => false,
    removeMember: async () => false,
    leaveFamily: async () => false,
    changeMemberRole: async () => false
  };
};

// Mock useToast hook
interface ToastOptions {
  type?: 'success' | 'danger' | 'warning' | 'info';
  duration?: number;
  animationType?: string;
  animationDuration?: number;
  position?: string;
}

interface ToastInterface {
  show: (message: string, options?: ToastOptions) => void;
  hide: (id?: string) => void;
  hideAll: () => void;
}

const useToast = (): ToastInterface => {
  return {
    show: (_message: string, _options?: ToastOptions) => {},
    hide: (_id?: string) => {},
    hideAll: () => {}
  };
};

// Define types for analytics data
export interface MetricsData {
  totalIncome: number;
  totalExpense: number;
  netSavings: number;
  savingsRate: number;
  topExpenseCategories: { categoryId: number, categoryName: string, amount: number }[];
  topIncomeCategories: { categoryId: number, categoryName: string, amount: number }[];
}

export interface CategoryBreakdown {
  categoryId: number;
  categoryName: string;
  amount: number;
  percentage: number;
  type: string;
}

export interface TrendData {
  date: string;
  value: number;
}

export interface NetWorthData {
  totalAssets: number;
  totalLiabilities: number;
  netWorth: number;
  assetBreakdown: { accountId: number, accountName: string, balance: number }[];
  liabilityBreakdown: { accountId: number, accountName: string, balance: number }[];
}

export interface CashflowData {
  totalIncome: number;
  totalExpense: number;
  netCashflow: number;
  monthlyData: { month: string, income: number, expense: number, net: number }[];
}

export interface AnomalyData {
  id: number;
  transactionId: number;
  description: string;
  amount: number;
  date: string;
  status: string;
  resolutionNotes?: string;
}

// Define monthly summary interface
export interface MonthlySummaryData {
  totalIncome: number;
  totalExpense: number;
  netSavings: number;
  savingsRate: number;
  transactionCount: number;
  averageExpense: number;
  largestExpense: number;
  topExpenseCategory: string;
  topIncomeSource: string;
  expensesByCategory: { category: string; amount: number }[];
  dailyTrend: { date: string; income: number; expense: number }[];
}

// Define the context interface
interface AnalyticsContextType {
  // State
  dateRange: { startDate: string; endDate: string };
  period: string;
  selectedFamilyId: number | null;
  metrics: MetricsData | null;
  categoryBreakdown: CategoryBreakdown[];
  trendData: TrendData[];
  netWorth: NetWorthData | null;
  netWorthTrend: TrendData[];
  cashflow: CashflowData | null;
  anomalies: AnomalyData[];
  loading: boolean;
  error: string | null;
  isLoading: boolean;

  // Actions
  setDateRange: (range: { startDate: string; endDate: string }) => void;
  setPeriod: (period: string) => void;
  setSelectedFamilyId: (id: number | null) => void;
  fetchMetrics: () => Promise<void>;
  fetchCategoryBreakdown: () => Promise<void>;
  fetchTrendData: (metricType: string, categoryId?: number) => Promise<void>;
  fetchNetWorth: (asOfDate?: string) => Promise<void>;
  fetchNetWorthTrend: () => Promise<void>;
  fetchCashflow: () => Promise<void>;
  fetchAnomalies: () => Promise<void>;
  updateAnomalyStatus: (id: number, status: string, resolutionNotes?: string) => Promise<boolean>;
  exportData: (format: string, type: string) => Promise<boolean>;
  getMonthlySummary: (month: Date) => Promise<MonthlySummaryData | null>;
}

// Create the context
const AnalyticsContext = createContext<AnalyticsContextType | undefined>(undefined);

// Provider component
export const AnalyticsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const { selectedFamily } = useFamily();
  const toast = useToast();

  // Default date range is current month
  const today = new Date();
  const defaultStartDate = format(startOfMonth(subMonths(today, 1)), 'yyyy-MM-dd');
  const defaultEndDate = format(endOfMonth(today), 'yyyy-MM-dd');

  // State
  const [dateRange, setDateRange] = useState({ startDate: defaultStartDate, endDate: defaultEndDate });
  const [period, setPeriod] = useState('Monthly');
  const [selectedFamilyId, setSelectedFamilyId] = useState<number | null>(null);
  const [metrics, setMetrics] = useState<MetricsData | null>(null);
  const [categoryBreakdown, setCategoryBreakdown] = useState<CategoryBreakdown[]>([]);
  const [trendData, setTrendData] = useState<TrendData[]>([]);
  const [netWorth, setNetWorth] = useState<NetWorthData | null>(null);
  const [netWorthTrend, setNetWorthTrend] = useState<TrendData[]>([]);
  const [cashflow, setCashflow] = useState<CashflowData | null>(null);
  const [anomalies, setAnomalies] = useState<AnomalyData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Update selected family when family context changes
  useEffect(() => {
    if (selectedFamily) {
      setSelectedFamilyId(selectedFamily.id);
    }
  }, [selectedFamily]);

  // Fetch metrics
  const fetchMetrics = async () => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.analytics.getMetrics(
        dateRange.startDate,
        dateRange.endDate,
        period,
        selectedFamilyId || undefined
      );

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'danger' });
      } else if (response.data) {
        setMetrics(response.data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch metrics';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'danger' });
    } finally {
      setLoading(false);
    }
  };

  // Fetch category breakdown
  const fetchCategoryBreakdown = async () => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.analytics.getCategoryBreakdown(
        dateRange.startDate,
        dateRange.endDate,
        selectedFamilyId || undefined
      );

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'danger' });
      } else if (response.data) {
        setCategoryBreakdown(response.data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch category breakdown';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'danger' });
    } finally {
      setLoading(false);
    }
  };

  // Fetch trend data
  const fetchTrendData = async (metricType: string, categoryId?: number) => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.analytics.getTrendData(
        metricType,
        dateRange.startDate,
        dateRange.endDate,
        period,
        selectedFamilyId || undefined,
        categoryId
      );

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'danger' });
      } else if (response.data) {
        setTrendData(response.data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch trend data';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'danger' });
    } finally {
      setLoading(false);
    }
  };

  // Fetch net worth
  const fetchNetWorth = async (asOfDate?: string) => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.analytics.getNetWorth(
        asOfDate,
        selectedFamilyId || undefined
      );

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'danger' });
      } else if (response.data) {
        setNetWorth(response.data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch net worth';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'danger' });
    } finally {
      setLoading(false);
    }
  };

  // Fetch net worth trend
  const fetchNetWorthTrend = async () => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.analytics.getNetWorthTrend(
        dateRange.startDate,
        dateRange.endDate,
        period,
        selectedFamilyId || undefined
      );

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'danger' });
      } else if (response.data) {
        setNetWorthTrend(response.data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch net worth trend';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'danger' });
    } finally {
      setLoading(false);
    }
  };

  // Fetch cashflow
  const fetchCashflow = async () => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.analytics.getCashflowAnalysis(
        dateRange.startDate,
        dateRange.endDate,
        selectedFamilyId || undefined
      );

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'danger' });
      } else if (response.data) {
        setCashflow(response.data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch cashflow';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'danger' });
    } finally {
      setLoading(false);
    }
  };

  // Fetch anomalies
  const fetchAnomalies = async () => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.analytics.getAnomalies(
        dateRange.startDate,
        dateRange.endDate,
        selectedFamilyId || undefined
      );

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'danger' });
      } else if (response.data) {
        setAnomalies(response.data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch anomalies';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'danger' });
    } finally {
      setLoading(false);
    }
  };

  // Update anomaly status
  const updateAnomalyStatus = async (id: number, status: string, resolutionNotes?: string): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.analytics.updateAnomalyStatus(id, {
        status,
        resolutionNotes
      });

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'danger' });
        return false;
      } else {
        // Refresh anomalies list
        await fetchAnomalies();
        toast.show('Anomaly status updated successfully', { type: 'success' });
        return true;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update anomaly status';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'danger' });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Export data
  const exportData = async (format: string, type: string): Promise<boolean> => {
    if (!isAuthenticated) return false;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.analytics.exportData(
        format,
        dateRange.startDate,
        dateRange.endDate,
        type,
        selectedFamilyId || undefined
      );

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'danger' });
        return false;
      } else {
        toast.show('Data exported successfully', { type: 'success' });
        return true;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to export data';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'danger' });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Get monthly summary
  const getMonthlySummary = async (month: Date): Promise<MonthlySummaryData | null> => {
    if (!isAuthenticated) return null;

    setLoading(true);
    setError(null);

    try {
      // Format the month to get month and year
      const monthNum = month.getMonth() + 1; // JavaScript months are 0-indexed
      const year = month.getFullYear();

      // Call the API to get monthly summary
      const response = await apiService.notifications.getMonthlySummary(monthNum, year);

      if (response.error) {
        setError(response.error);
        toast.show(response.error, { type: 'danger' });
        return null;
      }

      return response.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch monthly summary';
      setError(errorMessage);
      toast.show(errorMessage, { type: 'danger' });
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Context value
  const value: AnalyticsContextType = {
    dateRange,
    period,
    selectedFamilyId,
    metrics,
    categoryBreakdown,
    trendData,
    netWorth,
    netWorthTrend,
    cashflow,
    anomalies,
    loading,
    error,
    isLoading: loading, // Add isLoading alias for loading
    setDateRange,
    setPeriod,
    setSelectedFamilyId,
    fetchMetrics,
    fetchCategoryBreakdown,
    fetchTrendData,
    fetchNetWorth,
    fetchNetWorthTrend,
    fetchCashflow,
    fetchAnomalies,
    updateAnomalyStatus,
    exportData,
    getMonthlySummary
  };

  return (
    <AnalyticsContext.Provider value={value}>
      {children}
    </AnalyticsContext.Provider>
  );
};

// Custom hook to use the analytics context
export const useAnalytics = () => {
  const context = useContext(AnalyticsContext);
  if (context === undefined) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }
  return context;
};
