import React, { createContext, useState, useContext, useEffect } from 'react';
import apiService from '../services/api';
import dbService, { Account as DbAccount } from '../services/db';
import { useAuth } from './AuthContext';

// Extend the DB Account interface with additional fields needed for the context
interface Account extends DbAccount {
  userId: number;
  createdAt: string;
  updatedAt?: string;
  familyId?: number;
}

interface AccountContextType {
  accounts: Account[];
  isLoading: boolean;
  error: string | null;
  refreshAccounts: () => Promise<void>;
  getAccountById: (id: number) => Account | undefined;
}

const AccountContext = createContext<AccountContextType>({
  accounts: [],
  isLoading: false,
  error: null,
  refreshAccounts: async () => {},
  getAccountById: () => undefined,
});

export const useAccounts = () => useContext(AccountContext);

export const AccountProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user, isAuthenticated } = useAuth();

  const loadAccounts = async () => {
    if (!isAuthenticated || !user) {
      setAccounts([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Check if online
      const netInfo = await fetch('https://www.google.com', { method: 'HEAD' })
        .then(() => true)
        .catch(() => false);

      if (netInfo) {
        // Online - get from API
        const response = await apiService.accounts.getAccounts();

        if (response.status === 200 && response.data) {
          // Convert API accounts to context accounts
          const contextAccounts = response.data.map(account => ({
            ...account,
            userId: user?.id || 0,
            createdAt: account.createdAt || new Date().toISOString(),
            familyId: account.familyId || undefined
          }));

          setAccounts(contextAccounts as Account[]);

          // Save to local database
          for (const account of response.data) {
            await dbService.saveAccount(account);
          }
        } else {
          // Fallback to local database
          const localAccounts = await dbService.getAccounts();
          // Convert DB accounts to context accounts
          const contextAccounts = localAccounts.map(account => ({
            ...account,
            userId: user?.id || 0,
            createdAt: new Date().toISOString(),
            familyId: account.familyId || undefined
          }));
          setAccounts(contextAccounts as Account[]);
        }
      } else {
        // Offline - get from local database
        const localAccounts = await dbService.getAccounts();
        // Convert DB accounts to context accounts
        const contextAccounts = localAccounts.map(account => ({
          ...account,
          userId: user?.id || 0,
          createdAt: new Date().toISOString(),
          familyId: account.familyId
        }));
        setAccounts(contextAccounts as Account[]);
      }
    } catch (error) {
      console.error('Error loading accounts:', error);
      setError('Failed to load accounts. Please try again.');

      // Fallback to local database
      const localAccounts = await dbService.getAccounts();
      // Convert DB accounts to context accounts
      const contextAccounts = localAccounts.map(account => ({
        ...account,
        userId: user?.id || 0,
        createdAt: new Date().toISOString(),
        familyId: account.familyId
      }));
      setAccounts(contextAccounts as Account[]);
    } finally {
      setIsLoading(false);
    }
  };

  // Load accounts when authenticated
  useEffect(() => {
    loadAccounts();
  }, [isAuthenticated, user]);

  const getAccountById = (id: number) => {
    return accounts.find(account => account.id === id);
  };

  const value = {
    accounts,
    isLoading,
    error,
    refreshAccounts: loadAccounts,
    getAccountById,
  };

  return (
    <AccountContext.Provider value={value}>
      {children}
    </AccountContext.Provider>
  );
};
