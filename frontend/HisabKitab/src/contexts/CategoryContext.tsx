import React, { createContext, useState, useContext, useEffect } from 'react';
import apiService from '../services/api';
import dbService from '../services/db';
import { useAuth } from './AuthContext';
import { Category as ModelCategory } from '../models/Category';

// Define the Category interface for the context
interface Category {
  id: number;
  name: string;
  type: string;
  icon: string;
  color: string;
  parentCategoryId?: number | null;
  isSystem: boolean;
  isSynced: boolean;
  isDeleted?: boolean;
  familyId?: number | null;
  userId?: number | null;
  createdAt: string;
  updatedAt?: string;
}

interface Priority {
  id: number;
  name: string;
  color: string;
  order: number;
  createdAt: string;
  updatedAt?: string;
}

interface CategoryContextType {
  categories: Category[];
  priorities: Priority[];
  isLoading: boolean;
  error: string | null;
  refreshCategories: () => Promise<void>;
  getCategoryById: (id: number) => Category | undefined;
  getPriorityById: (id: number) => Priority | undefined;
  fetchCategories: () => Promise<void>;
}

const CategoryContext = createContext<CategoryContextType>({
  categories: [],
  priorities: [],
  isLoading: false,
  error: null,
  refreshCategories: async () => {},
  getCategoryById: () => undefined,
  getPriorityById: () => undefined,
  fetchCategories: async () => {},
});

export const useCategories = () => useContext(CategoryContext);

export const CategoryProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [priorities, setPriorities] = useState<Priority[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user, isAuthenticated } = useAuth();

  const loadCategories = async (): Promise<void> => {
    if (!isAuthenticated || !user) {
      setCategories([]);
      setPriorities([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Check if online
      const netInfo = await fetch('https://www.google.com', { method: 'HEAD' })
        .then(() => true)
        .catch(() => false);

      if (netInfo) {
        // Online - get from API
        const categoriesResponse = await apiService.categories.getCategories();
        const prioritiesResponse = await apiService.priorities.getAllPriorities();

        if (categoriesResponse.status === 200 && categoriesResponse.data) {
          // Save to local database
          for (const category of categoriesResponse.data) {
            // Convert API category to DB category format
            const dbCategory = {
              id: category.id,
              name: category.name,
              type: category.type,
              icon: category.icon || '',
              color: category.color || '#000000',
              parentCategoryId: category.parentCategoryId === null ? undefined : category.parentCategoryId,
              isSystem: category.isSystem,
              isSynced: true
            };
            await dbService.saveCategory(dbCategory);
          }

          // Convert API categories to context categories
          const contextCategories = categoriesResponse.data.map((category: any): Category => ({
            id: category.id,
            name: category.name,
            type: category.type,
            icon: category.icon || '',
            color: category.color || '#000000',
            parentCategoryId: category.parentCategoryId,
            isSystem: category.isSystem,
            isSynced: true,
            isDeleted: category.isDeleted || false,
            familyId: category.familyId,
            userId: category.userId,
            createdAt: category.createdAt || new Date().toISOString(),
            updatedAt: category.updatedAt
          }));
          setCategories(contextCategories);
        } else {
          // Fallback to local database
          const localCategories = await dbService.getCategories();
          // Convert DB categories to context categories
          const contextCategories = localCategories.map((category): Category => ({
            id: category.id || 0,
            name: category.name,
            type: category.type,
            icon: category.icon || '',
            color: category.color || '#000000',
            parentCategoryId: category.parentCategoryId,
            isSystem: category.isSystem,
            isSynced: category.isSynced,
            userId: user?.id || 0,
            createdAt: new Date().toISOString()
          }));
          setCategories(contextCategories);
        }

        if (prioritiesResponse.status === 200 && prioritiesResponse.data) {
          setPriorities(prioritiesResponse.data);
        } else {
          // Since dbService doesn't have a getPriorities method, we'll use an empty array
          setPriorities([]);
        }
      } else {
        // Offline - get from local database
        const localCategories = await dbService.getCategories();
        // Convert DB categories to context categories
        const contextCategories = localCategories.map((category): Category => ({
          id: category.id || 0,
          name: category.name,
          type: category.type,
          icon: category.icon || '',
          color: category.color || '#000000',
          parentCategoryId: category.parentCategoryId,
          isSystem: category.isSystem,
          isSynced: category.isSynced,
          userId: user?.id || 0,
          createdAt: new Date().toISOString()
        }));
        setCategories(contextCategories);
        // Since dbService doesn't have a getPriorities method, we'll use an empty array
        setPriorities([]);
      }
    } catch (error) {
      console.error('Error loading categories and priorities:', error);
      setError('Failed to load categories and priorities. Please try again.');

      // Fallback to local database
      const localCategories = await dbService.getCategories();
      // Convert DB categories to context categories
      const contextCategories = localCategories.map((category): Category => ({
        id: category.id || 0,
        name: category.name,
        type: category.type,
        icon: category.icon || '',
        color: category.color || '#000000',
        parentCategoryId: category.parentCategoryId,
        isSystem: category.isSystem,
        isSynced: category.isSynced,
        userId: user?.id || 0,
        createdAt: new Date().toISOString()
      }));
      setCategories(contextCategories);
      // Since dbService doesn't have a getPriorities method, we'll use an empty array
      setPriorities([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Load categories when authenticated
  useEffect(() => {
    loadCategories();
  }, [isAuthenticated, user]);

  const getCategoryById = (id: number) => {
    return categories.find(category => category.id === id);
  };

  const getPriorityById = (id: number) => {
    return priorities.find(priority => priority.id === id);
  };

  const value = {
    categories,
    priorities,
    isLoading,
    error,
    refreshCategories: loadCategories,
    getCategoryById,
    getPriorityById,
    fetchCategories: loadCategories,
  };

  return (
    <CategoryContext.Provider value={value}>
      {children}
    </CategoryContext.Provider>
  );
};
