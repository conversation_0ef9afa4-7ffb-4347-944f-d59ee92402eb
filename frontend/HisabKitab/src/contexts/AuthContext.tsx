import React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from 'react';
import { Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import { router } from 'expo-router';
import apiService from '../services/api';
import dbService, { User } from '../services/db';
import {
  registerForPushNotificationsAsync,
  registerPushTokenWithServer,
  unregisterPushTokenWithServer,
  setBadgeCount
} from '../services/pushNotifications';

// Interface for auth context
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isPlatformAdmin: boolean;
  isFamilyAdmin: boolean;
  hasRole: (role: string) => boolean;
  isFamilyMember: (familyId: number) => boolean;
  login: (username: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (userData: any) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  updateProfile: (userData: any) => Promise<{ success: boolean; error?: string }>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<{ success: boolean; error?: string }>;
  changeEmail: (newEmail: string, password: string) => Promise<{ success: boolean; error?: string }>;
}

// Create auth context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const isPlatformAdmin = user?.isPlatformAdmin || false;
  const isFamilyAdmin = user?.isFamilyAdmin || false;

  // Check if user has a specific role
  const hasRole = (role: string): boolean => {
    return user?.roles?.includes(role) || false;
  };

  // Check if user is a member of a specific family
  const isFamilyMember = (familyId: number): boolean => {
    return user?.familyMemberships?.some((membership: { familyId: number }) => membership.familyId === familyId) || false;
  };

  // Reference to notification listener
  const notificationListener = useRef<Notifications.Subscription | undefined>(undefined);
  const responseListener = useRef<Notifications.Subscription | undefined>(undefined);

  // Set up notification handlers
  useEffect(() => {
    // Configure notification behavior
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
        shouldShowBanner: true,
        shouldShowList: true,
      }),
    });

    // Listen for incoming notifications
    notificationListener.current = Notifications.addNotificationReceivedListener((notification: Notifications.Notification) => {
      console.log('Notification received:', notification);
    });

    // Listen for user interaction with notifications
    responseListener.current = Notifications.addNotificationResponseReceivedListener((response: Notifications.NotificationResponse) => {
      console.log('Notification response received:', response);
      const { notification } = response;
      const data = notification.request.content.data as any;

      // Handle notification based on type
      if (data.type === 'Loan') {
        // Navigate to loan details
        if (data.loanId) {
          // Use router.push instead of navigation.navigate
          router.push({
            pathname: '/loans/[id]',
            params: { id: data.loanId }
          } as any);
        }
      }
    });

    // Clean up listeners on unmount
    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, []);

  // Check if user is authenticated on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check if auth token exists
        const authToken = await dbService.getAuthToken();
        if (!authToken) {
          setIsLoading(false);
          return;
        }

        // Check if token is expired
        if (new Date(authToken.expiresAt) < new Date()) {
          await dbService.clearAuthToken();
          setIsLoading(false);
          return;
        }

        // Get user from database
        const user = await dbService.getCurrentUser();
        if (user) {
          setUser(user);
          setIsAuthenticated(true);

          // Register for push notifications
          const pushToken = await registerForPushNotificationsAsync();
          if (pushToken) {
            await registerPushTokenWithServer(pushToken);
          }

          // Reset badge count
          await setBadgeCount(0);
        }
      } catch (error) {
        console.error('Error checking auth:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Login function
  const login = async (username: string, password: string) => {
    try {
      setIsLoading(true);
      console.log(`Attempting to login with username: ${username}`);

      const response = await apiService.auth.login({ username, password });
      console.log('Login response:', response);

      if (response.data) {
        console.log('Login successful, saving user data');
        // Get user from database (saved by api service)
        const user = await dbService.getCurrentUser();
        if (user) {
          setUser(user);
          setIsAuthenticated(true);

          // Fetch user profile to get additional user data
          try {
            console.log('Fetching user profile');
            await apiService.user.getProfile();
            // Update user state with the latest data
            const updatedUser = await dbService.getCurrentUser();
            if (updatedUser) {
              setUser(updatedUser);
              console.log('User profile updated');
            }
          } catch (profileError) {
            console.error('Error fetching user profile:', profileError);
            // Continue with login even if profile fetch fails
          }
        } else {
          console.error('User not found in database after login');
        }
        return { success: true };
      } else {
        console.error('Login failed:', response.error);
        return { success: false, error: response.error || 'Login failed. Please check your credentials.' };
      }
    } catch (error) {
      console.error('Error logging in:', error);
      return {
        success: false,
        error: error instanceof Error
          ? `Login error: ${error.message}`
          : 'An unexpected error occurred during login'
      };
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (userData: any) => {
    try {
      setIsLoading(true);
      const response = await apiService.auth.register(userData);

      if (response.status === 200) {
        return { success: true };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      console.error('Error registering:', error);
      return { success: false, error: 'An error occurred during registration' };
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setIsLoading(true);

      // Get push token and unregister it
      try {
        const pushToken = await registerForPushNotificationsAsync();
        if (pushToken) {
          await unregisterPushTokenWithServer(pushToken.token);
        }
      } catch (tokenError) {
        console.error('Error unregistering push token:', tokenError);
        // Continue with logout even if token unregistration fails
      }

      await apiService.auth.logout();
    } catch (error) {
      console.error('Error logging out:', error);
    } finally {
      // Clear auth token and user
      await dbService.clearAuthToken();
      setUser(null);
      setIsAuthenticated(false);
      setIsLoading(false);
    }
  };

  // Update profile function
  const updateProfile = async (userData: any) => {
    try {
      setIsLoading(true);
      const response = await apiService.user.updateProfile(userData);

      if (response.status === 200) {
        // Get updated user from database
        const updatedUser = await dbService.getCurrentUser();
        if (updatedUser) {
          setUser(updatedUser);
        }
        return { success: true };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      return { success: false, error: 'An error occurred while updating profile' };
    } finally {
      setIsLoading(false);
    }
  };

  // Change password function
  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      setIsLoading(true);
      const response = await apiService.user.changePassword(currentPassword, newPassword);

      if (response.status === 200) {
        return { success: true };
      } else {
        // Check for specific error messages and provide user-friendly responses
        const errorMessage = response.error || '';
        if (errorMessage.toLowerCase().includes('current password') ||
            errorMessage.toLowerCase().includes('password does not match')) {
          return { success: false, error: 'Current password is incorrect' };
        } else {
          return { success: false, error: response.error };
        }
      }
    } catch (error) {
      console.error('Error changing password:', error);
      return { success: false, error: 'An error occurred while changing password' };
    } finally {
      setIsLoading(false);
    }
  };

  // Change email function
  const changeEmail = async (newEmail: string, password: string) => {
    try {
      setIsLoading(true);
      const response = await apiService.user.changeEmail(newEmail, password);

      if (response.status === 200) {
        // Get updated user from database
        const updatedUser = await dbService.getCurrentUser();
        if (updatedUser) {
          setUser(updatedUser);
        }
        return { success: true };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      console.error('Error changing email:', error);
      return { success: false, error: 'An error occurred while changing email' };
    } finally {
      setIsLoading(false);
    }
  };

  // Context value
  const value = {
    user,
    isLoading,
    isAuthenticated,
    isPlatformAdmin,
    isFamilyAdmin,
    hasRole,
    isFamilyMember,
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    changeEmail,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
