/**
 * Storage keys for AsyncStorage and IndexedDB
 */
export const STORAGE_KEYS = {
  // Auth
  AUTH_TOKEN: '@HisabKitab:authToken',
  REFRESH_TOKEN: '@HisabKitab:refreshToken',
  TOKEN_EXPIRY: '@HisabKitab:tokenExpiry',
  USER_DATA: '@HisabKitab:userData',
  
  // Data
  ACCOUNTS: '@HisabKitab:accounts',
  TRANSACTIONS: '@HisabKitab:transactions',
  CATEGORIES: '@HisabKitab:categories',
  RECEIPTS: '@HisabKitab:receipts',
  
  // Settings
  USER_SETTINGS: '@HisabKitab:userSettings',
  LANGUAGE: '@HisabKitab:language',
  THEME: '@HisabKitab:theme',
  
  // Sync
  SYNC_QUEUE: '@HisabKitab:syncQueue',
  SYNC_STATUS: '@HisabKitab:syncStatus',
  CONFLICT_LOGS: '@HisabKitab:conflictLogs',
  
  // Notifications
  NOTIFICATIONS: '@HisabKitab:notifications',
  LAST_NOTIFICATION_CHECK: '@HisabKitab:lastNotificationCheck',
  
  // Migration
  MIGRATION_COMPLETED: '@HisabKitab:migrationCompleted',
};
