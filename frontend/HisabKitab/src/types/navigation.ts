/**
 * Navigation types for the app
 */

// Define the app's route paths
export type AppRoutes = {
  // Tab routes
  '/': undefined;
  '/families': undefined;
  '/accounts': undefined;
  '/transactions': undefined;
  '/categories': undefined;
  '/settings': undefined;
  
  // Auth routes
  '/auth/login': undefined;
  '/auth/register': undefined;
  
  // Profile routes
  '/profile': undefined;
  
  // Family routes
  '/family/create': undefined;
  '/family/join': undefined;
  '/family/[id]': { id: string };
  '/family/edit/[id]': { id: string };
};

// Helper type for route paths
export type AppRoutePath = keyof AppRoutes;
