/**
 * Custom hook to provide the current theme based on the theme mode
 */

import { useMemo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import theme, { colors, spacing, typography, borderRadius, shadows, animation, zIndex } from './theme';

/**
 * Hook to get the current theme based on the theme mode
 * @returns The current theme object with all theme properties
 */
export const useAppTheme = () => {
  const { isDarkMode } = useTheme();
  
  // Memoize the theme to prevent unnecessary re-renders
  const currentTheme = useMemo(() => {
    return {
      // Colors based on theme mode
      colors: {
        primary: isDarkMode ? colors.primary.dark : colors.primary.light,
        secondary: isDarkMode ? colors.secondary.dark : colors.secondary.light,
        success: isDarkMode ? colors.success.dark : colors.success.light,
        danger: isDarkMode ? colors.danger.dark : colors.danger.light,
        warning: isDarkMode ? colors.warning.dark : colors.warning.light,
        info: isDarkMode ? colors.info.dark : colors.info.light,
        
        text: {
          primary: isDarkMode ? colors.text.dark.primary : colors.text.light.primary,
          secondary: isDarkMode ? colors.text.dark.secondary : colors.text.light.secondary,
          tertiary: isDarkMode ? colors.text.dark.tertiary : colors.text.light.tertiary,
          inverse: isDarkMode ? colors.text.dark.inverse : colors.text.light.inverse,
        },
        
        background: {
          primary: isDarkMode ? colors.background.dark.primary : colors.background.light.primary,
          secondary: isDarkMode ? colors.background.dark.secondary : colors.background.light.secondary,
          tertiary: isDarkMode ? colors.background.dark.tertiary : colors.background.light.tertiary,
        },
        
        border: {
          primary: isDarkMode ? colors.border.dark.primary : colors.border.light.primary,
          secondary: isDarkMode ? colors.border.dark.secondary : colors.border.light.secondary,
          tertiary: isDarkMode ? colors.border.dark.tertiary : colors.border.light.tertiary,
        },
        
        semantic: {
          income: isDarkMode ? colors.semantic.income.dark : colors.semantic.income.light,
          expense: isDarkMode ? colors.semantic.expense.dark : colors.semantic.expense.light,
          transfer: isDarkMode ? colors.semantic.transfer.dark : colors.semantic.transfer.light,
          urgent: isDarkMode ? colors.semantic.urgent.dark : colors.semantic.urgent.light,
          high: isDarkMode ? colors.semantic.high.dark : colors.semantic.high.light,
          normal: isDarkMode ? colors.semantic.normal.dark : colors.semantic.normal.light,
          low: isDarkMode ? colors.semantic.low.dark : colors.semantic.low.light,
        },
      },
      
      // Other theme properties
      spacing,
      typography,
      borderRadius,
      shadows: isDarkMode ? shadows.dark : shadows.light,
      animation,
      zIndex,
      
      // Helper for getting semantic colors based on transaction type
      getTransactionTypeColor: (type: number | string) => {
        const typeStr = typeof type === 'number' 
          ? (type === 0 ? 'income' : type === 1 ? 'expense' : 'transfer')
          : type.toLowerCase();
          
        switch (typeStr) {
          case 'income':
          case '0':
            return isDarkMode ? colors.semantic.income.dark : colors.semantic.income.light;
          case 'expense':
          case '1':
            return isDarkMode ? colors.semantic.expense.dark : colors.semantic.expense.light;
          case 'transfer':
          case '2':
            return isDarkMode ? colors.semantic.transfer.dark : colors.semantic.transfer.light;
          default:
            return isDarkMode ? colors.text.dark.primary : colors.text.light.primary;
        }
      },
      
      // Helper for getting semantic colors based on priority
      getPriorityColor: (priority: number | string) => {
        const priorityStr = typeof priority === 'number'
          ? (priority === 0 ? 'urgent' : priority === 1 ? 'high' : priority === 2 ? 'normal' : 'low')
          : priority.toLowerCase();
          
        switch (priorityStr) {
          case 'urgent':
          case '0':
            return isDarkMode ? colors.semantic.urgent.dark : colors.semantic.urgent.light;
          case 'high':
          case '1':
            return isDarkMode ? colors.semantic.high.dark : colors.semantic.high.light;
          case 'normal':
          case '2':
            return isDarkMode ? colors.semantic.normal.dark : colors.semantic.normal.light;
          case 'low':
          case '3':
            return isDarkMode ? colors.semantic.low.dark : colors.semantic.low.light;
          default:
            return isDarkMode ? colors.semantic.normal.dark : colors.semantic.normal.light;
        }
      },
    };
  }, [isDarkMode]);
  
  return currentTheme;
};

export default useAppTheme;
