/**
 * Centralized theme configuration for the Hisab-Kitab app
 * This file defines colors, spacing, typography, and other UI constants
 * to ensure consistency across the app.
 */

// Color palette
export const colors = {
  // Primary colors - Teal Jade
  primary: {
    light: '#3AAFA9', // Teal Jade - financially calming, modern and fresh
    dark: '#3AAFA9',  // Same for dark mode for consistency
  },
  secondary: {
    light: '#4FBFB9', // Lighter shade of primary
    dark: '#4FBFB9',
  },
  success: {
    light: '#4ADE80', // Spring Green - for success/positive events
    dark: '#4ADE80',
  },
  danger: {
    light: '#EF4444', // Rosy Red - for errors
    dark: '#EF4444',
  },
  warning: {
    light: '#FACC15', // Amber Yellow - for warnings
    dark: '#FACC15',
  },
  info: {
    light: '#60A5FA', // Blue - for information
    dark: '#60A5FA',
  },

  // Neutral colors
  text: {
    light: {
      primary: '#333333', // Dark text for light mode
      secondary: '#6E7781',
      tertiary: '#8B949E',
      inverse: '#F5F5F5', // <PERSON> - for high contrast
    },
    dark: {
      primary: '#E0E0E0', // Ash <PERSON> - for dark mode
      secondary: '#8B949E',
      tertiary: '#6E7781',
      inverse: '#333333',
    },
  },
  background: {
    light: {
      primary: '#F5F5F5', // Light background
      secondary: '#FFFFFF',
      tertiary: '#EAEEF2',
    },
    dark: {
      primary: '#0B0C10', // Deep Charcoal Blue - near-black but warm
      secondary: '#1F2833', // Dark cards for dark mode
      tertiary: '#2D3748',
    },
  },
  border: {
    light: {
      primary: '#E5E5E5', // Light border
      secondary: '#E1E4E8',
      tertiary: '#EAEEF2',
    },
    dark: {
      primary: '#2D3748', // Dark border
      secondary: '#21262D',
      tertiary: '#1C2128',
    },
  },

  // Semantic colors
  semantic: {
    income: {
      light: '#4ADE80', // Spring Green
      dark: '#4ADE80',
    },
    expense: {
      light: '#EF4444', // Rosy Red
      dark: '#EF4444',
    },
    transfer: {
      light: '#60A5FA', // Blue
      dark: '#60A5FA',
    },
    urgent: {
      light: '#EF4444', // Rosy Red
      dark: '#EF4444',
    },
    high: {
      light: '#FF6B6B', // Royal Pink - start of accent gradient
      dark: '#FF6B6B',
    },
    normal: {
      light: '#3AAFA9', // Teal Jade
      dark: '#3AAFA9',
    },
    low: {
      light: '#4ADE80', // Spring Green
      dark: '#4ADE80',
    },
  },

  // Cultural accent colors
  accent: {
    start: '#FF6B6B', // Royal Pink - start of gradient
    end: '#FFC3A0', // Salmon - end of gradient
  },
};

// Spacing
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// Typography
export const typography = {
  fontFamily: {
    base: "'Manrope', 'Noto Sans Devanagari', sans-serif",
    regular: 'Manrope-Regular',
    medium: 'Manrope-Medium',
    semiBold: 'Manrope-SemiBold',
    bold: 'Manrope-Bold',
    nepali: 'Noto Sans Devanagari',
  },
  fontSize: {
    caption: 12,    // Meta info, timestamps
    xs: 12,         // Same as caption for backward compatibility
    bodySmall: 14,  // Details, item names
    sm: 14,         // Same as bodySmall for backward compatibility
    bodyLarge: 16,  // Standard readable content
    md: 16,         // Same as bodyLarge for backward compatibility
    lg: 18,         // Larger readable content
    number: 22,     // Prices, goals, net worth
    xl: 22,         // Same as number for backward compatibility
    h3: 24,         // Card headers, drawer titles
    xxl: 24,        // Same as h3 for backward compatibility
    h2: 28,         // Section headers
    h1: 32,         // Dashboard titles, modals
    xxxl: 32,       // Same as h1 for backward compatibility
    hero: 36,       // Hero sections
  },
  lineHeight: {
    caption: 18,    // 1.5x line height for caption
    xs: 18,         // For backward compatibility
    bodySmall: 21,  // 1.5x line height for bodySmall
    sm: 21,         // For backward compatibility
    bodyLarge: 24,  // 1.5x line height for bodyLarge
    md: 24,         // For backward compatibility
    lg: 27,         // 1.5x line height for lg
    number: 28,     // Slightly tighter for numbers
    xl: 28,         // For backward compatibility
    h3: 32,         // Comfortable spacing for headers
    xxl: 32,        // For backward compatibility
    h2: 36,         // Comfortable spacing for headers
    h1: 40,         // Comfortable spacing for headers
    xxxl: 40,       // For backward compatibility
    hero: 44,       // Spacious for hero text
  },
  letterSpacing: {
    tight: -0.5,
    normal: 0,
    wide: 0.5,
    button: 0.5,    // Slightly wider for buttons
    uppercase: 1,   // Wider for uppercase text
  },
  fontWeight: {
    regular: "400" as const,
    medium: "500" as const,
    semibold: "600" as const,
    bold: "700" as const,
  },
};

// Border radius
export const borderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  round: 9999,
};

// Shadows
export const shadows = {
  light: {
    sm: {
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 2.0,
      elevation: 2,
    },
    md: {
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 4,
    },
    lg: {
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 5.0,
      elevation: 8,
    },
    // Special glow effect for cards
    glow: {
      shadowColor: '#3AAFA9', // Teal glow
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 6,
    },
    accentGlow: {
      shadowColor: '#FF6B6B', // Accent glow
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 6,
    },
  },
  dark: {
    sm: {
      shadowColor: 'rgba(0, 0, 0, 0.3)',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.4,
      shadowRadius: 3.0,
      elevation: 3,
    },
    md: {
      shadowColor: 'rgba(0, 0, 0, 0.3)',
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 0.5,
      shadowRadius: 5.0,
      elevation: 6,
    },
    lg: {
      shadowColor: 'rgba(0, 0, 0, 0.3)',
      shadowOffset: { width: 0, height: 5 },
      shadowOpacity: 0.6,
      shadowRadius: 7.0,
      elevation: 12,
    },
    // Special glow effect for cards
    glow: {
      shadowColor: '#3AAFA9', // Teal glow
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.5,
      shadowRadius: 10,
      elevation: 8,
    },
    accentGlow: {
      shadowColor: '#FF6B6B', // Accent glow
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.5,
      shadowRadius: 10,
      elevation: 8,
    },
  },
};

// Animation durations
export const animation = {
  fast: 150,
  normal: 300,
  slow: 500,
  // Animation types
  types: {
    fadeIn: 'fade-in',
    fadeOut: 'fade-out',
    slideUp: 'slide-up',
    slideDown: 'slide-down',
    slideLeft: 'slide-left',
    slideRight: 'slide-right',
    scale: 'scale',
    rotate: 'rotate',
    pulse: 'pulse',
  },
  // Animation curves
  curves: {
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
    linear: 'linear',
    spring: 'spring',
  },
};

// Z-index values
export const zIndex = {
  base: 1,
  dropdown: 10,
  modal: 100,
  toast: 1000,
};

// Export default theme object
export default {
  colors,
  spacing,
  typography,
  borderRadius,
  shadows,
  animation,
  zIndex,
};
