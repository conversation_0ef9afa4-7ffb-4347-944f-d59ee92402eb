// This file helps TypeScript find modules that might not be directly visible

// UI Components
declare module '../../components/ui/EmptyState' {
  import React from 'react';
  
  interface EmptyStateProps {
    icon: string;
    title: string;
    message: string;
    buttonTitle?: string;
    onButtonPress?: () => void;
  }
  
  const EmptyState: React.FC<EmptyStateProps>;
  export default EmptyState;
}

// Context Modules
declare module '../../contexts/AccountContext' {
  import React from 'react';
  
  interface Account {
    id: number;
    name: string;
    accountType: string;
    balance: number;
    initialBalance: number;
    currency: string;
    isActive: boolean;
    excludeFromStats: boolean;
    familyId?: number;
    userId: number;
    createdAt: string;
    updatedAt?: string;
  }
  
  interface AccountContextType {
    accounts: Account[];
    isLoading: boolean;
    error: string | null;
    fetchAccounts: () => Promise<void>;
    getAccount: (id: number) => Account | undefined;
    createAccount: (accountData: any) => Promise<any>;
    updateAccount: (id: number, accountData: any) => Promise<any>;
    deleteAccount: (id: number) => Promise<any>;
    refreshAccounts: () => Promise<void>;
  }
  
  export const useAccounts: () => AccountContextType;
  export const AccountProvider: React.FC<{ children: React.ReactNode }>;
}

declare module '../../contexts/CategoryContext' {
  import React from 'react';
  
  interface Category {
    id: number;
    name: string;
    type: string;
    icon: string;
    color: string;
    parentCategoryId?: number;
    familyId?: number;
    userId: number;
    createdAt: string;
    updatedAt?: string;
  }
  
  interface Priority {
    id: number;
    name: string;
    description?: string;
    color: string;
    createdAt: string;
    updatedAt?: string;
  }
  
  interface CategoryContextType {
    categories: Category[];
    priorities: Priority[];
    isLoading: boolean;
    error: string | null;
    refreshCategories: () => Promise<void>;
    refreshPriorities: () => Promise<void>;
    getCategoryById: (id: number) => Category | undefined;
    getPriorityById: (id: number) => Priority | undefined;
  }
  
  export const useCategories: () => CategoryContextType;
  export const CategoryProvider: React.FC<{ children: React.ReactNode }>;
}

// Transaction Components
declare module './TransactionItemList' {
  import React from 'react';
  
  interface TransactionItem {
    id?: number;
    name: string;
    amount: number | string;
    quantity?: number | string;
    categoryId?: number;
    notes?: string;
  }
  
  interface TransactionItemListProps {
    items: TransactionItem[];
    onAddItem: (item: TransactionItem) => void;
    onUpdateItem: (item: TransactionItem) => void;
    onRemoveItem: (itemId: number) => void;
    categories: Array<{ id: number; name: string; type: string }>;
  }
  
  const TransactionItemList: React.FC<TransactionItemListProps>;
  export default TransactionItemList;
}
