// English translations for typography showcase
export const enTypographyTranslations = {
  typography: {
    showcase: 'Typography Showcase',
    variants: 'Typography Variants',
    weights: 'Font Weights',
    textStyles: 'Text Styles',
    numberAnimation: 'Number Animation',
    hero: 'Hero Text',
    h1: 'Heading 1',
    h2: 'Heading 2',
    h3: 'Heading 3',
    bodyLarge: 'Body Large',
    bodySmall: 'Body Small',
    caption: 'Caption',
    number: 'Number',
    regular: 'Regular Weight',
    medium: 'Medium Weight',
    semibold: 'Semibold Weight',
    bold: 'Bold Weight',
    uppercase: 'UPPERCASE TEXT',
    coloredText: 'Colored Text',
    centeredText: 'Centered Text',
    underlinedText: 'Underlined Text',
  }
};

// Nepali translations for typography showcase
export const neTypographyTranslations = {
  typography: {
    showcase: 'टाइपोग्राफी प्रदर्शन',
    variants: 'टाइपोग्राफी प्रकारहरू',
    weights: 'फन्ट वजनहरू',
    textStyles: 'पाठ शैलीहरू',
    numberAnimation: 'संख्या एनिमेशन',
    hero: 'हिरो पाठ',
    h1: 'शीर्षक १',
    h2: 'शीर्षक २',
    h3: 'शीर्षक ३',
    bodyLarge: 'ठूलो मुख्य पाठ',
    bodySmall: 'सानो मुख्य पाठ',
    caption: 'क्याप्शन',
    number: 'संख्या',
    regular: 'नियमित वजन',
    medium: 'मध्यम वजन',
    semibold: 'अर्ध-बोल्ड वजन',
    bold: 'बोल्ड वजन',
    uppercase: 'ठूलो अक्षरको पाठ',
    coloredText: 'रंगीन पाठ',
    centeredText: 'केन्द्रित पाठ',
    underlinedText: 'रेखांकित पाठ',
  }
};
