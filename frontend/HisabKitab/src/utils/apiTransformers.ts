/**
 * API data transformation utilities for the Hisab-Kitab app
 * These utilities help transform data between frontend and backend formats
 */

import { Transaction, TransactionType } from '../models/Transaction';
import { Category, CATEGORY_TYPES } from '../models/Category';
import { Account } from '../models/Account';

/**
 * Transforms a transaction from API format to frontend format
 * @param apiTransaction Transaction data from the API
 * @returns Transaction in frontend format
 */
export const transformApiTransaction = (apiTransaction: any): Transaction => {
  // Handle transaction type conversion (numeric to enum)
  let transactionType: TransactionType;
  if (typeof apiTransaction.type === 'number') {
    transactionType = apiTransaction.type as TransactionType;
  } else if (typeof apiTransaction.type === 'string') {
    // Handle string type values
    switch (apiTransaction.type.toLowerCase()) {
      case 'income':
        transactionType = TransactionType.Income;
        break;
      case 'expense':
        transactionType = TransactionType.Expense;
        break;
      case 'transfer':
        transactionType = TransactionType.Transfer;
        break;
      default:
        // Default to expense if unknown
        transactionType = TransactionType.Expense;
    }
  } else {
    // Default to expense if type is missing
    transactionType = TransactionType.Expense;
  }

  // Handle date conversion
  const date = apiTransaction.date ? new Date(apiTransaction.date) : new Date();

  // Create transaction object with frontend format
  const transaction: Transaction = {
    id: apiTransaction.id,
    type: transactionType,
    amount: Number(apiTransaction.amount),
    description: apiTransaction.description || '',
    date: date.toISOString(),
    categoryId: apiTransaction.categoryId || 0,
    categoryName: apiTransaction.categoryName || '',
    accountId: apiTransaction.accountId || 0,
    accountName: apiTransaction.accountName || '',
    toAccountId: apiTransaction.toAccountId || 0,
    toAccountName: apiTransaction.toAccountName || '',
    userId: apiTransaction.userId || 0,
    username: apiTransaction.username || '',
    status: apiTransaction.status || 'Completed',
    isSynced: apiTransaction.isSynced !== false,
    syncStatus: apiTransaction.syncStatus || 'Synced',
    lastUpdatedAt: apiTransaction.lastUpdatedAt || new Date().toISOString(),
    createdAt: apiTransaction.createdAt || new Date().toISOString(),
    updatedAt: apiTransaction.updatedAt || undefined,
    items: Array.isArray(apiTransaction.items)
      ? apiTransaction.items.map((item: any) => ({
          id: item.id,
          transactionId: item.transactionId || apiTransaction.id,
          name: item.name || '',
          quantity: Number(item.quantity) || 1,
          amount: Number(item.amount) || 0,
          categoryId: item.categoryId || 0,
          notes: item.notes || '',
        }))
      : [],
  };

  // Add any additional properties that might be in the API but not in the interface
  if (apiTransaction.familyId) (transaction as any).familyId = apiTransaction.familyId;
  if (apiTransaction.receiptUrl) (transaction as any).receiptUrl = apiTransaction.receiptUrl;
  if (apiTransaction.tags) (transaction as any).tags = apiTransaction.tags;
  if (apiTransaction.location) (transaction as any).location = apiTransaction.location;

  return transaction;
};

/**
 * Transforms a transaction from frontend format to API format
 * @param transaction Transaction data from the frontend
 * @returns Transaction in API format
 */
export const transformTransactionForApi = (transaction: Transaction): any => {
  return {
    id: transaction.id,
    type: transaction.type,
    amount: transaction.amount,
    description: transaction.description,
    date: typeof transaction.date === 'object'
      ? new Date(transaction.date).toISOString()
      : transaction.date,
    categoryId: transaction.categoryId,
    accountId: transaction.accountId,
    toAccountId: transaction.toAccountId || null,
    // familyId is not in the Transaction interface but might be in the API
    familyId: (transaction as any).familyId,
    items: transaction.items?.map(item => ({
      id: item.id,
      transactionId: item.transactionId,
      name: item.name,
      quantity: item.quantity,
      price: (item as any).price,
      amount: item.amount,
    })) || [],
  };
};

/**
 * Transforms a category from API format to frontend format
 * @param apiCategory Category data from the API
 * @returns Category in frontend format
 */
export const transformApiCategory = (apiCategory: any): Category => {
  // Handle category type conversion
  let categoryType: string;
  if (typeof apiCategory.type === 'number') {
    // Convert numeric type to string
    categoryType = apiCategory.type === 0 ? CATEGORY_TYPES.INCOME : CATEGORY_TYPES.EXPENSE;
  } else if (typeof apiCategory.type === 'string') {
    // Handle string type values
    switch (apiCategory.type.toLowerCase()) {
      case 'income':
        categoryType = CATEGORY_TYPES.INCOME;
        break;
      case 'expense':
        categoryType = CATEGORY_TYPES.EXPENSE;
        break;
      default:
        // Default to expense if unknown
        categoryType = CATEGORY_TYPES.EXPENSE;
    }
  } else {
    // Default to expense if type is missing
    categoryType = CATEGORY_TYPES.EXPENSE;
  }

  // Create category object with frontend format
  const category: Category = {
    id: apiCategory.id,
    name: apiCategory.name || '',
    type: categoryType,
    icon: apiCategory.icon || 'cash-outline',
    color: apiCategory.color || '#0366D6',
    isSystem: apiCategory.isSystem || false,
    familyId: apiCategory.familyId || null,
    userId: apiCategory.userId || null,
    createdAt: apiCategory.createdAt || new Date().toISOString(),
  };

  // Add any additional properties that might be in the API but not in the interface
  (category as any).isDefault = apiCategory.isDefault || false;
  if (apiCategory.updatedAt) (category as any).updatedAt = apiCategory.updatedAt;

  return category;
};

/**
 * Transforms a category from frontend format to API format
 * @param category Category data from the frontend
 * @returns Category in API format
 */
export const transformCategoryForApi = (category: Category): any => {
  return {
    id: category.id,
    name: category.name,
    type: category.type,
    icon: category.icon,
    color: category.color,
    // isDefault is not in the Category interface but might be in the API
    isDefault: (category as any).isDefault,
    familyId: category.familyId,
  };
};

/**
 * Transforms an account from API format to frontend format
 * @param apiAccount Account data from the API
 * @returns Account in frontend format
 */
export const transformApiAccount = (apiAccount: any): Account => {
  // Create a base account with required properties
  const account: Account = {
    id: apiAccount.id,
    name: apiAccount.name || '',
    accountType: apiAccount.accountType || 'Cash',
    balance: Number(apiAccount.balance) || 0,
    initialBalance: Number(apiAccount.initialBalance) || 0,
    currency: apiAccount.currency || 'USD',
    isActive: apiAccount.isActive !== false,
    excludeFromStats: apiAccount.excludeFromStats || false,
    lastUpdatedAt: new Date(apiAccount.lastUpdatedAt || new Date()),
    isSynced: apiAccount.isSynced !== false,
    familyId: apiAccount.familyId || undefined,
    userId: apiAccount.userId || undefined,
    createdAt: apiAccount.createdAt || new Date().toISOString(),
    updatedAt: apiAccount.updatedAt || undefined,
  };

  // Add any additional properties that might be in the API but not in the interface
  (account as any).icon = apiAccount.icon || 'wallet-outline';
  (account as any).color = apiAccount.color || '#0366D6';
  (account as any).isDefault = apiAccount.isDefault || false;
  (account as any).isShared = apiAccount.isShared || false;

  return account;
};

/**
 * Transforms an account from frontend format to API format
 * @param account Account data from the frontend
 * @returns Account in API format
 */
export const transformAccountForApi = (account: Account): any => {
  // Create a base API account with standard properties
  const apiAccount: any = {
    id: account.id,
    name: account.name,
    accountType: account.accountType,
    balance: account.balance,
    initialBalance: account.initialBalance,
    currency: account.currency,
    isActive: account.isActive,
    excludeFromStats: account.excludeFromStats,
    familyId: account.familyId,
  };

  // Add any additional properties that might be in the account but not in the interface
  if ((account as any).icon) apiAccount.icon = (account as any).icon;
  if ((account as any).color) apiAccount.color = (account as any).color;
  if ((account as any).isDefault) apiAccount.isDefault = (account as any).isDefault;
  if ((account as any).isShared) apiAccount.isShared = (account as any).isShared;

  return apiAccount;
};

export default {
  transformApiTransaction,
  transformTransactionForApi,
  transformApiCategory,
  transformCategoryForApi,
  transformApiAccount,
  transformAccountForApi,
};
