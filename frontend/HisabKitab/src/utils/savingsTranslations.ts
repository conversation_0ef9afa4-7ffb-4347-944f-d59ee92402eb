// English translations for savings goals and wishlist
export const enSavingsTranslations = {
  savings: {
    savings: 'Savings',
    dashboard: 'Savings Dashboard',
    goals: 'Savings Goals',
    goal: 'Savings Goal',
    goalDetails: 'Goal Details',
    addGoal: 'Add Goal',
    createGoal: 'Create Goal',
    editGoal: 'Edit Goal',
    updateGoal: 'Update Goal',
    deleteGoal: 'Delete Goal',
    deleteGoalConfirmation: 'Are you sure you want to delete this savings goal?',
    deleteGoalError: 'Failed to delete savings goal. Please try again.',
    goalNotFound: 'Savings goal not found',
    updateGoalError: 'Failed to update savings goal. Please try again.',
    noGoals: 'No Savings Goals',
    noGoalsMessage: 'You have not added any savings goals yet.',
    title: 'Title',
    titlePlaceholder: 'Enter goal title',
    description: 'Description',
    descriptionPlaceholder: 'Enter goal description',
    targetAmount: 'Target Amount',
    currentAmount: 'Current Amount',
    startDate: 'Start Date',
    targetDate: 'Target Date',
    progress: 'Progress',
    remainingAmount: 'Remaining Amount',
    daysRemaining: 'Days Remaining',
    isOnTrack: 'On Track',
    notOnTrack: 'Behind Schedule',
    priority: 'Priority',
    status: 'Status',
    autoContribute: 'Auto Contribute',
    autoContributeAmount: 'Auto Contribution Amount',
    autoContributeFrequency: 'Auto Contribution Frequency',
    isShared: 'Shared with Family',
    personal: 'Personal',
    shared: 'Shared',
    owner: 'Owner',
    createdAt: 'Created At',
    updatedAt: 'Updated At',
    
    // Contributions
    contributions: 'Contributions',
    contribution: 'Contribution',
    addContribution: 'Add Contribution',
    editContribution: 'Edit Contribution',
    deleteContribution: 'Delete Contribution',
    deleteContributionConfirmation: 'Are you sure you want to delete this contribution?',
    deleteContributionError: 'Failed to delete contribution. Please try again.',
    contributionAmount: 'Contribution Amount',
    contributionDate: 'Contribution Date',
    contributionNotes: 'Notes',
    contributionNotesPlaceholder: 'Enter notes for this contribution',
    noContributions: 'No Contributions',
    noContributionsMessage: 'No contributions have been made to this goal yet.',
    
    // Forecast
    forecast: 'Forecast',
    forecastTimeline: 'Forecast Timeline',
    projectedCompletionDate: 'Projected Completion Date',
    monthlyContributionNeeded: 'Monthly Contribution Needed',
    weeklyContributionNeeded: 'Weekly Contribution Needed',
    isAchievable: 'Is Achievable',
    notAchievable: 'Not Achievable',
    timelineData: 'Timeline Data',
    
    // Status options
    statusOptions: {
      active: 'Active',
      achieved: 'Achieved',
      abandoned: 'Abandoned'
    },
  },
  
  wishlist: {
    wishlist: 'Wishlist',
    items: 'Wishlist Items',
    item: 'Wishlist Item',
    itemDetails: 'Item Details',
    addItem: 'Add Item',
    createItem: 'Create Item',
    editItem: 'Edit Item',
    updateItem: 'Update Item',
    deleteItem: 'Delete Item',
    deleteItemConfirmation: 'Are you sure you want to delete this wishlist item?',
    deleteItemError: 'Failed to delete wishlist item. Please try again.',
    itemNotFound: 'Wishlist item not found',
    updateItemError: 'Failed to update wishlist item. Please try again.',
    noItems: 'No Wishlist Items',
    noItemsMessage: 'You have not added any wishlist items yet.',
    title: 'Title',
    titlePlaceholder: 'Enter item title',
    description: 'Description',
    descriptionPlaceholder: 'Enter item description',
    estimatedPrice: 'Estimated Price',
    productUrl: 'Product URL',
    productUrlPlaceholder: 'Enter product URL',
    imageUrl: 'Image URL',
    imageUrlPlaceholder: 'Enter image URL',
    addedDate: 'Added Date',
    targetPurchaseDate: 'Target Purchase Date',
    priority: 'Priority',
    status: 'Status',
    isShared: 'Shared with Family',
    personal: 'Personal',
    shared: 'Shared',
    owner: 'Owner',
    createdAt: 'Created At',
    updatedAt: 'Updated At',
    
    // Link to savings goal
    linkedSavingsGoal: 'Linked Savings Goal',
    linkToGoal: 'Link to Savings Goal',
    unlinkFromGoal: 'Unlink from Savings Goal',
    selectGoal: 'Select a Savings Goal',
    noGoalsAvailable: 'No savings goals available to link',
    linkSuccess: 'Successfully linked to savings goal',
    unlinkSuccess: 'Successfully unlinked from savings goal',
    linkError: 'Failed to link to savings goal',
    unlinkError: 'Failed to unlink from savings goal',
    
    // Status options
    statusOptions: {
      pending: 'Pending',
      purchased: 'Purchased',
      abandoned: 'Abandoned'
    },
  }
};

// Nepali translations for savings goals and wishlist
export const neSavingsTranslations = {
  savings: {
    savings: 'बचत',
    dashboard: 'बचत ड्यासबोर्ड',
    goals: 'बचत लक्ष्यहरू',
    goal: 'बचत लक्ष्य',
    goalDetails: 'लक्ष्य विवरण',
    addGoal: 'लक्ष्य थप्नुहोस्',
    createGoal: 'लक्ष्य सिर्जना गर्नुहोस्',
    editGoal: 'लक्ष्य सम्पादन गर्नुहोस्',
    updateGoal: 'लक्ष्य अपडेट गर्नुहोस्',
    deleteGoal: 'लक्ष्य मेटाउनुहोस्',
    deleteGoalConfirmation: 'के तपाईं यो बचत लक्ष्य मेटाउन निश्चित हुनुहुन्छ?',
    deleteGoalError: 'बचत लक्ष्य मेटाउन असफल भयो। कृपया फेरि प्रयास गर्नुहोस्।',
    goalNotFound: 'बचत लक्ष्य फेला परेन',
    updateGoalError: 'बचत लक्ष्य अपडेट गर्न असफल भयो। कृपया फेरि प्रयास गर्नुहोस्।',
    noGoals: 'कुनै बचत लक्ष्य छैन',
    noGoalsMessage: 'तपाईंले अहिलेसम्म कुनै बचत लक्ष्य थप्नुभएको छैन।',
    title: 'शीर्षक',
    titlePlaceholder: 'लक्ष्य शीर्षक प्रविष्ट गर्नुहोस्',
    description: 'विवरण',
    descriptionPlaceholder: 'लक्ष्य विवरण प्रविष्ट गर्नुहोस्',
    targetAmount: 'लक्ष्य रकम',
    currentAmount: 'हालको रकम',
    startDate: 'सुरु मिति',
    targetDate: 'लक्ष्य मिति',
    progress: 'प्रगति',
    remainingAmount: 'बाँकी रकम',
    daysRemaining: 'बाँकी दिनहरू',
    isOnTrack: 'ट्र्याकमा छ',
    notOnTrack: 'तालिका पछाडि छ',
    priority: 'प्राथमिकता',
    status: 'स्थिति',
    autoContribute: 'स्वत: योगदान',
    autoContributeAmount: 'स्वत: योगदान रकम',
    autoContributeFrequency: 'स्वत: योगदान आवृत्ति',
    isShared: 'परिवारसँग साझा गरिएको',
    personal: 'व्यक्तिगत',
    shared: 'साझा गरिएको',
    owner: 'मालिक',
    createdAt: 'सिर्जना गरिएको मिति',
    updatedAt: 'अपडेट गरिएको मिति',
    
    // Contributions
    contributions: 'योगदानहरू',
    contribution: 'योगदान',
    addContribution: 'योगदान थप्नुहोस्',
    editContribution: 'योगदान सम्पादन गर्नुहोस्',
    deleteContribution: 'योगदान मेटाउनुहोस्',
    deleteContributionConfirmation: 'के तपाईं यो योगदान मेटाउन निश्चित हुनुहुन्छ?',
    deleteContributionError: 'योगदान मेटाउन असफल भयो। कृपया फेरि प्रयास गर्नुहोस्।',
    contributionAmount: 'योगदान रकम',
    contributionDate: 'योगदान मिति',
    contributionNotes: 'नोटहरू',
    contributionNotesPlaceholder: 'यस योगदानको लागि नोटहरू प्रविष्ट गर्नुहोस्',
    noContributions: 'कुनै योगदान छैन',
    noContributionsMessage: 'यस लक्ष्यमा अहिलेसम्म कुनै योगदान गरिएको छैन।',
    
    // Forecast
    forecast: 'पूर्वानुमान',
    forecastTimeline: 'पूर्वानुमान समयरेखा',
    projectedCompletionDate: 'अनुमानित पूरा हुने मिति',
    monthlyContributionNeeded: 'आवश्यक मासिक योगदान',
    weeklyContributionNeeded: 'आवश्यक साप्ताहिक योगदान',
    isAchievable: 'प्राप्त गर्न सकिने',
    notAchievable: 'प्राप्त गर्न नसकिने',
    timelineData: 'समयरेखा डाटा',
    
    // Status options
    statusOptions: {
      active: 'सक्रिय',
      achieved: 'प्राप्त गरिएको',
      abandoned: 'परित्याग गरिएको'
    },
  },
  
  wishlist: {
    wishlist: 'इच्छा सूची',
    items: 'इच्छा सूची वस्तुहरू',
    item: 'इच्छा सूची वस्तु',
    itemDetails: 'वस्तु विवरण',
    addItem: 'वस्तु थप्नुहोस्',
    createItem: 'वस्तु सिर्जना गर्नुहोस्',
    editItem: 'वस्तु सम्पादन गर्नुहोस्',
    updateItem: 'वस्तु अपडेट गर्नुहोस्',
    deleteItem: 'वस्तु मेटाउनुहोस्',
    deleteItemConfirmation: 'के तपाईं यो इच्छा सूची वस्तु मेटाउन निश्चित हुनुहुन्छ?',
    deleteItemError: 'इच्छा सूची वस्तु मेटाउन असफल भयो। कृपया फेरि प्रयास गर्नुहोस्।',
    itemNotFound: 'इच्छा सूची वस्तु फेला परेन',
    updateItemError: 'इच्छा सूची वस्तु अपडेट गर्न असफल भयो। कृपया फेरि प्रयास गर्नुहोस्।',
    noItems: 'कुनै इच्छा सूची वस्तु छैन',
    noItemsMessage: 'तपाईंले अहिलेसम्म कुनै इच्छा सूची वस्तु थप्नुभएको छैन।',
    title: 'शीर्षक',
    titlePlaceholder: 'वस्तु शीर्षक प्रविष्ट गर्नुहोस्',
    description: 'विवरण',
    descriptionPlaceholder: 'वस्तु विवरण प्रविष्ट गर्नुहोस्',
    estimatedPrice: 'अनुमानित मूल्य',
    productUrl: 'उत्पाद URL',
    productUrlPlaceholder: 'उत्पाद URL प्रविष्ट गर्नुहोस्',
    imageUrl: 'छवि URL',
    imageUrlPlaceholder: 'छवि URL प्रविष्ट गर्नुहोस्',
    addedDate: 'थपिएको मिति',
    targetPurchaseDate: 'लक्ष्य खरिद मिति',
    priority: 'प्राथमिकता',
    status: 'स्थिति',
    isShared: 'परिवारसँग साझा गरिएको',
    personal: 'व्यक्तिगत',
    shared: 'साझा गरिएको',
    owner: 'मालिक',
    createdAt: 'सिर्जना गरिएको मिति',
    updatedAt: 'अपडेट गरिएको मिति',
    
    // Link to savings goal
    linkedSavingsGoal: 'लिङ्क गरिएको बचत लक्ष्य',
    linkToGoal: 'बचत लक्ष्यमा लिङ्क गर्नुहोस्',
    unlinkFromGoal: 'बचत लक्ष्यबाट अनलिङ्क गर्नुहोस्',
    selectGoal: 'बचत लक्ष्य छान्नुहोस्',
    noGoalsAvailable: 'लिङ्क गर्नको लागि कुनै बचत लक्ष्य उपलब्ध छैन',
    linkSuccess: 'बचत लक्ष्यमा सफलतापूर्वक लिङ्क गरियो',
    unlinkSuccess: 'बचत लक्ष्यबाट सफलतापूर्वक अनलिङ्क गरियो',
    linkError: 'बचत लक्ष्यमा लिङ्क गर्न असफल भयो',
    unlinkError: 'बचत लक्ष्यबाट अनलिङ्क गर्न असफल भयो',
    
    // Status options
    statusOptions: {
      pending: 'प्रतीक्षारत',
      purchased: 'खरिद गरिएको',
      abandoned: 'परित्याग गरिएको'
    },
  }
};
