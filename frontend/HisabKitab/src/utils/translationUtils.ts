import i18n from '../utils/i18n';

/**
 * Safely translates a key, preventing raw keys from being displayed
 * @param key The translation key
 * @param options Translation options
 * @returns The translated string or a fallback
 */
export const safeTranslate = (
  key: string,
  options?: { fallback?: string; values?: Record<string, any> }
): string => {
  const { fallback, values } = options || {};

  // Check if the key exists in the current language
  const keyExists = i18n.exists(key);

  // Get the translation
  const translation = keyExists ? i18n.t(key, values) : null;

  // If the translation is the same as the key, it means the translation is missing
  const isMissingTranslation = translation === key;

  // Format the key for display if needed (convert 'namespace.key' to 'Key')
  const formatKeyForDisplay = (k: string): string => {
    const parts = k.split('.');
    const lastPart = parts[parts.length - 1];
    return lastPart
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/^./, (str) => str.toUpperCase()); // Capitalize first letter
  };

  // Determine what text to display
  return isMissingTranslation
    ? fallback || formatKeyForDisplay(key)
    : translation || '';
};

/**
 * Validates if a translation key exists in the current language
 * @param key The translation key to validate
 * @returns True if the key exists, false otherwise
 */
export const validateTranslationKey = (key: string): boolean => {
  return i18n.exists(key);
};

/**
 * Gets all missing translation keys in a component
 * @param keys Array of translation keys to check
 * @returns Array of missing translation keys
 */
export const getMissingTranslationKeys = (keys: string[]): string[] => {
  return keys.filter(key => !validateTranslationKey(key));
};

/**
 * Logs missing translation keys to the console
 * @param componentName The name of the component
 * @param keys Array of translation keys to check
 */
export const logMissingTranslationKeys = (componentName: string, keys: string[]): void => {
  const missingKeys = getMissingTranslationKeys(keys);
  if (missingKeys.length > 0) {
    console.warn(`Missing translation keys in ${componentName}:`, missingKeys);
  }
};

export default {
  safeTranslate,
  validateTranslationKey,
  getMissingTranslationKeys,
  logMissingTranslationKeys,
};
