/**
 * Comprehensive date utility functions for consistent date handling across the application
 */

/**
 * Converts a date to ISO string format for sending to the backend
 * @param date The date to convert
 * @returns ISO string representation of the date, or undefined if date is null/undefined
 */
export const formatDateForBackend = (date: Date | string | null | undefined): string | undefined => {
  if (!date) return undefined;

  // If it's a string, try to convert it to a Date first
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Check if date is valid
  if (isNaN(dateObj.getTime())) {
    console.warn('Invalid date provided to formatDateForBackend:', date);
    return undefined;
  }

  return dateObj.toISOString();
};

/**
 * Parses a date string from the backend into a Date object
 * @param dateString The date string from the backend
 * @returns Date object, or undefined if dateString is invalid
 */
export const parseDateFromBackend = (dateString: string | null | undefined): Date | undefined => {
  if (!dateString) return undefined;

  try {
    const date = new Date(dateString);
    // Check if date is valid
    if (isNaN(date.getTime())) {
      console.warn('Invalid date string from backend:', dateString);
      return undefined;
    }
    return date;
  } catch (error) {
    console.error('Error parsing date:', error);
    return undefined;
  }
};

/**
 * Formats a date for display in the UI
 * @param date The date to format
 * @param format The format to use (short, medium, long)
 * @returns Formatted date string
 */
export const formatDateForDisplay = (
  date: Date | string | null | undefined,
  format: 'short' | 'medium' | 'long' = 'medium'
): string => {
  if (!date) return '';

  const dateObj = typeof date === 'string' ? new Date(date) : date as Date;

  // Check if date is valid
  if (!dateObj || isNaN(dateObj.getTime())) {
    console.warn('Invalid date provided to formatDateForDisplay:', date);
    return '';
  }

  try {
    switch (format) {
      case 'short':
        return dateObj.toLocaleDateString();
      case 'long':
        return dateObj.toLocaleDateString(undefined, {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      case 'medium':
      default:
        return dateObj.toLocaleDateString(undefined, {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
    }
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

/**
 * Formats a date and time for display in the UI
 * @param date The date to format
 * @returns Formatted date and time string
 */
export const formatDateTimeForDisplay = (
  date: Date | string | null | undefined
): string => {
  if (!date) return '';

  const dateObj = typeof date === 'string' ? new Date(date) : date as Date;

  // Check if date is valid
  if (!dateObj || isNaN(dateObj.getTime())) {
    console.warn('Invalid date provided to formatDateTimeForDisplay:', date);
    return '';
  }

  try {
    return dateObj.toLocaleString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('Error formatting date time:', error);
    return '';
  }
};

/**
 * Formats a relative time (e.g., "2 hours ago")
 * @param date The date to format
 * @returns Relative time string
 */
export const formatRelativeTime = (
  date: Date | string | null | undefined
): string => {
  if (!date) return '';

  const dateObj = typeof date === 'string' ? new Date(date) : date as Date;

  // Check if date is valid
  if (!dateObj || isNaN(dateObj.getTime())) {
    console.warn('Invalid date provided to formatRelativeTime:', date);
    return '';
  }

  try {
    const now = new Date();
    const diffMs = now.getTime() - dateObj.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    const diffMonth = Math.floor(diffDay / 30);
    const diffYear = Math.floor(diffMonth / 12);

    if (diffSec < 60) {
      return 'just now';
    } else if (diffMin < 60) {
      return `${diffMin} minute${diffMin === 1 ? '' : 's'} ago`;
    } else if (diffHour < 24) {
      return `${diffHour} hour${diffHour === 1 ? '' : 's'} ago`;
    } else if (diffDay < 30) {
      return `${diffDay} day${diffDay === 1 ? '' : 's'} ago`;
    } else if (diffMonth < 12) {
      return `${diffMonth} month${diffMonth === 1 ? '' : 's'} ago`;
    } else {
      return `${diffYear} year${diffYear === 1 ? '' : 's'} ago`;
    }
  } catch (error) {
    console.error('Error formatting relative time:', error);
    return '';
  }
};

/**
 * Ensures a date is a Date object
 * @param date The date to ensure
 * @returns Date object, or undefined if date is invalid
 */
export const ensureDate = (date: Date | string | null | undefined): Date | undefined => {
  if (!date) return undefined;

  try {
    if (date instanceof Date) {
      return isNaN(date.getTime()) ? undefined : date;
    }

    if (typeof date === 'string') {
      const parsedDate = new Date(date);
      return isNaN(parsedDate.getTime()) ? undefined : parsedDate;
    }

    return undefined;
  } catch (error) {
    console.error('Error ensuring date:', error);
    return undefined;
  }
};

/**
 * Converts any date representation to an ISO string
 * @param date The date to convert
 * @returns ISO string, or undefined if date is invalid
 */
export const toISOString = (date: Date | string | null | undefined): string | undefined => {
  const dateObj = ensureDate(date);
  return dateObj?.toISOString();
};

export default {
  formatDateForBackend,
  parseDateFromBackend,
  formatDateForDisplay,
  formatDateTimeForDisplay,
  formatRelativeTime,
  ensureDate,
  toISOString,
};