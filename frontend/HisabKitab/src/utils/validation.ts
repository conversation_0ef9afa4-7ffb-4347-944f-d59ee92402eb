import { TFunction } from 'i18next';

/**
 * Validates a required field
 * @param value The value to validate
 * @param t The translation function
 * @returns Error message if invalid, empty string if valid
 */
export const validateRequired = (value: any, t: TFunction): string => {
  if (value === undefined || value === null || value === '') {
    return t('validation.required');
  }
  return '';
};

/**
 * Validates a string field has a minimum length
 * @param value The value to validate
 * @param minLength The minimum length
 * @param t The translation function
 * @returns Error message if invalid, empty string if valid
 */
export const validateMinLength = (value: string, minLength: number, t: TFunction): string => {
  if (!value || value.length < minLength) {
    return t('validation.minLength', { length: minLength });
  }
  return '';
};

/**
 * Validates a string field has a maximum length
 * @param value The value to validate
 * @param maxLength The maximum length
 * @param t The translation function
 * @returns Error message if invalid, empty string if valid
 */
export const validateMaxLength = (value: string, maxLength: number, t: TFunction): string => {
  if (value && value.length > maxLength) {
    return t('validation.maxLength', { length: maxLength });
  }
  return '';
};

/**
 * Validates an email address
 * @param value The value to validate
 * @param t The translation function
 * @returns Error message if invalid, empty string if valid
 */
export const validateEmail = (value: string, t: TFunction): string => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!value || !emailRegex.test(value)) {
    return t('validation.email');
  }
  return '';
};

/**
 * Validates a numeric value
 * @param value The value to validate
 * @param t The translation function
 * @returns Error message if invalid, empty string if valid
 */
export const validateNumber = (value: string, t: TFunction): string => {
  if (isNaN(Number(value))) {
    return t('validation.number');
  }
  return '';
};

/**
 * Validates a positive number
 * @param value The value to validate
 * @param t The translation function
 * @returns Error message if invalid, empty string if valid
 */
export const validatePositiveNumber = (value: string, t: TFunction): string => {
  const numberError = validateNumber(value, t);
  if (numberError) {
    return numberError;
  }

  if (Number(value) <= 0) {
    return t('validation.positive');
  }
  return '';
};

/**
 * Validates a field with multiple validation rules
 * @param value The value to validate
 * @param validations Array of validation functions
 * @returns The first error message, or empty string if valid
 */
export const validateField = (
  value: any,
  validations: ((value: any) => string)[]
): string => {
  for (const validation of validations) {
    const error = validation(value);
    if (error) {
      return error;
    }
  }
  return '';
};

/**
 * Creates a required field validator
 * @param t The translation function
 * @returns A validation function
 */
export const requiredValidator = (t: TFunction) => (value: any): string => {
  return validateRequired(value, t);
};

/**
 * Creates a minimum length validator
 * @param minLength The minimum length
 * @param t The translation function
 * @returns A validation function
 */
export const minLengthValidator = (minLength: number, t: TFunction) => (value: string): string => {
  return validateMinLength(value, minLength, t);
};

/**
 * Creates a maximum length validator
 * @param maxLength The maximum length
 * @param t The translation function
 * @returns A validation function
 */
export const maxLengthValidator = (maxLength: number, t: TFunction) => (value: string): string => {
  return validateMaxLength(value, maxLength, t);
};

/**
 * Creates an email validator
 * @param t The translation function
 * @returns A validation function
 */
export const emailValidator = (t: TFunction) => (value: string): string => {
  return validateEmail(value, t);
};

/**
 * Creates a number validator
 * @param t The translation function
 * @returns A validation function
 */
export const numberValidator = (t: TFunction) => (value: string): string => {
  return validateNumber(value, t);
};

/**
 * Creates a positive number validator
 * @param t The translation function
 * @returns A validation function
 */
export const positiveNumberValidator = (t: TFunction) => (value: string): string => {
  return validatePositiveNumber(value, t);
};

/**
 * Validates a non-negative number (zero or positive)
 * @param value The value to validate
 * @param t The translation function
 * @returns Error message if invalid, empty string if valid
 */
export const validateNonNegativeNumber = (value: string, t: TFunction): string => {
  const numberError = validateNumber(value, t);
  if (numberError) {
    return numberError;
  }

  if (Number(value) < 0) {
    return t('validation.nonNegative');
  }
  return '';
};

/**
 * Creates a non-negative number validator
 * @param t The translation function
 * @returns A validation function
 */
export const nonNegativeNumberValidator = (t: TFunction) => (value: string): string => {
  return validateNonNegativeNumber(value, t);
};

/**
 * Validates a date
 * @param value The value to validate
 * @param t The translation function
 * @returns Error message if invalid, empty string if valid
 */
export const validateDate = (value: any, t: TFunction): string => {
  if (!value) {
    return t('validation.required');
  }

  let date: Date;

  if (value instanceof Date) {
    date = value;
  } else if (typeof value === 'string' || typeof value === 'number') {
    date = new Date(value);
  } else {
    return t('validation.date');
  }

  if (isNaN(date.getTime())) {
    return t('validation.date');
  }

  return '';
};

/**
 * Creates a date validator
 * @param t The translation function
 * @returns A validation function
 */
export const dateValidator = (t: TFunction) => (value: any): string => {
  return validateDate(value, t);
};

/**
 * Validates a date is in the future
 * @param value The value to validate
 * @param t The translation function
 * @returns Error message if invalid, empty string if valid
 */
export const validateFutureDate = (value: any, t: TFunction): string => {
  const dateError = validateDate(value, t);
  if (dateError) {
    return dateError;
  }

  const date = new Date(value);
  const now = new Date();

  // Set time to midnight for date comparison
  now.setHours(0, 0, 0, 0);

  if (date < now) {
    return t('validation.futureDate');
  }

  return '';
};

/**
 * Creates a future date validator
 * @param t The translation function
 * @returns A validation function
 */
export const futureDateValidator = (t: TFunction) => (value: any): string => {
  return validateFutureDate(value, t);
};

/**
 * Validates a pattern match
 * @param value The value to validate
 * @param pattern The regular expression pattern
 * @param t The translation function
 * @param errorKey The translation key for the error message
 * @returns Error message if invalid, empty string if valid
 */
export const validatePattern = (
  value: string,
  pattern: RegExp,
  t: TFunction,
  errorKey: string = 'validation.pattern'
): string => {
  if (!value) {
    return '';
  }

  if (!pattern.test(value)) {
    return t(errorKey);
  }

  return '';
};

/**
 * Creates a pattern validator
 * @param pattern The regular expression pattern
 * @param t The translation function
 * @param errorKey The translation key for the error message
 * @returns A validation function
 */
export const patternValidator = (
  pattern: RegExp,
  t: TFunction,
  errorKey: string = 'validation.pattern'
) => (value: string): string => {
  return validatePattern(value, pattern, t, errorKey);
};
