// English translations for feature management
export const enFeatureTranslations = {
  features: {
    title: 'Feature Management',
    featureManagement: 'Feature Management',
    enableFeature: 'Enable Feature',
    disableFeature: 'Disable Feature',
    enabledFor: 'Enabled For',
    allUsers: 'All Users',
    betaUsers: 'Beta Users',
    specificUsers: 'Specific Users',
    selectEnabledFor: 'Select Enabled For',
    lastUpdated: 'Last updated: {{date}}',
    enabledSuccess: 'Feature "{{name}}" enabled successfully',
    disabledSuccess: 'Feature "{{name}}" disabled successfully',
    updatedSuccess: 'Feature "{{name}}" updated successfully',
    adminRequired: 'Admin Required',
    adminRequiredMessage: 'You need to be a platform admin to manage features.',
    viewOnlyMode: 'You are in view-only mode. Only platform admins can manage features.',
    noFeatures: 'No Features',
    noFeaturesMessage: 'No features have been defined yet.',
    notAvailable: 'Feature "{{feature}}" is not available',
    featureFlags: 'Feature Flags',
    featureToggle: 'Feature Toggle',
    featureSettings: 'Feature Settings',
    featureDetails: 'Feature Details',
    createFeature: 'Create Feature',
    editFeature: 'Edit Feature',
    deleteFeature: 'Delete Feature',
    deleteConfirmTitle: 'Delete Feature',
    deleteConfirmMessage: 'Are you sure you want to delete this feature?',
    deleteSuccess: 'Feature deleted successfully',
    createSuccess: 'Feature created successfully',
    updateSuccess: 'Feature updated successfully',
    name: 'Name',
    namePlaceholder: 'Enter feature name',
    nameRequired: 'Name is required',
    description: 'Description',
    descriptionPlaceholder: 'Enter feature description',
    descriptionRequired: 'Description is required',
    isEnabled: 'Is Enabled',
    enabledForUsers: 'Enabled For Users',
    selectUsers: 'Select Users',
    searchUsers: 'Search users',
    noUsersFound: 'No users found',
    addUser: 'Add User',
    removeUser: 'Remove User',
    userAdded: 'User added successfully',
    userRemoved: 'User removed successfully',
    featureEnabled: 'Feature enabled',
    featureDisabled: 'Feature disabled',
    featureEnabledMessage: 'Feature "{{name}}" has been enabled.',
    featureDisabledMessage: 'Feature "{{name}}" has been disabled.',
    featureUpdatedMessage: 'Feature "{{name}}" has been updated.',
    featureCreatedMessage: 'Feature "{{name}}" has been created.',
    featureDeletedMessage: 'Feature "{{name}}" has been deleted.',
    featureNotFound: 'Feature not found',
    featureNotFoundMessage: 'The requested feature could not be found.',
    featureAlreadyExists: 'Feature already exists',
    featureAlreadyExistsMessage: 'A feature with this name already exists.',
    featureNameInvalid: 'Feature name invalid',
    featureNameInvalidMessage: 'Feature name can only contain letters, numbers, and underscores.',
    featureNameTooLong: 'Feature name too long',
    featureNameTooLongMessage: 'Feature name cannot exceed 50 characters.',
    featureDescriptionTooLong: 'Feature description too long',
    featureDescriptionTooLongMessage: 'Feature description cannot exceed 255 characters.',
    featureEnabledForInvalid: 'Enabled for value invalid',
    featureEnabledForInvalidMessage: 'Enabled for value must be "all", "beta", or a list of user IDs.',
    featureEnabledForTooLong: 'Enabled for value too long',
    featureEnabledForTooLongMessage: 'Enabled for value cannot exceed 1000 characters.',
    featureEnabledForTooManyUsers: 'Too many users',
    featureEnabledForTooManyUsersMessage: 'Cannot enable for more than 100 specific users.',
    featureEnabledForUserNotFound: 'User not found',
    featureEnabledForUserNotFoundMessage: 'One or more of the specified users could not be found.',
    featureEnabledForUserAlreadyAdded: 'User already added',
    featureEnabledForUserAlreadyAddedMessage: 'This user has already been added to the enabled users list.',
    featureEnabledForUserNotAdded: 'User not added',
    featureEnabledForUserNotAddedMessage: 'This user has not been added to the enabled users list.',
  }
};

// Nepali translations for feature management
export const neFeatureTranslations = {
  features: {
    title: 'सुविधा व्यवस्थापन',
    featureManagement: 'सुविधा व्यवस्थापन',
    enableFeature: 'सुविधा सक्षम गर्नुहोस्',
    disableFeature: 'सुविधा अक्षम गर्नुहोस्',
    enabledFor: 'सक्षम गरिएको',
    allUsers: 'सबै प्रयोगकर्ताहरू',
    betaUsers: 'बेटा प्रयोगकर्ताहरू',
    specificUsers: 'विशिष्ट प्रयोगकर्ताहरू',
    selectEnabledFor: 'सक्षम गरिएको चयन गर्नुहोस्',
    lastUpdated: 'अन्तिम अपडेट: {{date}}',
    enabledSuccess: 'सुविधा "{{name}}" सफलतापूर्वक सक्षम गरियो',
    disabledSuccess: 'सुविधा "{{name}}" सफलतापूर्वक अक्षम गरियो',
    updatedSuccess: 'सुविधा "{{name}}" सफलतापूर्वक अपडेट गरियो',
    adminRequired: 'प्रशासक आवश्यक',
    adminRequiredMessage: 'सुविधाहरू व्यवस्थापन गर्न तपाईंलाई प्लेटफर्म प्रशासक हुन आवश्यक छ।',
    viewOnlyMode: 'तपाईं हेर्ने-मात्र मोडमा हुनुहुन्छ। केवल प्लेटफर्म प्रशासकहरूले सुविधाहरू व्यवस्थापन गर्न सक्छन्।',
    noFeatures: 'कुनै सुविधाहरू छैनन्',
    noFeaturesMessage: 'अहिलेसम्म कुनै सुविधाहरू परिभाषित गरिएका छैनन्।',
    notAvailable: 'सुविधा "{{feature}}" उपलब्ध छैन',
    featureFlags: 'सुविधा झण्डाहरू',
    featureToggle: 'सुविधा टगल',
    featureSettings: 'सुविधा सेटिङहरू',
    featureDetails: 'सुविधा विवरण',
    createFeature: 'सुविधा सिर्जना गर्नुहोस्',
    editFeature: 'सुविधा सम्पादन गर्नुहोस्',
    deleteFeature: 'सुविधा मेटाउनुहोस्',
    deleteConfirmTitle: 'सुविधा मेटाउनुहोस्',
    deleteConfirmMessage: 'के तपाईं यो सुविधा मेटाउन निश्चित हुनुहुन्छ?',
    deleteSuccess: 'सुविधा सफलतापूर्वक मेटाइयो',
    createSuccess: 'सुविधा सफलतापूर्वक सिर्जना गरियो',
    updateSuccess: 'सुविधा सफलतापूर्वक अपडेट गरियो',
    name: 'नाम',
    namePlaceholder: 'सुविधा नाम प्रविष्ट गर्नुहोस्',
    nameRequired: 'नाम आवश्यक छ',
    description: 'विवरण',
    descriptionPlaceholder: 'सुविधा विवरण प्रविष्ट गर्नुहोस्',
    descriptionRequired: 'विवरण आवश्यक छ',
    isEnabled: 'सक्षम छ',
    enabledForUsers: 'प्रयोगकर्ताहरूका लागि सक्षम',
    selectUsers: 'प्रयोगकर्ताहरू चयन गर्नुहोस्',
    searchUsers: 'प्रयोगकर्ताहरू खोज्नुहोस्',
    noUsersFound: 'कुनै प्रयोगकर्ताहरू फेला परेनन्',
    addUser: 'प्रयोगकर्ता थप्नुहोस्',
    removeUser: 'प्रयोगकर्ता हटाउनुहोस्',
    userAdded: 'प्रयोगकर्ता सफलतापूर्वक थपियो',
    userRemoved: 'प्रयोगकर्ता सफलतापूर्वक हटाइयो',
    featureEnabled: 'सुविधा सक्षम गरियो',
    featureDisabled: 'सुविधा अक्षम गरियो',
    featureEnabledMessage: 'सुविधा "{{name}}" सक्षम गरिएको छ।',
    featureDisabledMessage: 'सुविधा "{{name}}" अक्षम गरिएको छ।',
    featureUpdatedMessage: 'सुविधा "{{name}}" अपडेट गरिएको छ।',
    featureCreatedMessage: 'सुविधा "{{name}}" सिर्जना गरिएको छ।',
    featureDeletedMessage: 'सुविधा "{{name}}" मेटाइएको छ।',
    featureNotFound: 'सुविधा फेला परेन',
    featureNotFoundMessage: 'अनुरोध गरिएको सुविधा फेला पार्न सकिएन।',
    featureAlreadyExists: 'सुविधा पहिले नै अवस्थित छ',
    featureAlreadyExistsMessage: 'यो नामको सुविधा पहिले नै अवस्थित छ।',
    featureNameInvalid: 'सुविधा नाम अमान्य',
    featureNameInvalidMessage: 'सुविधा नाममा केवल अक्षरहरू, संख्याहरू, र अन्डरस्कोरहरू मात्र समावेश गर्न सकिन्छ।',
    featureNameTooLong: 'सुविधा नाम धेरै लामो',
    featureNameTooLongMessage: 'सुविधा नाम 50 वर्णहरू भन्दा बढी हुन सक्दैन।',
    featureDescriptionTooLong: 'सुविधा विवरण धेरै लामो',
    featureDescriptionTooLongMessage: 'सुविधा विवरण 255 वर्णहरू भन्दा बढी हुन सक्दैन।',
    featureEnabledForInvalid: 'सक्षम गरिएको मान अमान्य',
    featureEnabledForInvalidMessage: 'सक्षम गरिएको मान "all", "beta", वा प्रयोगकर्ता आईडीहरूको सूची हुनुपर्छ।',
    featureEnabledForTooLong: 'सक्षम गरिएको मान धेरै लामो',
    featureEnabledForTooLongMessage: 'सक्षम गरिएको मान 1000 वर्णहरू भन्दा बढी हुन सक्दैन।',
    featureEnabledForTooManyUsers: 'धेरै प्रयोगकर्ताहरू',
    featureEnabledForTooManyUsersMessage: '100 भन्दा बढी विशिष्ट प्रयोगकर्ताहरूका लागि सक्षम गर्न सकिँदैन।',
    featureEnabledForUserNotFound: 'प्रयोगकर्ता फेला परेन',
    featureEnabledForUserNotFoundMessage: 'तोकिएका एक वा बढी प्रयोगकर्ताहरू फेला पार्न सकिएन।',
    featureEnabledForUserAlreadyAdded: 'प्रयोगकर्ता पहिले नै थपिएको छ',
    featureEnabledForUserAlreadyAddedMessage: 'यो प्रयोगकर्ता पहिले नै सक्षम प्रयोगकर्ताहरूको सूचीमा थपिएको छ।',
    featureEnabledForUserNotAdded: 'प्रयोगकर्ता थपिएको छैन',
    featureEnabledForUserNotAddedMessage: 'यो प्रयोगकर्ता सक्षम प्रयोगकर्ताहरूको सूचीमा थपिएको छैन।',
  }
};
