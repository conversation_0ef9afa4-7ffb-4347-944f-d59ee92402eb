/**
 * Utility functions for formatting dates, currency, and other values
 */

import { format, isValid, formatDistanceToNow } from 'date-fns';
import i18n from './i18n';

/**
 * Format a date to a string
 * @param date The date to format
 * @param formatString Optional format string (default: 'yyyy-MM-dd')
 * @returns Formatted date string
 */
export const formatDate = (date: Date | string, formatString: string = 'yyyy-MM-dd'): string => {
  if (!date) return '';

  const dateObj = typeof date === 'string' ? new Date(date) : date;

  if (!isValid(dateObj)) {
    return '';
  }

  return format(dateObj, formatString);
};

/**
 * Format a date and time to a string
 * @param date The date to format
 * @param formatString Optional format string (default: 'yyyy-MM-dd HH:mm')
 * @returns Formatted date and time string
 */
export const formatDateTime = (date: Date | string, formatString: string = 'yyyy-MM-dd HH:mm'): string => {
  return formatDate(date, formatString);
};

/**
 * Format a time to a string
 * @param date The date to format
 * @param formatString Optional format string (default: 'HH:mm')
 * @returns Formatted time string
 */
export const formatTime = (date: Date | string, formatString: string = 'HH:mm'): string => {
  return formatDate(date, formatString);
};

/**
 * Format a currency value
 * @param amount The amount to format
 * @param currency Optional currency code (default: 'NPR')
 * @param locale Optional locale (default: current app locale)
 * @returns Formatted currency string
 */
export const formatCurrency = (
  amount: number | string,
  currency: string = 'NPR',
  locale?: string
): string => {
  if (amount === null || amount === undefined) return '';

  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  if (isNaN(numericAmount)) {
    return '';
  }

  // Use the app's current locale if not specified
  const currentLocale = locale || i18n.language || 'en';

  // Map our locale codes to standard locale codes
  const localeMap: Record<string, string> = {
    en: 'en-US',
    ne: 'ne-NP',
  };

  const mappedLocale = localeMap[currentLocale] || 'en-US';

  try {
    // Handle special case for NPR (Nepalese Rupee) which might not be supported in all browsers
    if (currency === 'NPR') {
      // Format with 2 decimal places and add NPR symbol
      return `NPR ${numericAmount.toFixed(2)}`;
    }

    return new Intl.NumberFormat(mappedLocale, {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(numericAmount);
  } catch (error) {
    // Fallback to basic formatting if Intl is not supported
    console.warn(`Error formatting currency: ${error}`);
    return `${currency} ${numericAmount.toFixed(2)}`;
  }
};

/**
 * Format a number with thousands separators
 * @param value The number to format
 * @param locale Optional locale (default: current app locale)
 * @returns Formatted number string
 */
export const formatNumber = (value: number | string, locale?: string): string => {
  if (value === null || value === undefined) return '';

  const numericValue = typeof value === 'string' ? parseFloat(value) : value;

  if (isNaN(numericValue)) {
    return '';
  }

  // Use the app's current locale if not specified
  const currentLocale = locale || i18n.language || 'en';

  // Map our locale codes to standard locale codes
  const localeMap: Record<string, string> = {
    en: 'en-US',
    ne: 'ne-NP',
  };

  const mappedLocale = localeMap[currentLocale] || 'en-US';

  try {
    return new Intl.NumberFormat(mappedLocale).format(numericValue);
  } catch (error) {
    // Fallback to basic formatting if Intl is not supported
    console.warn(`Error formatting number: ${error}`);
    return numericValue.toString();
  }
};

/**
 * Format a percentage value
 * @param value The percentage value to format
 * @param locale Optional locale (default: current app locale)
 * @returns Formatted percentage string
 */
export const formatPercentage = (value: number | string, locale?: string): string => {
  if (value === null || value === undefined) return '';

  const numericValue = typeof value === 'string' ? parseFloat(value) : value;

  if (isNaN(numericValue)) {
    return '';
  }

  // Use the app's current locale if not specified
  const currentLocale = locale || i18n.language || 'en';

  // Map our locale codes to standard locale codes
  const localeMap: Record<string, string> = {
    en: 'en-US',
    ne: 'ne-NP',
  };

  const mappedLocale = localeMap[currentLocale] || 'en-US';

  try {
    return new Intl.NumberFormat(mappedLocale, {
      style: 'percent',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(numericValue / 100);
  } catch (error) {
    // Fallback to basic formatting if Intl is not supported
    console.warn(`Error formatting percentage: ${error}`);
    return `${numericValue}%`;
  }
};

/**
 * Truncate a string to a maximum length
 * @param text The text to truncate
 * @param maxLength Maximum length (default: 50)
 * @param suffix Suffix to add when truncated (default: '...')
 * @returns Truncated string
 */
export const truncateText = (
  text: string,
  maxLength: number = 50,
  suffix: string = '...'
): string => {
  if (!text) return '';

  if (text.length <= maxLength) {
    return text;
  }

  return `${text.substring(0, maxLength)}${suffix}`;
};

/**
 * Capitalize the first letter of a string
 * @param text The text to capitalize
 * @returns Capitalized string
 */
export const capitalizeFirstLetter = (text: string): string => {
  if (!text) return '';

  return text.charAt(0).toUpperCase() + text.slice(1);
};

/**
 * Format a date to a relative time string (e.g., "2 hours ago")
 * @param date The date to format
 * @returns Relative time string
 */
export const formatRelativeTime = (date: Date | string): string => {
  if (!date) return '';

  const dateObj = typeof date === 'string' ? new Date(date) : date;

  if (!isValid(dateObj)) {
    return '';
  }

  try {
    return formatDistanceToNow(dateObj, { addSuffix: true });
  } catch (error) {
    console.warn(`Error formatting relative time: ${error}`);
    return formatDateTime(dateObj);
  }
};
