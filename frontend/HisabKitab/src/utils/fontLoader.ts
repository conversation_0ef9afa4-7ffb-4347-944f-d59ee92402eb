import * as Font from 'expo-font';
import { Platform } from 'react-native';

// Define the fonts to load
const fonts = {
  // Manrope fonts
  'Manrope-Regular': require('../../assets/fonts/Manrope-Regular.ttf'),
  'Manrope-Medium': require('../../assets/fonts/Manrope-Medium.ttf'),
  'Manrope-SemiBold': require('../../assets/fonts/Manrope-SemiBold.ttf'),
  'Manrope-Bold': require('../../assets/fonts/Manrope-Bold.ttf'),
  'Manrope-ExtraBold': require('../../assets/fonts/Manrope-ExtraBold.ttf'),
  
  // Noto Sans Devanagari fonts for Nepali support
  'Noto Sans Devanagari': require('../../assets/fonts/NotoSansDevanagari-Regular.ttf'),
  'Noto Sans Devanagari Medium': require('../../assets/fonts/NotoSansDevanagari-Medium.ttf'),
  'Noto Sans Devanagari SemiBold': require('../../assets/fonts/NotoSansDevanagari-SemiBold.ttf'),
  'Noto Sans Devanagari Bold': require('../../assets/fonts/NotoSansDevanagari-Bold.ttf'),
};

/**
 * Load all required fonts for the app
 * @returns Promise that resolves when fonts are loaded
 */
export const loadFonts = async (): Promise<void> => {
  try {
    await Font.loadAsync(fonts);
    console.log('Fonts loaded successfully');
  } catch (error) {
    console.error('Error loading fonts:', error);
    // Fall back to system fonts if custom fonts fail to load
    if (Platform.OS === 'web') {
      // Apply web fallback fonts via CSS
      const style = document.createElement('style');
      style.textContent = `
        @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;500;600;700&display=swap');
        
        body {
          font-family: 'Manrope', 'Noto Sans Devanagari', sans-serif;
        }
      `;
      document.head.appendChild(style);
    }
  }
};

/**
 * Get the appropriate font family based on the language
 * @param language The current language code (e.g., 'en', 'ne')
 * @returns The font family to use
 */
export const getFontFamilyForLanguage = (language: string): string => {
  // Use Noto Sans Devanagari for Nepali, Manrope for everything else
  switch (language) {
    case 'ne': // Nepali
      return 'Noto Sans Devanagari';
    default:
      return 'Manrope-Regular';
  }
};

/**
 * Get the appropriate font weight based on the language and desired weight
 * @param language The current language code (e.g., 'en', 'ne')
 * @param weight The desired font weight ('regular', 'medium', 'semibold', 'bold')
 * @returns The font family with the appropriate weight
 */
export const getFontWithWeight = (language: string, weight: 'regular' | 'medium' | 'semibold' | 'bold'): string => {
  if (language === 'ne') {
    // Nepali font weights
    switch (weight) {
      case 'medium':
        return 'Noto Sans Devanagari Medium';
      case 'semibold':
        return 'Noto Sans Devanagari SemiBold';
      case 'bold':
        return 'Noto Sans Devanagari Bold';
      default:
        return 'Noto Sans Devanagari';
    }
  } else {
    // Default Manrope font weights
    switch (weight) {
      case 'medium':
        return 'Manrope-Medium';
      case 'semibold':
        return 'Manrope-SemiBold';
      case 'bold':
        return 'Manrope-Bold';
      default:
        return 'Manrope-Regular';
    }
  }
};

export default {
  loadFonts,
  getFontFamilyForLanguage,
  getFontWithWeight,
};
