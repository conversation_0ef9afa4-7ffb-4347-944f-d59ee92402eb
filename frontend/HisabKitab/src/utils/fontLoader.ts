import * as Font from 'expo-font';
import { Platform } from 'react-native';

// Define the fonts to load - temporarily disabled until assets are available
const fonts = {
  // TODO: Add custom fonts when assets are available
  // For now, using system fonts
};

/**
 * Load all required fonts for the app
 * @returns Promise that resolves when fonts are loaded
 */
export const loadFonts = async (): Promise<void> => {
  try {
    // Skip loading custom fonts until assets are available
    console.log('Using system fonts (custom fonts disabled until assets are available)');

    // Apply web fallback fonts via CSS for web platform
    if (Platform.OS === 'web') {
      const style = document.createElement('style');
      style.textContent = `
        @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;500;600;700&display=swap');

        body {
          font-family: '<PERSON><PERSON><PERSON>', 'Noto Sans Devanagari', sans-serif;
        }
      `;
      document.head.appendChild(style);
    }
  } catch (error) {
    console.error('Error setting up fonts:', error);
  }
};

/**
 * Get the appropriate font family based on the language
 * @param language The current language code (e.g., 'en', 'ne')
 * @returns The font family to use
 */
export const getFontFamilyForLanguage = (language: string): string => {
  // Use system fonts until custom fonts are available
  switch (language) {
    case 'ne': // Nepali
      return Platform.OS === 'ios' ? 'Helvetica' : 'Roboto';
    default:
      return Platform.OS === 'ios' ? 'Helvetica' : 'Roboto';
  }
};

/**
 * Get the appropriate font weight based on the language and desired weight
 * @param language The current language code (e.g., 'en', 'ne')
 * @param weight The desired font weight ('regular', 'medium', 'semibold', 'bold')
 * @returns The font family with the appropriate weight
 */
export const getFontWithWeight = (language: string, weight: 'regular' | 'medium' | 'semibold' | 'bold'): string => {
  // Use system fonts until custom fonts are available
  // Font weight will be handled via fontWeight style property instead
  return Platform.OS === 'ios' ? 'Helvetica' : 'Roboto';
};

export default {
  loadFonts,
  getFontFamilyForLanguage,
  getFontWithWeight,
};
