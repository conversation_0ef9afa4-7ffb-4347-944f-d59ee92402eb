import i18n from './i18n';

/**
 * Utility functions for validating translation keys
 */

/**
 * Checks if a translation key exists in the current language
 * @param key The translation key to check
 * @returns True if the key exists, false otherwise
 */
export const translationKeyExists = (key: string): boolean => {
  if (!key) return false;
  
  try {
    const translation = i18n.t(key);
    // If the translation is the same as the key, it likely doesn't exist
    // This is a common behavior in i18next when a key is missing
    return translation !== key;
  } catch (error) {
    console.warn(`Error checking translation key ${key}:`, error);
    return false;
  }
};

/**
 * Gets a safe translation for a key, with a fallback if the key doesn't exist
 * @param key The translation key
 * @param fallback Optional fallback text if the key doesn't exist
 * @param options Optional translation options
 * @returns The translated text or fallback
 */
export const getSafeTranslation = (
  key: string,
  fallback?: string,
  options?: Record<string, any>
): string => {
  if (!key) return fallback || '';
  
  try {
    const translation = i18n.t(key, options);
    
    // Check if the translation is the same as the key (missing translation)
    if (translation === key && fallback) {
      console.warn(`Missing translation for key: ${key}`);
      return fallback;
    }
    
    return translation;
  } catch (error) {
    console.warn(`Error translating key ${key}:`, error);
    return fallback || key;
  }
};

/**
 * Validates a list of translation keys and returns any missing keys
 * @param keys Array of translation keys to validate
 * @returns Array of missing translation keys
 */
export const validateTranslationKeys = (keys: string[]): string[] => {
  const missingKeys: string[] = [];
  
  keys.forEach(key => {
    if (!translationKeyExists(key)) {
      missingKeys.push(key);
    }
  });
  
  return missingKeys;
};

/**
 * Extracts translation keys from a component's render output
 * This is a simplified version and may not catch all keys
 * @param componentString The string representation of a component
 * @returns Array of potential translation keys
 */
export const extractTranslationKeys = (componentString: string): string[] => {
  const keys: string[] = [];
  
  // Look for t('key') pattern
  const tFunctionRegex = /t\(['"]([^'"]+)['"]\)/g;
  let match;
  
  while ((match = tFunctionRegex.exec(componentString)) !== null) {
    keys.push(match[1]);
  }
  
  // Look for t('key', { ... }) pattern
  const tFunctionWithParamsRegex = /t\(['"]([^'"]+)['"]\s*,\s*\{/g;
  
  while ((match = tFunctionWithParamsRegex.exec(componentString)) !== null) {
    keys.push(match[1]);
  }
  
  return keys;
};

/**
 * Creates a wrapper around the translation function that logs warnings for missing keys
 * @param t The original translation function
 * @returns A wrapped translation function that logs warnings
 */
export const createSafeTranslationFunction = (t: Function): Function => {
  return (key: string, options?: Record<string, any>) => {
    const translation = t(key, options);
    
    // Check if the translation is the same as the key (missing translation)
    if (translation === key) {
      console.warn(`Missing translation for key: ${key}`);
    }
    
    return translation;
  };
};
