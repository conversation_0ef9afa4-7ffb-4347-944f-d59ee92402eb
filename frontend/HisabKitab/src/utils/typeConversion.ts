import { TransactionType, SyncStatus, FrequencyType } from '../models/Transaction';
import { SavingsGoalStatus } from '../models/SavingsGoal';
import { WishlistItemStatus } from '../models/WishlistItem';

/**
 * Utility functions for handling type conversions between backend and frontend
 *
 * This module provides a centralized place for all type conversions between
 * the frontend and backend, ensuring consistency across the application.
 */

/**
 * Enum for transaction status that matches backend values
 */
export enum TransactionStatus {
  Pending = 'Pending',
  Completed = 'Completed',
  Cancelled = 'Cancelled'
}

/**
 * Enum for priority levels that matches backend values
 */
export enum PriorityLevel {
  Low = 0,
  Normal = 1,
  High = 2,
  Urgent = 3
}

/**
 * Maps priority level enum values to display strings
 */
export const PriorityLevelNames: Record<PriorityLevel, string> = {
  [PriorityLevel.Low]: 'Low',
  [PriorityLevel.Normal]: 'Normal',
  [PriorityLevel.High]: 'High',
  [PriorityLevel.Urgent]: 'Urgent'
};

/**
 * Enum for category types that matches backend values
 */
export enum CategoryType {
  Income = 0,
  Expense = 1
}

/**
 * Converts a category type value (number or string) to a consistent string representation
 * @param type The category type value (could be number, string, or enum)
 * @returns The string representation ('Income' or 'Expense')
 */
export const getCategoryTypeString = (type: any): string => {
  // If it's a number or enum value
  if (typeof type === 'number') {
    return type === CategoryType.Income ? 'Income' : 'Expense';
  }

  // If it's already a string
  if (typeof type === 'string') {
    const lowerType = type.toLowerCase();
    if (lowerType === 'income' || lowerType === '0') {
      return 'Income';
    }
    return 'Expense'; // Default to expense for any other string
  }

  // Default fallback
  return 'Expense';
};

/**
 * Converts a string category type to the numeric enum value
 * @param typeString The string representation of category type
 * @returns The CategoryType enum value
 */
export const getCategoryTypeEnum = (typeString: string): CategoryType => {
  const lowerType = typeString.toLowerCase();
  if (lowerType === 'income') {
    return CategoryType.Income;
  }
  return CategoryType.Expense;
};

/**
 * Checks if a transaction is a transfer transaction
 * @param type The transaction type
 * @returns True if the transaction is a transfer
 */
export const isTransferTransaction = (type: any): boolean => {
  if (typeof type === 'number') {
    return type === TransactionType.Transfer;
  }
  if (typeof type === 'string') {
    return type.toLowerCase() === 'transfer';
  }
  return false;
};

/**
 * Gets the appropriate category ID for a transfer transaction
 * @param categories List of all categories
 * @param fallbackCategoryId Fallback category ID if no transfer category is found
 * @returns The ID of the transfer category, or fallbackCategoryId if not found
 */
export const getTransferCategoryId = (
  categories: Array<{ id: number; name: string; type: any }>,
  fallbackCategoryId: number = 0
): number => {
  // Look for a category named "Transfer"
  const transferCategory = categories.find(c =>
    c.name.toLowerCase() === 'transfer'
  );

  return transferCategory?.id || fallbackCategoryId;
};

/**
 * Converts a transaction status value to a consistent string representation
 * @param status The transaction status value (could be string or enum)
 * @returns The standardized transaction status string
 */
export const getTransactionStatusString = (status: any): string => {
  if (!status) return TransactionStatus.Pending;

  if (typeof status === 'string') {
    const upperStatus = status.toUpperCase();
    if (upperStatus === 'PENDING') return TransactionStatus.Pending;
    if (upperStatus === 'COMPLETED') return TransactionStatus.Completed;
    if (upperStatus === 'CANCELLED') return TransactionStatus.Cancelled;
  }

  // Default to completed if unknown
  return TransactionStatus.Completed;
};

/**
 * Converts a priority level value to a consistent string representation
 * @param level The priority level value (could be number, string, or enum)
 * @returns The standardized priority level string
 */
export const getPriorityLevelString = (level: any): string => {
  if (level === null || level === undefined) return PriorityLevelNames[PriorityLevel.Normal];

  if (typeof level === 'number') {
    // Check if the number is a valid PriorityLevel enum value
    if (level >= 0 && level <= 3) {
      return PriorityLevelNames[level as PriorityLevel] || PriorityLevelNames[PriorityLevel.Normal];
    }
    return PriorityLevelNames[PriorityLevel.Normal];
  }

  if (typeof level === 'string') {
    const upperLevel = level.toUpperCase();
    if (upperLevel === 'LOW') return PriorityLevelNames[PriorityLevel.Low];
    if (upperLevel === 'NORMAL') return PriorityLevelNames[PriorityLevel.Normal];
    if (upperLevel === 'HIGH') return PriorityLevelNames[PriorityLevel.High];
    if (upperLevel === 'URGENT') return PriorityLevelNames[PriorityLevel.Urgent];

    // Try to parse as number
    const numLevel = parseInt(level, 10);
    if (!isNaN(numLevel) && numLevel >= 0 && numLevel <= 3) {
      return PriorityLevelNames[numLevel as PriorityLevel];
    }
  }

  // Default to normal if unknown
  return PriorityLevelNames[PriorityLevel.Normal];
};

/**
 * Converts a priority level string to the numeric enum value
 * @param levelString The string representation of priority level
 * @returns The PriorityLevel enum value
 */
export const getPriorityLevelEnum = (levelString: string): PriorityLevel => {
  const upperLevel = levelString.toUpperCase();
  if (upperLevel === 'LOW') return PriorityLevel.Low;
  if (upperLevel === 'HIGH') return PriorityLevel.High;
  if (upperLevel === 'URGENT') return PriorityLevel.Urgent;

  // Default to normal
  return PriorityLevel.Normal;
};

/**
 * Ensures that a transaction type value is properly converted to the enum value
 * @param type The transaction type value (could be number, string, or already the correct enum)
 * @returns The correct TransactionType enum value
 */
export const ensureTransactionType = (type: any): TransactionType => {
  // If it's already a valid enum value, return it
  if (
    type === TransactionType.Income ||
    type === TransactionType.Expense ||
    type === TransactionType.Transfer
  ) {
    return type;
  }

  // If it's a string, convert it
  if (typeof type === 'string') {
    // Try to parse as a number first (for string representations of numbers)
    const numType = parseInt(type, 10);
    if (!isNaN(numType) && numType >= 0 && numType <= 2) {
      return numType as TransactionType;
    }

    // Otherwise, try to match the string name
    switch (type.toLowerCase()) {
      case 'income':
        return TransactionType.Income;
      case 'expense':
        return TransactionType.Expense;
      case 'transfer':
        return TransactionType.Transfer;
      default:
        return TransactionType.Expense; // Default to expense
    }
  }

  // If it's a number but not a valid enum value, ensure it's within range
  if (typeof type === 'number') {
    if (type >= 0 && type <= 2) {
      return type;
    }
    return TransactionType.Expense; // Default to expense
  }

  // Default fallback
  return TransactionType.Expense;
};

/**
 * Ensures that a sync status value is properly converted to the enum value
 * @param status The sync status value (could be number, string, or already the correct enum)
 * @returns The correct SyncStatus enum value
 */
export const ensureSyncStatus = (status: any): SyncStatus => {
  // If it's already a valid enum value, return it
  if (
    status === SyncStatus.Pending ||
    status === SyncStatus.Synced ||
    status === SyncStatus.Failed
  ) {
    return status;
  }

  // If it's a string, convert it
  if (typeof status === 'string') {
    switch (status.toLowerCase()) {
      case 'pending':
        return SyncStatus.Pending;
      case 'synced':
        return SyncStatus.Synced;
      case 'failed':
        return SyncStatus.Failed;
      default:
        return SyncStatus.Pending; // Default to pending
    }
  }

  // If it's a number but not a valid enum value, ensure it's within range
  if (typeof status === 'number') {
    if (status >= 0 && status <= 2) {
      return status;
    }
    return SyncStatus.Pending; // Default to pending
  }

  // Default fallback
  return SyncStatus.Pending;
};

/**
 * Converts a transaction object to ensure all type fields are properly set
 * @param transaction The transaction object to convert
 * @returns A new transaction object with proper types
 */
export const convertTransaction = (transaction: any) => {
  if (!transaction) return null;

  // Convert date strings to Date objects if needed
  let date = transaction.date;
  if (typeof date === 'string') {
    date = new Date(date);
  }

  // Convert lastUpdatedAt if it exists
  let lastUpdatedAt = transaction.lastUpdatedAt;
  if (lastUpdatedAt && typeof lastUpdatedAt === 'string') {
    lastUpdatedAt = new Date(lastUpdatedAt);
  }

  // Convert createdAt if it exists
  let createdAt = transaction.createdAt;
  if (createdAt && typeof createdAt === 'string') {
    createdAt = new Date(createdAt);
  }

  // Convert updatedAt if it exists
  let updatedAt = transaction.updatedAt;
  if (updatedAt && typeof updatedAt === 'string') {
    updatedAt = new Date(updatedAt);
  }

  return {
    ...transaction,
    type: ensureTransactionType(transaction.type),
    syncStatus: ensureSyncStatus(transaction.syncStatus),
    date,
    lastUpdatedAt,
    createdAt,
    updatedAt,
  };
};

/**
 * Converts a transaction object for API submission
 * @param transaction The transaction object to convert for API
 * @returns A new transaction object with proper types for the API
 */
export const convertTransactionForApi = (transaction: any) => {
  if (!transaction) return null;

  // Ensure type is a number for the API
  const type = ensureTransactionType(transaction.type);

  // Format dates as ISO strings for the API
  const date = transaction.date instanceof Date
    ? transaction.date.toISOString()
    : typeof transaction.date === 'string'
      ? new Date(transaction.date).toISOString()
      : new Date().toISOString();

  return {
    ...transaction,
    type, // This will be a number (enum value)
    date, // This will be an ISO string
  };
};

/**
 * Converts an array of transaction objects to ensure all type fields are properly set
 * @param transactions The array of transaction objects to convert
 * @returns A new array of transaction objects with proper types
 */
export const convertTransactions = (transactions: any[]) => {
  if (!transactions) return [];

  return transactions.map(convertTransaction);
};

/**
 * Ensures that a savings goal status value is properly converted to the correct string value
 * @param status The status value (could be number, string, or already the correct value)
 * @returns The correct SavingsGoalStatus string value
 */
export const ensureSavingsGoalStatus = (status: any): SavingsGoalStatus => {
  // If it's already a valid string value, return it
  if (
    status === 'Active' ||
    status === 'Achieved' ||
    status === 'Abandoned'
  ) {
    return status;
  }

  // If it's a number, convert it
  if (typeof status === 'number') {
    switch (status) {
      case 0:
        return 'Active';
      case 1:
        return 'Achieved';
      case 2:
        return 'Abandoned';
      default:
        return 'Active'; // Default to Active
    }
  }

  // If it's a string but not a valid value, try to match
  if (typeof status === 'string') {
    const lowerStatus = status.toLowerCase();
    if (lowerStatus.includes('active')) return 'Active';
    if (lowerStatus.includes('achieve')) return 'Achieved';
    if (lowerStatus.includes('abandon')) return 'Abandoned';
  }

  // Default fallback
  return 'Active';
};

/**
 * Ensures that a wishlist item status value is properly converted to the correct string value
 * @param status The status value (could be number, string, or already the correct value)
 * @returns The correct WishlistItemStatus string value
 */
export const ensureWishlistItemStatus = (status: any): WishlistItemStatus => {
  // If it's already a valid string value, return it
  if (
    status === 'Pending' ||
    status === 'Purchased' ||
    status === 'Abandoned'
  ) {
    return status;
  }

  // If it's a number, convert it
  if (typeof status === 'number') {
    switch (status) {
      case 0:
        return 'Pending';
      case 1:
        return 'Purchased';
      case 2:
        return 'Abandoned';
      default:
        return 'Pending'; // Default to Pending
    }
  }

  // If it's a string but not a valid value, try to match
  if (typeof status === 'string') {
    const lowerStatus = status.toLowerCase();
    if (lowerStatus.includes('pending')) return 'Pending';
    if (lowerStatus.includes('purchase')) return 'Purchased';
    if (lowerStatus.includes('abandon')) return 'Abandoned';
  }

  // Default fallback
  return 'Pending';
};

/**
 * Converts a FrequencyType enum value to its string representation
 * @param frequency The FrequencyType enum value
 * @returns The string representation of the frequency
 */
export const getFrequencyTypeString = (frequency: FrequencyType | null | undefined): string => {
  if (frequency === null || frequency === undefined) {
    return 'monthly'; // Default
  }

  switch (frequency) {
    case FrequencyType.Daily:
      return 'daily';
    case FrequencyType.Weekly:
      return 'weekly';
    case FrequencyType.Monthly:
      return 'monthly';
    case FrequencyType.Quarterly:
      return 'quarterly';
    case FrequencyType.Yearly:
      return 'yearly';
    default:
      return 'monthly';
  }
};

/**
 * Converts a string frequency type to the enum value
 * @param frequencyString The string representation of frequency type
 * @returns The FrequencyType enum value
 */
export const getFrequencyTypeEnum = (frequencyString: string): FrequencyType => {
  const lowerFreq = frequencyString.toLowerCase();

  switch (lowerFreq) {
    case 'daily':
      return FrequencyType.Daily;
    case 'weekly':
      return FrequencyType.Weekly;
    case 'monthly':
      return FrequencyType.Monthly;
    case 'quarterly':
      return FrequencyType.Quarterly;
    case 'yearly':
      return FrequencyType.Yearly;
    default:
      return FrequencyType.Monthly;
  }
};

/**
 * Converts any value to a number, with a default fallback
 * @param value The value to convert to a number
 * @param defaultValue The default value to use if conversion fails
 * @returns The converted number or the default value
 */
export const toNumber = (value: any, defaultValue: number = 0): number => {
  if (value === null || value === undefined) {
    return defaultValue;
  }

  if (typeof value === 'number') {
    return isNaN(value) ? defaultValue : value;
  }

  if (typeof value === 'string') {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? defaultValue : parsed;
  }

  return defaultValue;
};

/**
 * Converts any value to a boolean
 * @param value The value to convert to a boolean
 * @param defaultValue The default value to use if conversion is ambiguous
 * @returns The converted boolean or the default value
 */
export const toBoolean = (value: any, defaultValue: boolean = false): boolean => {
  if (value === null || value === undefined) {
    return defaultValue;
  }

  if (typeof value === 'boolean') {
    return value;
  }

  if (typeof value === 'number') {
    return value !== 0;
  }

  if (typeof value === 'string') {
    const lowerValue = value.toLowerCase();
    if (lowerValue === 'true' || lowerValue === 'yes' || lowerValue === '1') {
      return true;
    }
    if (lowerValue === 'false' || lowerValue === 'no' || lowerValue === '0') {
      return false;
    }
  }

  return defaultValue;
};
