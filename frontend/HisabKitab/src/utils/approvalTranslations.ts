// English translations for approval
export const enApprovalTranslations = {
  approval: {
    approval: 'Approval',
    requests: 'Approval Requests',
    createRequest: 'Create Request',
    myRequests: 'My Requests',
    toApprove: 'To Approve',
    approve: 'Approve',
    reject: 'Reject',
    rejectRequest: 'Reject Request',
    confirmApprove: 'Confirm Approval',
    confirmApproveMessage: 'Are you sure you want to approve this request?',
    requestCreated: 'Request Created',
    requestCreatedMessage: 'Your approval request has been created successfully.',
    selectFamily: 'Select a Family',
    amount: 'Amount',
    enterAmount: 'Enter amount',
    enterValidAmount: 'Please enter a valid amount',
    description: 'Description',
    enterDescription: 'Enter description',
    category: 'Category',
    selectCategory: 'Please select a category',
    requestDate: 'Request Date',
    responseDate: 'Response Date',
    rejectionReason: 'Rejection Reason',
    enterRejectionReason: 'Enter reason for rejection',
    noRequests: 'No Approval Requests',
    noRequestsToApprove: 'No Requests to Approve',
    submitRequest: 'Submit Request',
    check: 'Check',
    approvalRequired: 'Approval Required (Threshold: {{threshold}})',
    approvalNotRequired: 'Approval Not Required',
    approvalNotRequiredMessage: 'This transaction does not require approval based on current settings. Do you still want to request approval?',
    requestAnyway: 'Request Anyway',
    all: 'All',
    status: {
      pending: 'Pending',
      approved: 'Approved',
      rejected: 'Rejected'
    }
  }
};

// Nepali translations for approval
export const neApprovalTranslations = {
  approval: {
    approval: 'स्वीकृति',
    requests: 'स्वीकृति अनुरोधहरू',
    createRequest: 'अनुरोध सिर्जना गर्नुहोस्',
    myRequests: 'मेरा अनुरोधहरू',
    toApprove: 'स्वीकृत गर्न',
    approve: 'स्वीकृत गर्नुहोस्',
    reject: 'अस्वीकार गर्नुहोस्',
    rejectRequest: 'अनुरोध अस्वीकार गर्नुहोस्',
    confirmApprove: 'स्वीकृति पुष्टि गर्नुहोस्',
    confirmApproveMessage: 'के तपाईं यो अनुरोध स्वीकृत गर्न निश्चित हुनुहुन्छ?',
    requestCreated: 'अनुरोध सिर्जना गरियो',
    requestCreatedMessage: 'तपाईंको स्वीकृति अनुरोध सफलतापूर्वक सिर्जना गरिएको छ।',
    selectFamily: 'परिवार चयन गर्नुहोस्',
    amount: 'रकम',
    enterAmount: 'रकम प्रविष्ट गर्नुहोस्',
    enterValidAmount: 'कृपया मान्य रकम प्रविष्ट गर्नुहोस्',
    description: 'विवरण',
    enterDescription: 'विवरण प्रविष्ट गर्नुहोस्',
    category: 'श्रेणी',
    selectCategory: 'कृपया श्रेणी चयन गर्नुहोस्',
    requestDate: 'अनुरोध मिति',
    responseDate: 'प्रतिक्रिया मिति',
    rejectionReason: 'अस्वीकृति कारण',
    enterRejectionReason: 'अस्वीकृतिको कारण प्रविष्ट गर्नुहोस्',
    noRequests: 'कुनै स्वीकृति अनुरोधहरू छैनन्',
    noRequestsToApprove: 'स्वीकृत गर्न कुनै अनुरोधहरू छैनन्',
    submitRequest: 'अनुरोध पेश गर्नुहोस्',
    check: 'जाँच गर्नुहोस्',
    approvalRequired: 'स्वीकृति आवश्यक (सीमा: {{threshold}})',
    approvalNotRequired: 'स्वीकृति आवश्यक छैन',
    approvalNotRequiredMessage: 'हालको सेटिङहरूको आधारमा यो लेनदेनलाई स्वीकृति आवश्यक छैन। के तपाईं अझै पनि स्वीकृति अनुरोध गर्न चाहनुहुन्छ?',
    requestAnyway: 'जे भए पनि अनुरोध गर्नुहोस्',
    all: 'सबै',
    status: {
      pending: 'प्रक्रियामा',
      approved: 'स्वीकृत',
      rejected: 'अस्वीकृत'
    }
  }
};
