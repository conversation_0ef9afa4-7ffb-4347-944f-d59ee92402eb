/**
 * SSL Certificate Bypass for Development
 *
 * WARNING: This should ONLY be used in development, NEVER in production!
 *
 * This module provides a way to bypass SSL certificate validation in development
 * environments, which is useful when working with self-signed certificates.
 */

// Check if we're in development mode
const isDevelopment = __DEV__;

/**
 * Simple console logger for SSL bypass
 */
export function logSSLBypassInfo(): void {
  if (isDevelopment) {
    console.log('SSL bypass info: In React Native, you need to handle SSL certificate validation manually.');
    console.log('For development with self-signed certificates:');
    console.log('1. Use HTTP instead of HTTPS if possible');
    console.log('2. Add proper certificate handling in production');
  }
}

// Log SSL bypass info when imported
if (isDevelopment) {
  logSSLBypassInfo();
}
