import { Transaction, SyncStatus } from '../models/Transaction';

/**
 * Utility functions for handling offline sync and conflict resolution
 */

/**
 * Enum for conflict resolution strategies
 */
export enum ConflictResolutionStrategy {
  KeepLocal = 'local',
  KeepRemote = 'remote',
  MergeKeepNewer = 'merge_newer',
  MergeManual = 'merge_manual'
}

/**
 * Interface for a sync conflict
 */
export interface SyncConflict {
  localTransaction: Transaction;
  remoteTransaction: Transaction;
  conflictType: 'update' | 'delete';
  resolution?: ConflictResolutionStrategy;
}

/**
 * Detects conflicts between local and remote transactions
 * @param localTransactions Transactions from the local database
 * @param remoteTransactions Transactions from the remote server
 * @returns Array of conflicts that need resolution
 */
export const detectConflicts = (
  localTransactions: Transaction[],
  remoteTransactions: Transaction[]
): SyncConflict[] => {
  const conflicts: SyncConflict[] = [];

  // Create maps for faster lookup
  const remoteMap = new Map<number, Transaction>();
  remoteTransactions.forEach(t => {
    if (t.id) remoteMap.set(t.id, t);
  });

  // Check each local transaction for conflicts
  localTransactions.forEach(localTx => {
    if (!localTx.id) return; // Skip transactions without IDs

    const remoteTx = remoteMap.get(localTx.id);
    if (!remoteTx) return; // No conflict if remote doesn't have this transaction

    // Check if the transactions are different
    if (isTransactionDifferent(localTx, remoteTx)) {
      conflicts.push({
        localTransaction: localTx,
        remoteTransaction: remoteTx,
        conflictType: 'update'
      });
    }
  });

  return conflicts;
};

/**
 * Checks if two transactions are different in a way that would cause a conflict
 * @param tx1 First transaction
 * @param tx2 Second transaction
 * @returns True if the transactions have conflicting changes
 */
export const isTransactionDifferent = (tx1: Transaction, tx2: Transaction): boolean => {
  // Compare important fields that would cause conflicts
  if (tx1.amount !== tx2.amount) return true;
  if (tx1.description !== tx2.description) return true;
  if (tx1.type !== tx2.type) return true;
  if (tx1.accountId !== tx2.accountId) return true;
  if (tx1.categoryId !== tx2.categoryId) return true;
  if (tx1.status !== tx2.status) return true;

  // Compare dates (accounting for string vs Date objects)
  const date1 = new Date(tx1.date).getTime();
  const date2 = new Date(tx2.date).getTime();
  if (date1 !== date2) return true;

  // Compare items (if both have items)
  if (tx1.items && tx2.items) {
    if (tx1.items.length !== tx2.items.length) return true;

    // Compare each item
    for (let i = 0; i < tx1.items.length; i++) {
      const item1 = tx1.items[i];
      const item2 = tx2.items[i];

      if (item1.name !== item2.name) return true;
      if (item1.amount !== item2.amount) return true;
      if (item1.quantity !== item2.quantity) return true;
    }
  } else if ((tx1.items && !tx2.items) || (!tx1.items && tx2.items)) {
    return true;
  }

  return false;
};

/**
 * Resolves conflicts using the specified strategy
 * @param conflicts Array of conflicts to resolve
 * @param strategy Strategy to use for resolution
 * @returns Object containing resolved transactions and any remaining conflicts
 */
export const resolveConflicts = (
  conflicts: SyncConflict[],
  strategy: ConflictResolutionStrategy = ConflictResolutionStrategy.MergeKeepNewer
): {
  resolved: Transaction[],
  remainingConflicts: SyncConflict[]
} => {
  const resolved: Transaction[] = [];
  const remainingConflicts: SyncConflict[] = [];

  conflicts.forEach(conflict => {
    switch (strategy) {
      case ConflictResolutionStrategy.KeepLocal:
        resolved.push({
          ...conflict.localTransaction,
          syncStatus: SyncStatus.Pending,
          isSynced: false
        });
        break;

      case ConflictResolutionStrategy.KeepRemote:
        resolved.push({
          ...conflict.remoteTransaction,
          syncStatus: SyncStatus.Synced,
          isSynced: true
        });
        break;

      case ConflictResolutionStrategy.MergeKeepNewer:
        // Compare last updated timestamps
        const localUpdated = new Date(conflict.localTransaction.lastUpdatedAt || conflict.localTransaction.createdAt).getTime();
        const remoteUpdated = new Date(conflict.remoteTransaction.lastUpdatedAt || conflict.remoteTransaction.createdAt).getTime();

        if (localUpdated > remoteUpdated) {
          resolved.push({
            ...conflict.localTransaction,
            syncStatus: SyncStatus.Pending,
            isSynced: false
          });
        } else {
          resolved.push({
            ...conflict.remoteTransaction,
            syncStatus: SyncStatus.Synced,
            isSynced: true
          });
        }
        break;

      case ConflictResolutionStrategy.MergeManual:
        // Keep the conflict for manual resolution
        remainingConflicts.push(conflict);
        break;
    }
  });

  return { resolved, remainingConflicts };
};
