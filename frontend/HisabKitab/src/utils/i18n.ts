import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { enSavingsTranslations, neSavingsTranslations } from './savingsTranslations';
import { enAnalyticsTranslations, neAnalyticsTranslations } from './analyticsTranslations';
import { enBudgetTranslations, neBudgetTranslations } from './budgetTranslations';
import { enApprovalTranslations, neApprovalTranslations } from './approvalTranslations';
import { enNotificationTranslations, neNotificationTranslations } from './notificationTranslations';
import { enFeatureTranslations, neFeatureTranslations } from './featureTranslations';
import { enTypographyTranslations, neTypographyTranslations } from './typographyTranslations';

// For debugging translations
const DEBUG_TRANSLATIONS = false;

// English translations
const enTranslations = {
  common: {
    appName: 'Hisab-Kitab',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    back: 'Back',
    next: 'Next',
    done: 'Done',
    confirm: 'Confirm',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',
    comingSoon: 'Coming Soon',
    create: 'Create',
    update: 'Update',
    remove: 'Remove',
    invite: 'Invite',
    join: 'Join',
    leave: 'Leave',
    to: 'to',
    tryAgain: 'Try Again',
    unexpectedError: 'An unexpected error occurred. Please try again.',
    networkError: 'Network error. Please check your connection and try again.',
    serverError: 'Server error. Please try again later.',
    retry: 'Retry',
    goBack: 'Go Back',
    all: 'All',
    from: 'From',
    min: 'Min',
    max: 'Max',
    reset: 'Reset',
    apply: 'Apply',
    english: 'English',
    nepali: 'Nepali',
    validationError: 'Please check your input and try again.',
    permissionError: 'You don\'t have permission to perform this action.',
    notFoundError: 'The requested resource was not found.',
    authenticationError: 'Authentication error. Please log in again.',
    navigationError: 'Navigation Error',
    restartApp: 'An error occurred in the navigation. Please restart the app.',
    selectDate: 'Select Date',
  },
  auth: {
    login: 'Login',
    register: 'Register',
    logout: 'Logout',
    email: 'Email',
    username: 'Username',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    forgotPassword: 'Forgot Password?',
    resetPassword: 'Reset Password',
    firstName: 'First Name',
    lastName: 'Last Name',
    phoneNumber: 'Phone Number',
    loginSuccess: 'Login successful',
    registerSuccess: 'Registration successful',
    logoutSuccess: 'Logout successful',
    emptyFields: 'Please fill in all required fields',
    passwordMismatch: 'Passwords do not match',
    loginFailed: 'Login failed. Please check your credentials.',
    registerFailed: 'Registration failed. Please try again.',
    requiredFields: 'Please fill in all required fields',
    noAccount: 'Don\'t have an account? Register',
    haveAccount: 'Already have an account? Login',
    logoutConfirm: 'Are you sure you want to logout?',
    account: 'Account',
    darkMode: 'Dark Mode',
  },
  profile: {
    profile: 'Profile',
    updateProfile: 'Update Profile',
    changePassword: 'Change Password',
    changeEmail: 'Change Email',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    confirmNewPassword: 'Confirm New Password',
    updateSuccess: 'Profile updated successfully',
    updateFailed: 'Failed to update profile',
    passwordChangeSuccess: 'Password changed successfully',
    passwordChangeFailed: 'Failed to change password',
    incorrectCurrentPassword: 'The current password you entered is incorrect',
    passwordComplexity: 'New password does not meet complexity requirements',
    emailChangeSuccess: 'Email changed successfully',
    emailChangeFailed: 'Failed to change email',
    roles: 'Roles',
  },
  home: {
    welcome: 'Welcome to Hisab-Kitab',
    totalBalance: 'Total Balance',
    income: 'Income',
    expense: 'Expense',
    recentTransactions: 'Recent Transactions',
    viewAll: 'View All',
    noTransactions: 'No transactions yet',
  },
  accounts: {
    accounts: 'Accounts',
    title: 'Accounts',
    addAccount: 'Add Account',
    createAccount: 'Create Account',
    editAccount: 'Edit Account',
    updateAccount: 'Update Account',
    deleteAccount: 'Delete Account',
    accountDetails: 'Account Details',
    accountName: 'Account Name',
    accountNamePlaceholder: 'Enter account name',
    accountType: 'Account Type',
    balance: 'Balance',
    currentBalance: 'Current Balance',
    initialBalance: 'Initial Balance',
    currency: 'Currency',
    cash: 'Cash',
    bank: 'Bank',
    creditCard: 'Credit Card',
    investment: 'Investment',
    savings: 'Savings',
    loan: 'Loan',
    other: 'Other',
    isActive: 'Is Active',
    inactive: 'Inactive',
    shared: 'Shared',
    excludeFromStats: 'Exclude from Statistics',
    accountOwner: 'Account Owner',
    owner: 'Owner',
    personalAccount: 'Personal Account',
    familyAccount: 'Family Account',
    personal: 'Personal',
    lastUpdated: 'Last Updated',
    totalBalance: 'Total Balance',
    account: 'Account',
    noAccounts: 'No accounts found',
    createAccountError: 'Failed to create account',
    updateAccountError: 'Failed to update account',
    deleteAccountError: 'Failed to delete account',
    fetchAccountError: 'Failed to fetch account details',
    deleteAccountConfirm: 'Are you sure you want to delete account "{name}"?',
    shareAccount: 'Share Account',
    shareWithOthers: 'Share with Others',
    sharedWith: 'Shared With',
    share: 'Share',
    selectFamily: 'Select Family',
    selectFamilyPlaceholder: 'Select a family',
    selectMember: 'Select Member',
    selectPermissions: 'Select Permissions',
    viewOnly: 'View Only',
    viewAndEdit: 'View and Edit',
    fullAccess: 'Full Access',
    permissionLevels: 'Permission Levels',
    viewOnlyDescription: 'User can only view the account details and transactions. They cannot make any changes.',
    viewAndEditDescription: 'User can view the account and add/edit transactions. They cannot delete the account or remove other members.',
    fullAccessDescription: 'User has complete control over the account, including deleting it and managing who it is shared with.',
    noAvailableMembers: 'No available members to share with',
    selectMemberError: 'Please select a family member',
    shareSuccess: 'Account shared successfully',
    shareError: 'Failed to share account',
    removeShare: 'Remove Share',
    removeShareConfirm: 'Are you sure you want to remove sharing with {name}?',
    removeShareError: 'Failed to remove share',
  },
  transactions: {
    transactions: 'Transactions',
    transactionDetails: 'Transaction Details',
    addTransaction: 'Add Transaction',
    editTransaction: 'Edit Transaction',
    deleteTransaction: 'Delete Transaction',
    deleteTransactionConfirmation: 'Are you sure you want to delete this transaction?',
    deleteTransactionError: 'Failed to delete transaction. Please try again.',
    savedOffline: 'Transaction saved offline. It will be synced when you are online.',
    transactionNotFound: 'Transaction not found',
    updateTransactionError: 'Failed to update transaction. Please try again.',
    noTransactions: 'No Transactions',
    noTransactionsMessage: 'You have not added any transactions yet.',
    type: 'Type',
    amount: 'Amount',
    date: 'Date',
    account: 'Account',
    toAccount: 'To Account',
    category: 'Category',
    description: 'Description',
    descriptionPlaceholder: 'Enter description...',
    priority: 'Priority',
    status: 'Status',
    tags: 'Tags',
    tagsPlaceholder: 'Enter tags separated by commas...',
    location: 'Location',
    locationPlaceholder: 'Enter location...',
    items: 'Items',
    item: 'Item',
    itemName: 'Item Name',
    itemNamePlaceholder: 'Enter item name...',
    quantity: 'Quantity',
    notes: 'Notes',
    notesPlaceholder: 'Enter notes...',
    addItem: 'Add Item',
    editItem: 'Edit Item',
    noItems: 'No items added',
    oneItem: '1 item',
    multipleItems: '{{count}} items',
    income: 'Income',
    expense: 'Expense',
    transfer: 'Transfer',
    transferBetweenAccounts: 'Transfer from {{from}} to {{to}}',
    completed: 'Completed',
    pending: 'Pending',
    rejected: 'Rejected',
    uncategorized: 'Uncategorized',
    filterTransactions: 'Filter Transactions',
    startDate: 'Start Date',
    endDate: 'End Date',
    selectStartDate: 'Select start date',
    selectEndDate: 'Select end date',
    search: 'Search',
    searchPlaceholder: 'Search transactions...',
    sortBy: 'Sort By',
    order: 'Order',
    sortByDate: 'Date',
    sortByAmount: 'Amount',
    sortByCategory: 'Category',
    sortByAccount: 'Account',
    ascending: 'Ascending',
    descending: 'Descending',
    balance: 'Balance',
    from: 'From',
    to: 'To',
    noTransferCategory: 'No Transfer category found. Please contact the administrator.',
    transferNote: 'Transfers move money between your accounts without affecting your overall balance.',
  },
  categories: {
    categories: 'Categories',
    addCategory: 'Add Category',
    createCategory: 'Create Category',
    editCategory: 'Edit Category',
    updateCategory: 'Update Category',
    deleteCategory: 'Delete Category',
    categoryDetails: 'Category Details',
    categoryName: 'Category Name',
    categoryNamePlaceholder: 'Enter category name',
    categoryType: 'Category Type',
    income: 'Income',
    expense: 'Expense',
    icon: 'Icon',
    color: 'Color',
    selectIcon: 'Select Icon',
    selectColor: 'Select Color',
    parentCategory: 'Parent Category',
    noParent: 'No Parent Category',
    subcategories: 'Subcategories',
    mainCategories: 'Main Categories',
    categoryOwner: 'Category Owner',
    owner: 'Owner',
    personal: 'Personal',
    personalCategory: 'Personal Category',
    familyCategory: 'Family Category',
    system: 'System',
    createdAt: 'Created At',
    noCategories: 'No categories found',
    createCategoryError: 'Failed to create category',
    updateCategoryError: 'Failed to update category',
    deleteCategoryError: 'Failed to delete category',
    fetchCategoryError: 'Failed to fetch category details',
    deleteCategoryConfirm: 'Are you sure you want to delete category "{name}"?',
    createCategorySuccess: 'Category created successfully',
    updateCategorySuccess: 'Category updated successfully',
    deleteCategorySuccess: 'Category deleted successfully',
    budgetLimits: 'Budget Limits',
    addBudgetLimit: 'Add Budget Limit',
    editBudgetLimit: 'Edit Budget Limit',
    updateBudgetLimit: 'Update Budget Limit',
    deleteBudgetLimit: 'Delete Budget Limit',
    deleteBudgetLimitConfirm: 'Are you sure you want to delete this budget limit?',
    deleteBudgetLimitError: 'Failed to delete budget limit',
    noBudgetLimits: 'No budget limits set',
    budgetAmount: 'Budget Amount',
    budgetPeriod: 'Budget Period',
    daily: 'Daily',
    weekly: 'Weekly',
    monthly: 'Monthly',
    yearly: 'Yearly',
    addBudgetLimitError: 'Failed to add budget limit',
    updateBudgetLimitError: 'Failed to update budget limit',
    fetchBudgetLimitError: 'Failed to fetch budget limit details',
    createBudgetLimitSuccess: 'Budget limit created successfully',
    updateBudgetLimitSuccess: 'Budget limit updated successfully',
    deleteBudgetLimitSuccess: 'Budget limit deleted successfully',
    viewAllBudgetLimits: 'View All Budget Limits',
    budgetLimitHelp: 'About Budget Limits',
    budgetLimitHelpText: 'Budget limits help you track your spending in specific categories. Set a limit for a period (daily, weekly, monthly, or yearly) to monitor your expenses and stay within your financial goals.',
    category: 'Category',
    notificationThreshold: 'Notification Threshold',
    rolloverUnused: 'Rollover Unused Budget',
    rolloverUnusedHelp: 'If enabled, any unused budget will be added to the next period.',
  },
  priorities: {
    priorities: 'Priorities',
    priority: 'Priority',
    createPriority: 'Create Priority',
    editPriority: 'Edit Priority',
    updatePriority: 'Update Priority',
    deletePriority: 'Delete Priority',
    priorityName: 'Priority Name',
    priorityNamePlaceholder: 'Enter priority name',
    description: 'Description',
    descriptionPlaceholder: 'Enter priority description',
    color: 'Color',
    selectPriority: 'Select Priority',
    noPriorities: 'No priorities found',
    createPriorityError: 'Failed to create priority',
    updatePriorityError: 'Failed to update priority',
    deletePriorityError: 'Failed to delete priority',
    fetchPriorityError: 'Failed to fetch priority details',
    deletePriorityConfirm: 'Are you sure you want to delete priority "{name}"?',
    seedDefaultPriorities: 'Seed Default Priorities',
    seedSuccess: 'Default priorities seeded successfully',
    seedError: 'Failed to seed default priorities',
    createPrioritySuccess: 'Priority created successfully',
    updatePrioritySuccess: 'Priority updated successfully',
    deletePrioritySuccess: 'Priority deleted successfully',
    urgent: 'Urgent',
    high: 'High',
    normal: 'Normal',
    low: 'Low',
    urgentDescription: 'Critical tasks that require immediate attention',
    highDescription: 'Important tasks that should be completed soon',
    normalDescription: 'Regular tasks with standard priority',
    lowDescription: 'Tasks that can be completed when time permits',
  },
  settings: {
    settings: 'Settings',
    language: 'Language',
    theme: 'Theme',
    currency: 'Currency',
    notifications: 'Notifications',
    notificationsAndReminders: 'Notifications & Reminders',
    security: 'Security',
    about: 'About',
    help: 'Help',
    feedback: 'Feedback',
    logout: 'Logout',
    account: 'Account',
    darkMode: 'Dark Mode',
    advanced: 'Advanced',
    selectLanguage: 'Select Language',
    version: 'Version',
    termsOfService: 'Terms of Service',
    privacyPolicy: 'Privacy Policy',
    contactUs: 'Contact Us',
    rateApp: 'Rate App',
    shareApp: 'Share App',
    deleteAccount: 'Delete Account',
    deleteAccountConfirm: 'Are you sure you want to delete your account? This action cannot be undone.',
    deleteAccountSuccess: 'Your account has been deleted successfully.',
    deleteAccountError: 'Failed to delete your account. Please try again.',
  },
  validation: {
    required: 'This field is required',
    email: 'Please enter a valid email address',
    minLength: 'Must be at least {length} characters',
    maxLength: 'Must be at most {length} characters',
    passwordMatch: 'Passwords must match',
    passwordComplexity: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
    phoneNumber: 'Please enter a valid phone number',
    number: 'Please enter a valid number',
    positive: 'Please enter a positive number',
    percentageRange: 'Please enter a value between 0 and 100',
    fixErrors: 'Please fix the errors in the form',
  },

  loans: {
    loans: 'Loans',
    loan: 'Loan',
    loanDetails: 'Loan Details',
    addLoan: 'Add Loan',
    editLoan: 'Edit Loan',
    deleteLoan: 'Delete Loan',
    deleteLoanConfirmation: 'Are you sure you want to delete this loan?',
    deleteLoanError: 'Failed to delete loan. Please try again.',
    loanNotFound: 'Loan not found',
    updateLoanError: 'Failed to update loan. Please try again.',
    noLoans: 'No Loans',
    noLoansMessage: 'You have not added any loans yet.',

    // Loan properties
    title: 'Title',
    amount: 'Amount',
    interestRate: 'Interest Rate',
    interestType: 'Interest Type',
    paymentFrequency: 'Payment Frequency',
    startDate: 'Start Date',
    endDate: 'End Date',
    lender: 'Lender',
    borrower: 'Borrower',
    externalEntity: 'External Entity',
    externalEntityName: 'External Entity Name',
    status: 'Status',
    notes: 'Notes',
    totalPayable: 'Total Payable',
    remainingAmount: 'Remaining Amount',
    paidAmount: 'Paid Amount',
    monthlyEMI: 'Monthly EMI',
    predictedCompletionDate: 'Predicted Completion Date',

    // Interest types
    interestTypes: {
      flat: 'Flat',
      reducingBalance: 'Reducing Balance',
      compound: 'Compound'
    },

    // Frequency types
    frequencyTypes: {
      daily: 'Daily',
      weekly: 'Weekly',
      monthly: 'Monthly',
      quarterly: 'Quarterly',
      yearly: 'Yearly'
    },

    // Loan status
    statusOptions: {
      active: 'Active',
      completed: 'Completed',
      defaulted: 'Defaulted'
    },

    // Loan types
    loanType: 'Loan Type',
    loanTypes: {
      given: 'Given',
      taken: 'Taken',
      external: 'External'
    },

    // Loan dashboard
    dashboard: 'Loan Dashboard',
    loansGiven: 'Loans Given',
    loansTaken: 'Loans Taken',
    totalLoansGiven: 'Total Loans Given',
    totalLoansTaken: 'Total Loans Taken',
    activeLoans: 'Active Loans',
    completedLoans: 'Completed Loans',

    // Loan timeline
    timeline: 'Timeline',
    paymentSchedule: 'Payment Schedule',

    // Loan payments
    payments: 'Payments',
    payment: 'Payment',
    addPayment: 'Add Payment',
    editPayment: 'Edit Payment',
    deletePayment: 'Delete Payment',
    deletePaymentConfirmation: 'Are you sure you want to delete this payment?',
    deletePaymentError: 'Failed to delete payment. Please try again.',
    paymentDate: 'Payment Date',
    paymentAmount: 'Payment Amount',
    principalAmount: 'Principal Amount',
    interestAmount: 'Interest Amount',
    paymentMethod: 'Payment Method',
    noPayments: 'No Payments',
    noPaymentsMessage: 'No payments have been made for this loan yet.',

    // Loan reminders
    reminders: 'Reminders',
    reminder: 'Reminder',
    addReminder: 'Add Reminder',
    editReminder: 'Edit Reminder',
    deleteReminder: 'Delete Reminder',
    deleteReminderConfirmation: 'Are you sure you want to delete this reminder?',
    deleteReminderError: 'Failed to delete reminder. Please try again.',
    reminderDate: 'Reminder Date',
    reminderMessage: 'Message',
    isActive: 'Is Active',
    isSent: 'Is Sent',
    sentAt: 'Sent At',
    noReminders: 'No Reminders',
    noRemindersMessage: 'No reminders have been set for this loan yet.',

    // EMI calculator
    emiCalculator: 'EMI Calculator',
    calculateEMI: 'Calculate EMI',
    emiResult: 'Your EMI will be',

    // Loan summary
    summary: 'Loan Summary',
    loanSummary: 'Loan Summary',
    paymentBreakdown: 'Payment Breakdown',
    principalBreakdown: 'Principal',
    interestBreakdown: 'Interest',

    // Loan filters
    filterLoans: 'Filter Loans',
    filterByStatus: 'Filter by Status',
    filterByType: 'Filter by Type',
    filterByDate: 'Filter by Date',

    // Loan actions
    makePayment: 'Make Payment',
    viewTimeline: 'View Timeline',
    setReminder: 'Set Reminder',

    // Loan form
    loanForm: 'Loan Form',
    loanDetailsForm: 'Loan Details',
    paymentDetails: 'Payment Details',
    reminderDetails: 'Reminder Details',
    selectInterestType: 'Select Interest Type',
    selectFrequencyType: 'Select Frequency Type',
    selectStatus: 'Select Status',
    selectLoanType: 'Select Loan Type',

    // Loan errors
    invalidLoanData: 'Invalid loan data. Please check your input.',
    invalidPaymentData: 'Invalid payment data. Please check your input.',
    invalidReminderData: 'Invalid reminder data. Please check your input.',
  },
  receipts: {
    receipts: 'Receipts',
    receiptHistory: 'Receipt History',
    receiptDetails: 'Receipt Details',
    noReceipts: 'No Receipts',
    addReceiptPrompt: 'Add a receipt by taking a photo or uploading an image.',
    scanReceipt: 'Scan Receipt',
    receiptImage: 'Receipt Image',
    addReceiptImage: 'Add Receipt Image',
    cameraPermissionDenied: 'Camera permission denied. Please enable camera access in your device settings.',
    mediaLibraryPermissionDenied: 'Media library permission denied. Please enable media access in your device settings.',
    photoError: 'Error taking photo. Please try again.',
    imagePickerError: 'Error picking image. Please try again.',
    removeImage: 'Remove Image',
    removeImageConfirmation: 'Are you sure you want to remove this image?',
    addReceipt: 'Add Receipt',
    selectImageSource: 'Select image source',
    camera: 'Camera',
    gallery: 'Gallery',
    ocrAvailable: 'OCR scanning available. Tap the scan icon to extract data.',
    ocrDisabled: 'OCR scanning is disabled for this receipt.',
    ocrComingSoon: 'OCR scanning coming soon.',
    ocrError: 'Error processing receipt with OCR. Please try again.',
    serverOcrError: 'Error processing receipt on server. Please try again.',
    networkRequired: 'Network connection required for this operation.',
    languageDownloadError: 'Error downloading language data. Please try again.',
    languageNotAvailable: 'Language data not available. Please download it first.',
    downloadingLanguage: 'Downloading language data...',
    downloadComplete: 'Language data downloaded successfully.',
    processingReceipt: 'Processing receipt',
    processReceipt: 'Process Receipt',
    reprocess: 'Reprocess',
    selectProcessingOptions: 'Select processing options',
    processingMethod: 'Processing Method',
    deviceProcessing: 'Device',
    serverProcessing: 'Server',
    language: 'Language',
    dataExtracted: 'Data extracted',
    processExtractedData: 'Process Extracted Data',
    merchantName: 'Merchant Name',
    merchantNamePlaceholder: 'Enter merchant name',
    merchantNameRequired: 'Merchant name is required',
    date: 'Date',
    totalAmount: 'Total Amount',
    invalidAmount: 'Invalid amount',
    items: 'Items',
    noItems: 'No items found',
    itemNamePlaceholder: 'Item name',
    invalidItems: 'Some items have invalid data',
    category: 'Category',
    selectCategory: 'Select category',
    showRawText: 'Show raw text',
    hideRawText: 'Hide raw text',
    noRawText: 'No raw text available',
    status: 'Status',
    processed: 'Processed',
    pending: 'Pending',
    linkedTransaction: 'Linked Transaction',
    viewTransaction: 'View Transaction',
    unknownMerchant: 'Unknown Merchant',
    noTransaction: 'No transaction linked',
    noImage: 'No image available',
    filterReceipts: 'Filter Receipts',
    dateRange: 'Date Range',
    amountRange: 'Amount Range',
    merchant: 'Merchant',
    processingStatus: 'Processing Status',
    loadError: 'Error loading receipts',
    receiptNotFound: 'Receipt not found',
    alreadyProcessed: 'Already Processed',
    reprocessConfirmation: 'This receipt has already been processed. Do you want to reprocess it?',
    noImageToProcess: 'No image to process',
    updateSuccess: 'Receipt updated successfully',
    updateSuccessOffline: 'Receipt updated successfully (offline)',
    updateError: 'Error updating receipt',
    deleteReceipt: 'Delete Receipt',
    deleteConfirmation: 'Are you sure you want to delete this receipt?',
    deleteSuccessOffline: 'Receipt marked for deletion (offline)',
    deleteError: 'Error deleting receipt',
    noTransactionLinked: 'No transaction linked to this receipt',
    invalidReceiptId: 'Invalid receipt ID',
  },

  sync: {
    sync: 'Sync',
    syncStatus: 'Sync Status',
    syncing: 'Syncing...',
    synced: 'Synced',
    syncError: 'Sync Error',
    offline: 'Offline',
    upToDate: 'Up to Date',
    pendingChanges: '{{count}} Pending',
    neverSynced: 'Never synced',
    lastSynced: 'Last synced {{time}}',
    syncNow: 'Sync Now',
    viewConflicts: 'View Conflicts',
    syncQueue: 'Sync Queue',
    clearQueue: 'Clear Queue',
    clearSyncQueue: 'Clear Sync Queue',
    clearSyncQueueConfirmation: 'Are you sure you want to clear the sync queue? This will discard all pending changes.',
    syncQueueCleared: 'Sync queue cleared successfully',
    clearSyncQueueError: 'Error clearing sync queue',
    queueEmpty: 'Queue Empty',
    allChangesSynced: 'All changes have been synced',
    create: 'Create',
    update: 'Update',
    delete: 'Delete',
    conflict: 'Conflict',
    conflictResolution: 'Conflict Resolution',
    conflictsCount: '{{count}} Conflicts',
    noConflicts: 'No Conflicts',
    allConflictsResolved: 'All Conflicts Resolved',
    noConflictsMessage: 'There are no conflicts to resolve',
    selectConflict: 'Select a conflict to resolve',
    localVersion: 'Local Version',
    remoteVersion: 'Remote Version',
    useLocal: 'Use Local',
    useRemote: 'Use Remote',
    createMerged: 'Create Merged Version',
    mergeEditor: 'Merge Editor',
    editMergedData: 'Edit the merged data to resolve the conflict',
    invalidJson: 'Invalid JSON format',
    noMergedData: 'No merged data available',
    conflictResolved: 'Conflict resolved successfully',
    conflictResolutionFailed: 'Failed to resolve conflict',
    conflictResolutionError: 'Error resolving conflict',
    resolvingConflict: 'Resolving conflict...',
    noConflictProvided: 'No conflict provided',
    conflictsDetected: 'Conflicts Detected',
    conflictsMessage: 'Some changes could not be synced due to conflicts. Please resolve them.',
    conflicts: 'Conflicts',
  },

  migration: {
    title: 'Data Migration',
    description: 'We need to migrate your data to a new storage system for better offline support. This may take a few moments.',
    inProgress: 'Migrating your data to the new storage system...',
    completed: 'Data migration completed successfully!',
    migrating: 'Migrating data...',
    migrationSuccess: 'Migration Successful',
    migrationFailed: 'Migration Failed',
    migrationDetails: 'Migration Details',
    clearStorage: 'Clear Old Storage',
    skipClearing: 'Skip',
    clearStoragePrompt: 'Would you like to clear the old storage to free up space? Your data has been safely migrated.',
    retry: 'Retry Migration',
    error: 'Migration Error',
    errorMessage: 'An error occurred during migration: {{error}}',
    success: 'Success',
    storageCleared: 'Old storage cleared successfully.',
    clearStorageError: 'Error clearing old storage: {{error}}',
    authTokens: 'Auth Tokens',
  },

  family: {
    families: 'Families',
    family: 'Family',
    createFamily: 'Create Family',
    editFamily: 'Edit Family',
    familyName: 'Family Name',
    familyDescription: 'Family Description',
    familySettings: 'Family Settings',
    members: 'Members',
    addMember: 'Add Member',
    removeMember: 'Remove Member',
    inviteCode: 'Invite Code',
    generateInviteCode: 'Generate Invite Code',
    joinFamily: 'Join Family',
    leaveFamily: 'Leave Family',
    admin: 'Admin',
    member: 'Member',
    role: 'Role',
    updateRole: 'Update Role',
    createdBy: 'Created by',
    joinedAt: 'Joined at',
    noFamilies: 'You are not a member of any family yet',
    createFamilyPrompt: 'Create a family or join one using an invite code',
    inviteCodeExpiry: 'This code will expire in 7 days',
    copyInviteCode: 'Copy Invite Code',
    inviteCodeCopied: 'Invite code copied to clipboard',
    enterInviteCode: 'Enter invite code',
    invalidInviteCode: 'Invalid invite code',
    confirmLeaveFamily: 'Are you sure you want to leave this family?',
    confirmRemoveMember: 'Are you sure you want to remove this member?',
    lastAdmin: 'You cannot leave as you are the last admin. Please assign another admin first.',
    familyManagement: 'Family Management',
    noInviteCode: 'No invite code available',
    addMemberExplanation: 'To add a member to your family:\n\n1. Generate an invite code\n2. Share this code with the person you want to add\n3. They must register in the app first if they haven\'t already\n4. They should go to the Families tab and tap "Join Family"\n5. They enter the invite code you shared\n\nOnce they join using your code, they will appear in your family members list.',
    showInviteCode: 'Show Invite Code',
    howToAddMembers: 'How to Add Members',
    step1: 'Generate an invite code by clicking the button below',
    step2: 'Share the invite code with the person you want to add',
    step3: 'They must register in the app and use the code to join your family',
    alreadyMember: 'You are already a member of this family.',
    inviteCodeExpired: 'This invite code has expired. Please ask for a new one.',
    joinFailed: 'Failed to join the family. Please try again later.',
    joinSuccess: 'You have successfully joined the family. You can now view it in your families list.',
    noPermission: 'You do not have permission to access this family.',
    familyNotFound: 'Family not found. It may have been deleted.',
    notMember: 'You are not a member of this family.',
    leaveFailed: 'Failed to leave the family. Please try again later.',
  },
};

// Nepali translations
const neTranslations = {
  common: {
    appName: 'हिसाब-किताब',
    loading: 'लोड हुँदैछ...',
    error: 'त्रुटि',
    success: 'सफलता',
    cancel: 'रद्द गर्नुहोस्',
    save: 'सुरक्षित गर्नुहोस्',
    delete: 'मेटाउनुहोस्',
    edit: 'सम्पादन गर्नुहोस्',
    add: 'थप्नुहोस्',
    search: 'खोज्नुहोस्',
    filter: 'फिल्टर गर्नुहोस्',
    sort: 'क्रमबद्ध गर्नुहोस्',
    back: 'पछाडि',
    next: 'अर्को',
    done: 'सम्पन्न',
    confirm: 'पुष्टि गर्नुहोस्',
    yes: 'हो',
    no: 'होइन',
    ok: 'ठिक छ',
    comingSoon: 'चाँडै आउँदैछ',
    to: 'लाई',
    tryAgain: 'फेरि प्रयास गर्नुहोस्',
    unexpectedError: 'अनपेक्षित त्रुटि भयो। कृपया फेरि प्रयास गर्नुहोस्।',
    networkError: 'नेटवर्क त्रुटि। कृपया आफ्नो कनेक्सन जाँच गर्नुहोस् र फेरि प्रयास गर्नुहोस्।',
    serverError: 'सर्भर त्रुटि। कृपया पछि फेरि प्रयास गर्नुहोस्।',
    validationError: 'कृपया आफ्नो इनपुट जाँच गर्नुहोस् र फेरि प्रयास गर्नुहोस्।',
    permissionError: 'तपाईंसँग यो कार्य गर्ने अनुमति छैन।',
    notFoundError: 'अनुरोध गरिएको स्रोत फेला परेन।',
    authenticationError: 'प्रमाणीकरण त्रुटि। कृपया फेरि लगइन गर्नुहोस्।',
    navigationError: 'नेभिगेसन त्रुटि',
    restartApp: 'नेभिगेसनमा त्रुटि भयो। कृपया एप पुन: सुरु गर्नुहोस्।',
    selectDate: 'मिति छान्नुहोस्',
  },
  auth: {
    login: 'लगइन',
    register: 'दर्ता गर्नुहोस्',
    logout: 'लगआउट',
    email: 'इमेल',
    username: 'प्रयोगकर्ता नाम',
    password: 'पासवर्ड',
    confirmPassword: 'पासवर्ड पुष्टि गर्नुहोस्',
    forgotPassword: 'पासवर्ड बिर्सनुभयो?',
    resetPassword: 'पासवर्ड रिसेट गर्नुहोस्',
    firstName: 'पहिलो नाम',
    lastName: 'थर',
    phoneNumber: 'फोन नम्बर',
    loginSuccess: 'लगइन सफल भयो',
    registerSuccess: 'दर्ता सफल भयो',
    logoutSuccess: 'लगआउट सफल भयो',
    emptyFields: 'कृपया सबै आवश्यक फिल्डहरू भर्नुहोस्',
    passwordMismatch: 'पासवर्डहरू मेल खाँदैनन्',
    loginFailed: 'लगइन असफल भयो। कृपया आफ्नो प्रमाणहरू जाँच गर्नुहोस्।',
    registerFailed: 'दर्ता असफल भयो। कृपया फेरि प्रयास गर्नुहोस्।',
    requiredFields: 'कृपया सबै आवश्यक फिल्डहरू भर्नुहोस्',
    noAccount: 'खाता छैन? दर्ता गर्नुहोस्',
    haveAccount: 'पहिले नै खाता छ? लगइन गर्नुहोस्',
    logoutConfirm: 'के तपाईं लगआउट गर्न निश्चित हुनुहुन्छ?',
    account: 'खाता',
    darkMode: 'डार्क मोड',
  },
  profile: {
    profile: 'प्रोफाइल',
    updateProfile: 'प्रोफाइल अपडेट गर्नुहोस्',
    changePassword: 'पासवर्ड परिवर्तन गर्नुहोस्',
    changeEmail: 'इमेल परिवर्तन गर्नुहोस्',
    currentPassword: 'हालको पासवर्ड',
    newPassword: 'नयाँ पासवर्ड',
    confirmNewPassword: 'नयाँ पासवर्ड पुष्टि गर्नुहोस्',
    updateSuccess: 'प्रोफाइल सफलतापूर्वक अपडेट गरियो',
    updateFailed: 'प्रोफाइल अपडेट गर्न असफल भयो',
    passwordChangeSuccess: 'पासवर्ड सफलतापूर्वक परिवर्तन गरियो',
    passwordChangeFailed: 'पासवर्ड परिवर्तन गर्न असफल भयो',
    incorrectCurrentPassword: 'तपाईंले प्रविष्ट गर्नुभएको हालको पासवर्ड गलत छ',
    passwordComplexity: 'नयाँ पासवर्डले जटिलता आवश्यकताहरू पूरा गर्दैन',
    emailChangeSuccess: 'इमेल सफलतापूर्वक परिवर्तन गरियो',
    emailChangeFailed: 'इमेल परिवर्तन गर्न असफल भयो',
    roles: 'भूमिकाहरू',
  },
  home: {
    welcome: 'हिसाब-किताबमा स्वागत छ',
    totalBalance: 'कुल ब्यालेन्स',
    income: 'आम्दानी',
    expense: 'खर्च',
    recentTransactions: 'हालैका लेनदेनहरू',
    viewAll: 'सबै हेर्नुहोस्',
    noTransactions: 'अहिलेसम्म कुनै लेनदेन छैन',
  },
  accounts: {
    accounts: 'खाताहरू',
    title: 'खाताहरू',
    addAccount: 'खाता थप्नुहोस्',
    createAccount: 'खाता सिर्जना गर्नुहोस्',
    editAccount: 'खाता सम्पादन गर्नुहोस्',
    updateAccount: 'खाता अपडेट गर्नुहोस्',
    deleteAccount: 'खाता मेटाउनुहोस्',
    accountDetails: 'खाता विवरण',
    accountName: 'खाता नाम',
    accountNamePlaceholder: 'खाता नाम प्रविष्ट गर्नुहोस्',
    accountType: 'खाता प्रकार',
    balance: 'ब्यालेन्स',
    currentBalance: 'हालको ब्यालेन्स',
    initialBalance: 'प्रारम्भिक ब्यालेन्स',
    currency: 'मुद्रा',
    cash: 'नगद',
    bank: 'बैंक',
    creditCard: 'क्रेडिट कार्ड',
    investment: 'लगानी',
    savings: 'बचत',
    loan: 'ऋण',
    other: 'अन्य',
    isActive: 'सक्रिय छ',
    inactive: 'निष्क्रिय',
    shared: 'साझा गरिएको',
    excludeFromStats: 'तथ्याङ्कबाट बाहिर राख्नुहोस्',
    accountOwner: 'खाता मालिक',
    owner: 'मालिक',
    personalAccount: 'व्यक्तिगत खाता',
    familyAccount: 'परिवार खाता',
    personal: 'व्यक्तिगत',
    lastUpdated: 'अन्तिम अपडेट',
    totalBalance: 'कुल ब्यालेन्स',
    account: 'खाता',
    noAccounts: 'कुनै खाता फेला परेन',
    createAccountError: 'खाता सिर्जना गर्न असफल भयो',
    updateAccountError: 'खाता अपडेट गर्न असफल भयो',
    deleteAccountError: 'खाता मेटाउन असफल भयो',
    fetchAccountError: 'खाता विवरण प्राप्त गर्न असफल भयो',
    deleteAccountConfirm: 'के तपाईं "{name}" खाता मेटाउन निश्चित हुनुहुन्छ?',
    shareAccount: 'खाता साझा गर्नुहोस्',
    shareWithOthers: 'अरूसँग साझा गर्नुहोस्',
    sharedWith: 'साझा गरिएको',
    share: 'साझा गर्नुहोस्',
    selectFamily: 'परिवार छान्नुहोस्',
    selectFamilyPlaceholder: 'परिवार छान्नुहोस्',
    selectMember: 'सदस्य छान्नुहोस्',
    selectPermissions: 'अनुमतिहरू छान्नुहोस्',
    viewOnly: 'हेर्ने मात्र',
    viewAndEdit: 'हेर्ने र सम्पादन गर्ने',
    fullAccess: 'पूर्ण पहुँच',
    permissionLevels: 'अनुमति स्तरहरू',
    viewOnlyDescription: 'प्रयोगकर्ताले खाता विवरण र लेनदेनहरू मात्र हेर्न सक्छन्। उनीहरूले कुनै परिवर्तन गर्न सक्दैनन्।',
    viewAndEditDescription: 'प्रयोगकर्ताले खाता हेर्न र लेनदेनहरू थप्न/सम्पादन गर्न सक्छन्। उनीहरूले खाता मेटाउन वा अन्य सदस्यहरू हटाउन सक्दैनन्।',
    fullAccessDescription: 'प्रयोगकर्तासँग खाता मेटाउने र यसलाई कोसँग साझा गरिएको छ भन्ने व्यवस्थापन गर्ने सहित खातामाथि पूर्ण नियन्त्रण छ।',
    noAvailableMembers: 'साझा गर्न कुनै उपलब्ध सदस्य छैन',
    selectMemberError: 'कृपया परिवार सदस्य छान्नुहोस्',
    shareSuccess: 'खाता सफलतापूर्वक साझा गरियो',
    shareError: 'खाता साझा गर्न असफल भयो',
    removeShare: 'साझा हटाउनुहोस्',
    removeShareConfirm: 'के तपाईं {name} सँगको साझा हटाउन निश्चित हुनुहुन्छ?',
    removeShareError: 'साझा हटाउन असफल भयो',
  },
  transactions: {
    transactions: 'लेनदेनहरू',
    transactionDetails: 'लेनदेन विवरण',
    addTransaction: 'लेनदेन थप्नुहोस्',
    editTransaction: 'लेनदेन सम्पादन गर्नुहोस्',
    deleteTransaction: 'लेनदेन मेटाउनुहोस्',
    deleteTransactionConfirmation: 'के तपाईं यो लेनदेन मेटाउन निश्चित हुनुहुन्छ?',
    deleteTransactionError: 'लेनदेन मेटाउन असफल भयो। कृपया फेरि प्रयास गर्नुहोस्।',
    savedOffline: 'लेनदेन अफलाइन सुरक्षित गरियो। तपाईं अनलाइन हुँदा यो सिङ्क हुनेछ।',
    transactionNotFound: 'लेनदेन फेला परेन',
    updateTransactionError: 'लेनदेन अपडेट गर्न असफल भयो। कृपया फेरि प्रयास गर्नुहोस्।',
    noTransactions: 'कुनै लेनदेन छैन',
    noTransactionsMessage: 'तपाईंले अहिलेसम्म कुनै लेनदेन थप्नुभएको छैन।',
    type: 'प्रकार',
    amount: 'रकम',
    date: 'मिति',
    account: 'खाता',
    toAccount: 'गन्तव्य खाता',
    category: 'वर्ग',
    description: 'विवरण',
    descriptionPlaceholder: 'विवरण प्रविष्ट गर्नुहोस्...',
    priority: 'प्राथमिकता',
    status: 'स्थिति',
    tags: 'ट्यागहरू',
    tagsPlaceholder: 'अल्पविरामले छुट्याइएका ट्यागहरू प्रविष्ट गर्नुहोस्...',
    location: 'स्थान',
    locationPlaceholder: 'स्थान प्रविष्ट गर्नुहोस्...',
    items: 'वस्तुहरू',
    item: 'वस्तु',
    itemName: 'वस्तुको नाम',
    itemNamePlaceholder: 'वस्तुको नाम प्रविष्ट गर्नुहोस्...',
    quantity: 'परिमाण',
    notes: 'नोटहरू',
    notesPlaceholder: 'नोटहरू प्रविष्ट गर्नुहोस्...',
    addItem: 'वस्तु थप्नुहोस्',
    editItem: 'वस्तु सम्पादन गर्नुहोस्',
    noItems: 'कुनै वस्तु थपिएको छैन',
    oneItem: '१ वस्तु',
    multipleItems: '{{count}} वस्तुहरू',
    income: 'आम्दानी',
    expense: 'खर्च',
    transfer: 'ट्रान्सफर',
    transferBetweenAccounts: '{{from}} बाट {{to}} मा ट्रान्सफर',
    completed: 'पूरा भएको',
    pending: 'प्रक्रियामा',
    rejected: 'अस्वीकृत',
    uncategorized: 'वर्गीकृत नगरिएको',
    filterTransactions: 'लेनदेनहरू फिल्टर गर्नुहोस्',
    startDate: 'सुरु मिति',
    endDate: 'अन्त्य मिति',
    selectStartDate: 'सुरु मिति छान्नुहोस्',
    selectEndDate: 'अन्त्य मिति छान्नुहोस्',
    search: 'खोज्नुहोस्',
    searchPlaceholder: 'लेनदेनहरू खोज्नुहोस्...',
    sortBy: 'क्रमबद्ध गर्ने आधार',
    order: 'क्रम',
    sortByDate: 'मिति',
    sortByAmount: 'रकम',
    sortByCategory: 'वर्ग',
    sortByAccount: 'खाता',
    ascending: 'आरोही',
    descending: 'अवरोही',
    balance: 'ब्यालेन्स',
    from: 'बाट',
    to: 'लाई',
    noTransferCategory: 'ट्रान्सफर वर्ग फेला परेन। कृपया प्रशासकलाई सम्पर्क गर्नुहोस्।',
    transferNote: 'ट्रान्सफरले तपाईंको समग्र ब्यालेन्सलाई प्रभावित नगरी तपाईंको खाताहरू बीच पैसा सार्छ।',
  },
  categories: {
    categories: 'वर्गहरू',
    addCategory: 'वर्ग थप्नुहोस्',
    createCategory: 'वर्ग सिर्जना गर्नुहोस्',
    editCategory: 'वर्ग सम्पादन गर्नुहोस्',
    updateCategory: 'वर्ग अपडेट गर्नुहोस्',
    deleteCategory: 'वर्ग मेटाउनुहोस्',
    categoryDetails: 'वर्ग विवरण',
    categoryName: 'वर्ग नाम',
    categoryNamePlaceholder: 'वर्ग नाम प्रविष्ट गर्नुहोस्',
    categoryType: 'वर्ग प्रकार',
    income: 'आम्दानी',
    expense: 'खर्च',
    icon: 'आइकन',
    color: 'रंग',
    selectIcon: 'आइकन छान्नुहोस्',
    selectColor: 'रंग छान्नुहोस्',
    parentCategory: 'मूल वर्ग',
    noParent: 'कुनै मूल वर्ग छैन',
    subcategories: 'उपवर्गहरू',
    mainCategories: 'मुख्य वर्गहरू',
    categoryOwner: 'वर्ग मालिक',
    owner: 'मालिक',
    personal: 'व्यक्तिगत',
    personalCategory: 'व्यक्तिगत वर्ग',
    familyCategory: 'परिवार वर्ग',
    system: 'प्रणाली',
    createdAt: 'सिर्जना गरिएको',
    noCategories: 'कुनै वर्ग फेला परेन',
    createCategoryError: 'वर्ग सिर्जना गर्न असफल भयो',
    updateCategoryError: 'वर्ग अपडेट गर्न असफल भयो',
    deleteCategoryError: 'वर्ग मेटाउन असफल भयो',
    fetchCategoryError: 'वर्ग विवरण प्राप्त गर्न असफल भयो',
    deleteCategoryConfirm: 'के तपाईं "{name}" वर्ग मेटाउन निश्चित हुनुहुन्छ?',
    budgetLimits: 'बजेट सीमाहरू',
    addBudgetLimit: 'बजेट सीमा थप्नुहोस्',
    editBudgetLimit: 'बजेट सीमा सम्पादन गर्नुहोस्',
    updateBudgetLimit: 'बजेट सीमा अपडेट गर्नुहोस्',
    deleteBudgetLimit: 'बजेट सीमा मेटाउनुहोस्',
    deleteBudgetLimitConfirm: 'के तपाईं यो बजेट सीमा मेटाउन निश्चित हुनुहुन्छ?',
    deleteBudgetLimitError: 'बजेट सीमा मेटाउन असफल भयो',
    noBudgetLimits: 'कुनै बजेट सीमा सेट गरिएको छैन',
    budgetAmount: 'बजेट रकम',
    budgetPeriod: 'बजेट अवधि',
    daily: 'दैनिक',
    weekly: 'साप्ताहिक',
    monthly: 'मासिक',
    yearly: 'वार्षिक',
    addBudgetLimitError: 'बजेट सीमा थप्न असफल भयो',
    updateBudgetLimitError: 'बजेट सीमा अपडेट गर्न असफल भयो',
    fetchBudgetLimitError: 'बजेट सीमा विवरण प्राप्त गर्न असफल भयो',
    createBudgetLimitSuccess: 'बजेट सीमा सफलतापूर्वक सिर्जना गरियो',
    updateBudgetLimitSuccess: 'बजेट सीमा सफलतापूर्वक अपडेट गरियो',
    deleteBudgetLimitSuccess: 'बजेट सीमा सफलतापूर्वक मेटाइयो',
    viewAllBudgetLimits: 'सबै बजेट सीमाहरू हेर्नुहोस्',
    budgetLimitHelp: 'बजेट सीमाहरू बारे',
    budgetLimitHelpText: 'बजेट सीमाहरूले तपाईंलाई विशिष्ट वर्गहरूमा खर्च ट्र्याक गर्न मद्दत गर्छ। तपाईंको खर्चहरू अनुगमन गर्न र आफ्नो वित्तीय लक्ष्यहरू भित्र रहन एक अवधि (दैनिक, साप्ताहिक, मासिक, वा वार्षिक) को लागि सीमा सेट गर्नुहोस्।',
    category: 'वर्ग',
    notificationThreshold: 'सूचना सीमा',
    rolloverUnused: 'प्रयोग नगरिएको बजेट सार्नुहोस्',
    rolloverUnusedHelp: 'यदि सक्षम गरिएको छ भने, प्रयोग नगरिएको कुनै पनि बजेट अर्को अवधिमा थपिनेछ।',
  },
  priorities: {
    priorities: 'प्राथमिकताहरू',
    priority: 'प्राथमिकता',
    createPriority: 'प्राथमिकता सिर्जना गर्नुहोस्',
    editPriority: 'प्राथमिकता सम्पादन गर्नुहोस्',
    updatePriority: 'प्राथमिकता अपडेट गर्नुहोस्',
    deletePriority: 'प्राथमिकता मेटाउनुहोस्',
    priorityName: 'प्राथमिकता नाम',
    priorityNamePlaceholder: 'प्राथमिकता नाम प्रविष्ट गर्नुहोस्',
    description: 'विवरण',
    descriptionPlaceholder: 'प्राथमिकता विवरण प्रविष्ट गर्नुहोस्',
    color: 'रंग',
    selectPriority: 'प्राथमिकता छान्नुहोस्',
    noPriorities: 'कुनै प्राथमिकता फेला परेन',
    createPriorityError: 'प्राथमिकता सिर्जना गर्न असफल भयो',
    updatePriorityError: 'प्राथमिकता अपडेट गर्न असफल भयो',
    deletePriorityError: 'प्राथमिकता मेटाउन असफल भयो',
    fetchPriorityError: 'प्राथमिकता विवरण प्राप्त गर्न असफल भयो',
    deletePriorityConfirm: 'के तपाईं "{name}" प्राथमिकता मेटाउन निश्चित हुनुहुन्छ?',
    seedDefaultPriorities: 'पूर्वनिर्धारित प्राथमिकताहरू सिड गर्नुहोस्',
    seedSuccess: 'पूर्वनिर्धारित प्राथमिकताहरू सफलतापूर्वक सिड गरियो',
    seedError: 'पूर्वनिर्धारित प्राथमिकताहरू सिड गर्न असफल भयो',
    urgent: 'अत्यावश्यक',
    high: 'उच्च',
    normal: 'सामान्य',
    low: 'न्यून',
    urgentDescription: 'तत्काल ध्यान दिनुपर्ने महत्वपूर्ण कार्यहरू',
    highDescription: 'चाँडै पूरा गर्नुपर्ने महत्वपूर्ण कार्यहरू',
    normalDescription: 'मानक प्राथमिकता भएका नियमित कार्यहरू',
    lowDescription: 'समय अनुकूल हुँदा पूरा गर्न सकिने कार्यहरू',
  },
  validation: {
    required: 'यो फिल्ड आवश्यक छ',
    email: 'कृपया मान्य इमेल ठेगाना प्रविष्ट गर्नुहोस्',
    minLength: 'कम्तिमा {length} अक्षर हुनुपर्छ',
    maxLength: 'बढीमा {length} अक्षर हुनुपर्छ',
    passwordMatch: 'पासवर्डहरू मेल खानुपर्छ',
    passwordComplexity: 'पासवर्डमा कम्तिमा एउटा ठूलो अक्षर, एउटा सानो अक्षर, एउटा नम्बर, र एउटा विशेष अक्षर हुनुपर्छ',
    phoneNumber: 'कृपया मान्य फोन नम्बर प्रविष्ट गर्नुहोस्',
    number: 'कृपया मान्य संख्या प्रविष्ट गर्नुहोस्',
    positive: 'कृपया सकारात्मक संख्या प्रविष्ट गर्नुहोस्',
    percentageRange: 'कृपया 0 र 100 बीचको मान प्रविष्ट गर्नुहोस्',
    fixErrors: 'कृपया फारममा भएका त्रुटिहरू सच्याउनुहोस्',
  },
  settings: {
    settings: 'सेटिङहरू',
    language: 'भाषा',
    theme: 'थिम',
    currency: 'मुद्रा',
    notifications: 'सूचनाहरू',
    notificationsAndReminders: 'सूचनाहरू र रिमाइन्डरहरू',
    security: 'सुरक्षा',
    about: 'बारेमा',
    help: 'मद्दत',
    feedback: 'प्रतिक्रिया',
    logout: 'लगआउट',
    account: 'खाता',
    darkMode: 'डार्क मोड',
    advanced: 'उन्नत',
    selectLanguage: 'भाषा चयन गर्नुहोस्',
    version: 'संस्करण',
    termsOfService: 'सेवाका सर्तहरू',
    privacyPolicy: 'गोपनीयता नीति',
    contactUs: 'सम्पर्क गर्नुहोस्',
    rateApp: 'एप रेट गर्नुहोस्',
    shareApp: 'एप साझा गर्नुहोस्',
    deleteAccount: 'खाता मेटाउनुहोस्',
    deleteAccountConfirm: 'के तपाईं आफ्नो खाता मेटाउन निश्चित हुनुहुन्छ? यो कार्य पूर्ववत गर्न सकिँदैन।',
    deleteAccountSuccess: 'तपाईंको खाता सफलतापूर्वक मेटाइएको छ।',
    deleteAccountError: 'तपाईंको खाता मेटाउन असफल भयो। कृपया फेरि प्रयास गर्नुहोस्।',
  },
  loans: {
    loans: 'ऋणहरू',
    loan: 'ऋण',
    loanDetails: 'ऋण विवरण',
    addLoan: 'ऋण थप्नुहोस्',
    editLoan: 'ऋण सम्पादन गर्नुहोस्',
    deleteLoan: 'ऋण मेटाउनुहोस्',
    deleteLoanConfirmation: 'के तपाईं यो ऋण मेटाउन निश्चित हुनुहुन्छ?',
    deleteLoanError: 'ऋण मेटाउन असफल भयो। कृपया फेरि प्रयास गर्नुहोस्।',
    loanNotFound: 'ऋण फेला परेन',
    updateLoanError: 'ऋण अपडेट गर्न असफल भयो। कृपया फेरि प्रयास गर्नुहोस्।',
    noLoans: 'कुनै ऋण छैन',
    noLoansMessage: 'तपाईंले अहिलेसम्म कुनै ऋण थप्नुभएको छैन।',

    // Loan properties
    title: 'शीर्षक',
    amount: 'रकम',
    interestRate: 'ब्याज दर',
    interestType: 'ब्याज प्रकार',
    paymentFrequency: 'भुक्तानी आवृत्ति',
    startDate: 'सुरु मिति',
    endDate: 'अन्त्य मिति',
    lender: 'ऋणदाता',
    borrower: 'ऋणी',
    externalEntity: 'बाह्य संस्था',
    externalEntityName: 'बाह्य संस्थाको नाम',
    status: 'स्थिति',
    notes: 'नोटहरू',
    totalPayable: 'कुल भुक्तानी योग्य',
    remainingAmount: 'बाँकी रकम',
    paidAmount: 'भुक्तानी गरिएको रकम',
    monthlyEMI: 'मासिक ईएमआई',
    predictedCompletionDate: 'अनुमानित समाप्ति मिति',

    // Interest types
    interestTypes: {
      flat: 'फ्ल्याट',
      reducingBalance: 'घट्दो ब्यालेन्स',
      compound: 'चक्रवृद्धि'
    },

    // Frequency types
    frequencyTypes: {
      daily: 'दैनिक',
      weekly: 'साप्ताहिक',
      monthly: 'मासिक',
      quarterly: 'त्रैमासिक',
      yearly: 'वार्षिक'
    },

    // Loan status
    statusOptions: {
      active: 'सक्रिय',
      completed: 'पूरा भएको',
      defaulted: 'डिफल्ट भएको'
    },

    // Loan types
    loanType: 'ऋण प्रकार',
    loanTypes: {
      given: 'दिइएको',
      taken: 'लिइएको',
      external: 'बाह्य'
    },

    // Loan dashboard
    dashboard: 'ऋण ड्यासबोर्ड',
    loansGiven: 'दिइएका ऋणहरू',
    loansTaken: 'लिइएका ऋणहरू',
    totalLoansGiven: 'कुल दिइएका ऋणहरू',
    totalLoansTaken: 'कुल लिइएका ऋणहरू',
    activeLoans: 'सक्रिय ऋणहरू',
    completedLoans: 'पूरा भएका ऋणहरू',

    // Loan timeline
    timeline: 'समयरेखा',
    paymentSchedule: 'भुक्तानी तालिका',

    // Loan payments
    payments: 'भुक्तानीहरू',
    payment: 'भुक्तानी',
    addPayment: 'भुक्तानी थप्नुहोस्',
    editPayment: 'भुक्तानी सम्पादन गर्नुहोस्',
    deletePayment: 'भुक्तानी मेटाउनुहोस्',
    deletePaymentConfirmation: 'के तपाईं यो भुक्तानी मेटाउन निश्चित हुनुहुन्छ?',
    deletePaymentError: 'भुक्तानी मेटाउन असफल भयो। कृपया फेरि प्रयास गर्नुहोस्।',
    paymentDate: 'भुक्तानी मिति',
    paymentAmount: 'भुक्तानी रकम',
    principalAmount: 'मूल रकम',
    interestAmount: 'ब्याज रकम',
    paymentMethod: 'भुक्तानी विधि',
    noPayments: 'कुनै भुक्तानी छैन',
    noPaymentsMessage: 'यस ऋणको लागि अहिलेसम्म कुनै भुक्तानी गरिएको छैन।',

    // Loan reminders
    reminders: 'रिमाइन्डरहरू',
    reminder: 'रिमाइन्डर',
    addReminder: 'रिमाइन्डर थप्नुहोस्',
    editReminder: 'रिमाइन्डर सम्पादन गर्नुहोस्',
    deleteReminder: 'रिमाइन्डर मेटाउनुहोस्',
    deleteReminderConfirmation: 'के तपाईं यो रिमाइन्डर मेटाउन निश्चित हुनुहुन्छ?',
    deleteReminderError: 'रिमाइन्डर मेटाउन असफल भयो। कृपया फेरि प्रयास गर्नुहोस्।',
    reminderDate: 'रिमाइन्डर मिति',
    reminderMessage: 'सन्देश',
    isActive: 'सक्रिय छ',
    isSent: 'पठाइएको छ',
    sentAt: 'पठाइएको मिति',
    noReminders: 'कुनै रिमाइन्डर छैन',
    noRemindersMessage: 'यस ऋणको लागि अहिलेसम्म कुनै रिमाइन्डर सेट गरिएको छैन।',

    // EMI calculator
    emiCalculator: 'ईएमआई क्यालकुलेटर',
    calculateEMI: 'ईएमआई गणना गर्नुहोस्',
    emiResult: 'तपाईंको ईएमआई हुनेछ',

    // Loan summary
    summary: 'ऋण सारांश',
    loanSummary: 'ऋण सारांश',
    paymentBreakdown: 'भुक्तानी विभाजन',
    principalBreakdown: 'मूल',
    interestBreakdown: 'ब्याज',

    // Loan filters
    filterLoans: 'ऋणहरू फिल्टर गर्नुहोस्',
    filterByStatus: 'स्थिति अनुसार फिल्टर गर्नुहोस्',
    filterByType: 'प्रकार अनुसार फिल्टर गर्नुहोस्',
    filterByDate: 'मिति अनुसार फिल्टर गर्नुहोस्',

    // Loan actions
    makePayment: 'भुक्तानी गर्नुहोस्',
    viewTimeline: 'समयरेखा हेर्नुहोस्',
    setReminder: 'रिमाइन्डर सेट गर्नुहोस्',

    // Loan form
    loanForm: 'ऋण फारम',
    loanDetailsForm: 'ऋण विवरण',
    paymentDetails: 'भुक्तानी विवरण',
    reminderDetails: 'रिमाइन्डर विवरण',
    selectInterestType: 'ब्याज प्रकार छान्नुहोस्',
    selectFrequencyType: 'आवृत्ति प्रकार छान्नुहोस्',
    selectStatus: 'स्थिति छान्नुहोस्',
    selectLoanType: 'ऋण प्रकार छान्नुहोस्',

    // Loan errors
    invalidLoanData: 'अमान्य ऋण डाटा। कृपया आफ्नो इनपुट जाँच गर्नुहोस्।',
    invalidPaymentData: 'अमान्य भुक्तानी डाटा। कृपया आफ्नो इनपुट जाँच गर्नुहोस्।',
    invalidReminderData: 'अमान्य रिमाइन्डर डाटा। कृपया आफ्नो इनपुट जाँच गर्नुहोस्।',
  },

  receipts: {
    receipts: 'रसिदहरू',
    receiptHistory: 'रसिद इतिहास',
    receiptDetails: 'रसिद विवरण',
    noReceipts: 'कुनै रसिद छैन',
    addReceiptPrompt: 'फोटो खिचेर वा छवि अपलोड गरेर रसिद थप्नुहोस्।',
    scanReceipt: 'रसिद स्क्यान गर्नुहोस्',
    receiptImage: 'रसिद छवि',
    addReceiptImage: 'रसिद छवि थप्नुहोस्',
    cameraPermissionDenied: 'क्यामेरा अनुमति अस्वीकृत। कृपया आफ्नो उपकरण सेटिङमा क्यामेरा पहुँच सक्षम गर्नुहोस्।',
    mediaLibraryPermissionDenied: 'मिडिया लाइब्रेरी अनुमति अस्वीकृत। कृपया आफ्नो उपकरण सेटिङमा मिडिया पहुँच सक्षम गर्नुहोस्।',
    photoError: 'फोटो खिच्दा त्रुटि। कृपया फेरि प्रयास गर्नुहोस्।',
    imagePickerError: 'छवि चयन गर्दा त्रुटि। कृपया फेरि प्रयास गर्नुहोस्।',
    removeImage: 'छवि हटाउनुहोस्',
    removeImageConfirmation: 'के तपाईं यो छवि हटाउन निश्चित हुनुहुन्छ?',
    addReceipt: 'रसिद थप्नुहोस्',
    selectImageSource: 'छवि स्रोत चयन गर्नुहोस्',
    camera: 'क्यामेरा',
    gallery: 'ग्यालेरी',
    ocrAvailable: 'OCR स्क्यानिङ उपलब्ध छ। डाटा निकाल्न स्क्यान आइकनमा ट्याप गर्नुहोस्।',
    ocrDisabled: 'यस रसिदको लागि OCR स्क्यानिङ अक्षम छ।',
    ocrComingSoon: 'OCR स्क्यानिङ चाँडै आउँदैछ।',
    ocrError: 'OCR सँग रसिद प्रशोधन गर्दा त्रुटि। कृपया फेरि प्रयास गर्नुहोस्।',
    serverOcrError: 'सर्भरमा रसिद प्रशोधन गर्दा त्रुटि। कृपया फेरि प्रयास गर्नुहोस्।',
    networkRequired: 'यस कार्यको लागि नेटवर्क जडान आवश्यक छ।',
    languageDownloadError: 'भाषा डाटा डाउनलोड गर्दा त्रुटि। कृपया फेरि प्रयास गर्नुहोस्।',
    languageNotAvailable: 'भाषा डाटा उपलब्ध छैन। कृपया पहिले यसलाई डाउनलोड गर्नुहोस्।',
    downloadingLanguage: 'भाषा डाटा डाउनलोड गर्दै...',
    downloadComplete: 'भाषा डाटा सफलतापूर्वक डाउनलोड गरियो।',
    processingReceipt: 'रसिद प्रशोधन गर्दै',
    processReceipt: 'रसिद प्रशोधन गर्नुहोस्',
    reprocess: 'पुन: प्रशोधन गर्नुहोस्',
    selectProcessingOptions: 'प्रशोधन विकल्पहरू चयन गर्नुहोस्',
    processingMethod: 'प्रशोधन विधि',
    deviceProcessing: 'उपकरण',
    serverProcessing: 'सर्भर',
    language: 'भाषा',
    dataExtracted: 'डाटा निकालियो',
    processExtractedData: 'निकालिएको डाटा प्रशोधन गर्नुहोस्',
    merchantName: 'व्यापारीको नाम',
    merchantNamePlaceholder: 'व्यापारीको नाम प्रविष्ट गर्नुहोस्',
    merchantNameRequired: 'व्यापारीको नाम आवश्यक छ',
    date: 'मिति',
    totalAmount: 'कुल रकम',
    invalidAmount: 'अमान्य रकम',
    items: 'वस्तुहरू',
    noItems: 'कुनै वस्तु फेला परेन',
    itemNamePlaceholder: 'वस्तुको नाम',
    invalidItems: 'केही वस्तुहरूमा अमान्य डाटा छ',
    category: 'वर्ग',
    selectCategory: 'वर्ग चयन गर्नुहोस्',
    showRawText: 'कच्चा पाठ देखाउनुहोस्',
    hideRawText: 'कच्चा पाठ लुकाउनुहोस्',
    noRawText: 'कुनै कच्चा पाठ उपलब्ध छैन',
    status: 'स्थिति',
    processed: 'प्रशोधित',
    pending: 'प्रक्रियामा',
    linkedTransaction: 'लिङ्क गरिएको लेनदेन',
    viewTransaction: 'लेनदेन हेर्नुहोस्',
    unknownMerchant: 'अज्ञात व्यापारी',
    noTransaction: 'कुनै लेनदेन लिङ्क गरिएको छैन',
    noImage: 'कुनै छवि उपलब्ध छैन',
    filterReceipts: 'रसिदहरू फिल्टर गर्नुहोस्',
    dateRange: 'मिति दायरा',
    amountRange: 'रकम दायरा',
    merchant: 'व्यापारी',
    processingStatus: 'प्रशोधन स्थिति',
    loadError: 'रसिदहरू लोड गर्दा त्रुटि',
    receiptNotFound: 'रसिद फेला परेन',
    alreadyProcessed: 'पहिले नै प्रशोधित',
    reprocessConfirmation: 'यो रसिद पहिले नै प्रशोधित गरिएको छ। के तपाईं यसलाई पुन: प्रशोधन गर्न चाहनुहुन्छ?',
    noImageToProcess: 'प्रशोधन गर्न कुनै छवि छैन',
    updateSuccess: 'रसिद सफलतापूर्वक अपडेट गरियो',
    updateSuccessOffline: 'रसिद सफलतापूर्वक अपडेट गरियो (अफलाइन)',
    updateError: 'रसिद अपडेट गर्दा त्रुटि',
    deleteReceipt: 'रसिद मेटाउनुहोस्',
    deleteConfirmation: 'के तपाईं यो रसिद मेटाउन निश्चित हुनुहुन्छ?',
    deleteSuccessOffline: 'रसिद मेटाउनको लागि चिन्ह लगाइयो (अफलाइन)',
    deleteError: 'रसिद मेटाउँदा त्रुटि',
    noTransactionLinked: 'यस रसिदसँग कुनै लेनदेन लिङ्क गरिएको छैन',
    invalidReceiptId: 'अमान्य रसिद आईडी',
  },

  sync: {
    sync: 'सिंक',
    syncStatus: 'सिंक स्थिति',
    syncing: 'सिंक गर्दै...',
    synced: 'सिंक भयो',
    syncError: 'सिंक त्रुटि',
    offline: 'अफलाइन',
    upToDate: 'अद्यावधिक',
    pendingChanges: '{{count}} प्रक्रियामा',
    neverSynced: 'कहिल्यै सिंक गरिएको छैन',
    lastSynced: 'अन्तिम सिंक {{time}}',
    syncNow: 'अहिले सिंक गर्नुहोस्',
    viewConflicts: 'द्वन्द्वहरू हेर्नुहोस्',
    syncQueue: 'सिंक लाइन',
    clearQueue: 'लाइन खाली गर्नुहोस्',
    clearSyncQueue: 'सिंक लाइन खाली गर्नुहोस्',
    clearSyncQueueConfirmation: 'के तपाईं सिंक लाइन खाली गर्न निश्चित हुनुहुन्छ? यसले सबै प्रक्रियामा रहेका परिवर्तनहरू त्याग्नेछ।',
    syncQueueCleared: 'सिंक लाइन सफलतापूर्वक खाली गरियो',
    clearSyncQueueError: 'सिंक लाइन खाली गर्दा त्रुटि',
    queueEmpty: 'लाइन खाली छ',
    allChangesSynced: 'सबै परिवर्तनहरू सिंक गरिएका छन्',
    create: 'सिर्जना गर्नुहोस्',
    update: 'अपडेट गर्नुहोस्',
    delete: 'मेटाउनुहोस्',
    conflict: 'द्वन्द्व',
    conflictResolution: 'द्वन्द्व समाधान',
    conflictsCount: '{{count}} द्वन्द्वहरू',
    noConflicts: 'कुनै द्वन्द्व छैन',
    allConflictsResolved: 'सबै द्वन्द्वहरू समाधान गरिए',
    noConflictsMessage: 'समाधान गर्न कुनै द्वन्द्व छैन',
    selectConflict: 'समाधान गर्न द्वन्द्व चयन गर्नुहोस्',
    localVersion: 'स्थानीय संस्करण',
    remoteVersion: 'रिमोट संस्करण',
    useLocal: 'स्थानीय प्रयोग गर्नुहोस्',
    useRemote: 'रिमोट प्रयोग गर्नुहोस्',
    createMerged: 'मर्ज संस्करण सिर्जना गर्नुहोस्',
    mergeEditor: 'मर्ज सम्पादक',
    editMergedData: 'द्वन्द्व समाधान गर्न मर्ज डाटा सम्पादन गर्नुहोस्',
    invalidJson: 'अमान्य JSON ढाँचा',
    noMergedData: 'कुनै मर्ज डाटा उपलब्ध छैन',
    conflictResolved: 'द्वन्द्व सफलतापूर्वक समाधान गरियो',
    conflictResolutionFailed: 'द्वन्द्व समाधान गर्न असफल',
    conflictResolutionError: 'द्वन्द्व समाधान गर्दा त्रुटि',
    resolvingConflict: 'द्वन्द्व समाधान गर्दै...',
    noConflictProvided: 'कुनै द्वन्द्व प्रदान गरिएको छैन',
    conflictsDetected: 'द्वन्द्वहरू पत्ता लगाइयो',
    conflictsMessage: 'द्वन्द्वहरूका कारण केही परिवर्तनहरू सिंक गर्न सकिएन। कृपया तिनीहरूलाई समाधान गर्नुहोस्।',
    conflicts: 'द्वन्द्वहरू',
  },

  migration: {
    title: 'डाटा माइग्रेसन',
    description: 'हामीलाई तपाईंको डाटालाई राम्रो अफलाइन समर्थनको लागि नयाँ भण्डारण प्रणालीमा माइग्रेट गर्न आवश्यक छ। यसले केही क्षण लिन सक्छ।',
    inProgress: 'तपाईंको डाटालाई नयाँ भण्डारण प्रणालीमा माइग्रेट गर्दै...',
    completed: 'डाटा माइग्रेसन सफलतापूर्वक पूरा भयो!',
    migrating: 'डाटा माइग्रेट गर्दै...',
    migrationSuccess: 'माइग्रेसन सफल',
    migrationFailed: 'माइग्रेसन असफल',
    migrationDetails: 'माइग्रेसन विवरण',
    clearStorage: 'पुरानो भण्डारण खाली गर्नुहोस्',
    skipClearing: 'छोड्नुहोस्',
    clearStoragePrompt: 'के तपाईं स्थान खाली गर्न पुरानो भण्डारण खाली गर्न चाहनुहुन्छ? तपाईंको डाटा सुरक्षित रूपमा माइग्रेट गरिएको छ।',
    retry: 'माइग्रेसन पुन: प्रयास गर्नुहोस्',
    error: 'माइग्रेसन त्रुटि',
    errorMessage: 'माइग्रेसनको समयमा त्रुटि भयो: {{error}}',
    success: 'सफलता',
    storageCleared: 'पुरानो भण्डारण सफलतापूर्वक खाली गरियो।',
    clearStorageError: 'पुरानो भण्डारण खाली गर्दा त्रुटि: {{error}}',
    authTokens: 'प्रमाणीकरण टोकनहरू',
  },

  family: {
    families: 'परिवारहरू',
    family: 'परिवार',
    createFamily: 'परिवार सिर्जना गर्नुहोस्',
    editFamily: 'परिवार सम्पादन गर्नुहोस्',
    familyName: 'परिवारको नाम',
    familyDescription: 'परिवारको विवरण',
    familySettings: 'परिवार सेटिङहरू',
    members: 'सदस्यहरू',
    addMember: 'सदस्य थप्नुहोस्',
    removeMember: 'सदस्य हटाउनुहोस्',
    inviteCode: 'आमन्त्रण कोड',
    generateInviteCode: 'आमन्त्रण कोड सिर्जना गर्नुहोस्',
    joinFamily: 'परिवारमा सामेल हुनुहोस्',
    leaveFamily: 'परिवार छोड्नुहोस्',
    admin: 'प्रशासक',
    member: 'सदस्य',
    role: 'भूमिका',
    updateRole: 'भूमिका अपडेट गर्नुहोस्',
    createdBy: 'द्वारा सिर्जित',
    joinedAt: 'सामेल भएको',
    noFamilies: 'तपाईं अहिलेसम्म कुनै परिवारको सदस्य हुनुहुन्न',
    createFamilyPrompt: 'परिवार सिर्जना गर्नुहोस् वा आमन्त्रण कोड प्रयोग गरेर सामेल हुनुहोस्',
    inviteCodeExpiry: 'यो कोड ७ दिनमा समाप्त हुनेछ',
    copyInviteCode: 'आमन्त्रण कोड कपी गर्नुहोस्',
    inviteCodeCopied: 'आमन्त्रण कोड क्लिपबोर्डमा कपी गरियो',
    enterInviteCode: 'आमन्त्रण कोड प्रविष्ट गर्नुहोस्',
    invalidInviteCode: 'अमान्य आमन्त्रण कोड',
    confirmLeaveFamily: 'के तपाईं यो परिवार छोड्न निश्चित हुनुहुन्छ?',
    confirmRemoveMember: 'के तपाईं यो सदस्यलाई हटाउन निश्चित हुनुहुन्छ?',
    lastAdmin: 'तपाईं अन्तिम प्रशासक हुनुभएकोले छोड्न सक्नुहुन्न। कृपया पहिले अर्को प्रशासक नियुक्त गर्नुहोस्।',
    familyManagement: 'परिवार व्यवस्थापन',
    noInviteCode: 'कुनै आमन्त्रण कोड उपलब्ध छैन',
    addMemberExplanation: 'तपाईंको परिवारमा सदस्य थप्नको लागि:\n\n1. आमन्त्रण कोड सिर्जना गर्नुहोस्\n2. यो कोड तपाईंले थप्न चाहनुभएको व्यक्तिसँग साझा गर्नुहोस्\n3. यदि उनीहरूले पहिले नै दर्ता गरेका छैनन् भने उनीहरू एपमा दर्ता गर्नुपर्छ\n4. उनीहरूले परिवारहरू ट्याबमा गएर "परिवारमा सामेल हुनुहोस्" मा ट्याप गर्नुपर्छ\n5. उनीहरूले तपाईंले साझा गर्नुभएको आमन्त्रण कोड प्रविष्ट गर्छन्\n\nतपाईंको कोड प्रयोग गरेर सामेल भएपछि, उनीहरू तपाईंको परिवार सदस्यहरूको सूचीमा देखा पर्नेछन्।',
    showInviteCode: 'आमन्त्रण कोड देखाउनुहोस्',
    howToAddMembers: 'सदस्यहरू कसरी थप्ने',
    step1: 'तलको बटन क्लिक गरेर आमन्त्रण कोड सिर्जना गर्नुहोस्',
    step2: 'तपाईंले थप्न चाहनुभएको व्यक्तिसँग आमन्त्रण कोड साझा गर्नुहोस्',
    step3: 'उनीहरूले एपमा दर्ता गर्नुपर्छ र तपाईंको परिवारमा सामेल हुन कोड प्रयोग गर्नुपर्छ',
    alreadyMember: 'तपाईं पहिले नै यस परिवारको सदस्य हुनुहुन्छ।',
    inviteCodeExpired: 'यो आमन्त्रण कोड समाप्त भएको छ। कृपया नयाँ कोडको लागि अनुरोध गर्नुहोस्।',
    joinFailed: 'परिवारमा सामेल हुन असफल भयो। कृपया पछि फेरि प्रयास गर्नुहोस्।',
    joinSuccess: 'तपाईं सफलतापूर्वक परिवारमा सामेल हुनुभएको छ। तपाईं अब यसलाई आफ्नो परिवारहरूको सूचीमा हेर्न सक्नुहुन्छ।',
    noPermission: 'तपाईंसँग यो परिवारमा पहुँच गर्ने अनुमति छैन।',
    familyNotFound: 'परिवार फेला परेन। यो मेटिएको हुन सक्छ।',
    notMember: 'तपाईं यस परिवारको सदस्य हुनुहुन्न।',
    leaveFailed: 'परिवार छोड्न असफल भयो। कृपया पछि फेरि प्रयास गर्नुहोस्।',
  },
};

// Merge translations with all feature translations
const mergedEnTranslations = {
  ...enTranslations,
  ...enSavingsTranslations,
  ...enAnalyticsTranslations,
  ...enBudgetTranslations,
  ...enApprovalTranslations,
  ...enNotificationTranslations,
  ...enFeatureTranslations,
  ...enTypographyTranslations
};

const mergedNeTranslations = {
  ...neTranslations,
  ...neSavingsTranslations,
  ...neAnalyticsTranslations,
  ...neBudgetTranslations,
  ...neApprovalTranslations,
  ...neNotificationTranslations,
  ...neFeatureTranslations,
  ...neTypographyTranslations
};

// Initialize i18n
i18n
  .use(initReactI18next)
  .init({
    compatibilityJSON: 'v4', // Updated to v4 for compatibility
    resources: {
      en: { translation: mergedEnTranslations },
      ne: { translation: mergedNeTranslations },
    },
    lng: 'en', // Default language
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    debug: DEBUG_TRANSLATIONS, // Enable debug mode only when debugging translations
    react: {
      useSuspense: false, // Disable suspense to avoid issues with React Native
    },
    returnNull: false, // Return empty string instead of null
    returnEmptyString: false, // Return key instead of empty string
    parseMissingKeyHandler: (key) => {
      // Log missing keys in development
      if (__DEV__) {
        console.warn(`Missing translation key: ${key}`);
      }
      // Return the last part of the key as fallback, formatted nicely
      const parts = key.split('.');
      const lastPart = parts[parts.length - 1];
      // Convert camelCase to Title Case with spaces
      return lastPart
        .replace(/([A-Z])/g, ' $1') // Add space before capital letters
        .replace(/^./, (str) => str.toUpperCase()); // Capitalize first letter
    },
    missingKeyHandler: (lng, _ns, key, _fallbackValue) => {
      // Log missing keys to console in development
      if (__DEV__) {
        console.warn(`Missing translation key [${lng}]: ${key}`);
      }
    }
  });

// Function to load saved language preference
export const loadLanguagePreference = async () => {
  try {
    const savedLanguage = await AsyncStorage.getItem('language');
    if (savedLanguage) {
      await i18n.changeLanguage(savedLanguage);
    } else {
      // Set default language to English if no language is saved
      await i18n.changeLanguage('en');
      await AsyncStorage.setItem('language', 'en');
    }
  } catch (error) {
    console.error('Error loading language preference:', error);
    // Set default language to English in case of error
    await i18n.changeLanguage('en');
  }
};

// Function to save language preference
export const saveLanguagePreference = async (language: string) => {
  try {
    await AsyncStorage.setItem('language', language);
    await i18n.changeLanguage(language);
  } catch (error) {
    console.error('Error saving language preference:', error);
  }
};

export default i18n;
