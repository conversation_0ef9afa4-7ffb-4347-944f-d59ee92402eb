import AsyncStorage from '@react-native-async-storage/async-storage';
import { db } from '../services/db-dexie';
import { STORAGE_KEYS } from '../constants/storage';
import { Transaction } from '../models/Transaction';

// Define interfaces locally to avoid circular dependencies
interface Account {
  id?: number;
  name: string;
  accountType: string;
  balance: number;
  initialBalance: number;
  currency: string;
  isActive: boolean;
  excludeFromStats: boolean;
  lastUpdatedAt: Date | string;
  isSynced: boolean;
  isDeleted?: boolean;
}

interface Category {
  id?: number;
  name: string;
  type: string;
  icon?: string;
  color?: string;
  parentCategoryId?: number;
  isSystem: boolean;
  isSynced: boolean;
  isDeleted?: boolean;
}

interface Receipt {
  id: number;
  transactionId?: number;
  merchantName?: string;
  date: string;
  totalAmount: number;
  items: any[];
  rawText: string;
  imageUri?: string;
  language?: string;
  isProcessed: boolean;
  confidence?: number;
  createdAt?: string;
  updatedAt?: string;
  isDeleted: boolean;
}

interface SyncQueue {
  id?: number;
  entityType: string;
  entityId: number;
  operation: string;
  data: string;
  timestamp: string;
  status: string;
  retryCount: number;
}

interface SyncConflict {
  id?: number;
  entityType: string;
  entityId: number;
  localData: string;
  serverData: string;
  timestamp: string;
  resolved: boolean;
  resolution?: string;
}

/**
 * Migration status interface
 */
export interface MigrationStatus {
  inProgress: boolean;
  completed: boolean;
  error: string | null;
  progress: number;
  details: {
    accounts: number;
    transactions: number;
    categories: number;
    receipts: number;
    syncQueue: number;
    conflicts: number;
    settings: number;
    authTokens: number;
  };
}

/**
 * Check if migration has been completed
 */
export const checkMigrationStatus = async (): Promise<boolean> => {
  try {
    const migrationCompleted = await AsyncStorage.getItem(STORAGE_KEYS.MIGRATION_COMPLETED);
    return migrationCompleted === 'true';
  } catch (error) {
    console.error('Error checking migration status:', error);
    return false;
  }
};

/**
 * Migrate data from AsyncStorage to Dexie.js
 */
export const migrateToIndexedDB = async (
  progressCallback?: (status: MigrationStatus) => void
): Promise<MigrationStatus> => {
  // Initialize migration status
  const status: MigrationStatus = {
    inProgress: true,
    completed: false,
    error: null,
    progress: 0,
    details: {
      accounts: 0,
      transactions: 0,
      categories: 0,
      receipts: 0,
      syncQueue: 0,
      conflicts: 0,
      settings: 0,
      authTokens: 0,
    },
  };

  try {
    // Check if migration has already been completed
    const migrationCompleted = await checkMigrationStatus();
    if (migrationCompleted) {
      status.inProgress = false;
      status.completed = true;
      status.progress = 1;
      if (progressCallback) progressCallback(status);
      return status;
    }

    // Start transaction for atomic migration
    await db.transaction('rw',
      [db.accounts, db.transactions, db.categories, db.receipts,
       db.syncQueue, db.conflicts, db.authTokens, db.settings],
      async () => {

      // 1. Migrate accounts
      const accountsStr = await AsyncStorage.getItem(STORAGE_KEYS.ACCOUNTS);
      if (accountsStr) {
        const accounts: Account[] = JSON.parse(accountsStr);
        // Convert lastUpdatedAt to Date if it's a string
        const formattedAccounts = accounts.map(account => ({
          ...account,
          lastUpdatedAt: typeof account.lastUpdatedAt === 'string'
            ? new Date(account.lastUpdatedAt)
            : account.lastUpdatedAt
        }));
        await db.accounts.bulkPut(formattedAccounts as any[]);
        status.details.accounts = accounts.length;
      }
      status.progress = 0.125;
      if (progressCallback) progressCallback({ ...status });

      // 2. Migrate transactions
      const transactionsStr = await AsyncStorage.getItem(STORAGE_KEYS.TRANSACTIONS);
      if (transactionsStr) {
        const transactions: Transaction[] = JSON.parse(transactionsStr);
        await db.transactions.bulkPut(transactions);
        status.details.transactions = transactions.length;
      }
      status.progress = 0.25;
      if (progressCallback) progressCallback({ ...status });

      // 3. Migrate categories
      const categoriesStr = await AsyncStorage.getItem(STORAGE_KEYS.CATEGORIES);
      if (categoriesStr) {
        const categories: Category[] = JSON.parse(categoriesStr);
        await db.categories.bulkPut(categories);
        status.details.categories = categories.length;
      }
      status.progress = 0.375;
      if (progressCallback) progressCallback({ ...status });

      // 4. Migrate receipts
      const receiptsStr = await AsyncStorage.getItem(STORAGE_KEYS.RECEIPTS);
      if (receiptsStr) {
        const receipts: Receipt[] = JSON.parse(receiptsStr);
        await db.receipts.bulkPut(receipts);
        status.details.receipts = receipts.length;
      }
      status.progress = 0.5;
      if (progressCallback) progressCallback({ ...status });

      // 5. Migrate sync queue
      const syncQueueStr = await AsyncStorage.getItem(STORAGE_KEYS.SYNC_QUEUE);
      if (syncQueueStr) {
        const syncQueue: SyncQueue[] = JSON.parse(syncQueueStr);
        // Add missing properties required by the db schema
        const formattedSyncQueue = syncQueue.map(item => ({
          ...item,
          action: item.operation, // Map operation to action
          entityData: item.data,  // Map data to entityData
          createdAt: item.timestamp,
          priority: 1 // Default priority
        }));
        await db.syncQueue.bulkPut(formattedSyncQueue as any[]);
        status.details.syncQueue = syncQueue.length;
      }
      status.progress = 0.625;
      if (progressCallback) progressCallback({ ...status });

      // 6. Migrate conflicts
      const conflictsStr = await AsyncStorage.getItem(STORAGE_KEYS.CONFLICT_LOGS);
      if (conflictsStr) {
        const conflicts: SyncConflict[] = JSON.parse(conflictsStr);
        // Add missing properties required by the db schema
        const formattedConflicts = conflicts.map(conflict => ({
          ...conflict,
          remoteData: conflict.serverData, // Map serverData to remoteData
          createdAt: conflict.timestamp
        }));
        await db.conflicts.bulkPut(formattedConflicts as any[]);
        status.details.conflicts = conflicts.length;
      }
      status.progress = 0.75;
      if (progressCallback) progressCallback({ ...status });

      // 7. Migrate auth tokens
      const authTokenStr = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      const refreshTokenStr = await AsyncStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
      const tokenExpiryStr = await AsyncStorage.getItem(STORAGE_KEYS.TOKEN_EXPIRY);

      if (authTokenStr && refreshTokenStr && tokenExpiryStr) {
        await db.authTokens.put({
          id: 1,
          accessToken: authTokenStr,
          refreshToken: refreshTokenStr,
          expiresAt: new Date(tokenExpiryStr),
        });
        status.details.authTokens = 1;
      }
      status.progress = 0.875;
      if (progressCallback) progressCallback({ ...status });

      // 8. Migrate settings
      const settingsKeys = [
        STORAGE_KEYS.USER_SETTINGS,
        STORAGE_KEYS.LANGUAGE,
        STORAGE_KEYS.THEME,
        STORAGE_KEYS.SYNC_STATUS,
        STORAGE_KEYS.LAST_NOTIFICATION_CHECK,
      ];

      for (const key of settingsKeys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          await db.settings.put({ key, value });
          status.details.settings++;
        }
      }
      status.progress = 1;
      if (progressCallback) progressCallback({ ...status });

      // Mark migration as completed
      await AsyncStorage.setItem(STORAGE_KEYS.MIGRATION_COMPLETED, 'true');
    });

    // Update final status
    status.inProgress = false;
    status.completed = true;
    status.progress = 1;

    if (progressCallback) progressCallback(status);
    return status;
  } catch (error: unknown) {
    console.error('Error migrating data:', error);
    status.inProgress = false;
    status.error = error instanceof Error ? error.message : 'Unknown error during migration';

    if (progressCallback) progressCallback(status);
    return status;
  }
};

/**
 * Clear AsyncStorage data after successful migration
 * CAUTION: Only call this after verifying the migration was successful
 */
export const clearAsyncStorageAfterMigration = async (): Promise<void> => {
  try {
    // Check if migration has been completed
    const migrationCompleted = await checkMigrationStatus();
    if (!migrationCompleted) {
      throw new Error('Cannot clear AsyncStorage: Migration not completed');
    }

    // Get all keys
    const keys = await AsyncStorage.getAllKeys();

    // Filter out keys that should be preserved
    const keysToPreserve = [
      STORAGE_KEYS.MIGRATION_COMPLETED,
      // Add any other keys you want to preserve
    ];

    const keysToRemove = keys.filter(key => !keysToPreserve.includes(key));

    // Remove keys
    await AsyncStorage.multiRemove(keysToRemove);
  } catch (error) {
    console.error('Error clearing AsyncStorage:', error);
    throw error;
  }
};
