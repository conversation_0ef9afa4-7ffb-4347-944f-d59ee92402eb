// English translations for analytics
export const enAnalyticsTranslations = {
  analytics: {
    analytics: 'Analytics',
    dashboard: 'Analytics Dashboard',
    netWorth: 'Net Worth',
    cashflow: 'Cashflow',
    export: 'Export Data',
    income: 'Income',
    expense: 'Expense',
    totalIncome: 'Total Income',
    totalExpense: 'Total Expense',
    netSavings: 'Net Savings',
    savingsRate: 'Savings Rate',
    netCashflow: 'Net Cashflow',
    assets: 'Assets',
    liabilities: 'Liabilities',
    totalAssets: 'Total Assets',
    totalLiabilities: 'Total Liabilities',
    assetBreakdown: 'Asset Breakdown',
    liabilityBreakdown: 'Liability Breakdown',
    incomeVsExpense: 'Income vs Expense',
    topExpenseCategories: 'Top Expense Categories',
    topIncomeCategories: 'Top Income Categories',
    netWorthTrend: 'Net Worth Trend',
    monthlyCashflow: 'Monthly Cashflow',
    netCashflowTrend: 'Net Cashflow Trend',
    summary: 'Summary',
    overTime: 'Over Time',
    selectDateRange: 'Select Date Range',
    selectFormat: 'Select Format',
    selectDataType: 'Select Data Type',
    exportOptions: 'Export Options',
    selectFamily: 'Select Family',
    exportData: 'Export Data',
    exportSuccess: 'Export Successful',
    exportSuccessMessage: 'Your data has been exported successfully.',
    transactions: 'Transactions',
    categories: 'Categories',
    thisMonth: 'This Month',
    lastMonth: 'Last Month',
    last3Months: 'Last 3 Months',
    thisYear: 'This Year',
    customRange: 'Custom Range',
    selectStartDate: 'Select start date',
    selectEndDate: 'Select end date',
    period: {
      daily: 'Daily',
      weekly: 'Weekly',
      monthly: 'Monthly',
      quarterly: 'Quarterly',
      yearly: 'Yearly'
    }
  }
};

// Nepali translations for analytics
export const neAnalyticsTranslations = {
  analytics: {
    analytics: 'विश्लेषण',
    dashboard: 'विश्लेषण ड्यासबोर्ड',
    netWorth: 'कुल सम्पत्ति',
    cashflow: 'नगद प्रवाह',
    export: 'डाटा निर्यात',
    income: 'आम्दानी',
    expense: 'खर्च',
    totalIncome: 'कुल आम्दानी',
    totalExpense: 'कुल खर्च',
    netSavings: 'खुद बचत',
    savingsRate: 'बचत दर',
    netCashflow: 'खुद नगद प्रवाह',
    assets: 'सम्पत्ति',
    liabilities: 'दायित्व',
    totalAssets: 'कुल सम्पत्ति',
    totalLiabilities: 'कुल दायित्व',
    assetBreakdown: 'सम्पत्ति विभाजन',
    liabilityBreakdown: 'दायित्व विभाजन',
    incomeVsExpense: 'आम्दानी बनाम खर्च',
    topExpenseCategories: 'शीर्ष खर्च श्रेणीहरू',
    topIncomeCategories: 'शीर्ष आम्दानी श्रेणीहरू',
    netWorthTrend: 'कुल सम्पत्ति प्रवृत्ति',
    monthlyCashflow: 'मासिक नगद प्रवाह',
    netCashflowTrend: 'खुद नगद प्रवाह प्रवृत्ति',
    summary: 'सारांश',
    overTime: 'समय अनुसार',
    selectDateRange: 'मिति दायरा चयन गर्नुहोस्',
    selectFormat: 'ढाँचा चयन गर्नुहोस्',
    selectDataType: 'डाटा प्रकार चयन गर्नुहोस्',
    exportOptions: 'निर्यात विकल्पहरू',
    selectFamily: 'परिवार चयन गर्नुहोस्',
    exportData: 'डाटा निर्यात गर्नुहोस्',
    exportSuccess: 'निर्यात सफल',
    exportSuccessMessage: 'तपाईंको डाटा सफलतापूर्वक निर्यात गरिएको छ।',
    transactions: 'लेनदेनहरू',
    categories: 'श्रेणीहरू',
    thisMonth: 'यो महिना',
    lastMonth: 'गत महिना',
    last3Months: 'पछिल्लो ३ महिना',
    thisYear: 'यो वर्ष',
    customRange: 'अनुकूलित दायरा',
    selectStartDate: 'सुरु मिति चयन गर्नुहोस्',
    selectEndDate: 'अन्त्य मिति चयन गर्नुहोस्',
    period: {
      daily: 'दैनिक',
      weekly: 'साप्ताहिक',
      monthly: 'मासिक',
      quarterly: 'त्रैमासिक',
      yearly: 'वार्षिक'
    }
  }
};
