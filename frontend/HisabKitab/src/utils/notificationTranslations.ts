// English translations for notifications and reminders
export const enNotificationTranslations = {
  notifications: {
    title: 'Notifications',
    notificationCenter: 'Notification Center',
    markAllAsRead: 'Mark All as Read',
    markAsRead: 'Mark as Read',
    deleteAll: 'Delete All',
    delete: 'Delete',
    noNotifications: 'No Notifications',
    noNotificationsMessage: 'You have no notifications at the moment.',
    all: 'All',
    unread: 'Unread',
    read: 'Read',
    deleteConfirmTitle: 'Delete Notification',
    deleteConfirmMessage: 'Are you sure you want to delete this notification?',
    deleteAllConfirmTitle: 'Delete All Notifications',
    deleteAllConfirmMessage: 'Are you sure you want to delete all notifications?',
    deleteSuccess: 'Notification deleted successfully',
    deleteAllSuccess: 'All notifications deleted successfully',
    markReadSuccess: 'Notification marked as read',
    markAllReadSuccess: 'All notifications marked as read',
    notificationSettings: 'Notification Settings',
    pushNotifications: 'Push Notifications',
    emailNotifications: 'Email Notifications',
    inAppNotifications: 'In-App Notifications',
    notificationTypes: 'Notification Types',
    transactionNotifications: 'Transaction Notifications',
    loanNotifications: 'Loan Notifications',
    familyNotifications: 'Family Notifications',
    accountNotifications: 'Account Notifications',
    budgetNotifications: 'Budget Notifications',
    savingsNotifications: 'Savings Notifications',
    reminderNotifications: 'Reminder Notifications',
    systemNotifications: 'System Notifications',
    notificationFrequency: 'Notification Frequency',
    immediately: 'Immediately',
    daily: 'Daily',
    weekly: 'Weekly',
    monthly: 'Monthly',
    notificationSummary: 'Notification Summary',
    enableSummary: 'Enable Summary',
    summaryFrequency: 'Summary Frequency',
    summaryTime: 'Summary Time',
    notificationSound: 'Notification Sound',
    enableSound: 'Enable Sound',
    selectSound: 'Select Sound',
    notificationVibration: 'Notification Vibration',
    enableVibration: 'Enable Vibration',
    notificationLED: 'Notification LED',
    enableLED: 'Enable LED',
    selectLEDColor: 'Select LED Color',
    notificationBadge: 'Notification Badge',
    enableBadge: 'Enable Badge',
    badgeCount: 'Badge Count',
    notificationPreview: 'Notification Preview',
    enablePreview: 'Enable Preview',
    previewContent: 'Preview Content',
    notificationPrivacy: 'Notification Privacy',
    hideContent: 'Hide Content',
    showContent: 'Show Content',
    notificationHistory: 'Notification History',
    keepHistory: 'Keep History',
    clearHistory: 'Clear History',
    historyDuration: 'History Duration',
    days: 'Days',
    weeks: 'Weeks',
    months: 'Months',
    years: 'Years',
    forever: 'Forever',
    notificationPermissions: 'Notification Permissions',
    requestPermissions: 'Request Permissions',
    permissionsRequired: 'Permissions Required',
    permissionsRequiredMessage: 'Notification permissions are required to receive notifications.',
    permissionsDenied: 'Permissions Denied',
    permissionsDeniedMessage: 'Notification permissions have been denied. Please enable them in your device settings.',
    permissionsGranted: 'Permissions Granted',
    permissionsGrantedMessage: 'Notification permissions have been granted.',

    // New notification features
    regular: 'Regular',
    scheduled: 'Scheduled',
    summary: 'Summary',
    regularNotifications: 'Regular Notifications',
    scheduledNotifications: 'Scheduled Notifications',
    createScheduled: 'Create Scheduled',
    editScheduled: 'Edit Scheduled',
    scheduleSuccess: 'Notification scheduled successfully',
    scheduleError: 'Failed to schedule notification',
    updateScheduleSuccess: 'Scheduled notification updated successfully',
    updateScheduleError: 'Failed to update scheduled notification',
    deleteScheduleSuccess: 'Scheduled notification deleted successfully',
    deleteScheduleError: 'Failed to delete scheduled notification',
    noScheduledNotifications: 'No scheduled notifications',
    confirmDeleteScheduled: 'Are you sure you want to delete this scheduled notification?',
    scheduleNotification: 'Schedule Notification',
    enterTitle: 'Enter title',
    enterMessage: 'Enter message',
    scheduledDate: 'Scheduled Date',
    scheduledTime: 'Scheduled Time',
    titleRequired: 'Title is required',
    messageRequired: 'Message is required',
    futureTimeRequired: 'Scheduled time must be in the future',
    type: 'Type',
    relatedEntityType: 'Related Entity Type',
    relatedEntityId: 'Related Entity ID',
    actionUrl: 'Action URL',
    enterRelatedEntityType: 'Enter related entity type',
    enterRelatedEntityId: 'Enter related entity ID',
    enterActionUrl: 'Enter action URL',
    optional: 'Optional',
    isActive: 'Is Active',
    activeDescription: 'This notification will be sent at the scheduled time',
    inactiveDescription: 'This notification will not be sent',
    alreadySent: 'This notification has already been sent',
    inactive: 'Inactive',
    sent: 'Sent',
    missed: 'Missed',

    // Monthly summary
    monthlySummary: 'Monthly Summary',
    noSummaryData: 'No summary data available for this month',
    topExpenseCategories: 'Top Expense Categories',
    dailyTrend: 'Daily Trend',
    keyStats: 'Key Statistics',
    transactionCount: 'Transaction Count',
    averageExpense: 'Average Expense',
    largestExpense: 'Largest Expense',
    topExpenseCategory: 'Top Expense Category',
    topIncomeSource: 'Top Income Source',

    // Recurring notifications
    recurringNotifications: 'Recurring Notifications',
    createRecurring: 'Create Recurring',
    editRecurring: 'Edit Recurring',
    recurrenceType: 'Recurrence Type',
    recurrenceInterval: 'Recurrence Interval',
    startDate: 'Start Date',
    endDate: 'End Date',
    createRecurringSuccess: 'Recurring notification created successfully',
    createRecurringError: 'Failed to create recurring notification',
    updateRecurringSuccess: 'Recurring notification updated successfully',
    updateRecurringError: 'Failed to update recurring notification',
    deleteRecurringSuccess: 'Recurring notification deleted successfully',
    deleteRecurringError: 'Failed to delete recurring notification',
    noRecurringNotifications: 'No recurring notifications',
    confirmDeleteRecurring: 'Are you sure you want to delete this recurring notification?',
    nextRun: 'Next Run',
    lastRun: 'Last Run',

    // Push notifications
    deviceRegistration: 'Device Registration',
    deviceRegistered: 'Device registered successfully',
    deviceRegistrationError: 'Failed to register device',
    deviceTokenUpdated: 'Device token updated successfully',
    deviceTokenUpdateError: 'Failed to update device token',
    deviceUnregistered: 'Device unregistered successfully',
    deviceUnregistrationError: 'Failed to unregister device',

    // Offline errors
    offlineError: 'This feature requires an internet connection',
    fetchScheduledError: 'Failed to fetch scheduled notifications',
    fetchRecurringError: 'Failed to fetch recurring notifications',
    monthlySummaryError: 'Failed to fetch monthly summary',
  },
  reminders: {
    title: 'Reminders',
    reminderManagement: 'Reminder Management',
    reminderDetails: 'Reminder Details',
    createReminder: 'Create Reminder',
    editReminder: 'Edit Reminder',
    deleteReminder: 'Delete Reminder',
    deleteConfirmTitle: 'Delete Reminder',
    deleteConfirmMessage: 'Are you sure you want to delete this reminder?',
    deleteSuccess: 'Reminder deleted successfully',
    createSuccess: 'Reminder created successfully',
    updateSuccess: 'Reminder updated successfully',
    noReminders: 'No Reminders',
    noRemindersMessage: 'You have no reminders at the moment.',
    all: 'All',
    active: 'Active',
    inactive: 'Inactive',
    upcoming: 'Upcoming',
    overdue: 'Overdue',
    today: 'Today',
    tomorrow: 'Tomorrow',
    thisWeek: 'This Week',
    message: 'Message',
    messagePlaceholder: 'Enter reminder message',
    messageRequired: 'Message is required',
    date: 'Date',
    time: 'Time',
    loan: 'Loan',
    selectLoan: 'Select a loan',
    loanRequired: 'Loan is required',
    status: 'Status',
    setReminder: 'Set Reminder',
    scheduleNotification: 'Schedule Notification',
    notificationTitle: 'Reminder for {{loan}}',
    notificationScheduled: 'Notification scheduled successfully',
    notificationError: 'Failed to schedule notification',
  }
};

// Nepali translations for notifications and reminders
export const neNotificationTranslations = {
  notifications: {
    title: 'सूचनाहरू',
    notificationCenter: 'सूचना केन्द्र',
    markAllAsRead: 'सबै पढिएको मार्क गर्नुहोस्',
    markAsRead: 'पढिएको मार्क गर्नुहोस्',
    deleteAll: 'सबै मेटाउनुहोस्',
    delete: 'मेटाउनुहोस्',
    noNotifications: 'कुनै सूचना छैन',
    noNotificationsMessage: 'तपाईंसँग हाल कुनै सूचना छैन।',
    all: 'सबै',
    unread: 'नपढिएको',
    read: 'पढिएको',
    deleteConfirmTitle: 'सूचना मेटाउनुहोस्',
    deleteConfirmMessage: 'के तपाईं यो सूचना मेटाउन निश्चित हुनुहुन्छ?',
    deleteAllConfirmTitle: 'सबै सूचनाहरू मेटाउनुहोस्',
    deleteAllConfirmMessage: 'के तपाईं सबै सूचनाहरू मेटाउन निश्चित हुनुहुन्छ?',
    deleteSuccess: 'सूचना सफलतापूर्वक मेटाइयो',
    deleteAllSuccess: 'सबै सूचनाहरू सफलतापूर्वक मेटाइए',
    markReadSuccess: 'सूचना पढिएको मार्क गरियो',
    markAllReadSuccess: 'सबै सूचनाहरू पढिएको मार्क गरिए',
    notificationSettings: 'सूचना सेटिङहरू',
    pushNotifications: 'पुश सूचनाहरू',
    emailNotifications: 'इमेल सूचनाहरू',
    inAppNotifications: 'एप भित्रका सूचनाहरू',
    notificationTypes: 'सूचना प्रकारहरू',
    transactionNotifications: 'लेनदेन सूचनाहरू',
    loanNotifications: 'ऋण सूचनाहरू',
    familyNotifications: 'परिवार सूचनाहरू',
    accountNotifications: 'खाता सूचनाहरू',
    budgetNotifications: 'बजेट सूचनाहरू',
    savingsNotifications: 'बचत सूचनाहरू',
    reminderNotifications: 'रिमाइन्डर सूचनाहरू',
    systemNotifications: 'सिस्टम सूचनाहरू',
    notificationFrequency: 'सूचना आवृत्ति',
    immediately: 'तुरुन्तै',
    daily: 'दैनिक',
    weekly: 'साप्ताहिक',
    monthly: 'मासिक',
    notificationSummary: 'सूचना सारांश',
    enableSummary: 'सारांश सक्षम गर्नुहोस्',
    summaryFrequency: 'सारांश आवृत्ति',
    summaryTime: 'सारांश समय',
    notificationSound: 'सूचना ध्वनि',
    enableSound: 'ध्वनि सक्षम गर्नुहोस्',
    selectSound: 'ध्वनि चयन गर्नुहोस्',
    notificationVibration: 'सूचना भाइब्रेसन',
    enableVibration: 'भाइब्रेसन सक्षम गर्नुहोस्',
    notificationLED: 'सूचना LED',
    enableLED: 'LED सक्षम गर्नुहोस्',
    selectLEDColor: 'LED रंग चयन गर्नुहोस्',
    notificationBadge: 'सूचना ब्याज',
    enableBadge: 'ब्याज सक्षम गर्नुहोस्',
    badgeCount: 'ब्याज गणना',
    notificationPreview: 'सूचना पूर्वावलोकन',
    enablePreview: 'पूर्वावलोकन सक्षम गर्नुहोस्',
    previewContent: 'पूर्वावलोकन सामग्री',
    notificationPrivacy: 'सूचना गोपनीयता',
    hideContent: 'सामग्री लुकाउनुहोस्',
    showContent: 'सामग्री देखाउनुहोस्',
    notificationHistory: 'सूचना इतिहास',
    keepHistory: 'इतिहास राख्नुहोस्',
    clearHistory: 'इतिहास खाली गर्नुहोस्',
    historyDuration: 'इतिहास अवधि',
    days: 'दिनहरू',
    weeks: 'हप्ताहरू',
    months: 'महिनाहरू',
    years: 'वर्षहरू',
    forever: 'सधैंको लागि',
    notificationPermissions: 'सूचना अनुमतिहरू',
    requestPermissions: 'अनुमतिहरू अनुरोध गर्नुहोस्',
    permissionsRequired: 'अनुमतिहरू आवश्यक छन्',
    permissionsRequiredMessage: 'सूचनाहरू प्राप्त गर्न सूचना अनुमतिहरू आवश्यक छन्।',
    permissionsDenied: 'अनुमतिहरू अस्वीकृत',
    permissionsDeniedMessage: 'सूचना अनुमतिहरू अस्वीकृत गरिएका छन्। कृपया तिनीहरूलाई तपाईंको उपकरण सेटिङहरूमा सक्षम गर्नुहोस्।',
    permissionsGranted: 'अनुमतिहरू प्रदान गरिए',
    permissionsGrantedMessage: 'सूचना अनुमतिहरू प्रदान गरिएका छन्।',

    // New notification features
    regular: 'नियमित',
    scheduled: 'तालिकाबद्ध',
    summary: 'सारांश',
    regularNotifications: 'नियमित सूचनाहरू',
    scheduledNotifications: 'तालिकाबद्ध सूचनाहरू',
    createScheduled: 'तालिकाबद्ध सिर्जना गर्नुहोस्',
    editScheduled: 'तालिकाबद्ध सम्पादन गर्नुहोस्',
    scheduleSuccess: 'सूचना सफलतापूर्वक तालिकाबद्ध गरियो',
    scheduleError: 'सूचना तालिकाबद्ध गर्न असफल भयो',
    updateScheduleSuccess: 'तालिकाबद्ध सूचना सफलतापूर्वक अपडेट गरियो',
    updateScheduleError: 'तालिकाबद्ध सूचना अपडेट गर्न असफल भयो',
    deleteScheduleSuccess: 'तालिकाबद्ध सूचना सफलतापूर्वक मेटाइयो',
    deleteScheduleError: 'तालिकाबद्ध सूचना मेटाउन असफल भयो',
    noScheduledNotifications: 'कुनै तालिकाबद्ध सूचनाहरू छैनन्',
    confirmDeleteScheduled: 'के तपाईं यो तालिकाबद्ध सूचना मेटाउन निश्चित हुनुहुन्छ?',
    scheduleNotification: 'सूचना तालिकाबद्ध गर्नुहोस्',
    enterTitle: 'शीर्षक प्रविष्ट गर्नुहोस्',
    enterMessage: 'सन्देश प्रविष्ट गर्नुहोस्',
    scheduledDate: 'तालिकाबद्ध मिति',
    scheduledTime: 'तालिकाबद्ध समय',
    titleRequired: 'शीर्षक आवश्यक छ',
    messageRequired: 'सन्देश आवश्यक छ',
    futureTimeRequired: 'तालिकाबद्ध समय भविष्यमा हुनुपर्छ',
    type: 'प्रकार',
    relatedEntityType: 'सम्बन्धित इन्टिटी प्रकार',
    relatedEntityId: 'सम्बन्धित इन्टिटी आईडी',
    actionUrl: 'कार्य URL',
    enterRelatedEntityType: 'सम्बन्धित इन्टिटी प्रकार प्रविष्ट गर्नुहोस्',
    enterRelatedEntityId: 'सम्बन्धित इन्टिटी आईडी प्रविष्ट गर्नुहोस्',
    enterActionUrl: 'कार्य URL प्रविष्ट गर्नुहोस्',
    optional: 'वैकल्पिक',
    isActive: 'सक्रिय छ',
    activeDescription: 'यो सूचना तालिकाबद्ध समयमा पठाइनेछ',
    inactiveDescription: 'यो सूचना पठाइने छैन',
    alreadySent: 'यो सूचना पहिले नै पठाइसकिएको छ',
    inactive: 'निष्क्रिय',
    sent: 'पठाइएको',
    missed: 'छुटेको',

    // Monthly summary
    monthlySummary: 'मासिक सारांश',
    noSummaryData: 'यस महिनाको लागि कुनै सारांश डाटा उपलब्ध छैन',
    topExpenseCategories: 'शीर्ष खर्च वर्गहरू',
    dailyTrend: 'दैनिक प्रवृत्ति',
    keyStats: 'प्रमुख तथ्याङ्कहरू',
    transactionCount: 'लेनदेन संख्या',
    averageExpense: 'औसत खर्च',
    largestExpense: 'सबैभन्दा ठूलो खर्च',
    topExpenseCategory: 'शीर्ष खर्च वर्ग',
    topIncomeSource: 'शीर्ष आम्दानी स्रोत',

    // Recurring notifications
    recurringNotifications: 'आवर्ती सूचनाहरू',
    createRecurring: 'आवर्ती सिर्जना गर्नुहोस्',
    editRecurring: 'आवर्ती सम्पादन गर्नुहोस्',
    recurrenceType: 'आवर्ती प्रकार',
    recurrenceInterval: 'आवर्ती अन्तराल',
    startDate: 'सुरु मिति',
    endDate: 'अन्त्य मिति',
    createRecurringSuccess: 'आवर्ती सूचना सफलतापूर्वक सिर्जना गरियो',
    createRecurringError: 'आवर्ती सूचना सिर्जना गर्न असफल भयो',
    updateRecurringSuccess: 'आवर्ती सूचना सफलतापूर्वक अपडेट गरियो',
    updateRecurringError: 'आवर्ती सूचना अपडेट गर्न असफल भयो',
    deleteRecurringSuccess: 'आवर्ती सूचना सफलतापूर्वक मेटाइयो',
    deleteRecurringError: 'आवर्ती सूचना मेटाउन असफल भयो',
    noRecurringNotifications: 'कुनै आवर्ती सूचनाहरू छैनन्',
    confirmDeleteRecurring: 'के तपाईं यो आवर्ती सूचना मेटाउन निश्चित हुनुहुन्छ?',
    nextRun: 'अर्को चलाउने',
    lastRun: 'अन्तिम चलाइएको',

    // Push notifications
    deviceRegistration: 'उपकरण दर्ता',
    deviceRegistered: 'उपकरण सफलतापूर्वक दर्ता गरियो',
    deviceRegistrationError: 'उपकरण दर्ता गर्न असफल भयो',
    deviceTokenUpdated: 'उपकरण टोकन सफलतापूर्वक अपडेट गरियो',
    deviceTokenUpdateError: 'उपकरण टोकन अपडेट गर्न असफल भयो',
    deviceUnregistered: 'उपकरण सफलतापूर्वक दर्ता खारेज गरियो',
    deviceUnregistrationError: 'उपकरण दर्ता खारेज गर्न असफल भयो',

    // Offline errors
    offlineError: 'यस सुविधाको लागि इन्टरनेट जडान आवश्यक छ',
    fetchScheduledError: 'तालिकाबद्ध सूचनाहरू प्राप्त गर्न असफल भयो',
    fetchRecurringError: 'आवर्ती सूचनाहरू प्राप्त गर्न असफल भयो',
    monthlySummaryError: 'मासिक सारांश प्राप्त गर्न असफल भयो',
  },
  reminders: {
    title: 'रिमाइन्डरहरू',
    reminderManagement: 'रिमाइन्डर व्यवस्थापन',
    reminderDetails: 'रिमाइन्डर विवरण',
    createReminder: 'रिमाइन्डर सिर्जना गर्नुहोस्',
    editReminder: 'रिमाइन्डर सम्पादन गर्नुहोस्',
    deleteReminder: 'रिमाइन्डर मेटाउनुहोस्',
    deleteConfirmTitle: 'रिमाइन्डर मेटाउनुहोस्',
    deleteConfirmMessage: 'के तपाईं यो रिमाइन्डर मेटाउन निश्चित हुनुहुन्छ?',
    deleteSuccess: 'रिमाइन्डर सफलतापूर्वक मेटाइयो',
    createSuccess: 'रिमाइन्डर सफलतापूर्वक सिर्जना गरियो',
    updateSuccess: 'रिमाइन्डर सफलतापूर्वक अपडेट गरियो',
    noReminders: 'कुनै रिमाइन्डर छैन',
    noRemindersMessage: 'तपाईंसँग हाल कुनै रिमाइन्डर छैन।',
    all: 'सबै',
    active: 'सक्रिय',
    inactive: 'निष्क्रिय',
    upcoming: 'आउँदै गरेको',
    overdue: 'समय नाघेको',
    today: 'आज',
    tomorrow: 'भोलि',
    thisWeek: 'यो हप्ता',
    message: 'सन्देश',
    messagePlaceholder: 'रिमाइन्डर सन्देश प्रविष्ट गर्नुहोस्',
    messageRequired: 'सन्देश आवश्यक छ',
    date: 'मिति',
    time: 'समय',
    loan: 'ऋण',
    selectLoan: 'ऋण चयन गर्नुहोस्',
    loanRequired: 'ऋण आवश्यक छ',
    status: 'स्थिति',
    setReminder: 'रिमाइन्डर सेट गर्नुहोस्',
    scheduleNotification: 'सूचना तालिका बनाउनुहोस्',
    notificationTitle: '{{loan}} को लागि रिमाइन्डर',
    notificationScheduled: 'सूचना सफलतापूर्वक तालिका बनाइयो',
    notificationError: 'सूचना तालिका बनाउन असफल भयो',
  }
};
