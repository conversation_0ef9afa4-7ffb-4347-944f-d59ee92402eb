import { Alert, Platform } from 'react-native';
import { TFunction } from 'i18next';
import { useToast } from '../contexts/ToastContext';
import { ApiResponse } from '../services/api';

/**
 * Error types for categorizing errors
 */
export enum ErrorType {
  NETWORK = 'network',
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NOT_FOUND = 'notFound',
  SERVER = 'server',
  CONFLICT = 'conflict',
  OFFLINE = 'offline',
  UNKNOWN = 'unknown',
}

/**
 * Error codes mapped to error types and translation keys
 */
export const ERROR_CODES: Record<string, { type: ErrorType; key: string }> = {
  // Network errors
  'NETWORK_ERROR': { type: ErrorType.NETWORK, key: 'errors.networkError' },
  'TIMEOUT_ERROR': { type: ErrorType.NETWORK, key: 'errors.timeoutError' },

  // Authentication errors
  'INVALID_CREDENTIALS': { type: ErrorType.AUTHENTICATION, key: 'errors.invalidCredentials' },
  'TOKEN_EXPIRED': { type: ErrorType.AUTHENTICATION, key: 'errors.tokenExpired' },
  'UNAUTHORIZED': { type: ErrorType.AUTHENTICATION, key: 'errors.unauthorized' },

  // Authorization errors
  'FORBIDDEN': { type: ErrorType.AUTHORIZATION, key: 'errors.forbidden' },
  'INSUFFICIENT_PERMISSIONS': { type: ErrorType.AUTHORIZATION, key: 'errors.insufficientPermissions' },

  // Validation errors
  'VALIDATION_ERROR': { type: ErrorType.VALIDATION, key: 'errors.validationError' },
  'INVALID_INPUT': { type: ErrorType.VALIDATION, key: 'errors.invalidInput' },
  'REQUIRED_FIELD': { type: ErrorType.VALIDATION, key: 'errors.requiredField' },

  // Not found errors
  'NOT_FOUND': { type: ErrorType.NOT_FOUND, key: 'errors.notFound' },
  'RESOURCE_NOT_FOUND': { type: ErrorType.NOT_FOUND, key: 'errors.resourceNotFound' },
  'USER_NOT_FOUND': { type: ErrorType.NOT_FOUND, key: 'errors.userNotFound' },
  'FAMILY_NOT_FOUND': { type: ErrorType.NOT_FOUND, key: 'errors.familyNotFound' },
  'ACCOUNT_NOT_FOUND': { type: ErrorType.NOT_FOUND, key: 'errors.accountNotFound' },
  'CATEGORY_NOT_FOUND': { type: ErrorType.NOT_FOUND, key: 'errors.categoryNotFound' },
  'TRANSACTION_NOT_FOUND': { type: ErrorType.NOT_FOUND, key: 'errors.transactionNotFound' },

  // Conflict errors
  'ALREADY_EXISTS': { type: ErrorType.CONFLICT, key: 'errors.alreadyExists' },
  'USER_ALREADY_EXISTS': { type: ErrorType.CONFLICT, key: 'errors.userAlreadyExists' },
  'EMAIL_ALREADY_EXISTS': { type: ErrorType.CONFLICT, key: 'errors.emailAlreadyExists' },
  'ALREADY_MEMBER': { type: ErrorType.CONFLICT, key: 'errors.alreadyMember' },

  // Server errors
  'SERVER_ERROR': { type: ErrorType.SERVER, key: 'errors.serverError' },
  'DATABASE_ERROR': { type: ErrorType.SERVER, key: 'errors.databaseError' },

  // Offline errors
  'OFFLINE': { type: ErrorType.OFFLINE, key: 'errors.offline' },
  'SYNC_ERROR': { type: ErrorType.OFFLINE, key: 'errors.syncError' },

  // Unknown errors
  'UNKNOWN_ERROR': { type: ErrorType.UNKNOWN, key: 'errors.unknownError' },
};

/**
 * HTTP status codes mapped to error types and translation keys
 */
export const HTTP_STATUS_CODES: Record<number, { type: ErrorType; key: string }> = {
  400: { type: ErrorType.VALIDATION, key: 'errors.badRequest' },
  401: { type: ErrorType.AUTHENTICATION, key: 'errors.unauthorized' },
  403: { type: ErrorType.AUTHORIZATION, key: 'errors.forbidden' },
  404: { type: ErrorType.NOT_FOUND, key: 'errors.notFound' },
  409: { type: ErrorType.CONFLICT, key: 'errors.conflict' },
  422: { type: ErrorType.VALIDATION, key: 'errors.validationError' },
  500: { type: ErrorType.SERVER, key: 'errors.serverError' },
  502: { type: ErrorType.SERVER, key: 'errors.badGateway' },
  503: { type: ErrorType.SERVER, key: 'errors.serviceUnavailable' },
  504: { type: ErrorType.SERVER, key: 'errors.gatewayTimeout' },
};

/**
 * Error response interface
 */
export interface ErrorResponse {
  type: ErrorType;
  message: string;
  code?: string;
  details?: Record<string, string>;
  status?: number;
}

/**
 * Interface for toast-like notifications
 */
export interface ToastInterface {
  show: (message: string, options?: any) => string;
  hide: (id: string) => void;
  hideAll: () => void;
}

/**
 * Hook to get the toast interface for use in components
 * @returns The toast interface
 */
export const useErrorToast = (): ToastInterface => {
  return useToast();
}

/**
 * Parses an API error response into a standardized error response
 * @param error The error object from the API
 * @param t The translation function
 * @returns Standardized error response
 */
export const parseApiError = (error: any, t: TFunction): ErrorResponse => {
  // If it's an API response with error data
  if (error && typeof error === 'object' && 'status' in error) {
    const apiError = error as ApiResponse<any>;
    const status = apiError.status;

    // If the API returned a specific error code
    if (apiError.error && typeof apiError.error === 'object' && 'code' in apiError.error) {
      const errorObj = apiError.error as any;
      const errorCode = errorObj.code as string;
      const errorInfo = ERROR_CODES[errorCode] || { type: ErrorType.UNKNOWN, key: 'errors.unknownError' };
      const errorDetails = errorObj.details || {};

      return {
        type: errorInfo.type,
        message: String(t(errorInfo.key, errorDetails)),
        code: errorCode,
        details: errorDetails as Record<string, string>,
        status,
      };
    }

    // If we have a status code but no specific error code
    if (status && HTTP_STATUS_CODES[status]) {
      const errorInfo = HTTP_STATUS_CODES[status];

      return {
        type: errorInfo.type,
        message: t(errorInfo.key),
        status,
      };
    }

    // If we have an error message but no code
    if (apiError.error && typeof apiError.error === 'string') {
      return {
        type: ErrorType.UNKNOWN,
        message: apiError.error,
        status,
      };
    }
  }

  // If it's a network error
  if (error && error.message && error.message.includes('Network')) {
    return {
      type: ErrorType.NETWORK,
      message: t('errors.networkError'),
      code: 'NETWORK_ERROR',
    };
  }

  // If it's a timeout error
  if (error && error.message && error.message.includes('timeout')) {
    return {
      type: ErrorType.NETWORK,
      message: t('errors.timeoutError'),
      code: 'TIMEOUT_ERROR',
    };
  }

  // If it's a standard Error object
  if (error instanceof Error) {
    return {
      type: ErrorType.UNKNOWN,
      message: error.message,
    };
  }

  // Default fallback
  return {
    type: ErrorType.UNKNOWN,
    message: t('errors.unknownError'),
  };
};

/**
 * Gets a user-friendly error message for a specific error
 * @param error The error object
 * @param t The translation function
 * @returns User-friendly error message
 */
export const getErrorMessage = (error: any, t: TFunction): string => {
  const errorResponse = parseApiError(error, t);
  return errorResponse.message;
};

/**
 * Handles API errors consistently across the app
 * @param error The error object or message
 * @param t The translation function
 * @param specificErrorKey Translation key for a specific error message
 * @param defaultErrorKey Translation key for a default error message
 * @param toast Optional toast interface for showing notifications
 * @param logError Whether to log the error to console (default: true)
 */
export const handleApiError = (
  error: any,
  t: TFunction,
  specificErrorKey: string,
  defaultErrorKey: string = 'common.unexpectedError',
  toast?: ToastInterface,
  logError: boolean = true
): string => {
  // Parse the error to get a standardized error response
  const errorResponse = parseApiError(error, t);

  // Use the parsed error message or fallback to specific/default error keys
  let errorMessage = errorResponse.message;

  if (!errorMessage) {
    errorMessage = t(specificErrorKey, errorResponse.details);
    if (!errorMessage || errorMessage === specificErrorKey) {
      errorMessage = t(defaultErrorKey);
    }
  }

  // Log error if needed
  if (logError) {
    console.error(`API Error (${specificErrorKey}):`, error);
    console.error('Parsed error:', errorResponse);
  }

  // Show error message
  if (toast) {
    toast.show(errorMessage, {
      type: 'danger',
      // Add additional properties based on error type
      duration: errorResponse.type === ErrorType.VALIDATION ? 5000 : 3000,
    });
  } else {
    // Fallback to Alert
    Alert.alert(t('common.error'), errorMessage);
  }

  return errorMessage;
};

/**
 * Shows a success message
 * @param t The translation function
 * @param successKey Translation key for the success message
 * @param toast Optional toast interface for showing notifications
 * @param params Optional parameters for translation
 */
export const showSuccessMessage = (
  t: TFunction,
  successKey: string,
  toast?: ToastInterface,
  params?: Record<string, any>
): void => {
  const message = t(successKey, params);

  if (toast) {
    toast.show(message, { type: 'success' });
  } else {
    Alert.alert(t('common.success'), message);
  }
};

/**
 * Shows a confirmation dialog and executes the onConfirm callback if confirmed
 * @param t The translation function
 * @param titleKey Translation key for the title
 * @param messageKey Translation key for the message
 * @param onConfirm Callback to execute when confirmed
 * @param messageParams Parameters for the message translation
 */
export const showConfirmationDialog = (
  t: TFunction,
  titleKey: string,
  messageKey: string,
  onConfirm: () => void,
  messageParams?: Record<string, any>
): void => {
  Alert.alert(
    t(titleKey),
    t(messageKey, messageParams),
    [
      {
        text: t('common.cancel'),
        style: 'cancel',
      },
      {
        text: t('common.confirm'),
        style: 'destructive',
        onPress: onConfirm,
      },
    ]
  );
};

/**
 * Show an error message to the user
 * @param message Error message to display
 * @param title Optional title for the error message
 */
export const showErrorMessage = (message: string, title?: string): void => {
  if (Platform.OS === 'web') {
    // For web, use browser alert
    window.alert(`${title || 'Error'}: ${message}`);
  } else {
    // For mobile, use React Native Alert
    Alert.alert(title || 'Error', message);
  }
};
