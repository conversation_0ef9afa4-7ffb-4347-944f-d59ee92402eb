import * as ImageManipulator from 'expo-image-manipulator';
import * as FileSystem from 'expo-file-system';

/**
 * Image compression quality levels
 */
export enum CompressionQuality {
  Low = 0.3,
  Medium = 0.5,
  High = 0.7,
  VeryHigh = 0.9,
}

/**
 * Image resize options
 */
export interface ResizeOptions {
  width?: number;
  height?: number;
  maintainAspectRatio?: boolean;
}

/**
 * Compress an image
 * @param uri Image URI
 * @param quality Compression quality (0-1)
 * @param resize Resize options
 * @returns Compressed image URI
 */
export const compressImage = async (
  uri: string,
  quality: CompressionQuality = CompressionQuality.Medium,
  resize?: ResizeOptions
): Promise<string> => {
  try {
    // Define actions
    const actions: ImageManipulator.Action[] = [];

    // Add resize action if needed
    if (resize) {
      if (resize.maintainAspectRatio) {
        // Only specify one dimension to maintain aspect ratio
        if (resize.width && !resize.height) {
          actions.push({ resize: { width: resize.width } });
        } else if (!resize.width && resize.height) {
          actions.push({ resize: { height: resize.height } });
        } else if (resize.width && resize.height) {
          // If both dimensions are specified, use the smaller one to maintain aspect ratio
          actions.push({ resize: { width: resize.width } });
        }
      } else if (resize.width && resize.height) {
        // Resize to exact dimensions
        actions.push({ resize: { width: resize.width, height: resize.height } });
      }
    }

    // Compress image
    const result = await ImageManipulator.manipulateAsync(
      uri,
      actions,
      {
        compress: quality,
        format: ImageManipulator.SaveFormat.JPEG,
      }
    );

    return result.uri;
  } catch (error) {
    console.error('Error compressing image:', error);
    return uri; // Return original URI if compression fails
  }
};

/**
 * Get image file size in bytes
 * @param uri Image URI
 * @returns File size in bytes
 */
export const getImageFileSize = async (uri: string): Promise<number> => {
  try {
    const fileInfo = await FileSystem.getInfoAsync(uri);
    // Check if file exists and has size property
    if (fileInfo.exists && 'size' in fileInfo) {
      return fileInfo.size;
    }
    return 0;
  } catch (error) {
    console.error('Error getting image file size:', error);
    return 0;
  }
};

/**
 * Convert image to base64
 * @param uri Image URI
 * @returns Base64 string
 */
export const imageToBase64 = async (uri: string): Promise<string> => {
  try {
    return await FileSystem.readAsStringAsync(uri, {
      encoding: FileSystem.EncodingType.Base64,
    });
  } catch (error) {
    console.error('Error converting image to base64:', error);
    return '';
  }
};

/**
 * Auto-compress image based on file size
 * @param uri Image URI
 * @param maxSizeBytes Maximum file size in bytes (default: 1MB)
 * @returns Compressed image URI
 */
export const autoCompressImage = async (
  uri: string,
  maxSizeBytes: number = 1024 * 1024 // 1MB default
): Promise<string> => {
  try {
    // Get original file size
    const originalSize = await getImageFileSize(uri);

    // If already smaller than max size, return original
    if (originalSize <= maxSizeBytes) {
      return uri;
    }

    // Calculate target quality based on size ratio
    const sizeRatio = maxSizeBytes / originalSize;
    let targetQuality = sizeRatio * 0.9; // Slightly more compression than needed

    // Ensure quality is within valid range
    targetQuality = Math.max(0.1, Math.min(0.9, targetQuality));

    // Determine resize options based on file size
    let resizeOptions: ResizeOptions | undefined;

    if (originalSize > 5 * 1024 * 1024) { // 5MB
      resizeOptions = { width: 1200, maintainAspectRatio: true };
    } else if (originalSize > 2 * 1024 * 1024) { // 2MB
      resizeOptions = { width: 1600, maintainAspectRatio: true };
    }

    // Compress image
    const compressedUri = await compressImage(
      uri,
      targetQuality as CompressionQuality,
      resizeOptions
    );

    // Check if compression was successful
    const compressedSize = await getImageFileSize(compressedUri);

    // If still too large, compress again with more aggressive settings
    if (compressedSize > maxSizeBytes) {
      return await compressImage(
        compressedUri,
        CompressionQuality.Low,
        { width: 1000, maintainAspectRatio: true }
      );
    }

    return compressedUri;
  } catch (error) {
    console.error('Error auto-compressing image:', error);
    return uri; // Return original URI if compression fails
  }
};
