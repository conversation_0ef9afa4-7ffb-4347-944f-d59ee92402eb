/**
 * Standardized form validation system for the Hisab-Kitab app
 */

import { TFunction } from 'i18next';

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

/**
 * Validation rule interface
 */
export interface ValidationRule {
  type: string;
  message?: string;
  value?: any;
}

/**
 * Validation schema interface
 */
export interface ValidationSchema {
  [field: string]: ValidationRule[];
}

/**
 * Validates a required field
 * @param value The value to validate
 * @param t The translation function
 * @param message Optional custom error message
 * @returns Error message if invalid, empty string if valid
 */
export const validateRequired = (
  value: any, 
  t: TFunction, 
  message?: string
): string => {
  if (value === undefined || value === null || value === '') {
    return message || t('validation.required');
  }
  return '';
};

/**
 * Validates an email address
 * @param value The value to validate
 * @param t The translation function
 * @param message Optional custom error message
 * @returns Error message if invalid, empty string if valid
 */
export const validateEmail = (
  value: string, 
  t: TFunction, 
  message?: string
): string => {
  if (!value) return '';
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(value)) {
    return message || t('validation.email');
  }
  return '';
};

/**
 * Validates a minimum length
 * @param value The value to validate
 * @param minLength The minimum length
 * @param t The translation function
 * @param message Optional custom error message
 * @returns Error message if invalid, empty string if valid
 */
export const validateMinLength = (
  value: string, 
  minLength: number, 
  t: TFunction, 
  message?: string
): string => {
  if (!value) return '';
  
  if (value.length < minLength) {
    return message || t('validation.minLength', { length: minLength });
  }
  return '';
};

/**
 * Validates a maximum length
 * @param value The value to validate
 * @param maxLength The maximum length
 * @param t The translation function
 * @param message Optional custom error message
 * @returns Error message if invalid, empty string if valid
 */
export const validateMaxLength = (
  value: string, 
  maxLength: number, 
  t: TFunction, 
  message?: string
): string => {
  if (!value) return '';
  
  if (value.length > maxLength) {
    return message || t('validation.maxLength', { length: maxLength });
  }
  return '';
};

/**
 * Validates a numeric value
 * @param value The value to validate
 * @param t The translation function
 * @param message Optional custom error message
 * @returns Error message if invalid, empty string if valid
 */
export const validateNumber = (
  value: string, 
  t: TFunction, 
  message?: string
): string => {
  if (!value) return '';
  
  if (isNaN(Number(value))) {
    return message || t('validation.number');
  }
  return '';
};

/**
 * Validates a positive number
 * @param value The value to validate
 * @param t The translation function
 * @param message Optional custom error message
 * @returns Error message if invalid, empty string if valid
 */
export const validatePositiveNumber = (
  value: string, 
  t: TFunction, 
  message?: string
): string => {
  if (!value) return '';
  
  const numberError = validateNumber(value, t);
  if (numberError) {
    return numberError;
  }

  if (Number(value) <= 0) {
    return message || t('validation.positive');
  }
  return '';
};

/**
 * Validates a non-negative number
 * @param value The value to validate
 * @param t The translation function
 * @param message Optional custom error message
 * @returns Error message if invalid, empty string if valid
 */
export const validateNonNegativeNumber = (
  value: string, 
  t: TFunction, 
  message?: string
): string => {
  if (!value) return '';
  
  const numberError = validateNumber(value, t);
  if (numberError) {
    return numberError;
  }

  if (Number(value) < 0) {
    return message || t('validation.nonNegative');
  }
  return '';
};

/**
 * Validates a date
 * @param value The value to validate
 * @param t The translation function
 * @param message Optional custom error message
 * @returns Error message if invalid, empty string if valid
 */
export const validateDate = (
  value: any, 
  t: TFunction, 
  message?: string
): string => {
  if (!value) return '';

  let date: Date;

  if (value instanceof Date) {
    date = value;
  } else if (typeof value === 'string' || typeof value === 'number') {
    date = new Date(value);
  } else {
    return message || t('validation.date');
  }

  if (isNaN(date.getTime())) {
    return message || t('validation.date');
  }

  return '';
};

/**
 * Validates a form against a schema
 * @param values The form values
 * @param schema The validation schema
 * @param t The translation function
 * @returns Validation result
 */
export const validateForm = (
  values: Record<string, any>, 
  schema: ValidationSchema, 
  t: TFunction
): ValidationResult => {
  const errors: Record<string, string> = {};
  let isValid = true;

  // Validate each field against its rules
  Object.entries(schema).forEach(([field, rules]) => {
    const value = values[field];

    // Apply each rule to the field
    for (const rule of rules) {
      let error = '';

      switch (rule.type) {
        case 'required':
          error = validateRequired(value, t, rule.message);
          break;
        case 'email':
          error = validateEmail(value, t, rule.message);
          break;
        case 'minLength':
          error = validateMinLength(value, rule.value, t, rule.message);
          break;
        case 'maxLength':
          error = validateMaxLength(value, rule.value, t, rule.message);
          break;
        case 'number':
          error = validateNumber(value, t, rule.message);
          break;
        case 'positiveNumber':
          error = validatePositiveNumber(value, t, rule.message);
          break;
        case 'nonNegativeNumber':
          error = validateNonNegativeNumber(value, t, rule.message);
          break;
        case 'date':
          error = validateDate(value, t, rule.message);
          break;
      }

      // If there's an error, add it to the errors object and break the loop
      if (error) {
        errors[field] = error;
        isValid = false;
        break;
      }
    }
  });

  return { isValid, errors };
};

export default {
  validateForm,
  validateRequired,
  validateEmail,
  validateMinLength,
  validateMaxLength,
  validateNumber,
  validatePositiveNumber,
  validateNonNegativeNumber,
  validateDate,
};
