import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { Alert } from 'react-native';

// Import screens
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import HomeScreen from '../screens/HomeScreen';
import AccountsScreen from '../screens/AccountsScreen';
import CategoriesScreen from '../screens/CategoriesScreen';
import SettingsScreen from '../screens/SettingsScreen';

// Import transaction screens
import TransactionListScreen from '../screens/transactions/TransactionListScreen';
import TransactionDetailsScreen from '../screens/transactions/TransactionDetailsScreen';
import AddTransactionScreen from '../screens/transactions/AddTransactionScreen';
import EditTransactionScreen from '../screens/transactions/EditTransactionScreen';

// Import notification screens
import NotificationScreen from '../screens/notifications/NotificationScreen';
import CreateScheduledNotificationScreen from '../screens/notifications/CreateScheduledNotificationScreen';
import EditScheduledNotificationScreen from '../screens/notifications/EditScheduledNotificationScreen';

// Import contexts
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';

// Import error boundary
import ErrorBoundary from '../components/ErrorBoundary';

// Create navigators
const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Transactions navigator
const TransactionsNavigator = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: isDarkMode ? '#1D3D47' : '#A1CEDC',
        },
        headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="TransactionList"
        component={TransactionListScreen}
        options={{ title: t('transactions.transactions') }}
      />
      <Stack.Screen
        name="TransactionDetails"
        component={TransactionDetailsScreen}
        options={{ title: t('transactions.transactionDetails') }}
      />
      <Stack.Screen
        name="AddTransaction"
        component={AddTransactionScreen}
        options={{ title: t('transactions.addTransaction') }}
      />
      <Stack.Screen
        name="EditTransaction"
        component={EditTransactionScreen}
        options={{ title: t('transactions.editTransaction') }}
      />
    </Stack.Navigator>
  );
};

// Notifications navigator
const NotificationsNavigator = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: isDarkMode ? '#1D3D47' : '#A1CEDC',
        },
        headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="NotificationMain"
        component={NotificationScreen}
        options={{ title: t('notifications.title') }}
      />
      <Stack.Screen
        name="CreateScheduledNotification"
        component={CreateScheduledNotificationScreen}
        options={{ title: t('notifications.createScheduled') }}
      />
      <Stack.Screen
        name="EditScheduledNotification"
        component={EditScheduledNotificationScreen}
        options={{ title: t('notifications.editScheduled') }}
      />
    </Stack.Navigator>
  );
};

// Auth navigator
const AuthNavigator = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: isDarkMode ? '#1D3D47' : '#A1CEDC',
        },
        headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="Login"
        component={LoginScreen}
        options={{ title: t('auth.login') }}
      />
      <Stack.Screen
        name="Register"
        component={RegisterScreen}
        options={{ title: t('auth.register') }}
      />
    </Stack.Navigator>
  );
};

// Tab navigator
const TabNavigator = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Accounts') {
            iconName = focused ? 'wallet' : 'wallet-outline';
          } else if (route.name === 'Transactions') {
            iconName = focused ? 'swap-horizontal' : 'swap-horizontal-outline';
          } else if (route.name === 'Categories') {
            iconName = focused ? 'list' : 'list-outline';
          } else if (route.name === 'Notifications') {
            iconName = focused ? 'notifications' : 'notifications-outline';
          } else if (route.name === 'Settings') {
            iconName = focused ? 'settings' : 'settings-outline';
          }

          return <Ionicons name={iconName as any} size={size} color={color} />;
        },
        tabBarActiveTintColor: isDarkMode ? '#58A6FF' : '#0366D6',
        tabBarInactiveTintColor: isDarkMode ? '#8B949E' : '#6E7781',
        tabBarStyle: {
          backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
          borderTopColor: isDarkMode ? '#30363D' : '#D0D7DE',
        },
        headerStyle: {
          backgroundColor: isDarkMode ? '#1D3D47' : '#A1CEDC',
        },
        headerTintColor: isDarkMode ? '#FFFFFF' : '#000000',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{ title: t('common.appName') }}
      />
      <Tab.Screen
        name="Accounts"
        component={AccountsScreen}
        options={{ title: t('accounts.accounts') }}
      />
      <Tab.Screen
        name="Transactions"
        component={TransactionsNavigator}
        options={{ title: t('transactions.transactions'), headerShown: false }}
      />
      <Tab.Screen
        name="Categories"
        component={CategoriesScreen}
        options={{ title: t('categories.categories') }}
      />
      <Tab.Screen
        name="Notifications"
        component={NotificationsNavigator}
        options={{
          title: t('notifications.title'),
          headerShown: false
        }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{ title: t('settings.settings') }}
      />
    </Tab.Navigator>
  );
};

// App navigator
const AppNavigator = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const { t } = useTranslation();

  if (isLoading) {
    // You could return a loading screen here
    return null;
  }

  // Handle errors in the navigation
  const handleNavigationError = (error: Error) => {
    console.error('Navigation error:', error);
    Alert.alert(
      t('common.navigationError'),
      t('common.restartApp')
    );
  };

  return (
    <ErrorBoundary
      onError={(error) => {
        console.error('Error caught by ErrorBoundary in AppNavigator:', error);
        handleNavigationError(error);
      }}
    >
      <NavigationContainer>
        {isAuthenticated ? <TabNavigator /> : <AuthNavigator />}
      </NavigationContainer>
    </ErrorBoundary>
  );
};

export default AppNavigator;
