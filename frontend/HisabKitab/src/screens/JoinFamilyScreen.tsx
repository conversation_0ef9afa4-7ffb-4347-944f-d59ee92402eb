import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import apiService from '../services/api';
import dbService from '../services/db';

const JoinFamilyScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const router = useRouter();

  const [inviteCode, setInviteCode] = useState('');
  const [loading, setLoading] = useState(false);

  const handleJoinFamily = async () => {
    if (!inviteCode.trim()) {
      Alert.alert(
        t('common.error'),
        t('family.enterInviteCode')
      );
      return;
    }

    try {
      setLoading(true);

      const response = await apiService.family.joinFamily(inviteCode.trim());

      if (response.data) {
        // Save family to local database
        const savedFamily = {
          ...response.data,
          createdAt: new Date(response.data.createdAt),
          updatedAt: new Date(response.data.updatedAt),
          isSynced: true
        };
        await dbService.saveFamily(savedFamily);

        // Navigate to families list instead of directly to family details
        // This prevents the "no permission" error that can occur when trying to
        // access the family details immediately after joining
        router.replace('/families' as any);

        // Show success message
        Alert.alert(
          t('common.success'),
          t('family.joinSuccess')
        );
      } else {
        // Check for specific error messages
        const errorMessage = response.error || '';

        console.log('Join family error:', errorMessage);

        if (errorMessage.toLowerCase().includes('already a member')) {
          Alert.alert(
            t('common.error'),
            t('family.alreadyMember')
          );
        } else if (errorMessage.toLowerCase().includes('invalid') || errorMessage.toLowerCase().includes('not found')) {
          Alert.alert(
            t('common.error'),
            t('family.invalidInviteCode')
          );
        } else if (errorMessage.toLowerCase().includes('expired')) {
          Alert.alert(
            t('common.error'),
            t('family.inviteCodeExpired')
          );
        } else if (errorMessage.toLowerCase().includes('required role')) {
          Alert.alert(
            t('common.error'),
            t('family.noPermission')
          );
        } else {
          Alert.alert(
            t('common.error'),
            errorMessage || t('family.joinFailed')
          );
        }
      }
    } catch (error) {
      console.error('Error joining family:', error);

      // Try to extract a meaningful error message
      const errorMessage = error instanceof Error ? error.message : '';

      if (errorMessage.toLowerCase().includes('already a member')) {
        Alert.alert(
          t('common.error'),
          t('family.alreadyMember')
        );
      } else {
        Alert.alert(
          t('common.error'),
          t('family.joinFailed')
        );
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      <ScrollView
        style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}
        contentContainerStyle={styles.contentContainer}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons
              name="arrow-back"
              size={24}
              color={isDarkMode ? '#FFFFFF' : '#000000'}
            />
          </TouchableOpacity>
          <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('family.joinFamily')}
          </Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('family.inviteCode')} *
          </Text>
          <TextInput
            style={[
              styles.input,
              {
                backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                color: isDarkMode ? '#FFFFFF' : '#000000',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
              }
            ]}
            placeholder={t('family.enterInviteCode')}
            placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
            value={inviteCode}
            onChangeText={setInviteCode}
            autoCapitalize="characters"
            maxLength={20}
          />

          <TouchableOpacity
            style={[
              styles.joinButton,
              {
                backgroundColor: isDarkMode ? '#238636' : '#2DA44E',
                opacity: loading ? 0.7 : 1
              }
            ]}
            onPress={handleJoinFamily}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#FFFFFF" size="small" />
            ) : (
              <Text style={styles.joinButtonText}>
                {t('family.joinFamily')}
              </Text>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.infoContainer}>
          <Ionicons
            name="information-circle-outline"
            size={24}
            color={isDarkMode ? '#8B949E' : '#6E7781'}
            style={styles.infoIcon}
          />
          <Text style={[styles.infoText, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {t('family.enterInviteCode')}
          </Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  formContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    marginBottom: 16,
  },
  joinButton: {
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  joinButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
  },
  infoIcon: {
    marginRight: 8,
  },
  infoText: {
    fontSize: 14,
    flex: 1,
  },
});

export default JoinFamilyScreen;
