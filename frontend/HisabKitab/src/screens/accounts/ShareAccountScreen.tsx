import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import apiService from '../../services/api';
import Dropdown from '../../components/ui/Dropdown';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';

// Import Account model
import { Account, AccountShare } from '../../models/Account';

interface Family {
  id: number;
  name: string;
}

interface FamilyMember {
  id: number;
  firstName: string;
  lastName: string;
  username: string;
}

type RouteParams = {
  ShareAccount: {
    accountId: number;
  };
};

const ShareAccountScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();
  const route = useRoute<RouteProp<RouteParams, 'ShareAccount'>>();
  const { accountId } = route.params;

  const [account, setAccount] = useState<Account | null>(null);
  const [families, setFamilies] = useState<Family[]>([]);
  const [selectedFamily, setSelectedFamily] = useState<number | null>(null);
  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([]);
  const [selectedMember, setSelectedMember] = useState<number | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState('View');
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Permission options
  const permissionOptions = [
    { label: t('accounts.viewOnly'), value: 'View' },
    { label: t('accounts.viewAndEdit'), value: 'View,Edit' },
    { label: t('accounts.fullAccess'), value: 'View,Edit,Delete' },
  ];

  // Fetch account and families
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Fetch account
        const accountResponse = await apiService.accounts.getAccount(accountId);

        if (accountResponse.status === 200 && accountResponse.data) {
          setAccount(accountResponse.data);
        } else {
          console.error('Failed to fetch account:', accountResponse.error);
          Alert.alert(
            t('common.error'),
            accountResponse.error || t('accounts.fetchAccountError')
          );
          navigation.goBack();
        }

        // Fetch families
        const familiesResponse = await apiService.family.getFamilies();

        if (familiesResponse.status === 200 && familiesResponse.data) {
          setFamilies(familiesResponse.data);

          // If account is a family account, preselect that family
          if (accountResponse.data && accountResponse.data.familyId) {
            const accountFamily = familiesResponse.data.find(
              (family: Family) => family.id === accountResponse.data?.familyId
            );

            if (accountFamily) {
              setSelectedFamily(accountFamily.id);

              // Fetch family members
              const membersResponse = await apiService.family.getFamilyMembers(accountFamily.id);

              if (membersResponse.status === 200 && membersResponse.data) {
                // Filter out members who already have access
                const existingMemberIds = accountResponse.data?.sharedWith?.map(
                  (share: AccountShare) => share.familyMemberId
                ) || [];

                const availableMembers = membersResponse.data.filter(
                  (member: FamilyMember) => !existingMemberIds.includes(member.id)
                );

                setFamilyMembers(availableMembers);
              }
            }
          }
        } else {
          console.error('Failed to fetch families:', familiesResponse.error);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        Alert.alert(
          t('common.error'),
          t('accounts.fetchAccountError')
        );
        navigation.goBack();
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [accountId, navigation, t]);

  // Handle family selection
  const handleFamilyChange = async (value: string | number | boolean | null) => {
    const familyId = value as number;
    setSelectedFamily(familyId);
    setSelectedMember(null);
    setFamilyMembers([]);

    if (familyId) {
      try {
        const response = await apiService.family.getFamilyMembers(familyId);

        if (response.status === 200 && response.data) {
          if (account) {
            // Filter out members who already have access
            const existingMemberIds = account.sharedWith?.map(
              (share: AccountShare) => share.familyMemberId
            ) || [];

            const availableMembers = response.data.filter(
              (member: FamilyMember) => !existingMemberIds.includes(member.id)
            );

            setFamilyMembers(availableMembers);
          } else {
            // If account is not loaded yet, show all members
            setFamilyMembers(response.data);
          }
        } else {
          console.error('Failed to fetch family members:', response.error);
        }
      } catch (error) {
        console.error('Error fetching family members:', error);
      }
    }
  };

  // Handle share account
  const handleShareAccount = async () => {
    if (!selectedFamily || !selectedMember) {
      Alert.alert(
        t('common.error'),
        t('accounts.selectMemberError')
      );
      return;
    }

    try {
      setIsSubmitting(true);

      const shareData = {
        familyMemberId: selectedMember,
        permissions: selectedPermissions,
      };

      const response = await apiService.accounts.shareAccount(accountId, shareData);

      if (response.status === 201 && response.data) {
        Alert.alert(
          t('common.success'),
          t('accounts.shareSuccess'),
          [
            {
              text: t('common.ok'),
              onPress: () => navigation.goBack(),
            },
          ]
        );
      } else {
        Alert.alert(
          t('common.error'),
          response.error || t('accounts.shareError')
        );
      }
    } catch (error) {
      console.error('Error sharing account:', error);
      Alert.alert(
        t('common.error'),
        t('accounts.shareError')
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render member item
  const renderMemberItem = ({ item }: { item: FamilyMember }) => (
    <TouchableOpacity
      style={[
        styles.memberItem,
        selectedMember === item.id && {
          backgroundColor: isDarkMode ? '#30363D' : '#F6F8FA',
          borderColor: isDarkMode ? '#58A6FF' : '#0366D6',
        },
      ]}
      onPress={() => setSelectedMember(item.id)}
    >
      <View style={styles.memberInfo}>
        <Ionicons
          name="person-outline"
          size={20}
          color={isDarkMode ? '#8B949E' : '#6E7781'}
          style={styles.memberIcon}
        />
        <View>
          <Text
            style={[
              styles.memberName,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
          >
            {item.firstName} {item.lastName}
          </Text>
          <Text
            style={[
              styles.memberUsername,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            @{item.username}
          </Text>
        </View>
      </View>

      {selectedMember === item.id && (
        <Ionicons
          name="checkmark-circle"
          size={24}
          color={isDarkMode ? '#58A6FF' : '#0366D6'}
        />
      )}
    </TouchableOpacity>
  );

  if (isLoading || !account) {
    return (
      <View
        style={[
          styles.loadingContainer,
          { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
        ]}
      >
        <ActivityIndicator
          size="large"
          color={isDarkMode ? '#58A6FF' : '#0366D6'}
        />
      </View>
    );
  }

  return (
    <ScrollView
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
      ]}
      contentContainerStyle={styles.scrollContent}
    >
      <View style={styles.content}>
        {/* Permission explanation card */}
        <Card style={styles.explanationCard}>
          <View style={styles.explanationHeader}>
            <Ionicons
              name="information-circle-outline"
              size={24}
              color={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
            <Text
              style={[
                styles.explanationTitle,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' },
              ]}
            >
              {t('accounts.permissionLevels')}
            </Text>
          </View>

          <View style={styles.permissionItem}>
            <View style={styles.permissionHeader}>
              <Ionicons
                name="eye-outline"
                size={20}
                color={isDarkMode ? '#8B949E' : '#6E7781'}
              />
              <Text
                style={[
                  styles.permissionTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                ]}
              >
                {t('accounts.viewOnly')}
              </Text>
            </View>
            <Text
              style={[
                styles.permissionDescription,
                { color: isDarkMode ? '#8B949E' : '#6E7781' },
              ]}
            >
              {t('accounts.viewOnlyDescription')}
            </Text>
          </View>

          <View style={styles.permissionItem}>
            <View style={styles.permissionHeader}>
              <Ionicons
                name="pencil-outline"
                size={20}
                color={isDarkMode ? '#8B949E' : '#6E7781'}
              />
              <Text
                style={[
                  styles.permissionTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                ]}
              >
                {t('accounts.viewAndEdit')}
              </Text>
            </View>
            <Text
              style={[
                styles.permissionDescription,
                { color: isDarkMode ? '#8B949E' : '#6E7781' },
              ]}
            >
              {t('accounts.viewAndEditDescription')}
            </Text>
          </View>

          <View style={styles.permissionItem}>
            <View style={styles.permissionHeader}>
              <Ionicons
                name="trash-outline"
                size={20}
                color={isDarkMode ? '#8B949E' : '#6E7781'}
              />
              <Text
                style={[
                  styles.permissionTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                ]}
              >
                {t('accounts.fullAccess')}
              </Text>
            </View>
            <Text
              style={[
                styles.permissionDescription,
                { color: isDarkMode ? '#8B949E' : '#6E7781' },
              ]}
            >
              {t('accounts.fullAccessDescription')}
            </Text>
          </View>
        </Card>

        {/* Family selection */}
        <Dropdown
          label={t('accounts.selectFamily')}
          options={families.map((family: Family) => ({
            label: family.name,
            value: family.id,
          }))}
          selectedValue={selectedFamily}
          onValueChange={handleFamilyChange}
          placeholder={t('accounts.selectFamilyPlaceholder')}
        />

        {/* Permissions selection */}
        <Dropdown
          label={t('accounts.selectPermissions')}
          options={permissionOptions}
          selectedValue={selectedPermissions}
          onValueChange={(value) => setSelectedPermissions(value as string)}
        />

        {/* Members list */}
        {selectedFamily && (
          <>
            <Text
              style={[
                styles.membersTitle,
                { color: isDarkMode ? '#8B949E' : '#6E7781' },
              ]}
            >
              {t('accounts.selectMember')}
            </Text>

            {familyMembers.length > 0 ? (
              <FlatList
                data={familyMembers}
                renderItem={renderMemberItem}
                keyExtractor={(item) => item.id.toString()}
                style={styles.membersList}
              />
            ) : (
              <View style={styles.emptyContainer}>
                <Text
                  style={[
                    styles.emptyText,
                    { color: isDarkMode ? '#8B949E' : '#6E7781' },
                  ]}
                >
                  {t('accounts.noAvailableMembers')}
                </Text>
              </View>
            )}
          </>
        )}
      </View>

      <View style={styles.footer}>
        <Button
          title={t('common.cancel')}
          onPress={() => navigation.goBack()}
          variant="secondary"
          style={styles.footerButton}
        />
        <Button
          title={t('accounts.shareAccount')}
          onPress={handleShareAccount}
          isLoading={isSubmitting}
          disabled={!selectedMember}
          style={styles.footerButton}
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  explanationCard: {
    marginBottom: 24,
  },
  explanationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  explanationTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  permissionItem: {
    marginBottom: 16,
  },
  permissionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  permissionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  permissionDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 28,
  },
  membersTitle: {
    fontSize: 16,
    marginTop: 16,
    marginBottom: 8,
  },
  membersList: {
    flex: 1,
  },
  memberItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 8,
    borderColor: '#30363D',
  },
  memberInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  memberIcon: {
    marginRight: 8,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '600',
  },
  memberUsername: {
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#30363D',
  },
  footerButton: {
    flex: 1,
    marginHorizontal: 8,
  },
});

export default ShareAccountScreen;
