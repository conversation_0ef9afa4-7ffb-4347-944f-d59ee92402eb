import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import apiService from '../../services/api';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { formatCurrency, formatDate } from '../../utils/formatters';

type RouteParams = {
  AccountDetails: {
    accountId: number;
  };
};

const AccountDetailsScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();
  const route = useRoute<RouteProp<RouteParams, 'AccountDetails'>>();
  const { accountId } = route.params;

  const [account, setAccount] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSharing, setIsSharing] = useState(false);

  // Fetch account
  useEffect(() => {
    const fetchAccount = async () => {
      try {
        setIsLoading(true);
        const response = await apiService.accounts.getAccount(accountId);

        if (response.status === 200 && response.data) {
          setAccount(response.data);
        } else {
          console.error('Failed to fetch account:', response.error);
          Alert.alert(
            t('common.error'),
            response.error || t('accounts.fetchAccountError')
          );
          navigation.goBack();
        }
      } catch (error) {
        console.error('Error fetching account:', error);
        Alert.alert(
          t('common.error'),
          t('accounts.fetchAccountError')
        );
        navigation.goBack();
      } finally {
        setIsLoading(false);
      }
    };

    fetchAccount();
  }, [accountId, navigation, t]);

  // Get account icon based on account type
  const getAccountIcon = (accountType: string): any => {
    switch (accountType) {
      case 'Cash':
        return 'cash-outline';
      case 'Bank':
        return 'business-outline';
      case 'CreditCard':
        return 'card-outline';
      case 'Investment':
        return 'trending-up-outline';
      case 'Savings':
        return 'wallet-outline';
      case 'Loan':
        return 'receipt-outline';
      default:
        return 'wallet-outline';
    }
  };

  // Handle edit account
  const handleEditAccount = () => {
    navigation.navigate('EditAccount', { accountId });
  };

  // Handle delete account
  const handleDeleteAccount = () => {
    Alert.alert(
      t('accounts.deleteAccount'),
      t('accounts.deleteAccountConfirm', { name: account.name }),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await apiService.accounts.deleteAccount(accountId);

              if (response.status === 204) {
                navigation.goBack();
              } else {
                Alert.alert(
                  t('common.error'),
                  response.error || t('accounts.deleteAccountError')
                );
              }
            } catch (error) {
              console.error('Error deleting account:', error);
              Alert.alert(
                t('common.error'),
                t('accounts.deleteAccountError')
              );
            }
          },
        },
      ]
    );
  };

  // Handle share account
  const handleShareAccount = () => {
    navigation.navigate('ShareAccount', { accountId });
  };

  // Handle remove share
  const handleRemoveShare = (shareId: number, memberName: string) => {
    Alert.alert(
      t('accounts.removeShare'),
      t('accounts.removeShareConfirm', { name: memberName }),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.remove'),
          style: 'destructive',
          onPress: async () => {
            try {
              setIsSharing(true);
              const response = await apiService.accounts.removeAccountShare(shareId);

              if (response.status === 204) {
                // Refresh account data
                const accountResponse = await apiService.accounts.getAccount(accountId);
                if (accountResponse.status === 200 && accountResponse.data) {
                  setAccount(accountResponse.data);
                }
              } else {
                Alert.alert(
                  t('common.error'),
                  response.error || t('accounts.removeShareError')
                );
              }
            } catch (error) {
              console.error('Error removing share:', error);
              Alert.alert(
                t('common.error'),
                t('accounts.removeShareError')
              );
            } finally {
              setIsSharing(false);
            }
          },
        },
      ]
    );
  };

  if (isLoading || !account) {
    return (
      <View
        style={[
          styles.loadingContainer,
          { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
        ]}
      >
        <ActivityIndicator
          size="large"
          color={isDarkMode ? '#58A6FF' : '#0366D6'}
        />
      </View>
    );
  }

  return (
    <ScrollView
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
      ]}
      contentContainerStyle={styles.contentContainer}
    >
      {/* Account header */}
      <View style={styles.header}>
        <View
          style={[
            styles.iconContainer,
            { backgroundColor: isDarkMode ? '#30363D' : '#F6F8FA' },
          ]}
        >
          <Ionicons
            name={getAccountIcon(account.accountType)}
            size={32}
            color={isDarkMode ? '#FFFFFF' : '#24292E'}
          />
        </View>

        <Text
          style={[
            styles.accountName,
            { color: isDarkMode ? '#FFFFFF' : '#24292E' },
          ]}
        >
          {account.name}
        </Text>

        <Text
          style={[
            styles.accountType,
            { color: isDarkMode ? '#8B949E' : '#6E7781' },
          ]}
        >
          {t(`accounts.${account.accountType.toLowerCase()}`)}
        </Text>

        {!account.isActive && (
          <View style={styles.inactiveTag}>
            <Text style={styles.inactiveTagText}>
              {t('accounts.inactive')}
            </Text>
          </View>
        )}
      </View>

      {/* Balance card */}
      <Card
        style={{
          ...styles.balanceCard,
          borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
        }}
      >
        <View style={styles.balanceRow}>
          <Text
            style={[
              styles.balanceLabel,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t('accounts.currentBalance')}
          </Text>
          <Text
            style={[
              styles.balanceAmount,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
              account.balance < 0 && styles.negativeBalance,
            ]}
          >
            {formatCurrency(account.balance, account.currency)}
          </Text>
        </View>

        <View style={styles.balanceRow}>
          <Text
            style={[
              styles.balanceLabel,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t('accounts.initialBalance')}
          </Text>
          <Text
            style={[
              styles.balanceValue,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
          >
            {formatCurrency(account.initialBalance, account.currency)}
          </Text>
        </View>
      </Card>

      {/* Account details */}
      <Card
        title={t('accounts.accountDetails')}
        style={{ borderColor: isDarkMode ? '#30363D' : '#D0D7DE' }}
      >
        <View style={styles.detailRow}>
          <Text
            style={[
              styles.detailLabel,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t('accounts.currency')}
          </Text>
          <Text
            style={[
              styles.detailValue,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
          >
            {account.currency}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text
            style={[
              styles.detailLabel,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t('accounts.owner')}
          </Text>
          <Text
            style={[
              styles.detailValue,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
          >
            {account.familyId
              ? t('accounts.familyAccount')
              : t('accounts.personalAccount')}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text
            style={[
              styles.detailLabel,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t('accounts.excludeFromStats')}
          </Text>
          <Text
            style={[
              styles.detailValue,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
          >
            {account.excludeFromStats ? t('common.yes') : t('common.no')}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text
            style={[
              styles.detailLabel,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t('accounts.lastUpdated')}
          </Text>
          <Text
            style={[
              styles.detailValue,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
          >
            {formatDate(account.lastUpdatedAt)}
          </Text>
        </View>
      </Card>

      {/* Shared with */}
      {account.sharedWith && account.sharedWith.length > 0 && (
        <Card
          title={t('accounts.sharedWith')}
          style={{ borderColor: isDarkMode ? '#30363D' : '#D0D7DE' }}
        >
          {account.sharedWith.map((share: any) => (
            <View key={share.id} style={styles.shareItem}>
              <View style={styles.shareMember}>
                <Ionicons
                  name="person-outline"
                  size={20}
                  color={isDarkMode ? '#8B949E' : '#6E7781'}
                  style={styles.shareIcon}
                />
                <View style={styles.shareMemberInfo}>
                  <Text
                    style={[
                      styles.shareMemberName,
                      { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                    ]}
                  >
                    {share.memberFullName || share.memberUsername}
                  </Text>
                  <Text
                    style={[
                      styles.sharePermissions,
                      { color: isDarkMode ? '#8B949E' : '#6E7781' },
                    ]}
                  >
                    {share.permissions}
                  </Text>
                </View>
              </View>

              <TouchableOpacity
                onPress={() => handleRemoveShare(share.id, share.memberFullName || share.memberUsername)}
                disabled={isSharing}
              >
                <Ionicons
                  name="close-circle-outline"
                  size={24}
                  color={isDarkMode ? '#F85149' : '#DA3633'}
                />
              </TouchableOpacity>
            </View>
          ))}

          <Button
            title={t('accounts.shareWithOthers')}
            onPress={handleShareAccount}
            variant="outline"
            icon="share-outline"
            style={styles.shareButton}
            isLoading={isSharing}
          />
        </Card>
      )}

      {/* Action buttons */}
      <View style={styles.actions}>
        <Button
          title={t('common.edit')}
          onPress={handleEditAccount}
          variant="primary"
          icon="pencil-outline"
          style={styles.actionButton}
        />

        <Button
          title={t('common.delete')}
          onPress={handleDeleteAccount}
          variant="danger"
          icon="trash-outline"
          style={styles.actionButton}
        />

        {account.sharedWith && account.sharedWith.length === 0 && (
          <Button
            title={t('accounts.share')}
            onPress={handleShareAccount}
            variant="secondary"
            icon="share-outline"
            style={styles.actionButton}
            isLoading={isSharing}
          />
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  accountName: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  accountType: {
    fontSize: 16,
    marginBottom: 8,
  },
  inactiveTag: {
    backgroundColor: '#F85149',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  inactiveTagText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  balanceCard: {
    marginBottom: 16,
  },
  balanceRow: {
    marginBottom: 12,
  },
  balanceLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  balanceAmount: {
    fontSize: 28,
    fontWeight: '700',
  },
  balanceValue: {
    fontSize: 16,
  },
  negativeBalance: {
    color: '#F85149',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  shareItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  shareMember: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  shareIcon: {
    marginRight: 8,
  },
  shareMemberInfo: {
    flex: 1,
  },
  shareMemberName: {
    fontSize: 14,
    fontWeight: '600',
  },
  sharePermissions: {
    fontSize: 12,
  },
  shareButton: {
    marginTop: 16,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
});

export default AccountDetailsScreen;
