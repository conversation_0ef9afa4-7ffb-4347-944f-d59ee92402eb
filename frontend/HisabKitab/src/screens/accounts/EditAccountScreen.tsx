import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import apiService from '../../services/api';
import AccountForm from '../../components/accounts/AccountForm';

type RouteParams = {
  EditAccount: {
    accountId: number;
  };
};

const EditAccountScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();
  const route = useRoute<RouteProp<RouteParams, 'EditAccount'>>();
  const { accountId } = route.params;

  const [account, setAccount] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [families, setFamilies] = useState([]);

  // Fetch account and families
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Fetch account
        const accountResponse = await apiService.accounts.getAccount(accountId);

        if (accountResponse.status === 200 && accountResponse.data) {
          setAccount(accountResponse.data);
        } else {
          console.error('Failed to fetch account:', accountResponse.error);
          Alert.alert(
            t('common.error'),
            accountResponse.error || t('accounts.fetchAccountError')
          );
          navigation.goBack();
        }

        // Fetch families
        const familiesResponse = await apiService.family.getFamilies();

        if (familiesResponse.status === 200 && familiesResponse.data) {
          setFamilies(familiesResponse.data);
        } else {
          console.error('Failed to fetch families:', familiesResponse.error);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        Alert.alert(
          t('common.error'),
          t('accounts.fetchAccountError')
        );
        navigation.goBack();
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [accountId, navigation, t]);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    // Show confirmation dialog before updating
    Alert.alert(
      t('accounts.updateAccount'),
      t('accounts.updateAccountConfirm', { name: account.name }),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.update'),
          onPress: async () => {
            try {
              setIsSubmitting(true);

              // Create account data with all required fields for the API
              const accountData = {
                id: accountId,
                name: values.name,
                accountType: values.accountType,
                initialBalance: values.initialBalance,
                balance: account?.balance || values.initialBalance, // Preserve current balance
                currency: values.currency,
                isActive: values.isActive,
                excludeFromStats: values.excludeFromStats,
                isSynced: true,
                lastUpdatedAt: new Date(),
                familyId: values.familyId,
                createdAt: account?.createdAt || new Date().toISOString(),
              };

              const response = await apiService.accounts.updateAccount(accountId, accountData);

              if (response.status === 200 && response.data) {
                // Show success message
                Alert.alert(
                  t('common.success'),
                  t('accounts.updateAccountSuccess'),
                  [
                    {
                      text: t('common.ok'),
                      onPress: () => navigation.goBack()
                    }
                  ]
                );
              } else {
                Alert.alert(
                  t('common.error'),
                  response.error || t('accounts.updateAccountError')
                );
              }
            } catch (error) {
              console.error('Error updating account:', error);
              Alert.alert(
                t('common.error'),
                t('accounts.updateAccountError')
              );
            } finally {
              setIsSubmitting(false);
            }
          }
        }
      ]
    );
  };

  if (isLoading || !account) {
    return (
      <View
        style={[
          styles.loadingContainer,
          { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
        ]}
      >
        <ActivityIndicator
          size="large"
          color={isDarkMode ? '#58A6FF' : '#0366D6'}
        />
      </View>
    );
  }

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
      ]}
    >
      <AccountForm
        initialValues={{
          id: account.id,
          name: account.name,
          accountType: account.accountType,
          initialBalance: account.initialBalance,
          currency: account.currency,
          isActive: account.isActive,
          excludeFromStats: account.excludeFromStats,
          familyId: account.familyId,
        }}
        onSubmit={handleSubmit}
        isLoading={isSubmitting}
        mode="edit"
        families={families}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default EditAccountScreen;
