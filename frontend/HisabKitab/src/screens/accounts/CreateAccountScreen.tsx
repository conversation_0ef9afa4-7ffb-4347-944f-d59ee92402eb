import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import apiService from '../../services/api';
import AccountForm from '../../components/accounts/AccountForm';

const CreateAccountScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation();

  const [isLoading, setIsLoading] = useState(false);
  const formRef = useRef<any>(null);
  const [families, setFamilies] = useState([]);

  // Fetch families
  useEffect(() => {
    const fetchFamilies = async () => {
      try {
        const response = await apiService.family.getFamilies();

        if (response.status === 200 && response.data) {
          setFamilies(response.data);
        } else {
          console.error('Failed to fetch families:', response.error);
        }
      } catch (error) {
        console.error('Error fetching families:', error);
      }
    };

    fetchFamilies();
  }, []);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    // Show confirmation dialog before creating
    Alert.alert(
      t('accounts.createAccount'),
      t('accounts.createAccountConfirm', { name: values.name }),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.create'),
          onPress: async () => {
            try {
              setIsLoading(true);

              // Create account data with required fields for the API
              // The API will assign an ID when creating the account
              const accountData = {
                id: 0, // Temporary ID, will be replaced by the server
                name: values.name,
                accountType: values.accountType,
                initialBalance: values.initialBalance,
                balance: values.initialBalance, // Set initial balance as current balance
                currency: values.currency,
                familyId: values.familyId,
                excludeFromStats: values.excludeFromStats,
                isActive: true,
                isSynced: true,
                lastUpdatedAt: new Date(),
                createdAt: new Date().toISOString(),
              };

              const response = await apiService.accounts.createAccount(accountData);

              if (response.status === 201 && response.data) {
                // Show success message
                Alert.alert(
                  t('common.success'),
                  t('accounts.createAccountSuccess'),
                  [
                    {
                      text: t('common.ok'),
                      onPress: () => {
                        // Reset form
                        formRef.current?.resetForm();
                        // Navigate back
                        navigation.goBack();
                      }
                    }
                  ]
                );
              } else {
                Alert.alert(
                  t('common.error'),
                  response.error || t('accounts.createAccountError')
                );
              }
            } catch (error) {
              console.error('Error creating account:', error);
              Alert.alert(
                t('common.error'),
                t('accounts.createAccountError')
              );
            } finally {
              setIsLoading(false);
            }
          }
        }
      ]
    );
  };

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
      ]}
    >
      <AccountForm
        ref={formRef}
        onSubmit={handleSubmit}
        isLoading={isLoading}
        mode="create"
        families={families}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default CreateAccountScreen;
