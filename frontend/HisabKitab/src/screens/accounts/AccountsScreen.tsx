import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import apiService from '../../services/api';
import AccountCard from '../../components/accounts/AccountCard';
import { handleApiError, showSuccessMessage } from '../../utils/errorHandling';
import { Account } from '../../models/Account';

const AccountsScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();

  const [accounts, setAccounts] = useState<Account[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [totalBalance, setTotalBalance] = useState(0);



  // Refresh accounts when screen is focused
  useFocusEffect(
    useCallback(() => {
      let isMounted = true;

      const loadAccounts = async () => {
        try {
          setIsLoading(true);
          const response = await apiService.accounts.getAccounts();

          if (!isMounted) return;

          if (response.status === 200 && response.data) {
            setAccounts(response.data);

            // Calculate total balance
            const total = response.data.reduce((sum: number, account: Account) => {
              if (account.isActive && !account.excludeFromStats) {
                return sum + account.balance;
              }
              return sum;
            }, 0);

            setTotalBalance(total);
          } else {
            if (!isMounted) return;
            handleApiError(response, t, 'accounts.fetchAccountsError');
          }
        } catch (error) {
          if (!isMounted) return;
          handleApiError(error, t, 'accounts.fetchAccountsError');
        } finally {
          if (isMounted) {
            setIsLoading(false);
            setIsRefreshing(false);
          }
        }
      };

      loadAccounts();

      // Cleanup function to prevent state updates after unmount
      return () => {
        isMounted = false;
      };
    }, [t])
  );

  // Handle refresh
  const handleRefresh = () => {
    setIsRefreshing(true);

    // Use the same logic as in useFocusEffect but without the isMounted check
    const loadAccounts = async () => {
      try {
        const response = await apiService.accounts.getAccounts();

        if (response.status === 200 && response.data) {
          setAccounts(response.data);

          // Calculate total balance
          const total = response.data.reduce((sum: number, account: Account) => {
            if (account.isActive && !account.excludeFromStats) {
              return sum + account.balance;
            }
            return sum;
          }, 0);

          setTotalBalance(total);
        } else {
          handleApiError(response, t, 'accounts.fetchAccountsError');
        }
      } catch (error) {
        handleApiError(error, t, 'accounts.fetchAccountsError');
      } finally {
        setIsLoading(false);
        setIsRefreshing(false);
      }
    };

    loadAccounts();
  };

  // Handle account press
  const handleAccountPress = (accountId: number) => {
    navigation.navigate('AccountDetails', { accountId });
  };

  // Handle edit account
  const handleEditAccount = (accountId: number) => {
    navigation.navigate('EditAccount', { accountId });
  };

  // Handle delete account
  const handleDeleteAccount = (accountId: number, accountName: string) => {
    Alert.alert(
      t('accounts.deleteAccount'),
      t('accounts.deleteAccountConfirm', { name: accountName }),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);
              const response = await apiService.accounts.deleteAccount(accountId);

              if (response.status === 204) {
                // Remove account from state
                setAccounts(accounts.filter((account: Account) => account.id !== accountId));

                // Recalculate total balance
                const updatedAccounts = accounts.filter((account: Account) => account.id !== accountId);
                const total = updatedAccounts.reduce((sum: number, account: Account) => {
                  if (account.isActive && !account.excludeFromStats) {
                    return sum + account.balance;
                  }
                  return sum;
                }, 0);

                setTotalBalance(total);

                // Show success message
                showSuccessMessage(t, 'accounts.deleteAccountSuccess');
              } else {
                handleApiError(response, t, 'accounts.deleteAccountError');
              }
            } catch (error) {
              handleApiError(error, t, 'accounts.deleteAccountError');
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  // Render account item
  const renderAccountItem = ({ item }: { item: Account }) => (
    <AccountCard
      id={item.id}
      name={item.name}
      accountType={item.accountType}
      balance={item.balance}
      currency={item.currency}
      isActive={item.isActive}
      isShared={item.sharedWith && item.sharedWith.length > 0}
      onPress={() => handleAccountPress(item.id)}
      onEdit={() => handleEditAccount(item.id)}
      onDelete={() => handleDeleteAccount(item.id, item.name)}
    />
  );

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons
        name="wallet-outline"
        size={64}
        color={isDarkMode ? '#30363D' : '#D0D7DE'}
      />
      <Text
        style={[
          styles.emptyText,
          { color: isDarkMode ? '#8B949E' : '#6E7781' },
        ]}
      >
        {t('accounts.noAccounts')}
      </Text>
      <TouchableOpacity
        style={[
          styles.addButton,
          { backgroundColor: isDarkMode ? '#238636' : '#2EA043' },
        ]}
        onPress={() => navigation.navigate('CreateAccount')}
      >
        <Ionicons name="add" size={20} color="#FFFFFF" />
        <Text style={styles.addButtonText}>{t('accounts.addAccount')}</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
      ]}
    >
      {/* Balance summary */}
      <View
        style={[
          styles.balanceSummary,
          { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
        ]}
      >
        <Text
          style={[
            styles.balanceLabel,
            { color: isDarkMode ? '#8B949E' : '#6E7781' },
          ]}
        >
          {t('accounts.totalBalance')}
        </Text>
        <Text
          style={[
            styles.balanceAmount,
            { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            totalBalance < 0 && styles.negativeBalance,
          ]}
        >
          NPR {totalBalance.toFixed(2)}
        </Text>
      </View>

      {/* Accounts list */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={isDarkMode ? '#58A6FF' : '#0366D6'}
          />
        </View>
      ) : (
        <FlatList
          data={accounts}
          renderItem={renderAccountItem}
          keyExtractor={(item: Account) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={['#0366D6']}
              tintColor={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
          }
        />
      )}

      {/* Add account button */}
      {accounts.length > 0 && (
        <TouchableOpacity
          style={[
            styles.floatingButton,
            { backgroundColor: isDarkMode ? '#238636' : '#2EA043' },
          ]}
          onPress={() => navigation.navigate('CreateAccount')}
        >
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  balanceSummary: {
    padding: 16,
    marginBottom: 8,
    alignItems: 'center',
  },
  balanceLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  balanceAmount: {
    fontSize: 24,
    fontWeight: '700',
  },
  negativeBalance: {
    color: '#F85149',
  },
  listContent: {
    padding: 16,
    paddingBottom: 80,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 6,
  },
  addButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    marginLeft: 8,
  },
  floatingButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});

export default AccountsScreen;
