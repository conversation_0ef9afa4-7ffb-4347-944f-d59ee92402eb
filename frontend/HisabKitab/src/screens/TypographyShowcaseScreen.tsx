import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Switch, TouchableOpacity } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import Typography from '../components/ui/Typography';
import NumberAnimation from '../components/ui/NumberAnimation';
import Card from '../components/ui/Card';
import { Ionicons } from '@expo/vector-icons';

const TypographyShowcaseScreen: React.FC = () => {
  const { colors, isDarkMode } = useTheme();
  const { t, i18n } = useTranslation();
  const [isNepali, setIsNepali] = useState(false);
  const [animatedValue, setAnimatedValue] = useState(1000);

  // Toggle language between English and Nepali
  const toggleLanguage = () => {
    const newLanguage = i18n.language === 'en' ? 'ne' : 'en';
    i18n.changeLanguage(newLanguage);
    setIsNepali(newLanguage === 'ne');
  };

  // Increase the animated value
  const increaseValue = () => {
    setAnimatedValue(prev => prev + 500);
  };

  // Decrease the animated value
  const decreaseValue = () => {
    setAnimatedValue(prev => Math.max(0, prev - 500));
  };

  return (
    <ScrollView
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? colors.background.dark : colors.background.light }
      ]}
      contentContainerStyle={styles.contentContainer}
    >
      {/* Header */}
      <View style={styles.header}>
        <Typography variant="h1" weight="bold">
          {t('typography.showcase')}
        </Typography>
        <View style={styles.languageToggle}>
          <Typography variant="bodySmall">EN</Typography>
          <Switch
            value={isNepali}
            onValueChange={toggleLanguage}
            trackColor={{ false: '#767577', true: colors.primary }}
            thumbColor={isNepali ? colors.accent.start : '#f4f3f4'}
          />
          <Typography variant="bodySmall">नेपाली</Typography>
        </View>
      </View>

      {/* Typography Variants */}
      <Card style={styles.section}>
        <Typography variant="h3" weight="semibold" style={styles.sectionTitle}>
          {t('typography.variants')}
        </Typography>

        <View style={styles.typographyRow}>
          <Typography variant="hero" weight="bold">
            {t('typography.hero')}
          </Typography>
        </View>

        <View style={styles.typographyRow}>
          <Typography variant="h1" weight="bold">
            {t('typography.h1')}
          </Typography>
        </View>

        <View style={styles.typographyRow}>
          <Typography variant="h2" weight="semibold">
            {t('typography.h2')}
          </Typography>
        </View>

        <View style={styles.typographyRow}>
          <Typography variant="h3" weight="semibold">
            {t('typography.h3')}
          </Typography>
        </View>

        <View style={styles.typographyRow}>
          <Typography variant="bodyLarge">
            {t('typography.bodyLarge')}
          </Typography>
        </View>

        <View style={styles.typographyRow}>
          <Typography variant="bodySmall">
            {t('typography.bodySmall')}
          </Typography>
        </View>

        <View style={styles.typographyRow}>
          <Typography variant="caption">
            {t('typography.caption')}
          </Typography>
        </View>
      </Card>

      {/* Font Weights */}
      <Card style={styles.section}>
        <Typography variant="h3" weight="semibold" style={styles.sectionTitle}>
          {t('typography.weights')}
        </Typography>

        <View style={styles.typographyRow}>
          <Typography variant="bodyLarge" weight="regular">
            {t('typography.regular')}
          </Typography>
        </View>

        <View style={styles.typographyRow}>
          <Typography variant="bodyLarge" weight="medium">
            {t('typography.medium')}
          </Typography>
        </View>

        <View style={styles.typographyRow}>
          <Typography variant="bodyLarge" weight="semibold">
            {t('typography.semibold')}
          </Typography>
        </View>

        <View style={styles.typographyRow}>
          <Typography variant="bodyLarge" weight="bold">
            {t('typography.bold')}
          </Typography>
        </View>
      </Card>

      {/* Number Animation */}
      <Card style={styles.section}>
        <Typography variant="h3" weight="semibold" style={styles.sectionTitle}>
          {t('typography.numberAnimation')}
        </Typography>

        <View style={styles.animationContainer}>
          <NumberAnimation
            value={animatedValue}
            prefix="₹ "
            duration={500}
            variant="number"
            weight="bold"
            color={animatedValue > 1000 ? colors.semantic.income.light : colors.semantic.expense.light}
          />

          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[styles.button, { backgroundColor: colors.semantic.expense.light }]}
              onPress={decreaseValue}
            >
              <Ionicons name="remove" size={24} color="#FFFFFF" />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, { backgroundColor: colors.semantic.income.light }]}
              onPress={increaseValue}
            >
              <Ionicons name="add" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </View>
      </Card>

      {/* Text Styles */}
      <Card style={styles.section}>
        <Typography variant="h3" weight="semibold" style={styles.sectionTitle}>
          {t('typography.textStyles')}
        </Typography>

        <View style={styles.typographyRow}>
          <Typography variant="bodyLarge" uppercase>
            {t('typography.uppercase')}
          </Typography>
        </View>

        <View style={styles.typographyRow}>
          <Typography variant="bodyLarge" color={colors.primary}>
            {t('typography.coloredText')}
          </Typography>
        </View>

        <View style={styles.typographyRow}>
          <Typography variant="bodyLarge" align="center">
            {t('typography.centeredText')}
          </Typography>
        </View>

        <View style={styles.typographyRow}>
          <Typography variant="bodyLarge" style={{ textDecorationLine: 'underline' }}>
            {t('typography.underlinedText')}
          </Typography>
        </View>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  header: {
    marginBottom: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  languageToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  section: {
    marginBottom: 24,
    padding: 16,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  typographyRow: {
    marginBottom: 12,
    paddingVertical: 4,
  },
  animationContainer: {
    alignItems: 'center',
    marginVertical: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    marginTop: 16,
    gap: 16,
  },
  button: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default TypographyShowcaseScreen;
