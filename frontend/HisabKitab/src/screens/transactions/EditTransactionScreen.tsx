import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { useNavigation, useRoute } from '@react-navigation/native';
import TransactionForm from '../../components/transactions/TransactionForm';
import apiService from '../../services/api';
import dbService from '../../services/db';
import { useAccounts } from '../../contexts/AccountContext';
import { useCategories } from '../../contexts/CategoryContext';
import { Transaction } from '../../models/Transaction';
import { convertTransaction } from '../../utils/typeConversion';

const EditTransactionScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const { transactionId } = route.params as { transactionId: number };
  const { accounts } = useAccounts();
  const { categories, priorities } = useCategories();

  // State
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load transaction
  useEffect(() => {
    const loadTransaction = async () => {
      setIsLoading(true);

      try {
        // Try to get from API first
        const response = await apiService.transactions.getTransaction(transactionId);

        if (response.status === 200) {
          // Convert transaction to ensure proper type handling
          const convertedTransaction = convertTransaction(response.data);
          setTransaction(convertedTransaction);
        } else {
          // Fallback to local database
          const localTransaction = await dbService.getTransaction(transactionId);
          if (localTransaction) {
            // Convert transaction to ensure proper type handling
            const convertedTransaction = convertTransaction(localTransaction as unknown as Transaction);
            setTransaction(convertedTransaction);
          } else {
            throw new Error('Transaction not found');
          }
        }
      } catch (error) {
        console.error('Error loading transaction:', error);

        // Fallback to local database
        const localTransaction = await dbService.getTransaction(transactionId);
        if (localTransaction) {
          // Convert transaction to ensure proper type handling
          const convertedTransaction = convertTransaction(localTransaction as unknown as Transaction);
          setTransaction(convertedTransaction);
        } else {
          Alert.alert(
            t('common.error'),
            t('transactions.transactionNotFound'),
            [
              {
                text: t('common.ok'),
                onPress: () => navigation.goBack(),
              },
            ]
          );
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadTransaction();
  }, [transactionId, navigation, t]);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    try {
      setIsSubmitting(true);

      // Check if online
      const netInfo = await fetch('https://www.google.com', { method: 'HEAD' })
        .then(() => true)
        .catch(() => false);

      if (netInfo) {
        // Online - update via API
        const response = await apiService.transactions.updateTransaction(transactionId, values);

        if (response.status === 200) {
          // Save to local database
          const localTransaction = {
            ...(response.data || {}),
            date: new Date(response.data?.date || new Date()),
            lastUpdatedAt: new Date(),
            isSynced: true,
            syncStatus: 'Synced',
          };
          await dbService.saveTransaction(localTransaction as any);

          // Navigate back
          navigation.goBack();
        } else {
          throw new Error(response.error || 'Failed to update transaction');
        }
      } else {
        // Offline - save to local database
        const localTransaction = await dbService.getTransaction(transactionId);
        if (localTransaction) {
          const updatedTransaction = {
            ...localTransaction,
            ...values,
            date: new Date(values.date),
            lastUpdatedAt: new Date(),
            isSynced: false,
            syncStatus: 'Pending',
          };
          await dbService.saveTransaction(updatedTransaction as any);

          // Navigate back
          navigation.goBack();
        } else {
          throw new Error('Transaction not found');
        }
      }
    } catch (error) {
      console.error('Error updating transaction:', error);

      // Save to local database
      const localTransaction = await dbService.getTransaction(transactionId);
      if (localTransaction) {
        const updatedTransaction = {
          ...localTransaction,
          ...values,
          date: new Date(values.date),
          lastUpdatedAt: new Date(),
          isSynced: false,
          syncStatus: 'Pending',
        };
        await dbService.saveTransaction(updatedTransaction as any);

        Alert.alert(
          t('common.notice'),
          t('transactions.savedOffline'),
          [
            {
              text: t('common.ok'),
              onPress: () => navigation.goBack(),
            },
          ]
        );
      } else {
        Alert.alert(
          t('common.error'),
          t('transactions.updateTransactionError')
        );
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
      </View>
    );
  }

  if (!transaction) {
    return (
      <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
      </View>
    );
  }

  // Format transaction for form
  const formattedTransaction = {
    id: transaction.id,
    amount: transaction.amount,
    description: transaction.description || '',
    date: new Date(transaction.date),
    type: transaction.type,
    accountId: transaction.accountId,
    toAccountId: transaction.toAccountId,
    categoryId: transaction.categoryId,
    priorityId: transaction.priorityId,
    status: transaction.status,
    receiptImage: transaction.receiptImage,
    tags: transaction.tags,
    location: transaction.location,
    items: transaction.items || [],
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      <TransactionForm
        initialValues={formattedTransaction}
        onSubmit={handleSubmit}
        isLoading={isSubmitting}
        mode="edit"
        accounts={accounts.filter(a => a.id !== undefined).map(a => ({ id: a.id as number, name: a.name }))}
        categories={categories.filter(c => c.id !== undefined).map(c => ({ id: c.id as number, name: c.name, type: c.type }))}
        priorities={priorities.map(p => ({ id: p.id, name: p.name }))}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default EditTransactionScreen;
