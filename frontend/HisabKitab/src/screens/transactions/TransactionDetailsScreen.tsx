import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import apiService from '../../services/api';
import dbService from '../../services/db';
import {
  Transaction,
  TransactionType,
  getTransactionTypeIcon,
  getTransactionTypeColor,
  getTransactionStatusColor,
} from '../../models/Transaction';
import { formatDate, formatCurrency } from '../../utils/formatters';
import { convertTransaction } from '../../utils/typeConversion';

const TransactionDetailsScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const { transactionId } = route.params as { transactionId: number };

  // State
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load transaction
  useEffect(() => {
    const loadTransaction = async () => {
      setIsLoading(true);

      try {
        // Try to get from API first
        const response = await apiService.transactions.getTransaction(transactionId);

        if (response.status === 200) {
          // Convert transaction to ensure proper type handling
          const convertedTransaction = convertTransaction(response.data);
          setTransaction(convertedTransaction);
        } else {
          // Fallback to local database
          const localTransaction = await dbService.getTransaction(transactionId);
          if (localTransaction) {
            // Convert transaction to ensure proper type handling
            const convertedTransaction = convertTransaction(localTransaction as unknown as Transaction);
            setTransaction(convertedTransaction);
          } else {
            throw new Error('Transaction not found');
          }
        }
      } catch (error) {
        console.error('Error loading transaction:', error);

        // Fallback to local database
        const localTransaction = await dbService.getTransaction(transactionId);
        if (localTransaction) {
          // Convert transaction to ensure proper type handling
          const convertedTransaction = convertTransaction(localTransaction as unknown as Transaction);
          setTransaction(convertedTransaction);
        } else {
          Alert.alert(
            t('common.error'),
            t('transactions.transactionNotFound'),
            [
              {
                text: t('common.ok'),
                onPress: () => navigation.goBack(),
              },
            ]
          );
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadTransaction();
  }, [transactionId, navigation, t]);

  // Handle edit transaction
  const handleEditTransaction = () => {
    navigation.navigate('EditTransaction', { transactionId });
  };

  // Handle delete transaction
  const handleDeleteTransaction = () => {
    Alert.alert(
      t('transactions.deleteTransaction'),
      t('transactions.deleteTransactionConfirmation'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);

              // Delete from API
              const response = await apiService.transactions.deleteTransaction(transactionId);

              if (response.status === 200) {
                // Delete from local database
                await dbService.deleteTransaction(transactionId);

                // Navigate back
                navigation.goBack();
              } else {
                throw new Error(response.error || 'Failed to delete transaction');
              }
            } catch (error) {
              console.error('Error deleting transaction:', error);

              // Mark for deletion when online
              const localTransaction = await dbService.getTransaction(transactionId);
              if (localTransaction) {
                localTransaction.isSynced = false;
                localTransaction.syncStatus = 'Pending';
                await dbService.saveTransaction(localTransaction);
                await dbService.deleteTransaction(transactionId);

                // Navigate back
                navigation.goBack();
              }

              Alert.alert(
                t('common.error'),
                t('transactions.deleteTransactionError')
              );
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
      </View>
    );
  }

  if (!transaction) {
    return (
      <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <Text style={{ color: isDarkMode ? '#FFFFFF' : '#24292E' }}>
          {t('transactions.transactionNotFound')}
        </Text>
      </View>
    );
  }

  const {
    amount,
    description,
    date,
    type,
    accountName,
    toAccountName,
    categoryName,
    priorityName,
    status,
    tags,
    location,
    items,
    createdAt,
    updatedAt,
  } = transaction;

  // Convert string icon name to a valid Ionicons name
  const typeIcon = getTransactionTypeIcon(type) as any;
  const typeColor = getTransactionTypeColor(type);
  const statusColor = getTransactionStatusColor(status);

  const formattedDate = formatDate(new Date(date));
  const formattedAmount = formatCurrency(amount);
  const formattedCreatedAt = createdAt ? formatDate(new Date(createdAt)) : '';
  const formattedUpdatedAt = updatedAt ? formatDate(new Date(updatedAt)) : '';

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <View
            style={[
              styles.iconBackground,
              { backgroundColor: typeColor },
            ]}
          >
            <Ionicons name={typeIcon} size={24} color="#FFFFFF" />
          </View>

          <Text
            style={[
              styles.amount,
              {
                color: type === TransactionType.Income
                  ? '#3FB950'
                  : type === TransactionType.Expense
                    ? '#F85149'
                    : isDarkMode ? '#FFFFFF' : '#24292E',
              },
            ]}
          >
            {type === TransactionType.Income ? '+' : type === TransactionType.Expense ? '-' : ''}
            {formattedAmount}
          </Text>
        </View>

        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('transactions.date')}
            </Text>
            <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {formattedDate}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('transactions.type')}
            </Text>
            <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {type === TransactionType.Income
                ? t('transactions.income')
                : type === TransactionType.Expense
                  ? t('transactions.expense')
                  : t('transactions.transfer')}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('transactions.account')}
            </Text>
            <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {accountName}
            </Text>
          </View>

          {type === TransactionType.Transfer && toAccountName && (
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('transactions.toAccount')}
              </Text>
              <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                {toAccountName}
              </Text>
            </View>
          )}

          {type !== TransactionType.Transfer && categoryName && (
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('transactions.category')}
              </Text>
              <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                {categoryName}
              </Text>
            </View>
          )}

          {priorityName && (
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('transactions.priority')}
              </Text>
              <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                {priorityName}
              </Text>
            </View>
          )}

          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('transactions.status')}
            </Text>
            <View
              style={[
                styles.statusBadge,
                { backgroundColor: statusColor + '20' }, // Add transparency
              ]}
            >
              <Text
                style={[
                  styles.statusText,
                  { color: statusColor },
                ]}
              >
                {status}
              </Text>
            </View>
          </View>

          {description && (
            <View style={styles.descriptionContainer}>
              <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('transactions.description')}
              </Text>
              <Text style={[styles.description, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                {description}
              </Text>
            </View>
          )}

          {tags && (
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('transactions.tags')}
              </Text>
              <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                {tags}
              </Text>
            </View>
          )}

          {location && (
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('transactions.location')}
              </Text>
              <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                {location}
              </Text>
            </View>
          )}

          {items && items.length > 0 && (
            <View style={styles.itemsContainer}>
              <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                {t('transactions.items')}
              </Text>

              {items.map((item, index) => (
                <View
                  key={item.id || index}
                  style={[
                    styles.itemCard,
                    {
                      backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                      borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                    },
                  ]}
                >
                  <View style={styles.itemHeader}>
                    <Text
                      style={[
                        styles.itemName,
                        { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                      ]}
                    >
                      {item.name}
                    </Text>
                    <Text
                      style={[
                        styles.itemAmount,
                        { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                      ]}
                    >
                      {formatCurrency(item.amount)}
                    </Text>
                  </View>

                  <View style={styles.itemDetails}>
                    <Text
                      style={[
                        styles.itemDetail,
                        { color: isDarkMode ? '#8B949E' : '#6E7781' },
                      ]}
                    >
                      {t('transactions.quantity')}: {item.quantity || 1}
                    </Text>

                    {item.categoryName && (
                      <Text
                        style={[
                          styles.itemDetail,
                          { color: isDarkMode ? '#8B949E' : '#6E7781' },
                        ]}
                      >
                        {t('transactions.category')}: {item.categoryName}
                      </Text>
                    )}

                    {item.notes && (
                      <Text
                        style={[
                          styles.itemDetail,
                          { color: isDarkMode ? '#8B949E' : '#6E7781' },
                        ]}
                      >
                        {t('transactions.notes')}: {item.notes}
                      </Text>
                    )}
                  </View>
                </View>
              ))}
            </View>
          )}

          <View style={styles.metadataContainer}>
            <Text style={[styles.metadataLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('common.createdAt')}: {formattedCreatedAt}
            </Text>
            {formattedUpdatedAt && (
              <Text style={[styles.metadataLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('common.updatedAt')}: {formattedUpdatedAt}
              </Text>
            )}
          </View>
        </View>
      </ScrollView>

      <View style={styles.actionsContainer}>
        <TouchableOpacity
          style={[
            styles.actionButton,
            {
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
            },
          ]}
          onPress={handleEditTransaction}
        >
          <Ionicons
            name="pencil-outline"
            size={20}
            color={isDarkMode ? '#58A6FF' : '#0366D6'}
          />
          <Text
            style={[
              styles.actionButtonText,
              { color: isDarkMode ? '#58A6FF' : '#0366D6' },
            ]}
          >
            {t('common.edit')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.actionButton,
            {
              backgroundColor: isDarkMode ? '#21262D' : '#F6F8FA',
              borderColor: isDarkMode ? '#F85149' : '#F85149',
            },
          ]}
          onPress={handleDeleteTransaction}
        >
          <Ionicons
            name="trash-outline"
            size={20}
            color="#F85149"
          />
          <Text
            style={[
              styles.actionButtonText,
              { color: '#F85149' },
            ]}
          >
            {t('common.delete')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  iconBackground: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  amount: {
    fontSize: 24,
    fontWeight: '600',
  },
  detailsContainer: {
    marginBottom: 24,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  descriptionContainer: {
    marginBottom: 12,
  },
  description: {
    fontSize: 14,
    marginTop: 4,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  itemsContainer: {
    marginTop: 24,
    marginBottom: 24,
  },
  itemCard: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '500',
  },
  itemAmount: {
    fontSize: 14,
    fontWeight: '600',
  },
  itemDetails: {
    marginTop: 4,
  },
  itemDetail: {
    fontSize: 12,
    marginBottom: 4,
  },
  metadataContainer: {
    marginTop: 24,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#30363D',
  },
  metadataLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#30363D',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 16,
    paddingVertical: 10,
    flex: 1,
    marginHorizontal: 8,
  },
  actionButtonText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
});

export default TransactionDetailsScreen;
