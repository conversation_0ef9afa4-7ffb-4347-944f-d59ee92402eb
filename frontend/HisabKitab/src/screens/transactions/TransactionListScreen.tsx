import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import TransactionCard from '../../components/transactions/TransactionCard';
import TransactionFilter from '../../components/transactions/TransactionFilter';
import TransactionStatisticsChart from '../../components/transactions/TransactionStatisticsChart';
import BatchOperationsBar from '../../components/transactions/BatchOperationsBar';
import CategorySelectionModal from '../../components/transactions/CategorySelectionModal';
import TagSelectionModal from '../../components/transactions/TagSelectionModal';
import EmptyState from '../../components/ui/EmptyState';
import LoadingIndicator from '../../components/ui/LoadingIndicator';
import apiService from '../../services/api';
import dbService from '../../services/db';
import syncService from '../../services/sync';
import { Transaction, TransactionFilterDto, TransactionType } from '../../models/Transaction';
import { convertTransactions } from '../../utils/typeConversion';
import { useAuth } from '../../contexts/AuthContext';
import { useAccounts } from '../../contexts/AccountContext';
import { useCategories } from '../../contexts/CategoryContext';

const TransactionListScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const navigation = useNavigation<any>();
  const { /* user */ } = useAuth(); // Keeping the auth context for future use
  const { accounts } = useAccounts();
  const { categories, priorities } = useCategories();

  // State
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [filter, setFilter] = useState<TransactionFilterDto>({});
  interface CategorySummary {
    categoryId: number;
    categoryName: string;
    amount: number;
    type: TransactionType;
    color?: string;
  }

  const [statistics, setStatistics] = useState({
    income: 0,
    expense: 0,
    balance: 0,
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    endDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
    categorySummaries: [] as CategorySummary[],
  });
  const [statsPeriod, setStatsPeriod] = useState('month');

  // Batch operations state
  const [selectedTransactions, setSelectedTransactions] = useState<number[]>([]);
  const [isBatchMode, setIsBatchMode] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showTagModal, setShowTagModal] = useState(false);
  const [recentTags, setRecentTags] = useState<string[]>([]);

  // Load transactions
  const loadTransactions = useCallback(async (showLoading = true) => {
    if (showLoading) {
      setIsLoading(true);
    }

    try {
      // Check if online
      const netInfo = await fetch('https://www.google.com', { method: 'HEAD' })
        .then(() => true)
        .catch(() => false);

      if (netInfo) {
        // Online - get from API
        const response = await apiService.transactions.getTransactions(filter);

        if (response.status === 200) {
          // Convert transaction types to ensure consistency
          const convertedTransactions = convertTransactions(response.data || []);
          setTransactions(convertedTransactions || []);

          // Save to local database
          for (const transaction of convertedTransactions) {
            const localTransaction = {
              ...transaction,
              date: new Date(transaction.date),
              lastUpdatedAt: new Date(),
              isSynced: true,
              syncStatus: 'Synced',
            };
            await dbService.saveTransaction(localTransaction as any);
          }

          // Get statistics based on period
          let startDate, endDate;
          const now = new Date();

          switch (statsPeriod) {
            case 'week':
              // Start of week (Sunday)
              startDate = new Date(now);
              startDate.setDate(now.getDate() - now.getDay());
              startDate.setHours(0, 0, 0, 0);
              // End of week (Saturday)
              endDate = new Date(now);
              endDate.setDate(now.getDate() + (6 - now.getDay()));
              endDate.setHours(23, 59, 59, 999);
              break;
            case 'year':
              startDate = new Date(now.getFullYear(), 0, 1);
              endDate = new Date(now.getFullYear(), 11, 31);
              break;
            case 'month':
            default:
              startDate = new Date(now.getFullYear(), now.getMonth(), 1);
              endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
              break;
          }

          const statsResponse = await apiService.transactions.getTransactionStatistics(
            startDate.toISOString(),
            endDate.toISOString()
          );

          // Get category summaries
          const categorySummaries: CategorySummary[] = [];

          // Get expense categories summary
          const expenseCategoriesResponse = await apiService.transactions.getCategorySummary(
            startDate.toISOString(),
            endDate.toISOString(),
            TransactionType.Expense
          );

          if (expenseCategoriesResponse.status === 200) {
            categorySummaries.push(...expenseCategoriesResponse.data.map((cat: any) => ({
              categoryId: cat.categoryId,
              categoryName: cat.categoryName,
              amount: cat.amount,
              type: TransactionType.Expense
            })));
          }

          // Get income categories summary
          const incomeCategoriesResponse = await apiService.transactions.getCategorySummary(
            startDate.toISOString(),
            endDate.toISOString(),
            TransactionType.Income
          );

          if (incomeCategoriesResponse.status === 200) {
            categorySummaries.push(...incomeCategoriesResponse.data.map((cat: any) => ({
              categoryId: cat.categoryId,
              categoryName: cat.categoryName,
              amount: cat.amount,
              type: TransactionType.Income
            })));
          }

          if (statsResponse.status === 200) {
            setStatistics({
              income: statsResponse.data.income,
              expense: statsResponse.data.expense,
              balance: statsResponse.data.balance,
              startDate,
              endDate,
              categorySummaries,
            });
          }
        } else {
          // Fallback to local database
          const localTransactions = await dbService.getTransactions(filter);
          // Convert DB transactions to model transactions with type assertion and ensure proper type conversion
          const modelTransactions = convertTransactions(localTransactions.map(t => ({
            ...t,
            id: t.id || 0,
            description: t.description || '',
            accountName: accounts.find(a => a.id === t.accountId)?.name || '',
            categoryName: categories.find(c => c.id === t.categoryId)?.name || '',
            username: '',     // This would be populated from the user context
          })));
          setTransactions(modelTransactions);
        }
      } else {
        // Offline - get from local database
        const localTransactions = await dbService.getTransactions(filter);
        // Convert DB transactions to model transactions with type assertion and ensure proper type conversion
        const modelTransactions = convertTransactions(localTransactions.map(t => ({
          ...t,
          id: t.id || 0,
          description: t.description || '',
          accountName: accounts.find(a => a.id === t.accountId)?.name || '',
          categoryName: categories.find(c => c.id === t.categoryId)?.name || '',
          username: '',
        })));
        setTransactions(modelTransactions);
      }
    } catch (error) {
      console.error('Error loading transactions:', error);

      // Fallback to local database
      const localTransactions = await dbService.getTransactions(filter);
      // Convert DB transactions to model transactions with type assertion and ensure proper type conversion
      const modelTransactions = convertTransactions(localTransactions.map(t => ({
        ...t,
        id: t.id || 0,
        description: t.description || '',
        accountName: accounts.find(a => a.id === t.accountId)?.name || '',
        categoryName: categories.find(c => c.id === t.categoryId)?.name || '',
        username: '',
      })));
      setTransactions(modelTransactions);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [filter]);

  // Load transactions on mount and when filter changes
  useEffect(() => {
    loadTransactions();
  }, [loadTransactions]);

  // Refresh when screen is focused
  useFocusEffect(
    useCallback(() => {
      loadTransactions(false);
    }, [loadTransactions])
  );

  // Handle refresh
  const handleRefresh = () => {
    setIsRefreshing(true);
    loadTransactions(false);
  };

  // Handle filter
  const handleApplyFilter = (newFilter: TransactionFilterDto) => {
    setFilter(newFilter);
  };

  // Handle reset filter
  const handleResetFilter = () => {
    setFilter({});
  };

  // Navigate to transaction details
  const handleViewTransaction = (transaction: Transaction) => {
    navigation.navigate('TransactionDetails', { transactionId: transaction.id });
  };

  // Navigate to edit transaction
  const handleEditTransaction = (transaction: Transaction) => {
    navigation.navigate('EditTransaction', { transactionId: transaction.id });
  };

  // Delete transaction
  const handleDeleteTransaction = (transaction: Transaction) => {
    Alert.alert(
      t('transactions.deleteTransaction'),
      t('transactions.deleteTransactionConfirmation'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);

              // Delete from API
              const response = await apiService.transactions.deleteTransaction(transaction.id);

              if (response.status === 200) {
                // Delete from local database
                await dbService.deleteTransaction(transaction.id);

                // Refresh transactions
                loadTransactions(false);
              } else {
                throw new Error(response.error || 'Failed to delete transaction');
              }
            } catch (error) {
              console.error('Error deleting transaction:', error);

              // Mark for deletion when online
              const localTransaction = await dbService.getTransaction(transaction.id);
              if (localTransaction) {
                localTransaction.isSynced = false;
                localTransaction.syncStatus = 'Pending';
                await dbService.saveTransaction(localTransaction);
                await dbService.deleteTransaction(transaction.id);

                // Refresh transactions
                loadTransactions(false);
              }

              Alert.alert(
                t('common.error'),
                t('transactions.deleteTransactionError')
              );
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  // Navigate to add transaction
  const handleAddTransaction = () => {
    navigation.navigate('AddTransaction');
  };

  // Handle sync for a specific transaction
  const handleSyncTransaction = async (transaction: Transaction) => {
    try {
      setIsLoading(true);

      // Add to sync queue if not already there
      if (!transaction.isSynced) {
        await syncService.syncData();

        // Refresh transactions
        await loadTransactions(false);
      }
    } catch (error) {
      console.error('Error syncing transaction:', error);
      Alert.alert(
        t('common.error'),
        t('sync.syncError')
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Toggle batch mode
  const toggleBatchMode = () => {
    setIsBatchMode(!isBatchMode);
    setSelectedTransactions([]);
  };

  // Toggle transaction selection
  const toggleTransactionSelection = (transaction: Transaction) => {
    if (!isBatchMode) {
      setIsBatchMode(true);
    }

    setSelectedTransactions(prev => {
      const id = transaction.id;
      if (prev.includes(id)) {
        return prev.filter(transactionId => transactionId !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  // Handle batch delete
  const handleBatchDelete = () => {
    if (selectedTransactions.length === 0) return;

    Alert.alert(
      t('transactions.deleteTransactions'),
      t('transactions.deleteTransactionsConfirmation', { count: selectedTransactions.length }),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);

              // Call batch delete API
              const response = await apiService.transactions.batchDeleteTransactions(selectedTransactions);

              if (response.status === 200) {
                // Delete from local database
                for (const id of selectedTransactions) {
                  await dbService.deleteTransaction(id);
                }

                // Reset batch mode
                setIsBatchMode(false);
                setSelectedTransactions([]);

                // Refresh transactions
                await loadTransactions(false);
              } else {
                throw new Error('Failed to delete transactions');
              }
            } catch (error) {
              console.error('Error deleting transactions:', error);
              Alert.alert(
                t('common.error'),
                t('transactions.deleteError')
              );
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  // Handle batch categorize
  const handleBatchCategorize = (categoryId: number) => {
    if (selectedTransactions.length === 0) return;

    Alert.alert(
      t('transactions.updateTransactions'),
      t('transactions.updateCategoryConfirmation', { count: selectedTransactions.length }),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.update'),
          onPress: async () => {
            try {
              setIsLoading(true);

              // Prepare batch update data
              const updateData = selectedTransactions.map(id => ({
                id,
                categoryId,
              }));

              // Call batch update API
              const response = await apiService.transactions.batchUpdateTransactions(updateData as any);

              if (response.status === 200) {
                // Update in local database
                for (const id of selectedTransactions) {
                  const transaction = await dbService.getTransaction(id);
                  if (transaction) {
                    await dbService.saveTransaction({
                      ...transaction,
                      categoryId,
                      lastUpdatedAt: new Date(),
                    } as any);
                  }
                }

                // Close modal
                setShowCategoryModal(false);

                // Reset batch mode
                setIsBatchMode(false);
                setSelectedTransactions([]);

                // Refresh transactions
                await loadTransactions(false);
              } else {
                throw new Error('Failed to update transactions');
              }
            } catch (error) {
              console.error('Error updating transactions:', error);
              Alert.alert(
                t('common.error'),
                t('transactions.updateError')
              );
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  // Handle batch tag
  const handleBatchTag = (tag: string) => {
    if (selectedTransactions.length === 0) return;

    Alert.alert(
      t('transactions.updateTransactions'),
      t('transactions.updateTagConfirmation', { count: selectedTransactions.length }),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.update'),
          onPress: async () => {
            try {
              setIsLoading(true);

              // Save to recent tags
              setRecentTags(prev => {
                if (!prev.includes(tag)) {
                  const newTags = [tag, ...prev];
                  // Keep only the 10 most recent tags
                  return newTags.slice(0, 10);
                }
                return prev;
              });

              // Prepare batch update data
              const updateData = selectedTransactions.map(id => ({
                id,
                tags: tag,
              }));

              // Call batch update API
              const response = await apiService.transactions.batchUpdateTransactions(updateData as any);

              if (response.status === 200) {
                // Update in local database
                for (const id of selectedTransactions) {
                  const transaction = await dbService.getTransaction(id);
                  if (transaction) {
                    await dbService.saveTransaction({
                      ...transaction,
                      tags: tag,
                      lastUpdatedAt: new Date(),
                    } as any);
                  }
                }

                // Close modal
                setShowTagModal(false);

                // Reset batch mode
                setIsBatchMode(false);
                setSelectedTransactions([]);

                // Refresh transactions
                await loadTransactions(false);
              } else {
                throw new Error('Failed to update transactions');
              }
            } catch (error) {
              console.error('Error updating transactions:', error);
              Alert.alert(
                t('common.error'),
                t('transactions.updateError')
              );
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  // Render transaction item
  const renderTransactionItem = ({ item }: { item: Transaction }) => (
    <TransactionCard
      transaction={item}
      onPress={isBatchMode ? () => toggleTransactionSelection(item) : () => handleViewTransaction(item)}
      onEdit={isBatchMode ? undefined : () => handleEditTransaction(item)}
      onDelete={isBatchMode ? undefined : () => handleDeleteTransaction(item)}
      onSync={isBatchMode ? undefined : () => handleSyncTransaction(item)}
      showSyncStatus={!isBatchMode}
      isSelected={selectedTransactions.includes(item.id)}
      selectionMode={isBatchMode}
      onLongPress={() => toggleTransactionSelection(item)}
    />
  );

  // Render empty state
  const renderEmptyState = () => (
    <EmptyState
      icon="wallet-outline"
      title={t('transactions.noTransactions')}
      message={t('transactions.noTransactionsMessage')}
      buttonTitle={t('transactions.addTransaction')}
      onButtonPress={handleAddTransaction}
    />
  );

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      <View style={styles.header}>
        <View style={styles.filterContainer}>
          <TransactionFilter
            onApplyFilter={handleApplyFilter}
            onResetFilter={handleResetFilter}
            initialFilter={filter}
            accounts={accounts.filter(a => a.id !== undefined).map(a => ({ id: a.id as number, name: a.name }))}
            categories={categories.filter(c => c.id !== undefined).map(c => ({ id: c.id as number, name: c.name, type: c.type }))}
            priorities={priorities.map(p => ({ id: p.id, name: p.name }))}
          />
        </View>

        <TouchableOpacity
          style={[
            styles.addButton,
            { backgroundColor: isDarkMode ? '#238636' : '#2EA043' },
          ]}
          onPress={handleAddTransaction}
        >
          <Ionicons name="add-outline" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <TransactionStatisticsChart
        income={statistics.income}
        expense={statistics.expense}
        balance={statistics.balance}
        startDate={statistics.startDate}
        endDate={statistics.endDate}
        categorySummaries={statistics.categorySummaries}
        onPeriodChange={(period) => {
          setStatsPeriod(period);
          loadTransactions(false);
        }}
      />

      {isLoading ? (
        <LoadingIndicator message={t('common.loading')} />
      ) : (
        <FlatList
          data={transactions}
          renderItem={renderTransactionItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={[isDarkMode ? '#58A6FF' : '#0366D6']}
              tintColor={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
          }
          ListEmptyComponent={renderEmptyState}
        />
      )}

      {/* Batch Operations Bar */}
      <BatchOperationsBar
        selectedCount={selectedTransactions.length}
        onDelete={handleBatchDelete}
        onCategorize={() => setShowCategoryModal(true)}
        onTag={() => setShowTagModal(true)}
        onCancel={toggleBatchMode}
        visible={isBatchMode && selectedTransactions.length > 0}
      />

      {/* Category Selection Modal */}
      <CategorySelectionModal
        visible={showCategoryModal}
        onClose={() => setShowCategoryModal(false)}
        onSelect={handleBatchCategorize}
        categories={categories.filter(c => c.id !== undefined).map(c => ({
          id: c.id as number,
          name: c.name,
          type: c.type
        }))}
      />

      {/* Tag Selection Modal */}
      <TagSelectionModal
        visible={showTagModal}
        onClose={() => setShowTagModal(false)}
        onSelect={handleBatchTag}
        recentTags={recentTags}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  filterContainer: {
    flex: 1,
    marginRight: 16,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },

  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  listContent: {
    padding: 16,
    flexGrow: 1,
  },
});

export default TransactionListScreen;
