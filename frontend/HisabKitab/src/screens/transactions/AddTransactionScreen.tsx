import React, { useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import TransactionForm from '../../components/transactions/TransactionForm';
import apiService from '../../services/api';
import dbService from '../../services/db';
import { useAccounts } from '../../contexts/AccountContext';
import { useCategories } from '../../contexts/CategoryContext';
import { useAuth } from '../../contexts/AuthContext';

const AddTransactionScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const navigation = useNavigation<any>();
  const { accounts } = useAccounts();
  const { categories, priorities } = useCategories();
  const { user } = useAuth();

  // State
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    try {
      setIsSubmitting(true);

      // Check if online
      const netInfo = await fetch('https://www.google.com', { method: 'HEAD' })
        .then(() => true)
        .catch(() => false);

      if (netInfo) {
        // Online - create via API
        const response = await apiService.transactions.createTransaction(values);

        if (response.status === 201) {
          // Save to local database
          const localTransaction = {
            ...(response.data || {}),
            date: new Date(response.data?.date || new Date()),
            lastUpdatedAt: new Date(),
            isSynced: true,
            syncStatus: 'Synced',
          };
          await dbService.saveTransaction(localTransaction as any);

          // Navigate back
          navigation.goBack();
        } else {
          throw new Error(response.error || 'Failed to create transaction');
        }
      } else {
        // Offline - save to local database
        const localTransaction = {
          ...values,
          date: new Date(values.date),
          lastUpdatedAt: new Date(),
          isSynced: false,
          syncStatus: 'Pending',
          userId: user?.id,
        };
        await dbService.saveTransaction(localTransaction as any);

        // Navigate back
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error creating transaction:', error);

      // Save to local database
      const localTransaction = {
        ...values,
        date: new Date(values.date),
        lastUpdatedAt: new Date(),
        isSynced: false,
        syncStatus: 'Pending',
        userId: user?.id,
      };
      await dbService.saveTransaction(localTransaction as any);

      Alert.alert(
        t('common.notice'),
        t('transactions.savedOffline'),
        [
          {
            text: t('common.ok'),
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      <TransactionForm
        onSubmit={handleSubmit}
        isLoading={isSubmitting}
        mode="create"
        accounts={accounts.filter(a => a.id !== undefined).map(a => ({ id: a.id as number, name: a.name }))}
        categories={categories.filter(c => c.id !== undefined).map(c => ({ id: c.id as number, name: c.name, type: c.type }))}
        priorities={priorities.map(p => ({ id: p.id, name: p.name }))}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default AddTransactionScreen;
