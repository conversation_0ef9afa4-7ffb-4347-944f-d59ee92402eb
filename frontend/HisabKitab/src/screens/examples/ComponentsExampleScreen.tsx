import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

// Import all the components we created
import NotificationChannelConfig from '../../components/notifications/NotificationChannelConfig';
import NotificationGroup from '../../components/notifications/NotificationGroup';
import FeatureToggle from '../../components/common/FeatureToggle';
import TranslationPreview from '../../components/settings/TranslationPreview';
import ReminderCategorySelector, { DEFAULT_REMINDER_CATEGORIES } from '../../components/reminders/ReminderCategorySelector';
import MonthlySummaryCharts from '../../components/analytics/MonthlySummaryCharts';
import NotificationPermissionPrompt from '../../components/notifications/NotificationPermissionPrompt';

// Sample data for demonstration
const sampleNotifications = [
  {
    id: 1,
    title: 'New Transaction',
    message: 'You have a new transaction of $50 from your Main Account',
    body: 'You have a new transaction of $50 from your Main Account',
    type: 'Transaction',
    isRead: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    referenceId: 123,
    referenceType: 'Transaction',
  },
  {
    id: 2,
    title: 'Budget Alert',
    message: 'You have reached 80% of your Food budget for this month',
    body: 'You have reached 80% of your Food budget for this month',
    type: 'Budget',
    isRead: true,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    referenceId: 456,
    referenceType: 'Budget',
  },
  {
    id: 3,
    title: 'Loan Payment Due',
    message: 'Your loan payment of $200 is due tomorrow',
    body: 'Your loan payment of $200 is due tomorrow',
    type: 'Loan',
    isRead: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
    referenceId: 789,
    referenceType: 'Loan',
  },
];

// Sample data for monthly summary charts
const sampleChartData = {
  totalIncome: 5000,
  totalExpense: 3500,
  currencyCode: 'USD',
  currencySymbol: '$',
  categoryBreakdown: [
    { name: 'Food', amount: 1200, color: '#F85149' },
    { name: 'Rent', amount: 1500, color: '#58A6FF' },
    { name: 'Transportation', amount: 400, color: '#3FB950' },
    { name: 'Entertainment', amount: 300, color: '#F0883E' },
    { name: 'Shopping', amount: 100, color: '#8957E5' },
  ],
  savingsTrend: [
    { label: 'Jan', value: 1000 },
    { label: 'Feb', value: 1200 },
    { label: 'Mar', value: 800 },
    { label: 'Apr', value: 1500 },
    { label: 'May', value: 1300 },
    { label: 'Jun', value: 1800 },
  ],
  budgetUsage: [
    { category: 'Food', spent: 1200, budget: 1500 },
    { category: 'Rent', spent: 1500, budget: 1500 },
    { category: 'Transport', spent: 400, budget: 600 },
    { category: 'Entertain', spent: 300, budget: 500 },
    { category: 'Shopping', spent: 100, budget: 300 },
  ],
  accountBalances: [
    { name: 'Main', balance: 3000 },
    { name: 'Savings', balance: 10000 },
    { name: 'Credit', balance: -500 },
  ],
};

const ComponentsExampleScreen: React.FC = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  // State for components
  const [selectedCategory, setSelectedCategory] = useState(DEFAULT_REMINDER_CATEGORIES[0]);
  const [showPermissionPrompt, setShowPermissionPrompt] = useState(false);

  // Handle notification press
  const handleNotificationPress = (notification: any) => {
    Alert.alert(
      notification.title,
      notification.body,
      [{ text: 'OK' }]
    );
  };

  // Handle notification delete
  const handleNotificationDelete = async (id: number): Promise<void> => {
    console.log('Deleting notification:', id);
    Alert.alert(
      t('notifications.deleteConfirmTitle'),
      t('notifications.deleteConfirmMessage'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: () => {
            Alert.alert(t('common.success'), t('notifications.deleteSuccess'));
          },
        },
      ]
    );
  };

  // Handle translation edit
  const handleTranslationEdit = async (key: string, value: string, language: string) => {
    // In a real app, this would update the translation in the backend
    Alert.alert(
      t('translations.updated'),
      `Updated "${key}" for ${language} to "${value}"`,
      [{ text: 'OK' }]
    );

    return true;
  };

  // Render section header
  const renderSectionHeader = (title: string) => (
    <View style={styles.sectionHeader}>
      <Text
        style={[
          styles.sectionTitle,
          { color: isDarkMode ? '#FFFFFF' : '#000000' },
        ]}
      >
        {title}
      </Text>
      <View style={[styles.divider, { backgroundColor: isDarkMode ? '#30363D' : '#D0D7DE' }]} />
    </View>
  );

  return (
    <ScrollView
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
      ]}
      contentContainerStyle={styles.contentContainer}
    >
      <Text
        style={[
          styles.screenTitle,
          { color: isDarkMode ? '#FFFFFF' : '#000000' },
        ]}
      >
        {t('examples.componentsShowcase')}
      </Text>

      {/* Notification Channel Configuration */}
      {renderSectionHeader(t('examples.notificationChannels'))}
      <NotificationChannelConfig />

      {/* Notification Group */}
      {renderSectionHeader(t('examples.notificationGroups'))}
      <NotificationGroup
        title={t('notifications.transactionNotifications')}
        notifications={sampleNotifications}
        onPressNotification={handleNotificationPress}
        onDeleteNotification={async (id) => {
          console.log('Delete notification:', id);
          await handleNotificationDelete(id);
          return Promise.resolve();
        }}
        isDarkMode={isDarkMode}
        icon="swap-horizontal-outline"
      />

      {/* Feature Toggle */}
      {renderSectionHeader(t('examples.featureToggles'))}
      <FeatureToggle
        featureName="PushNotifications"
        title={t('features.pushNotifications')}
        description={t('features.pushNotificationsDescription')}
        icon="notifications-outline"
        isDarkMode={isDarkMode}
        onToggle={(enabled) => {
          Alert.alert(
            enabled ? t('features.enabled') : t('features.disabled'),
            enabled
              ? t('features.enabledSuccess', { name: 'Push Notifications' })
              : t('features.disabledSuccess', { name: 'Push Notifications' })
          );
        }}
      />

      <FeatureToggle
        featureName="OfflineMode"
        title={t('features.offlineMode')}
        description={t('features.offlineModeDescription')}
        icon="cloud-offline-outline"
        isDarkMode={isDarkMode}
        showAdminControls
      />

      {/* Translation Preview */}
      {renderSectionHeader(t('examples.translationPreview'))}
      <TranslationPreview
        translationKey="common.save"
        defaultText="Save"
        onEdit={handleTranslationEdit}
        showEditControls
      />

      {/* Reminder Category Selector */}
      {renderSectionHeader(t('examples.reminderCategories'))}
      <ReminderCategorySelector
        selectedCategory={selectedCategory}
        onSelectCategory={setSelectedCategory}
        allowCustom
      />

      {/* Monthly Summary Charts */}
      {renderSectionHeader(t('examples.monthlySummaryCharts'))}
      <MonthlySummaryCharts
        month={new Date()}
        data={sampleChartData}
        chartTypes={['income-expense', 'category-breakdown']}
      />

      {/* Notification Permission Prompt */}
      {renderSectionHeader(t('examples.notificationPermission'))}
      <TouchableOpacity
        style={[
          styles.permissionButton,
          { backgroundColor: isDarkMode ? '#1F6FEB' : '#0366D6' },
        ]}
        onPress={() => setShowPermissionPrompt(true)}
      >
        <Ionicons
          name="notifications-outline"
          size={20}
          color="#FFFFFF"
          style={styles.permissionButtonIcon}
        />
        <Text style={styles.permissionButtonText}>
          {t('notifications.showPermissionPrompt')}
        </Text>
      </TouchableOpacity>

      <NotificationPermissionPrompt
        visible={showPermissionPrompt}
        onClose={() => setShowPermissionPrompt(false)}
        onPermissionGranted={() => {
          Alert.alert(
            t('notifications.permissionsGranted'),
            t('notifications.permissionsGrantedMessage')
          );
        }}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  screenTitle: {
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 24,
    textAlign: 'center',
  },
  sectionHeader: {
    marginTop: 24,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  divider: {
    height: 1,
    width: '100%',
  },
  permissionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
  },
  permissionButtonIcon: {
    marginRight: 8,
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default ComponentsExampleScreen;
