import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  RefreshControl,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import apiService from '../../services/api';
import dbService from '../../services/db';
import { ReceiptHistoryItem, ReceiptFilterOptions, getReceiptStatusColor, getReceiptStatusText } from '../../models/Receipt';
import { formatDate, formatCurrency } from '../../utils/formatters';
import EmptyState from '../../components/ui/EmptyState';
import LoadingIndicator from '../../components/ui/LoadingIndicator';
import ErrorMessage from '../../components/ui/ErrorMessage';
import ReceiptFilterModal from '../../components/receipts/ReceiptFilterModal';
import NetInfo from '@react-native-community/netinfo';
import { Receipt } from '../../services/db';

const ReceiptHistoryScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const navigation = useNavigation<any>();

  // State
  const [receipts, setReceipts] = useState<ReceiptHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [filter, setFilter] = useState<ReceiptFilterOptions>({});

  // Load receipts
  const loadReceipts = async (showRefreshing = true) => {
    try {
      if (showRefreshing) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      // Check network status
      const netInfo = await NetInfo.fetch();

      if (netInfo.isConnected) {
        // Online - get from API
        const response = await apiService.receipts.getReceiptHistory(filter);

        if (response.status === 200 && response.data) {
          setReceipts(response.data);

          // Save to local database for offline access
          // Convert ReceiptHistoryItem to Receipt for database storage
          const receiptsToSave = response.data.map((item: ReceiptHistoryItem): Receipt => ({
            id: item.id,
            transactionId: item.transactionId,
            merchantName: item.merchantName || '',
            date: item.date,
            totalAmount: item.totalAmount,
            items: [], // Required by Receipt interface
            rawText: '', // Required by Receipt interface
            imageUri: item.imageUri,
            isProcessed: item.isProcessed,
            createdAt: item.createdAt,
            updatedAt: new Date().toISOString(),
            isDeleted: false
          }));

          await dbService.batchSaveReceipts(receiptsToSave);
        } else {
          throw new Error(response.error || 'Failed to load receipts');
        }
      } else {
        // Offline - get from local database
        const localReceipts = await dbService.getReceipts(filter);

        // Convert Receipt to ReceiptHistoryItem
        const historyItems: ReceiptHistoryItem[] = localReceipts.map(receipt => ({
          id: receipt.id,
          transactionId: receipt.transactionId || 0, // Ensure transactionId is not undefined
          merchantName: receipt.merchantName,
          date: receipt.date || new Date().toISOString(),
          totalAmount: receipt.totalAmount,
          imageUri: receipt.imageUri,
          isProcessed: receipt.isProcessed,
          createdAt: receipt.createdAt || new Date().toISOString()
        }));

        setReceipts(historyItems);
      }
    } catch (error) {
      console.error('Error loading receipts:', error);
      setError(t('receipts.loadError'));
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Load receipts when screen is focused
  useFocusEffect(
    useCallback(() => {
      loadReceipts();
    }, [filter])
  );

  // Handle refresh
  const handleRefresh = () => {
    loadReceipts(true);
  };

  // Handle filter
  const handleFilter = (newFilter: ReceiptFilterOptions) => {
    setFilter(newFilter);
    setShowFilterModal(false);
  };

  // Handle view receipt
  const handleViewReceipt = (receipt: ReceiptHistoryItem) => {
    navigation.navigate('ReceiptDetails', { receiptId: receipt.id });
  };

  // Render receipt item
  const renderReceiptItem = ({ item }: { item: ReceiptHistoryItem }) => (
    <TouchableOpacity
      style={[
        styles.receiptCard,
        { backgroundColor: isDarkMode ? '#161B22' : '#FFFFFF' }
      ]}
      onPress={() => handleViewReceipt(item)}
    >
      <View style={styles.receiptHeader}>
        <Text style={[
          styles.merchantName,
          { color: isDarkMode ? '#FFFFFF' : '#24292E' }
        ]}>
          {item.merchantName || t('receipts.unknownMerchant')}
        </Text>
        <Text style={[
          styles.receiptDate,
          { color: isDarkMode ? '#8B949E' : '#6E7781' }
        ]}>
          {formatDate(item.date)}
        </Text>
      </View>

      <View style={styles.receiptContent}>
        {item.imageUri ? (
          <Image
            source={{ uri: item.imageUri }}
            style={styles.receiptThumbnail}
            resizeMode="cover"
          />
        ) : (
          <View style={[
            styles.receiptThumbnailPlaceholder,
            { backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA' }
          ]}>
            <Ionicons
              name="receipt-outline"
              size={24}
              color={isDarkMode ? '#8B949E' : '#6E7781'}
            />
          </View>
        )}

        <View style={styles.receiptDetails}>
          <Text style={[
            styles.transactionDescription,
            { color: isDarkMode ? '#8B949E' : '#6E7781' }
          ]}>
            {item.transactionDescription || t('receipts.noTransaction')}
          </Text>

          <Text style={[
            styles.receiptAmount,
            { color: isDarkMode ? '#FFFFFF' : '#24292E' }
          ]}>
            {formatCurrency(item.totalAmount)}
          </Text>

          <View style={[
            styles.statusBadge,
            { backgroundColor: getReceiptStatusColor(item.isProcessed) + '20' }
          ]}>
            <Text style={[
              styles.statusText,
              { color: getReceiptStatusColor(item.isProcessed) }
            ]}>
              {getReceiptStatusText(item.isProcessed)}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  // Render empty state
  const renderEmptyState = () => (
    <EmptyState
      icon="receipt-outline"
      title={t('receipts.noReceipts')}
      message={t('receipts.addReceiptPrompt')}
      actionLabel={t('receipts.scanReceipt')}
      onAction={() => navigation.navigate('AddTransaction')}
    />
  );

  return (
    <View style={[
      styles.container,
      { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }
    ]}>
      <View style={styles.header}>
        <Text style={[
          styles.title,
          { color: isDarkMode ? '#FFFFFF' : '#24292E' }
        ]}>
          {t('receipts.receiptHistory')}
        </Text>

        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilterModal(true)}
        >
          <Ionicons
            name="filter-outline"
            size={24}
            color={isDarkMode ? '#FFFFFF' : '#24292E'}
          />
        </TouchableOpacity>
      </View>

      {error ? (
        <ErrorMessage
          message={error}
          onRetry={handleRefresh}
        />
      ) : isLoading ? (
        <LoadingIndicator message={t('common.loading')} />
      ) : (
        <FlatList
          data={receipts}
          renderItem={renderReceiptItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={[isDarkMode ? '#58A6FF' : '#0366D6']}
              tintColor={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
          }
          ListEmptyComponent={renderEmptyState}
        />
      )}

      <ReceiptFilterModal
        visible={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        onApply={handleFilter}
        initialFilter={filter}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  filterButton: {
    padding: 8,
  },
  listContent: {
    padding: 16,
    paddingBottom: 32,
  },
  receiptCard: {
    borderRadius: 8,
    marginBottom: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  receiptHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  merchantName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  receiptDate: {
    fontSize: 12,
  },
  receiptContent: {
    flexDirection: 'row',
  },
  receiptThumbnail: {
    width: 60,
    height: 60,
    borderRadius: 4,
  },
  receiptThumbnailPlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  receiptDetails: {
    flex: 1,
    marginLeft: 12,
  },
  transactionDescription: {
    fontSize: 14,
    marginBottom: 4,
  },
  receiptAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default ReceiptHistoryScreen;
