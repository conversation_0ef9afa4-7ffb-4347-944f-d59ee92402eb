import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import apiService from '../../services/api';
import dbService from '../../services/db';
import { useOCR } from '../../contexts/OCRContext';
import { ReceiptData } from '../../models/Receipt';
import { Receipt } from '../../services/db';
import { formatDate, formatCurrency } from '../../utils/formatters';
import Button from '../../components/ui/Button';
import ReceiptOCRProcessor from '../../components/receipts/ReceiptOCRProcessor';
import { useCategories } from '../../contexts/CategoryContext';
import NetInfo from '@react-native-community/netinfo';

// Define props interface
interface ReceiptDetailsScreenProps {
  receiptId: number;
}

const ReceiptDetailsScreen: React.FC<ReceiptDetailsScreenProps> = ({ receiptId }) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const navigation = useNavigation<any>();
  const { categories } = useCategories();
  const { processImage, isProcessing } = useOCR();

  // State
  const [receipt, setReceipt] = useState<ReceiptData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showOCRProcessor, setShowOCRProcessor] = useState(false);

  // Load receipt
  const loadReceipt = async () => {
    if (!receiptId) {
      setError(t('receipts.invalidReceiptId'));
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Check network status
      const netInfo = await NetInfo.fetch();

      if (netInfo.isConnected) {
        // Online - get from API
        const response = await apiService.receipts.getReceipt(receiptId);

        if (response.status === 200 && response.data) {
          setReceipt(response.data);

          // Save to local database for offline access
          // Ensure receipt has an id before saving and convert to Receipt type
          if (response.data && response.data.id !== undefined) {
            // Convert ReceiptData to Receipt type with required fields
            const receiptToSave: Receipt = {
              id: response.data.id,
              transactionId: response.data.transactionId,
              merchantName: response.data.merchantName,
              date: response.data.date,
              totalAmount: response.data.totalAmount || 0,
              items: response.data.items || [],
              rawText: response.data.rawText || '',
              imageUri: response.data.imageUri,
              language: response.data.language,
              isProcessed: response.data.isProcessed === true, // Ensure boolean
              confidence: response.data.confidence,
              createdAt: response.data.createdAt,
              updatedAt: response.data.updatedAt,
              isDeleted: false
            };
            await dbService.saveReceipt(receiptToSave);
          }
        } else {
          throw new Error(response.error || 'Failed to load receipt');
        }
      } else {
        // Offline - get from local database
        const localReceipt = await dbService.getReceipt(receiptId);

        if (localReceipt) {
          setReceipt(localReceipt);
        } else {
          throw new Error('Receipt not found in local database');
        }
      }
    } catch (error) {
      console.error('Error loading receipt:', error);
      setError(t('receipts.loadError'));
    } finally {
      setIsLoading(false);
    }
  };

  // Load receipt when screen is focused
  useEffect(() => {
    loadReceipt();
  }, [receiptId]);

  // Process receipt with OCR
  const handleProcessReceipt = async () => {
    if (!receipt || !receipt.imageUri) {
      Alert.alert(
        t('common.error'),
        t('receipts.noImageToProcess')
      );
      return;
    }

    try {
      // Check if already processed
      if (receipt.isProcessed) {
        Alert.alert(
          t('receipts.alreadyProcessed'),
          t('receipts.reprocessConfirmation'),
          [
            {
              text: t('common.cancel'),
              style: 'cancel',
            },
            {
              text: t('common.reprocess'),
              onPress: () => setShowOCRProcessor(true),
            },
          ]
        );
        return;
      }

      // Process with OCR
      const result = await processImage(receipt.imageUri);

      if (result) {
        // Update receipt with OCR data
        const updatedReceipt: ReceiptData = {
          ...receipt,
          ...result,
          isProcessed: true,
        };

        setReceipt(updatedReceipt);
        setShowOCRProcessor(true);
      }
    } catch (error) {
      console.error('Error processing receipt:', error);
      Alert.alert(
        t('common.error'),
        t('receipts.ocrError')
      );
    }
  };

  // Save processed receipt
  const handleSaveProcessedReceipt = async (updatedData: ReceiptData) => {
    try {
      setIsLoading(true);

      // Check network status
      const netInfo = await NetInfo.fetch();

      if (netInfo.isConnected) {
        // Online - update via API
        const response = await apiService.receipts.updateReceipt(receiptId, updatedData);

        if (response.status === 200 && response.data) {
          setReceipt(response.data);

          // Save to local database
          if (response.data && response.data.id !== undefined) {
            // Convert ReceiptData to Receipt type with required fields
            const receiptToSave: Receipt = {
              id: response.data.id,
              transactionId: response.data.transactionId,
              merchantName: response.data.merchantName,
              date: response.data.date,
              totalAmount: response.data.totalAmount || 0,
              items: response.data.items || [],
              rawText: response.data.rawText || '',
              imageUri: response.data.imageUri,
              language: response.data.language,
              isProcessed: response.data.isProcessed === true, // Ensure boolean
              confidence: response.data.confidence,
              createdAt: response.data.createdAt,
              updatedAt: response.data.updatedAt,
              isDeleted: false
            };
            await dbService.saveReceipt(receiptToSave);
          }

          Alert.alert(
            t('common.success'),
            t('receipts.updateSuccess')
          );
        } else {
          throw new Error(response.error || 'Failed to update receipt');
        }
      } else {
        // Offline - save to local database
        // Convert to Receipt type with required fields
        const updatedReceipt: Receipt = {
          id: receiptId,
          transactionId: updatedData.transactionId,
          merchantName: updatedData.merchantName,
          date: updatedData.date,
          totalAmount: updatedData.totalAmount || 0,
          items: updatedData.items || [],
          rawText: updatedData.rawText || '',
          imageUri: updatedData.imageUri,
          language: updatedData.language,
          isProcessed: true, // Ensure boolean
          confidence: updatedData.confidence,
          createdAt: updatedData.createdAt,
          updatedAt: new Date().toISOString(),
          isDeleted: false
        };

        await dbService.saveReceipt(updatedReceipt);
        setReceipt(updatedReceipt);

        Alert.alert(
          t('common.success'),
          t('receipts.updateSuccessOffline')
        );
      }

      setShowOCRProcessor(false);
    } catch (error) {
      console.error('Error saving processed receipt:', error);
      Alert.alert(
        t('common.error'),
        t('receipts.updateError')
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Delete receipt
  const handleDeleteReceipt = () => {
    Alert.alert(
      t('receipts.deleteReceipt'),
      t('receipts.deleteConfirmation'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);

              // Check network status
              const netInfo = await NetInfo.fetch();

              if (netInfo.isConnected) {
                // Online - delete via API
                const response = await apiService.receipts.deleteReceipt(receiptId);

                if (response.status === 204) {
                  // Delete from local database
                  await dbService.deleteReceipt(receiptId);

                  navigation.goBack();
                } else {
                  throw new Error(response.error || 'Failed to delete receipt');
                }
              } else {
                // Offline - mark for deletion in local database
                await dbService.markReceiptForDeletion(receiptId);

                Alert.alert(
                  t('common.success'),
                  t('receipts.deleteSuccessOffline')
                );

                navigation.goBack();
              }
            } catch (error) {
              console.error('Error deleting receipt:', error);
              Alert.alert(
                t('common.error'),
                t('receipts.deleteError')
              );
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  // View transaction
  const handleViewTransaction = () => {
    if (receipt && receipt.transactionId) {
      navigation.navigate('TransactionDetails', { transactionId: receipt.transactionId });
    } else {
      Alert.alert(
        t('common.error'),
        t('receipts.noTransactionLinked')
      );
    }
  };

  if (showOCRProcessor && receipt) {
    // Convert categories to the format expected by ReceiptOCRProcessor
    const formattedCategories = categories.map(category => ({
      id: category.id || 0, // Ensure id is not undefined
      name: category.name || '',
      type: category.type || 'Expense'
    }));

    return (
      <ReceiptOCRProcessor
        receiptData={receipt}
        onSave={handleSaveProcessedReceipt}
        onCancel={() => setShowOCRProcessor(false)}
        categories={formattedCategories}
      />
    );
  }

  return (
    <View style={[
      styles.container,
      { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }
    ]}>
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
          <Text style={[
            styles.loadingText,
            { color: isDarkMode ? '#FFFFFF' : '#24292E' }
          ]}>
            {t('common.loading')}
          </Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Ionicons
            name="alert-circle-outline"
            size={48}
            color="#F85149"
          />
          <Text style={[
            styles.errorText,
            { color: isDarkMode ? '#FFFFFF' : '#24292E' }
          ]}>
            {error}
          </Text>
          <Button
            title={t('common.retry')}
            onPress={loadReceipt}
            type="primary"
            style={styles.retryButton}
          />
        </View>
      ) : receipt ? (
        <ScrollView contentContainerStyle={styles.contentContainer}>
          <View style={styles.header}>
            <Text style={[
              styles.title,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' }
            ]}>
              {receipt.merchantName || t('receipts.unknownMerchant')}
            </Text>

            <Text style={[
              styles.date,
              { color: isDarkMode ? '#8B949E' : '#6E7781' }
            ]}>
              {formatDate(receipt.date || '')}
            </Text>
          </View>

          {receipt.imageUri ? (
            <View style={styles.imageContainer}>
              <Image
                source={{ uri: receipt.imageUri }}
                style={styles.image}
                resizeMode="contain"
              />
            </View>
          ) : (
            <View style={[
              styles.noImageContainer,
              { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
            ]}>
              <Ionicons
                name="image-outline"
                size={48}
                color={isDarkMode ? '#8B949E' : '#6E7781'}
              />
              <Text style={[
                styles.noImageText,
                { color: isDarkMode ? '#8B949E' : '#6E7781' }
              ]}>
                {t('receipts.noImage')}
              </Text>
            </View>
          )}

          <View style={[
            styles.infoCard,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
          ]}>
            <View style={styles.infoRow}>
              <Text style={[
                styles.infoLabel,
                { color: isDarkMode ? '#8B949E' : '#6E7781' }
              ]}>
                {t('receipts.totalAmount')}
              </Text>
              <Text style={[
                styles.infoValue,
                styles.amountValue,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' }
              ]}>
                {formatCurrency(receipt.totalAmount)}
              </Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={[
                styles.infoLabel,
                { color: isDarkMode ? '#8B949E' : '#6E7781' }
              ]}>
                {t('receipts.status')}
              </Text>
              <View style={[
                styles.statusBadge,
                {
                  backgroundColor: receipt.isProcessed ?
                    'rgba(59, 185, 80, 0.2)' :
                    'rgba(240, 136, 62, 0.2)'
                }
              ]}>
                <Text style={[
                  styles.statusText,
                  {
                    color: receipt.isProcessed ?
                      '#3FB950' :
                      '#F0883E'
                  }
                ]}>
                  {receipt.isProcessed ?
                    t('receipts.processed') :
                    t('receipts.pending')}
                </Text>
              </View>
            </View>

            {receipt.transactionId && (
              <View style={styles.infoRow}>
                <Text style={[
                  styles.infoLabel,
                  { color: isDarkMode ? '#8B949E' : '#6E7781' }
                ]}>
                  {t('receipts.linkedTransaction')}
                </Text>
                <TouchableOpacity
                  style={styles.viewTransactionButton}
                  onPress={handleViewTransaction}
                >
                  <Text style={[
                    styles.viewTransactionText,
                    { color: isDarkMode ? '#58A6FF' : '#0366D6' }
                  ]}>
                    {t('receipts.viewTransaction')}
                  </Text>
                  <Ionicons
                    name="chevron-forward"
                    size={16}
                    color={isDarkMode ? '#58A6FF' : '#0366D6'}
                  />
                </TouchableOpacity>
              </View>
            )}
          </View>

          {receipt.items && receipt.items.length > 0 && (
            <View style={[
              styles.itemsCard,
              { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
            ]}>
              <Text style={[
                styles.itemsTitle,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' }
              ]}>
                {t('receipts.items')}
              </Text>

              {receipt.items.map((item, index) => (
                <View key={index} style={styles.itemRow}>
                  <View style={styles.itemDetails}>
                    <Text style={[
                      styles.itemName,
                      { color: isDarkMode ? '#FFFFFF' : '#24292E' }
                    ]}>
                      {item.name}
                    </Text>

                    {item.quantity && item.quantity > 1 && (
                      <Text style={[
                        styles.itemQuantity,
                        { color: isDarkMode ? '#8B949E' : '#6E7781' }
                      ]}>
                        x{item.quantity}
                      </Text>
                    )}
                  </View>

                  <Text style={[
                    styles.itemAmount,
                    { color: isDarkMode ? '#FFFFFF' : '#24292E' }
                  ]}>
                    {formatCurrency(item.amount)}
                  </Text>
                </View>
              ))}
            </View>
          )}

          <View style={styles.actions}>
            <Button
              title={receipt.isProcessed ? t('receipts.reprocess') : t('receipts.processReceipt')}
              onPress={handleProcessReceipt}
              type="primary"
              style={styles.actionButton}
              icon="scan-outline"
              isLoading={isProcessing}
            />

            <Button
              title={t('common.delete')}
              onPress={handleDeleteReceipt}
              type="danger"
              style={styles.actionButton}
              icon="trash-outline"
            />
          </View>
        </ScrollView>
      ) : (
        <View style={styles.errorContainer}>
          <Ionicons
            name="document-outline"
            size={48}
            color={isDarkMode ? '#8B949E' : '#6E7781'}
          />
          <Text style={[
            styles.errorText,
            { color: isDarkMode ? '#FFFFFF' : '#24292E' }
          ]}>
            {t('receipts.receiptNotFound')}
          </Text>
          <Button
            title={t('common.goBack')}
            onPress={() => navigation.goBack()}
            type="primary"
            style={styles.retryButton}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    minWidth: 120,
  },
  contentContainer: {
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  date: {
    fontSize: 14,
  },
  imageContainer: {
    width: '100%',
    height: 300,
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  noImageContainer: {
    width: '100%',
    height: 200,
    marginBottom: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noImageText: {
    marginTop: 8,
    fontSize: 14,
  },
  infoCard: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  amountValue: {
    fontSize: 18,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  viewTransactionButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewTransactionText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  itemsCard: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  itemsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(128, 128, 128, 0.2)',
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 14,
  },
  itemQuantity: {
    fontSize: 12,
    marginTop: 2,
  },
  itemAmount: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
});

export default ReceiptDetailsScreen;
