import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  FlatList,
  RefreshControl,
  Clipboard,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import apiService from '../services/api';
import dbService, { Family, FamilyMember } from '../services/db';

const FamilyDetailsScreen = ({ route }: any) => {
  const { familyId } = route.params;
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { user, isPlatformAdmin, isFamilyAdmin } = useAuth();
  const router = useRouter();

  const [family, setFamily] = useState<Family | null>(null);
  const [members, setMembers] = useState<FamilyMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [inviteCode, setInviteCode] = useState<string | null>(null);
  const [generatingCode, setGeneratingCode] = useState(false);

  const fetchFamilyDetails = async () => {
    try {
      setLoading(true);

      // Get family details
      const familyResponse = await apiService.family.getFamily(familyId);

      if (familyResponse.data) {
        const familyData = {
          ...familyResponse.data,
          createdAt: new Date(familyResponse.data.createdAt),
          updatedAt: new Date(familyResponse.data.updatedAt),
          isSynced: true
        };

        await dbService.saveFamily(familyData);
        setFamily(familyData);

        // Check if current user is admin
        const currentUserMember = familyData.members?.find(
          (m: any) => m.userId === user?.id
        );

        // Use the values from AuthContext
        console.log('User roles check:', {
          contextIsPlatformAdmin: isPlatformAdmin,
          contextIsFamilyAdmin: isFamilyAdmin,
          userRoles: user?.roles,
          memberRole: currentUserMember?.role
        });

        // Set isAdmin if user is a platform admin or has Admin role in this specific family
        // Note: Having the FamilyAdmin role doesn't automatically make you an admin of every family
        setIsAdmin(isPlatformAdmin || currentUserMember?.role === 'Admin');

        // Save members to local database
        if (familyData.members && familyData.members.length > 0) {
          const membersList: FamilyMember[] = [];

          for (const member of familyData.members) {
            await dbService.saveFamilyMember(familyId, {
              ...member,
              joinedAt: new Date(member.joinedAt)
            });
            membersList.push({
              ...member,
              joinedAt: new Date(member.joinedAt)
            });
          }

          setMembers(membersList);
        }
      } else {
        // Check for specific error messages
        const errorMessage = familyResponse.error || '';
        console.log('Family details error:', errorMessage);

        if (errorMessage.toLowerCase().includes('required role')) {
          Alert.alert(
            t('common.error'),
            t('family.noPermission')
          );
          router.back();
          return;
        }

        // Try to load from local database
        const localFamily = await dbService.getFamily(familyId);
        if (localFamily) {
          setFamily(localFamily);

          // Get members from local database
          const localMembers = await dbService.getFamilyMembers(familyId);
          setMembers(localMembers);

          // Check if current user is admin
          const currentUserMember = localMembers.find(
            (m) => m.userId === user?.id
          );

          // Use the values from AuthContext
          console.log('User roles check (local):', {
            contextIsPlatformAdmin: isPlatformAdmin,
            contextIsFamilyAdmin: isFamilyAdmin,
            userRoles: user?.roles,
            memberRole: currentUserMember?.role
          });

          // Set isAdmin if user is a platform admin or has Admin role in this specific family
          // Note: Having the FamilyAdmin role doesn't automatically make you an admin of every family
          setIsAdmin(isPlatformAdmin || currentUserMember?.role === 'Admin');
        } else {
          Alert.alert(
            t('common.error'),
            t('family.familyNotFound')
          );
          router.back();
        }
      }
    } catch (error) {
      console.error('Error fetching family details:', error);

      // Check if the error is related to permissions
      const errorMessage = error instanceof Error ? error.message : '';
      if (errorMessage.toLowerCase().includes('required role') ||
          errorMessage.toLowerCase().includes('permission') ||
          errorMessage.toLowerCase().includes('403')) {
        Alert.alert(
          t('common.error'),
          t('family.noPermission')
        );
        router.back();
        return;
      }

      // Try to load from local database
      const localFamily = await dbService.getFamily(familyId);
      if (localFamily) {
        setFamily(localFamily);

        // Get members from local database
        const localMembers = await dbService.getFamilyMembers(familyId);
        setMembers(localMembers);

        // Check if current user is admin
        const currentUserMember = localMembers.find(
          (m) => m.userId === user?.id
        );

        // Use the values from AuthContext
        console.log('User roles check (error):', {
          contextIsPlatformAdmin: isPlatformAdmin,
          contextIsFamilyAdmin: isFamilyAdmin,
          userRoles: user?.roles,
          memberRole: currentUserMember?.role
        });

        // Set isAdmin if user is a platform admin or has Admin role in this specific family
        // Note: Having the FamilyAdmin role doesn't automatically make you an admin of every family
        setIsAdmin(isPlatformAdmin || currentUserMember?.role === 'Admin');
      } else {
        Alert.alert(
          t('common.error'),
          t('family.familyNotFound')
        );
        router.back();
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchFamilyDetails();
  }, [familyId]);

  const onRefresh = () => {
    setRefreshing(true);
    fetchFamilyDetails();
  };

  const handleGenerateInviteCode = async () => {
    try {
      setGeneratingCode(true);

      // Check if we already have an invite code
      if (inviteCode || family?.inviteCode) {
        // Show the existing invite code
        Alert.alert(
          t('family.inviteCode'),
          inviteCode || family?.inviteCode || '',
          [
            {
              text: t('common.cancel'),
              style: 'cancel'
            },
            {
              text: t('family.copyInviteCode'),
              onPress: handleCopyInviteCode
            },
            {
              text: t('family.generateInviteCode'),
              onPress: async () => {
                // Generate a new invite code
                await generateNewInviteCode();
              }
            }
          ]
        );
        setGeneratingCode(false);
        return;
      }

      await generateNewInviteCode();
    } catch (error) {
      console.error('Error generating invite code:', error);
      Alert.alert(
        t('common.error'),
        t('common.error')
      );
      setGeneratingCode(false);
    }
  };

  const generateNewInviteCode = async () => {
    try {
      const response = await apiService.family.generateInviteCode(familyId);

      if (response.data) {
        setInviteCode(response.data.inviteCode);

        // Update family in local database
        if (family) {
          const updatedFamily = {
            ...family,
            inviteCode: response.data.inviteCode
          };

          await dbService.saveFamily(updatedFamily);
          setFamily(updatedFamily);
        }

        // Show success message with the invite code
        Alert.alert(
          t('family.inviteCode'),
          response.data.inviteCode,
          [
            {
              text: t('common.ok'),
              style: 'cancel'
            },
            {
              text: t('family.copyInviteCode'),
              onPress: handleCopyInviteCode
            }
          ]
        );
      } else {
        Alert.alert(
          t('common.error'),
          response.error || t('common.error')
        );
      }
    } catch (error) {
      console.error('Error generating new invite code:', error);
      Alert.alert(
        t('common.error'),
        t('common.error')
      );
    } finally {
      setGeneratingCode(false);
    }
  };

  const handleCopyInviteCode = () => {
    if (inviteCode || family?.inviteCode) {
      // TODO: Replace with expo-clipboard when available
      // This uses the deprecated Clipboard API but works for now
      Clipboard.setString(inviteCode || family?.inviteCode || '');
      Alert.alert(
        t('common.success'),
        t('family.inviteCodeCopied')
      );
    }
  };

  const handleEditFamily = () => {
    if (family) {
      router.push({
        pathname: '/family/edit/[id]' as any,
        params: { id: family.id.toString() }
      });
    }
  };

  const handleDeleteFamily = () => {
    if (!family) return;

    Alert.alert(
      t('common.delete') + ' ' + t('family.family'),
      t('common.confirmDelete') + ' ' + family.name + '?',
      [
        {
          text: t('common.cancel'),
          style: 'cancel'
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);

              const response = await apiService.family.deleteFamily(family.id);

              if (response.status === 204) {
                // Delete family from local database
                await dbService.deleteFamily(family.id);

                // Navigate back to families list
                router.push('/families' as any);
              } else {
                Alert.alert(
                  t('common.error'),
                  response.error || t('common.error')
                );
              }
            } catch (error) {
              console.error('Error deleting family:', error);
              Alert.alert(
                t('common.error'),
                t('common.error')
              );
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const handleLeaveFamily = () => {
    Alert.alert(
      t('family.leaveFamily'),
      t('family.confirmLeaveFamily'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel'
        },
        {
          text: t('common.yes'),
          onPress: async () => {
            try {
              setLoading(true);

              const response = await apiService.family.leaveFamily(familyId);

              if (response.status === 204) {
                // Remove family member from local database
                if (user?.id) {
                  await dbService.removeFamilyMember(familyId, user.id);
                }

                router.back();
              } else {
                // Check for specific error messages
                const errorMessage = response.error || '';

                if (errorMessage.toLowerCase().includes('last admin')) {
                  Alert.alert(
                    t('common.error'),
                    t('family.lastAdmin')
                  );
                } else if (errorMessage.toLowerCase().includes('not a member')) {
                  Alert.alert(
                    t('common.error'),
                    t('family.notMember')
                  );
                } else if (errorMessage.toLowerCase().includes('not found')) {
                  Alert.alert(
                    t('common.error'),
                    t('family.familyNotFound')
                  );
                } else {
                  Alert.alert(
                    t('common.error'),
                    errorMessage || t('family.leaveFailed')
                  );
                }
              }
            } catch (error) {
              console.error('Error leaving family:', error);

              // Try to extract a meaningful error message
              const errorMessage = error instanceof Error ? error.message : '';

              if (errorMessage.toLowerCase().includes('last admin')) {
                Alert.alert(
                  t('common.error'),
                  t('family.lastAdmin')
                );
              } else if (errorMessage.toLowerCase().includes('not a member')) {
                Alert.alert(
                  t('common.error'),
                  t('family.notMember')
                );
              } else {
                Alert.alert(
                  t('common.error'),
                  t('family.leaveFailed')
                );
              }
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const handleAddMember = () => {
    // Show a detailed explanation of how to add members
    Alert.alert(
      t('family.addMember'),
      t('family.addMemberExplanation'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel'
        },
        {
          text: t('family.showInviteCode'),
          onPress: () => {
            // If we already have an invite code, show it
            if (inviteCode || family?.inviteCode) {
              Alert.alert(
                t('family.inviteCode'),
                inviteCode || family?.inviteCode || '',
                [
                  {
                    text: t('common.ok'),
                    style: 'cancel'
                  },
                  {
                    text: t('family.copyInviteCode'),
                    onPress: handleCopyInviteCode
                  }
                ]
              );
            } else {
              // Generate a new invite code
              handleGenerateInviteCode();
            }
          }
        }
      ]
    );
  };

  const handleRemoveMember = (memberId: number) => {
    Alert.alert(
      t('family.removeMember'),
      t('family.confirmRemoveMember'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel'
        },
        {
          text: t('common.yes'),
          onPress: async () => {
            try {
              setLoading(true);

              const response = await apiService.family.removeMember(familyId, memberId);

              if (response.status === 204) {
                // Remove family member from local database
                await dbService.removeFamilyMember(familyId, memberId);

                // Refresh family details
                fetchFamilyDetails();
              } else {
                Alert.alert(
                  t('common.error'),
                  response.error || t('common.error')
                );
              }
            } catch (error) {
              console.error('Error removing family member:', error);
              Alert.alert(
                t('common.error'),
                t('common.error')
              );
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const handleUpdateMemberRole = (memberId: number, currentRole: string) => {
    // Toggle between Admin and Member
    const newRole = currentRole === 'Admin' ? 'Member' : 'Admin';

    Alert.alert(
      t('family.updateRole'),
      `${t('family.updateRole')} ${t('common.to')} ${t(`family.${newRole.toLowerCase()}`)}?`,
      [
        {
          text: t('common.cancel'),
          style: 'cancel'
        },
        {
          text: t('common.yes'),
          onPress: async () => {
            try {
              setLoading(true);

              const response = await apiService.family.updateMemberRole(
                familyId,
                memberId,
                { role: newRole }
              );

              if (response.status === 204) {
                // Update family member in local database
                const memberToUpdate = members.find(m => m.userId === memberId);
                if (memberToUpdate) {
                  await dbService.saveFamilyMember(familyId, {
                    ...memberToUpdate,
                    role: newRole
                  });
                }

                // Refresh family details
                fetchFamilyDetails();
              } else {
                Alert.alert(
                  t('common.error'),
                  response.error || t('common.error')
                );
              }
            } catch (error) {
              console.error('Error updating member role:', error);
              Alert.alert(
                t('common.error'),
                t('common.error')
              );
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const renderMemberItem = ({ item }: { item: FamilyMember }) => (
    <View
      style={[
        styles.memberItem,
        { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }
      ]}
    >
      <View style={styles.memberInfo}>
        <Text style={[styles.memberName, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {item.firstName} {item.lastName}
        </Text>
        <Text style={[styles.memberUsername, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
          @{item.username}
        </Text>
      </View>

      <View style={styles.memberActions}>
        <View
          style={[
            styles.roleBadge,
            {
              backgroundColor: item.role === 'Admin'
                ? (isDarkMode ? '#1F6FEB' : '#0969DA')
                : (isDarkMode ? '#238636' : '#2DA44E')
            }
          ]}
        >
          <Text style={styles.roleText}>
            {t(`family.${item.role.toLowerCase()}`)}
          </Text>
        </View>

        {isAdmin && String(user?.id) !== String(item.userId) && (
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleUpdateMemberRole(item.userId, item.role)}
            >
              <Ionicons
                name="swap-horizontal"
                size={20}
                color={isDarkMode ? '#8B949E' : '#6E7781'}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleRemoveMember(item.userId)}
            >
              <Ionicons
                name="trash-outline"
                size={20}
                color="#F85149"
              />
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );

  if (loading && !refreshing) {
    return (
      <View
        style={[
          styles.container,
          styles.loadingContainer,
          { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }
        ]}
      >
        <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      <ScrollView
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#0366D6']}
            tintColor={isDarkMode ? '#58A6FF' : '#0366D6'}
          />
        }
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
            accessibilityLabel="Back button"
          >
            <Ionicons
              name="arrow-back"
              size={24}
              color={isDarkMode ? '#FFFFFF' : '#000000'}
            />
          </TouchableOpacity>
        </View>

        <View style={styles.familyTitleRow}>
          <Text style={[styles.familyName, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {family?.name}
          </Text>

          {isAdmin && (
            <TouchableOpacity
              style={styles.editFamilyButton}
              onPress={handleEditFamily}
              accessibilityLabel="Edit family button"
            >
              <Ionicons
                name="create-outline"
                size={22}
                color={isDarkMode ? '#58A6FF' : '#0366D6'}
              />
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.familyInfo}>

          {family?.description && (
            <Text style={[styles.familyDescription, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {family.description}
            </Text>
          )}

          <View style={styles.familyMeta}>
            <Text style={[styles.familyMetaText, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('family.createdBy')}: {family?.createdByUsername}
            </Text>
            <Text style={[styles.familyMetaText, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {family?.createdAt ? new Date(family.createdAt).toLocaleDateString() : ''}
            </Text>
          </View>
        </View>

        <View
          style={[
            styles.inviteSection,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
          ]}
        >
          <View style={styles.inviteHeader}>
            <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {t('family.inviteCode')}
            </Text>

            {isAdmin && (
              <TouchableOpacity
                style={[
                  styles.generateButton,
                  {
                    backgroundColor: isDarkMode ? '#1F6FEB' : '#0969DA',
                    opacity: generatingCode ? 0.7 : 1
                  }
                ]}
                onPress={handleGenerateInviteCode}
                disabled={generatingCode}
                accessibilityLabel="Generate invite code button"
              >
                {generatingCode ? (
                  <ActivityIndicator color="#FFFFFF" size="small" />
                ) : (
                  <Text style={styles.generateButtonText}>
                    {(inviteCode || family?.inviteCode) ? t('family.copyInviteCode') : t('family.generateInviteCode')}
                  </Text>
                )}
              </TouchableOpacity>
            )}
          </View>

          {(inviteCode || family?.inviteCode) ? (
            <>
              <View style={styles.inviteCodeContainer}>
                <Text style={[styles.inviteCode, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                  {inviteCode || family?.inviteCode}
                </Text>

                <TouchableOpacity
                  style={styles.copyButton}
                  onPress={handleCopyInviteCode}
                  accessibilityLabel="Copy invite code button"
                >
                  <Ionicons
                    name="copy-outline"
                    size={20}
                    color={isDarkMode ? '#58A6FF' : '#0366D6'}
                  />
                </TouchableOpacity>
              </View>
              <Text style={[styles.inviteExpiry, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('family.inviteCodeExpiry')}
              </Text>
            </>
          ) : (
            <Text style={[styles.noInviteCode, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {isAdmin
                ? t('family.generateInviteCode') + ' ' + t('common.to') + ' ' + t('family.addMember')
                : t('family.noInviteCode')}
            </Text>
          )}
        </View>

        <View style={styles.membersSection}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {t('family.members')} ({members.length})
            </Text>

            {isAdmin && (
              <TouchableOpacity
                style={styles.addButton}
                onPress={handleAddMember}
              >
                <Ionicons
                  name="person-add-outline"
                  size={20}
                  color="#FFFFFF"
                />
              </TouchableOpacity>
            )}
          </View>

          {members.length > 0 ? (
            <FlatList
              data={members}
              renderItem={renderMemberItem}
              keyExtractor={(item) => item.userId.toString()}
              scrollEnabled={false}
            />
          ) : (
            <View style={styles.emptyMembersContainer}>
              <Text style={[styles.emptyMembersText, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('family.noMembers')}
              </Text>

              {isAdmin && (
                <View style={styles.addMemberGuide}>
                  <Text style={[styles.addMemberGuideTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                    {t('family.howToAddMembers')}
                  </Text>

                  <View style={styles.addMemberStep}>
                    <View style={[styles.stepNumber, { backgroundColor: isDarkMode ? '#1F6FEB' : '#0969DA' }]}>
                      <Text style={styles.stepNumberText}>1</Text>
                    </View>
                    <Text style={[styles.stepText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                      {t('family.step1')}
                    </Text>
                  </View>

                  <View style={styles.addMemberStep}>
                    <View style={[styles.stepNumber, { backgroundColor: isDarkMode ? '#1F6FEB' : '#0969DA' }]}>
                      <Text style={styles.stepNumberText}>2</Text>
                    </View>
                    <Text style={[styles.stepText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                      {t('family.step2')}
                    </Text>
                  </View>

                  <View style={styles.addMemberStep}>
                    <View style={[styles.stepNumber, { backgroundColor: isDarkMode ? '#1F6FEB' : '#0969DA' }]}>
                      <Text style={styles.stepNumberText}>3</Text>
                    </View>
                    <Text style={[styles.stepText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                      {t('family.step3')}
                    </Text>
                  </View>

                  <TouchableOpacity
                    style={[styles.generateInviteButton, { backgroundColor: isDarkMode ? '#1F6FEB' : '#0969DA' }]}
                    onPress={handleGenerateInviteCode}
                  >
                    <Text style={styles.generateInviteButtonText}>
                      {t('family.generateInviteCode')}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          )}
        </View>

        {isAdmin && (
          <TouchableOpacity
            style={[styles.deleteButton, { backgroundColor: '#F85149' }]}
            onPress={handleDeleteFamily}
          >
            <Ionicons
              name="trash-outline"
              size={20}
              color="#FFFFFF"
              style={styles.buttonIcon}
            />
            <Text style={styles.buttonText}>
              {t('common.delete')} {t('family.family')}
            </Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.leaveButton, { backgroundColor: '#F85149' }]}
          onPress={handleLeaveFamily}
        >
          <Ionicons
            name="exit-outline"
            size={20}
            color="#FFFFFF"
            style={styles.buttonIcon}
          />
          <Text style={styles.buttonText}>
            {t('family.leaveFamily')}
          </Text>
        </TouchableOpacity>


      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingVertical: 4,
  },
  backButton: {
    padding: 8,
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
    backgroundColor: '#0366D6',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  editButton: {
    padding: 4,
  },
  familyTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  editFamilyButton: {
    padding: 8,
    borderRadius: 20,
  },
  familyInfo: {
    marginBottom: 24,
  },
  familyName: {
    fontSize: 28,
    fontWeight: 'bold',
    flex: 1,
  },
  familyDescription: {
    fontSize: 16,
    marginBottom: 16,
  },
  familyMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  familyMetaText: {
    fontSize: 14,
  },
  inviteSection: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  inviteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  generateButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  generateButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  inviteCodeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  inviteCode: {
    fontSize: 20,
    fontWeight: 'bold',
    letterSpacing: 1,
  },
  copyButton: {
    padding: 4,
  },
  inviteExpiry: {
    fontSize: 12,
    textAlign: 'center',
  },
  noInviteCode: {
    fontSize: 14,
    marginTop: 8,
    marginBottom: 8,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  membersSection: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  addButton: {
    padding: 4,
    backgroundColor: '#0366D6',
    borderRadius: 16,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  memberItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  memberUsername: {
    fontSize: 14,
  },
  memberActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  roleBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  roleText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  actionButtons: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 4,
    marginLeft: 8,
  },
  leaveButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 48,
    borderRadius: 8,
    marginBottom: 24,
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },

  deleteButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 48,
    borderRadius: 8,
    marginBottom: 16,
  },
  emptyMembersContainer: {
    padding: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  emptyMembersText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  addMemberGuide: {
    width: '100%',
    backgroundColor: 'transparent',
    borderRadius: 8,
    padding: 16,
    marginTop: 8,
  },
  addMemberGuideTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  addMemberStep: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  stepNumber: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stepNumberText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  stepText: {
    fontSize: 16,
    flex: 1,
  },
  generateInviteButton: {
    height: 44,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  generateInviteButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default FamilyDetailsScreen;
