import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Switch,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useLoans } from '../../contexts/LoanContext';
import DateTimePicker from '@react-native-community/datetimepicker';

interface RouteParams {
  loanId: number;
}

const AddPaymentScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const route = useRoute();
  const navigation = useNavigation<any>();
  const { loanId } = route.params as RouteParams;
  
  const { getLoanById, createPayment } = useLoans();
  
  // Form state
  const [amount, setAmount] = useState('');
  const [principalAmount, setPrincipalAmount] = useState('');
  const [interestAmount, setInterestAmount] = useState('');
  const [paymentDate, setPaymentDate] = useState(new Date());
  const [paymentMethod, setPaymentMethod] = useState('');
  const [isScheduled, setIsScheduled] = useState(false);
  const [notes, setNotes] = useState('');
  
  // UI state
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loan, setLoan] = useState<any>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Load loan details
  useEffect(() => {
    const loadLoan = async () => {
      const loanData = getLoanById(loanId);
      setLoan(loanData);
      
      // Set default values based on EMI
      if (loanData) {
        setAmount(loanData.monthlyEMI.toString());
        
        // Calculate default principal and interest amounts
        const defaultInterestAmount = loanData.amount * (loanData.interestRate / 100 / 12);
        const defaultPrincipalAmount = loanData.monthlyEMI - defaultInterestAmount;
        
        setPrincipalAmount(defaultPrincipalAmount.toFixed(2));
        setInterestAmount(defaultInterestAmount.toFixed(2));
      }
    };
    
    loadLoan();
  }, [loanId]);

  // Update interest amount when amount and principal amount change
  useEffect(() => {
    if (amount && principalAmount) {
      const calculatedInterest = parseFloat(amount) - parseFloat(principalAmount);
      if (calculatedInterest >= 0) {
        setInterestAmount(calculatedInterest.toFixed(2));
      }
    }
  }, [amount, principalAmount]);

  // Update principal amount when amount and interest amount change
  useEffect(() => {
    if (amount && interestAmount) {
      const calculatedPrincipal = parseFloat(amount) - parseFloat(interestAmount);
      if (calculatedPrincipal >= 0) {
        setPrincipalAmount(calculatedPrincipal.toFixed(2));
      }
    }
  }, [amount, interestAmount]);

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!amount.trim()) {
      newErrors.amount = t('validation.required');
    } else if (isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      newErrors.amount = t('validation.positive');
    }

    if (!principalAmount.trim()) {
      newErrors.principalAmount = t('validation.required');
    } else if (isNaN(parseFloat(principalAmount)) || parseFloat(principalAmount) < 0) {
      newErrors.principalAmount = t('validation.number');
    }

    if (!interestAmount.trim()) {
      newErrors.interestAmount = t('validation.required');
    } else if (isNaN(parseFloat(interestAmount)) || parseFloat(interestAmount) < 0) {
      newErrors.interestAmount = t('validation.number');
    }

    // Check if principal + interest = amount
    const totalAmount = parseFloat(principalAmount) + parseFloat(interestAmount);
    if (!isNaN(totalAmount) && !isNaN(parseFloat(amount)) && Math.abs(totalAmount - parseFloat(amount)) > 0.01) {
      newErrors.amount = t('loans.invalidPaymentData');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      Alert.alert(t('common.error'), t('validation.fixErrors'));
      return;
    }

    setIsLoading(true);
    try {
      const paymentData = {
        loanId,
        amount: parseFloat(amount),
        principalAmount: parseFloat(principalAmount),
        interestAmount: parseFloat(interestAmount),
        paymentDate: paymentDate.toISOString(),
        paymentMethod,
        isScheduled,
        notes,
      };

      const result = await createPayment(paymentData);
      if (result) {
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error creating payment:', error);
      Alert.alert(t('common.error'), t('loans.invalidPaymentData'));
    } finally {
      setIsLoading(false);
    }
  };

  if (!loan) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
      </View>
    );
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}
      contentContainerStyle={styles.contentContainer}
    >
      {/* Loan Info */}
      <View style={[styles.loanInfoContainer, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
        <Text style={[styles.loanTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {loan.title}
        </Text>
        <Text style={[styles.loanAmount, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
          {t('loans.remainingAmount')}: {loan.remainingAmount.toLocaleString('en-US', { style: 'currency', currency: 'NPR' })}
        </Text>
      </View>

      {/* Amount */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.paymentAmount')} *
        </Text>
        <TextInput
          style={[
            styles.input,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
            errors.amount && styles.inputError,
          ]}
          value={amount}
          onChangeText={setAmount}
          placeholder="0.00"
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          keyboardType="numeric"
        />
        {errors.amount && (
          <Text style={styles.errorText}>{errors.amount}</Text>
        )}
      </View>

      {/* Principal Amount */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.principalAmount')} *
        </Text>
        <TextInput
          style={[
            styles.input,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
            errors.principalAmount && styles.inputError,
          ]}
          value={principalAmount}
          onChangeText={setPrincipalAmount}
          placeholder="0.00"
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          keyboardType="numeric"
        />
        {errors.principalAmount && (
          <Text style={styles.errorText}>{errors.principalAmount}</Text>
        )}
      </View>

      {/* Interest Amount */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.interestAmount')} *
        </Text>
        <TextInput
          style={[
            styles.input,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
            errors.interestAmount && styles.inputError,
          ]}
          value={interestAmount}
          onChangeText={setInterestAmount}
          placeholder="0.00"
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          keyboardType="numeric"
        />
        {errors.interestAmount && (
          <Text style={styles.errorText}>{errors.interestAmount}</Text>
        )}
      </View>

      {/* Payment Date */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.paymentDate')} *
        </Text>
        <TouchableOpacity
          style={[
            styles.dateInput,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
          onPress={() => setShowDatePicker(true)}
        >
          <Text style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}>
            {paymentDate.toLocaleDateString()}
          </Text>
          <Ionicons
            name="calendar-outline"
            size={20}
            color={isDarkMode ? '#FFFFFF' : '#000000'}
          />
        </TouchableOpacity>
        {showDatePicker && (
          <DateTimePicker
            value={paymentDate}
            mode="date"
            display="default"
            onChange={(event, selectedDate) => {
              setShowDatePicker(false);
              if (selectedDate) {
                setPaymentDate(selectedDate);
              }
            }}
          />
        )}
      </View>

      {/* Payment Method */}
      <View style={styles.formGroup}>
        <View style={styles.labelRow}>
          <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('loans.paymentMethod')}
          </Text>
          <Text style={[styles.optionalText, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            ({t('common.optional')})
          </Text>
        </View>
        <TextInput
          style={[
            styles.input,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
          ]}
          value={paymentMethod}
          onChangeText={setPaymentMethod}
          placeholder={t('loans.paymentMethod')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
        />
      </View>

      {/* Is Scheduled */}
      <View style={styles.formGroup}>
        <View style={styles.switchContainer}>
          <Text style={[styles.switchLabel, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('common.scheduled')}
          </Text>
          <Switch
            value={isScheduled}
            onValueChange={setIsScheduled}
            trackColor={{ false: '#767577', true: isDarkMode ? '#0366D6' : '#0366D6' }}
            thumbColor={isScheduled ? '#FFFFFF' : '#f4f3f4'}
          />
        </View>
      </View>

      {/* Notes */}
      <View style={styles.formGroup}>
        <View style={styles.labelRow}>
          <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('loans.notes')}
          </Text>
          <Text style={[styles.optionalText, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            ({t('common.optional')})
          </Text>
        </View>
        <TextInput
          style={[
            styles.textArea,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
          ]}
          value={notes}
          onChangeText={setNotes}
          placeholder={t('loans.notes')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          multiline
          numberOfLines={4}
          textAlignVertical="top"
        />
      </View>

      {/* Submit Button */}
      <TouchableOpacity
        style={[
          styles.submitButton,
          { backgroundColor: isDarkMode ? '#238636' : '#2DA44E' },
          isLoading && { opacity: 0.7 },
        ]}
        onPress={handleSubmit}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator size="small" color="#FFFFFF" />
        ) : (
          <Text style={styles.submitButtonText}>{t('loans.addPayment')}</Text>
        )}
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loanInfoContainer: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  loanTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  loanAmount: {
    fontSize: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  labelRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  optionalText: {
    fontSize: 14,
    marginLeft: 8,
  },
  input: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#F85149',
  },
  errorText: {
    color: '#F85149',
    fontSize: 12,
    marginTop: 4,
  },
  dateInput: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  textArea: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    minHeight: 100,
  },
  submitButton: {
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default AddPaymentScreen;
