import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useLoans } from '../../contexts/LoanContext';
import { Loan, LoanPayment, LoanReminder } from '../../models/Loan';
import { formatCurrency, formatDate } from '../../utils/formatters';

// Define interfaces for our placeholder components
interface TabItem {
  key: string;
  title: string;
}

interface TabViewProps {
  tabs: TabItem[];
  activeTab: string;
  onTabChange: (key: string) => void;
  isDarkMode: boolean;
}

interface PaymentListProps {
  payments: LoanPayment[];
  onPaymentPress: (payment: LoanPayment) => void;
  onAddPayment: () => void;
  isDarkMode: boolean;
}

interface ReminderListProps {
  reminders: LoanReminder[];
  onReminderPress: (reminder: LoanReminder) => void;
  onAddReminder: () => void;
  isDarkMode: boolean;
}

interface LoanSummaryProps {
  loan: Loan;
  isDarkMode: boolean;
}

// Placeholder components until the actual components are properly linked
const TabView: React.FC<TabViewProps> = ({ tabs, activeTab, onTabChange, isDarkMode }) => (
  <View style={{ flexDirection: 'row', borderBottomWidth: 1, borderBottomColor: '#30363D' }}>
    {tabs.map((tab) => (
      <TouchableOpacity
        key={tab.key}
        style={{
          padding: 12,
          borderBottomWidth: 2,
          borderBottomColor: activeTab === tab.key ? (isDarkMode ? '#58A6FF' : '#0366D6') : 'transparent'
        }}
        onPress={() => onTabChange(tab.key)}
      >
        <Text style={{
          color: activeTab === tab.key
            ? (isDarkMode ? '#58A6FF' : '#0366D6')
            : (isDarkMode ? '#8B949E' : '#6E7781'),
          fontWeight: activeTab === tab.key ? 'bold' : 'normal'
        }}>
          {tab.title}
        </Text>
      </TouchableOpacity>
    ))}
  </View>
);

// Placeholder for PaymentList
const PaymentList: React.FC<PaymentListProps> = ({ isDarkMode }) => (
  <View style={{ padding: 16 }}>
    <Text style={{ color: isDarkMode ? '#FFFFFF' : '#000000', fontSize: 18, fontWeight: 'bold' }}>
      Payments (Coming Soon)
    </Text>
  </View>
);

// Placeholder for ReminderList
const ReminderList: React.FC<ReminderListProps> = ({ isDarkMode }) => (
  <View style={{ padding: 16 }}>
    <Text style={{ color: isDarkMode ? '#FFFFFF' : '#000000', fontSize: 18, fontWeight: 'bold' }}>
      Reminders (Coming Soon)
    </Text>
  </View>
);

// Placeholder for LoanSummary
const LoanSummary: React.FC<LoanSummaryProps> = ({ loan, isDarkMode }) => (
  <View style={{ padding: 16 }}>
    <Text style={{ color: isDarkMode ? '#FFFFFF' : '#000000', fontSize: 18, fontWeight: 'bold' }}>
      Loan Summary (Coming Soon)
    </Text>
    <Text style={{ color: isDarkMode ? '#FFFFFF' : '#000000', marginTop: 8 }}>
      Amount: {formatCurrency(loan.amount)}
    </Text>
    <Text style={{ color: isDarkMode ? '#FFFFFF' : '#000000', marginTop: 4 }}>
      Interest Rate: {loan.interestRate}%
    </Text>
  </View>
);

interface RouteParams {
  loanId: number;
}

const LoanDetailsScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const route = useRoute();
  const navigation = useNavigation<any>();
  const { loanId } = route.params as RouteParams;

  const { getLoanById, refreshLoans, deleteLoan, getPaymentsByLoanId, getRemindersByLoanId } = useLoans();

  const [loan, setLoan] = useState<Loan | undefined>(undefined);
  const [payments, setPayments] = useState<LoanPayment[]>([]);
  const [reminders, setReminders] = useState<LoanReminder[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('summary');

  // Load loan details
  useEffect(() => {
    const loadLoanDetails = async () => {
      setIsLoading(true);
      try {
        await refreshLoans();
        const loanData = getLoanById(loanId);
        setLoan(loanData);

        if (loanData) {
          const paymentsData = await getPaymentsByLoanId(loanId);
          setPayments(paymentsData);

          const remindersData = await getRemindersByLoanId(loanId);
          setReminders(remindersData);
        }
      } catch (error) {
        console.error('Error loading loan details:', error);
        Alert.alert(t('common.error'), t('loans.loanNotFound'));
      } finally {
        setIsLoading(false);
      }
    };

    loadLoanDetails();
  }, [loanId]);

  // Handle edit loan
  const handleEditLoan = () => {
    navigation.navigate('EditLoan', { loanId });
  };

  // Handle delete loan
  const handleDeleteLoan = () => {
    Alert.alert(
      t('loans.deleteLoan'),
      t('loans.deleteLoanConfirmation'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await deleteLoan(loanId);
              if (success) {
                navigation.goBack();
              }
            } catch (error) {
              console.error('Error deleting loan:', error);
              Alert.alert(t('common.error'), t('loans.deleteLoanError'));
            }
          },
        },
      ]
    );
  };

  // Handle view timeline
  const handleViewTimeline = () => {
    navigation.navigate('LoanTimeline', { loanId });
  };

  // Handle add payment
  const handleAddPayment = () => {
    navigation.navigate('AddPayment', { loanId });
  };

  // Handle add reminder
  const handleAddReminder = () => {
    navigation.navigate('AddReminder', { loanId });
  };

  // Handle payment press
  const handlePaymentPress = (payment: LoanPayment) => {
    navigation.navigate('EditPayment', { paymentId: payment.id, loanId });
  };

  // Handle reminder press
  const handleReminderPress = (reminder: LoanReminder) => {
    navigation.navigate('EditReminder', { reminderId: reminder.id, loanId });
  };

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
      </View>
    );
  }

  if (!loan) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <Ionicons name="alert-circle-outline" size={48} color={isDarkMode ? '#F85149' : '#CF222E'} />
        <Text style={[styles.errorText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.loanNotFound')}
        </Text>
        <TouchableOpacity
          style={[styles.button, { backgroundColor: isDarkMode ? '#58A6FF' : '#0366D6' }]}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.buttonText}>{t('common.back')}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Determine if the loan is given or taken
  const isLoanGiven = loan.lenderUserId !== null;
  const isLoanTaken = loan.borrowerUserId !== null;
  const isExternalEntity = loan.isExternalEntity;

  // Get loan type label
  const getLoanTypeLabel = () => {
    if (isLoanGiven) {
      return t('loans.loanTypes.given');
    } else if (isLoanTaken) {
      return t('loans.loanTypes.taken');
    } else {
      return t('loans.loanTypes.external');
    }
  };

  // Format loan status
  const formatStatus = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return t('loans.statusOptions.active');
      case 'completed':
        return t('loans.statusOptions.completed');
      case 'defaulted':
        return t('loans.statusOptions.defaulted');
      default:
        return status;
    }
  };

  // Get counterparty name
  const getCounterpartyName = () => {
    if (isExternalEntity) {
      return loan.externalEntityName || '';
    } else if (isLoanGiven) {
      return loan.borrowerUsername || '';
    } else {
      return loan.lenderUsername || '';
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {loan.title}
          </Text>
          <View style={styles.badgeContainer}>
            <View
              style={[
                styles.statusBadge,
                {
                  backgroundColor:
                    loan.status.toLowerCase() === 'active'
                      ? isDarkMode ? '#238636' : '#2DA44E'
                      : loan.status.toLowerCase() === 'completed'
                      ? isDarkMode ? '#58A6FF' : '#0366D6'
                      : isDarkMode ? '#F85149' : '#CF222E',
                },
              ]}
            >
              <Text style={styles.statusText}>{formatStatus(loan.status)}</Text>
            </View>
            <View
              style={[
                styles.typeBadge,
                {
                  backgroundColor: isDarkMode ? '#30363D' : '#E1E4E8',
                },
              ]}
            >
              <Text
                style={[
                  styles.typeText,
                  { color: isDarkMode ? '#FFFFFF' : '#000000' },
                ]}
              >
                {getLoanTypeLabel()}
              </Text>
            </View>
          </View>
        </View>
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: isDarkMode ? '#30363D' : '#E1E4E8' }]}
            onPress={handleEditLoan}
          >
            <Ionicons name="pencil-outline" size={20} color={isDarkMode ? '#FFFFFF' : '#000000'} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: isDarkMode ? '#30363D' : '#E1E4E8' }]}
            onPress={handleDeleteLoan}
          >
            <Ionicons name="trash-outline" size={20} color={isDarkMode ? '#F85149' : '#CF222E'} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Quick actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity
          style={[styles.quickActionButton, { backgroundColor: isDarkMode ? '#30363D' : '#E1E4E8' }]}
          onPress={handleViewTimeline}
        >
          <Ionicons name="time-outline" size={20} color={isDarkMode ? '#FFFFFF' : '#000000'} />
          <Text style={[styles.quickActionText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('loans.viewTimeline')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.quickActionButton, { backgroundColor: isDarkMode ? '#30363D' : '#E1E4E8' }]}
          onPress={handleAddPayment}
        >
          <Ionicons name="cash-outline" size={20} color={isDarkMode ? '#FFFFFF' : '#000000'} />
          <Text style={[styles.quickActionText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('loans.makePayment')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.quickActionButton, { backgroundColor: isDarkMode ? '#30363D' : '#E1E4E8' }]}
          onPress={handleAddReminder}
        >
          <Ionicons name="notifications-outline" size={20} color={isDarkMode ? '#FFFFFF' : '#000000'} />
          <Text style={[styles.quickActionText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('loans.setReminder')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Tab view */}
      <TabView
        tabs={[
          { key: 'summary', title: t('loans.summary') },
          { key: 'payments', title: t('loans.payments') },
          { key: 'reminders', title: t('loans.reminders') },
        ]}
        activeTab={activeTab}
        onTabChange={setActiveTab}
        isDarkMode={isDarkMode}
      />

      {/* Tab content */}
      <ScrollView style={styles.content}>
        {activeTab === 'summary' && (
          <LoanSummary loan={loan} isDarkMode={isDarkMode} />
        )}
        {activeTab === 'payments' && (
          <PaymentList
            payments={payments}
            onPaymentPress={handlePaymentPress}
            onAddPayment={handleAddPayment}
            isDarkMode={isDarkMode}
          />
        )}
        {activeTab === 'reminders' && (
          <ReminderList
            reminders={reminders}
            onReminderPress={handleReminderPress}
            onAddReminder={handleAddReminder}
            isDarkMode={isDarkMode}
          />
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 18,
    textAlign: 'center',
    marginVertical: 16,
  },
  button: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  badgeContainer: {
    flexDirection: 'row',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  typeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  typeText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  actionsContainer: {
    flexDirection: 'row',
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  quickActions: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  quickActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  quickActionText: {
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  content: {
    flex: 1,
  },
});

export default LoanDetailsScreen;
