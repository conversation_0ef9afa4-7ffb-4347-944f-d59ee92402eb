import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useLoans } from '../../contexts/LoanContext';
import { InterestType, FrequencyType } from '../../models/Loan';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import { formatCurrency } from '../../utils/formatters';

const EMICalculatorScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();
  const { calculateEMI, getInterestTypeOptions, getFrequencyTypeOptions } = useLoans();

  // Form state
  const [principal, setPrincipal] = useState('');
  const [interestRate, setInterestRate] = useState('');
  const [interestType, setInterestType] = useState<InterestType>(InterestType.ReducingBalance);
  const [paymentFrequency, setPaymentFrequency] = useState<FrequencyType>(FrequencyType.Monthly);
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState<Date | null>(null);
  
  // UI state
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [isCalculating, setIsCalculating] = useState(false);
  const [calculatedEMI, setCalculatedEMI] = useState<number | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get options for dropdowns
  const interestTypeOptions = getInterestTypeOptions();
  const frequencyTypeOptions = getFrequencyTypeOptions();

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!principal.trim()) {
      newErrors.principal = t('validation.required');
    } else if (isNaN(parseFloat(principal)) || parseFloat(principal) <= 0) {
      newErrors.principal = t('validation.positive');
    }

    if (!interestRate.trim()) {
      newErrors.interestRate = t('validation.required');
    } else if (
      isNaN(parseFloat(interestRate)) ||
      parseFloat(interestRate) < 0 ||
      parseFloat(interestRate) > 100
    ) {
      newErrors.interestRate = t('validation.percentageRange');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle calculate EMI
  const handleCalculate = async () => {
    if (!validateForm()) {
      Alert.alert(t('common.error'), t('validation.fixErrors'));
      return;
    }

    setIsCalculating(true);
    try {
      const emi = await calculateEMI({
        principal: parseFloat(principal),
        interestRate: parseFloat(interestRate),
        interestType,
        paymentFrequency,
        startDate: startDate.toISOString(),
        endDate: endDate ? endDate.toISOString() : undefined,
      });
      
      setCalculatedEMI(emi);
    } catch (error) {
      console.error('Error calculating EMI:', error);
      Alert.alert(t('common.error'), t('common.unexpectedError'));
    } finally {
      setIsCalculating(false);
    }
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}
      contentContainerStyle={styles.contentContainer}
    >
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.emiCalculator')}
        </Text>
        <Text style={[styles.headerSubtitle, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
          {t('loans.calculateEMI')}
        </Text>
      </View>

      {/* Principal */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.amount')} *
        </Text>
        <TextInput
          style={[
            styles.input,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
            errors.principal && styles.inputError,
          ]}
          value={principal}
          onChangeText={setPrincipal}
          placeholder="0.00"
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          keyboardType="numeric"
        />
        {errors.principal && (
          <Text style={styles.errorText}>{errors.principal}</Text>
        )}
      </View>

      {/* Interest Rate */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.interestRate')} (%) *
        </Text>
        <TextInput
          style={[
            styles.input,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
            errors.interestRate && styles.inputError,
          ]}
          value={interestRate}
          onChangeText={setInterestRate}
          placeholder="0.00"
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          keyboardType="numeric"
        />
        {errors.interestRate && (
          <Text style={styles.errorText}>{errors.interestRate}</Text>
        )}
      </View>

      {/* Interest Type */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.interestType')} *
        </Text>
        <View
          style={[
            styles.pickerContainer,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
        >
          <Picker
            selectedValue={interestType}
            onValueChange={(itemValue) => setInterestType(itemValue as InterestType)}
            style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}
            dropdownIconColor={isDarkMode ? '#FFFFFF' : '#000000'}
          >
            {interestTypeOptions.map((option) => (
              <Picker.Item
                key={option.value}
                label={option.label}
                value={option.value}
              />
            ))}
          </Picker>
        </View>
      </View>

      {/* Payment Frequency */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.paymentFrequency')} *
        </Text>
        <View
          style={[
            styles.pickerContainer,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
        >
          <Picker
            selectedValue={paymentFrequency}
            onValueChange={(itemValue) => setPaymentFrequency(itemValue as FrequencyType)}
            style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}
            dropdownIconColor={isDarkMode ? '#FFFFFF' : '#000000'}
          >
            {frequencyTypeOptions.map((option) => (
              <Picker.Item
                key={option.value}
                label={option.label}
                value={option.value}
              />
            ))}
          </Picker>
        </View>
      </View>

      {/* Start Date */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.startDate')} *
        </Text>
        <TouchableOpacity
          style={[
            styles.dateInput,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
          onPress={() => setShowStartDatePicker(true)}
        >
          <Text style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}>
            {startDate.toLocaleDateString()}
          </Text>
          <Ionicons
            name="calendar-outline"
            size={20}
            color={isDarkMode ? '#FFFFFF' : '#000000'}
          />
        </TouchableOpacity>
        {showStartDatePicker && (
          <DateTimePicker
            value={startDate}
            mode="date"
            display="default"
            onChange={(event, selectedDate) => {
              setShowStartDatePicker(false);
              if (selectedDate) {
                setStartDate(selectedDate);
              }
            }}
          />
        )}
      </View>

      {/* End Date (Optional) */}
      <View style={styles.formGroup}>
        <View style={styles.labelRow}>
          <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('loans.endDate')}
          </Text>
          <Text style={[styles.optionalText, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            ({t('common.optional')})
          </Text>
        </View>
        <TouchableOpacity
          style={[
            styles.dateInput,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
          onPress={() => setShowEndDatePicker(true)}
        >
          <Text style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}>
            {endDate ? endDate.toLocaleDateString() : t('common.none')}
          </Text>
          <Ionicons
            name="calendar-outline"
            size={20}
            color={isDarkMode ? '#FFFFFF' : '#000000'}
          />
        </TouchableOpacity>
        {showEndDatePicker && (
          <DateTimePicker
            value={endDate || new Date()}
            mode="date"
            display="default"
            onChange={(event, selectedDate) => {
              setShowEndDatePicker(false);
              if (selectedDate) {
                setEndDate(selectedDate);
              }
            }}
          />
        )}
      </View>

      {/* Calculate Button */}
      <TouchableOpacity
        style={[
          styles.calculateButton,
          { backgroundColor: isDarkMode ? '#58A6FF' : '#0366D6' },
          isCalculating && { opacity: 0.7 },
        ]}
        onPress={handleCalculate}
        disabled={isCalculating}
      >
        {isCalculating ? (
          <ActivityIndicator size="small" color="#FFFFFF" />
        ) : (
          <Text style={styles.calculateButtonText}>{t('loans.calculateEMI')}</Text>
        )}
      </TouchableOpacity>

      {/* Result */}
      {calculatedEMI !== null && (
        <View
          style={[
            styles.resultContainer,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
        >
          <Text style={[styles.resultLabel, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('loans.emiResult')}
          </Text>
          <Text style={[styles.resultValue, { color: isDarkMode ? '#58A6FF' : '#0366D6' }]}>
            {formatCurrency(calculatedEMI)}
          </Text>
          
          <View style={styles.resultDetails}>
            <View style={styles.resultDetailItem}>
              <Text style={[styles.resultDetailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('loans.amount')}
              </Text>
              <Text style={[styles.resultDetailValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {formatCurrency(parseFloat(principal))}
              </Text>
            </View>
            
            <View style={styles.resultDetailItem}>
              <Text style={[styles.resultDetailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('loans.interestRate')}
              </Text>
              <Text style={[styles.resultDetailValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {interestRate}%
              </Text>
            </View>
            
            <View style={styles.resultDetailItem}>
              <Text style={[styles.resultDetailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('loans.interestType')}
              </Text>
              <Text style={[styles.resultDetailValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {interestTypeOptions.find(option => option.value === interestType)?.label}
              </Text>
            </View>
            
            <View style={styles.resultDetailItem}>
              <Text style={[styles.resultDetailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('loans.paymentFrequency')}
              </Text>
              <Text style={[styles.resultDetailValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {frequencyTypeOptions.find(option => option.value === paymentFrequency)?.label}
              </Text>
            </View>
          </View>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  labelRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  optionalText: {
    fontSize: 14,
    marginLeft: 8,
  },
  input: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#F85149',
  },
  errorText: {
    color: '#F85149',
    fontSize: 12,
    marginTop: 4,
  },
  pickerContainer: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  dateInput: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  calculateButton: {
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  calculateButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  resultContainer: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
  },
  resultLabel: {
    fontSize: 16,
    marginBottom: 8,
  },
  resultValue: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  resultDetails: {
    borderTopWidth: 1,
    borderTopColor: '#30363D',
    paddingTop: 16,
  },
  resultDetailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  resultDetailLabel: {
    fontSize: 14,
  },
  resultDetailValue: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default EMICalculatorScreen;
