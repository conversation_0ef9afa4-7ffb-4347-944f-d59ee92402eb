import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Switch,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useLoans } from '../../contexts/LoanContext';
import { useAuth } from '../../contexts/AuthContext';
import { InterestType, FrequencyType } from '../../models/Loan';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import { formatCurrency } from '../../utils/formatters';

const CreateLoanScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();
  const { createLoan, calculateEMI, getInterestTypeOptions, getFrequencyTypeOptions } = useLoans();
  const { user } = useAuth();

  // Form state
  const [title, setTitle] = useState('');
  const [amount, setAmount] = useState('');
  const [interestRate, setInterestRate] = useState('');
  const [interestType, setInterestType] = useState<InterestType>(InterestType.ReducingBalance);
  const [paymentFrequency, setPaymentFrequency] = useState<FrequencyType>(FrequencyType.Monthly);
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [loanType, setLoanType] = useState('given'); // 'given', 'taken', 'external'
  const [externalEntityName, setExternalEntityName] = useState('');
  const [notes, setNotes] = useState('');
  
  // UI state
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [isCalculatingEMI, setIsCalculatingEMI] = useState(false);
  const [calculatedEMI, setCalculatedEMI] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get options for dropdowns
  const interestTypeOptions = getInterestTypeOptions();
  const frequencyTypeOptions = getFrequencyTypeOptions();

  // Calculate EMI when form values change
  useEffect(() => {
    const calculateEMIValue = async () => {
      if (
        amount &&
        !isNaN(parseFloat(amount)) &&
        interestRate &&
        !isNaN(parseFloat(interestRate))
      ) {
        setIsCalculatingEMI(true);
        try {
          const emi = await calculateEMI({
            principal: parseFloat(amount),
            interestRate: parseFloat(interestRate),
            interestType,
            paymentFrequency,
            startDate: startDate.toISOString(),
            endDate: endDate ? endDate.toISOString() : undefined,
          });
          setCalculatedEMI(emi);
        } catch (error) {
          console.error('Error calculating EMI:', error);
        } finally {
          setIsCalculatingEMI(false);
        }
      } else {
        setCalculatedEMI(null);
      }
    };

    calculateEMIValue();
  }, [amount, interestRate, interestType, paymentFrequency, startDate, endDate]);

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!title.trim()) {
      newErrors.title = t('validation.required');
    }

    if (!amount.trim()) {
      newErrors.amount = t('validation.required');
    } else if (isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      newErrors.amount = t('validation.positive');
    }

    if (!interestRate.trim()) {
      newErrors.interestRate = t('validation.required');
    } else if (
      isNaN(parseFloat(interestRate)) ||
      parseFloat(interestRate) < 0 ||
      parseFloat(interestRate) > 100
    ) {
      newErrors.interestRate = t('validation.percentageRange');
    }

    if (loanType === 'external' && !externalEntityName.trim()) {
      newErrors.externalEntityName = t('validation.required');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      Alert.alert(t('common.error'), t('validation.fixErrors'));
      return;
    }

    setIsSubmitting(true);
    try {
      const loanData = {
        title,
        amount: parseFloat(amount),
        interestRate: parseFloat(interestRate),
        interestType,
        paymentFrequency,
        startDate: startDate.toISOString(),
        endDate: endDate ? endDate.toISOString() : undefined,
        lenderUserId: loanType === 'given' ? user?.id : undefined,
        borrowerUserId: loanType === 'taken' ? user?.id : undefined,
        isExternalEntity: loanType === 'external',
        externalEntityName: loanType === 'external' ? externalEntityName : undefined,
        status: 'Active',
        notes,
      };

      const result = await createLoan(loanData);
      if (result) {
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error creating loan:', error);
      Alert.alert(t('common.error'), t('loans.invalidLoanData'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}
      contentContainerStyle={styles.contentContainer}
    >
      {/* Title */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.title')} *
        </Text>
        <TextInput
          style={[
            styles.input,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
            errors.title && styles.inputError,
          ]}
          value={title}
          onChangeText={setTitle}
          placeholder={t('loans.title')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
        />
        {errors.title && (
          <Text style={styles.errorText}>{errors.title}</Text>
        )}
      </View>

      {/* Amount */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.amount')} *
        </Text>
        <TextInput
          style={[
            styles.input,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
            errors.amount && styles.inputError,
          ]}
          value={amount}
          onChangeText={setAmount}
          placeholder="0.00"
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          keyboardType="numeric"
        />
        {errors.amount && (
          <Text style={styles.errorText}>{errors.amount}</Text>
        )}
      </View>

      {/* Interest Rate */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.interestRate')} (%) *
        </Text>
        <TextInput
          style={[
            styles.input,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
            errors.interestRate && styles.inputError,
          ]}
          value={interestRate}
          onChangeText={setInterestRate}
          placeholder="0.00"
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          keyboardType="numeric"
        />
        {errors.interestRate && (
          <Text style={styles.errorText}>{errors.interestRate}</Text>
        )}
      </View>

      {/* Interest Type */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.interestType')} *
        </Text>
        <View
          style={[
            styles.pickerContainer,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
        >
          <Picker
            selectedValue={interestType}
            onValueChange={(itemValue) => setInterestType(itemValue as InterestType)}
            style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}
            dropdownIconColor={isDarkMode ? '#FFFFFF' : '#000000'}
          >
            {interestTypeOptions.map((option) => (
              <Picker.Item
                key={option.value}
                label={option.label}
                value={option.value}
              />
            ))}
          </Picker>
        </View>
      </View>

      {/* Payment Frequency */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.paymentFrequency')} *
        </Text>
        <View
          style={[
            styles.pickerContainer,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
        >
          <Picker
            selectedValue={paymentFrequency}
            onValueChange={(itemValue) => setPaymentFrequency(itemValue as FrequencyType)}
            style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}
            dropdownIconColor={isDarkMode ? '#FFFFFF' : '#000000'}
          >
            {frequencyTypeOptions.map((option) => (
              <Picker.Item
                key={option.value}
                label={option.label}
                value={option.value}
              />
            ))}
          </Picker>
        </View>
      </View>

      {/* Start Date */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.startDate')} *
        </Text>
        <TouchableOpacity
          style={[
            styles.dateInput,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
          onPress={() => setShowStartDatePicker(true)}
        >
          <Text style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}>
            {startDate.toLocaleDateString()}
          </Text>
          <Ionicons
            name="calendar-outline"
            size={20}
            color={isDarkMode ? '#FFFFFF' : '#000000'}
          />
        </TouchableOpacity>
        {showStartDatePicker && (
          <DateTimePicker
            value={startDate}
            mode="date"
            display="default"
            onChange={(event, selectedDate) => {
              setShowStartDatePicker(false);
              if (selectedDate) {
                setStartDate(selectedDate);
              }
            }}
          />
        )}
      </View>

      {/* End Date (Optional) */}
      <View style={styles.formGroup}>
        <View style={styles.labelRow}>
          <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('loans.endDate')}
          </Text>
          <Text style={[styles.optionalText, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            ({t('common.optional')})
          </Text>
        </View>
        <TouchableOpacity
          style={[
            styles.dateInput,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
          onPress={() => setShowEndDatePicker(true)}
        >
          <Text style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}>
            {endDate ? endDate.toLocaleDateString() : t('common.none')}
          </Text>
          <Ionicons
            name="calendar-outline"
            size={20}
            color={isDarkMode ? '#FFFFFF' : '#000000'}
          />
        </TouchableOpacity>
        {showEndDatePicker && (
          <DateTimePicker
            value={endDate || new Date()}
            mode="date"
            display="default"
            onChange={(event, selectedDate) => {
              setShowEndDatePicker(false);
              if (selectedDate) {
                setEndDate(selectedDate);
              }
            }}
          />
        )}
      </View>

      {/* Loan Type */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.loanType')} *
        </Text>
        <View style={styles.loanTypeContainer}>
          <TouchableOpacity
            style={[
              styles.loanTypeButton,
              loanType === 'given' && [
                styles.activeLoanTypeButton,
                { backgroundColor: isDarkMode ? '#238636' : '#2DA44E' },
              ],
              { borderColor: isDarkMode ? '#30363D' : '#E1E4E8' },
            ]}
            onPress={() => setLoanType('given')}
          >
            <Text
              style={[
                styles.loanTypeText,
                loanType === 'given' && styles.activeLoanTypeText,
                { color: loanType === 'given' ? '#FFFFFF' : isDarkMode ? '#FFFFFF' : '#000000' },
              ]}
            >
              {t('loans.loanTypes.given')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.loanTypeButton,
              loanType === 'taken' && [
                styles.activeLoanTypeButton,
                { backgroundColor: isDarkMode ? '#F85149' : '#CF222E' },
              ],
              { borderColor: isDarkMode ? '#30363D' : '#E1E4E8' },
            ]}
            onPress={() => setLoanType('taken')}
          >
            <Text
              style={[
                styles.loanTypeText,
                loanType === 'taken' && styles.activeLoanTypeText,
                { color: loanType === 'taken' ? '#FFFFFF' : isDarkMode ? '#FFFFFF' : '#000000' },
              ]}
            >
              {t('loans.loanTypes.taken')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.loanTypeButton,
              loanType === 'external' && [
                styles.activeLoanTypeButton,
                { backgroundColor: isDarkMode ? '#58A6FF' : '#0366D6' },
              ],
              { borderColor: isDarkMode ? '#30363D' : '#E1E4E8' },
            ]}
            onPress={() => setLoanType('external')}
          >
            <Text
              style={[
                styles.loanTypeText,
                loanType === 'external' && styles.activeLoanTypeText,
                { color: loanType === 'external' ? '#FFFFFF' : isDarkMode ? '#FFFFFF' : '#000000' },
              ]}
            >
              {t('loans.loanTypes.external')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* External Entity Name (if external) */}
      {loanType === 'external' && (
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('loans.externalEntityName')} *
          </Text>
          <TextInput
            style={[
              styles.input,
              { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
              errors.externalEntityName && styles.inputError,
            ]}
            value={externalEntityName}
            onChangeText={setExternalEntityName}
            placeholder={t('loans.externalEntityName')}
            placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          />
          {errors.externalEntityName && (
            <Text style={styles.errorText}>{errors.externalEntityName}</Text>
          )}
        </View>
      )}

      {/* Notes (Optional) */}
      <View style={styles.formGroup}>
        <View style={styles.labelRow}>
          <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('loans.notes')}
          </Text>
          <Text style={[styles.optionalText, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            ({t('common.optional')})
          </Text>
        </View>
        <TextInput
          style={[
            styles.textArea,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
          ]}
          value={notes}
          onChangeText={setNotes}
          placeholder={t('loans.notes')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          multiline
          numberOfLines={4}
          textAlignVertical="top"
        />
      </View>

      {/* EMI Calculation */}
      <View
        style={[
          styles.emiContainer,
          { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
        ]}
      >
        <Text style={[styles.emiTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.emiResult')}
        </Text>
        {isCalculatingEMI ? (
          <ActivityIndicator size="small" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
        ) : calculatedEMI ? (
          <Text style={[styles.emiValue, { color: isDarkMode ? '#58A6FF' : '#0366D6' }]}>
            {formatCurrency(calculatedEMI)}
          </Text>
        ) : (
          <Text style={[styles.emiPlaceholder, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            --
          </Text>
        )}
      </View>

      {/* Submit Button */}
      <TouchableOpacity
        style={[
          styles.submitButton,
          { backgroundColor: isDarkMode ? '#238636' : '#2DA44E' },
          isSubmitting && { opacity: 0.7 },
        ]}
        onPress={handleSubmit}
        disabled={isSubmitting}
      >
        {isSubmitting ? (
          <ActivityIndicator size="small" color="#FFFFFF" />
        ) : (
          <Text style={styles.submitButtonText}>{t('loans.addLoan')}</Text>
        )}
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  labelRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  optionalText: {
    fontSize: 14,
    marginLeft: 8,
  },
  input: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#F85149',
  },
  errorText: {
    color: '#F85149',
    fontSize: 12,
    marginTop: 4,
  },
  pickerContainer: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  dateInput: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  textArea: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    minHeight: 100,
  },
  loanTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  loanTypeButton: {
    flex: 1,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  activeLoanTypeButton: {
    borderWidth: 0,
  },
  loanTypeText: {
    fontWeight: 'bold',
  },
  activeLoanTypeText: {
    color: '#FFFFFF',
  },
  emiContainer: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    alignItems: 'center',
  },
  emiTitle: {
    fontSize: 16,
    marginBottom: 8,
  },
  emiValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  emiPlaceholder: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  submitButton: {
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default CreateLoanScreen;
