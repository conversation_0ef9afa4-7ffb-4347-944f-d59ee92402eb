import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Switch,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useLoans } from '../../contexts/LoanContext';
import DateTimePicker from '@react-native-community/datetimepicker';

interface RouteParams {
  paymentId: number;
  loanId: number;
}

const EditPaymentScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const route = useRoute();
  const navigation = useNavigation<any>();
  const { paymentId, loanId } = route.params as RouteParams;
  
  const { getLoanById, getPaymentById, updatePayment, deletePayment } = useLoans();
  
  // Form state
  const [amount, setAmount] = useState('');
  const [principalAmount, setPrincipalAmount] = useState('');
  const [interestAmount, setInterestAmount] = useState('');
  const [paymentDate, setPaymentDate] = useState(new Date());
  const [paymentMethod, setPaymentMethod] = useState('');
  const [isScheduled, setIsScheduled] = useState(false);
  const [notes, setNotes] = useState('');
  
  // UI state
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loan, setLoan] = useState<any>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Load payment details
  useEffect(() => {
    const loadPayment = async () => {
      setIsLoading(true);
      try {
        const loanData = getLoanById(loanId);
        setLoan(loanData);
        
        const payment = await getPaymentById(paymentId);
        if (payment) {
          setAmount(payment.amount.toString());
          setPrincipalAmount(payment.principalAmount.toString());
          setInterestAmount(payment.interestAmount.toString());
          setPaymentDate(new Date(payment.paymentDate));
          setPaymentMethod(payment.paymentMethod || '');
          setIsScheduled(payment.isScheduled);
          setNotes(payment.notes || '');
        } else {
          Alert.alert(t('common.error'), t('common.itemNotFound'));
          navigation.goBack();
        }
      } catch (error) {
        console.error('Error loading payment:', error);
        Alert.alert(t('common.error'), t('common.unexpectedError'));
        navigation.goBack();
      } finally {
        setIsLoading(false);
      }
    };
    
    loadPayment();
  }, [paymentId, loanId]);

  // Update interest amount when amount and principal amount change
  useEffect(() => {
    if (amount && principalAmount) {
      const calculatedInterest = parseFloat(amount) - parseFloat(principalAmount);
      if (calculatedInterest >= 0) {
        setInterestAmount(calculatedInterest.toFixed(2));
      }
    }
  }, [amount, principalAmount]);

  // Update principal amount when amount and interest amount change
  useEffect(() => {
    if (amount && interestAmount) {
      const calculatedPrincipal = parseFloat(amount) - parseFloat(interestAmount);
      if (calculatedPrincipal >= 0) {
        setPrincipalAmount(calculatedPrincipal.toFixed(2));
      }
    }
  }, [amount, interestAmount]);

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!amount.trim()) {
      newErrors.amount = t('validation.required');
    } else if (isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      newErrors.amount = t('validation.positive');
    }

    if (!principalAmount.trim()) {
      newErrors.principalAmount = t('validation.required');
    } else if (isNaN(parseFloat(principalAmount)) || parseFloat(principalAmount) < 0) {
      newErrors.principalAmount = t('validation.number');
    }

    if (!interestAmount.trim()) {
      newErrors.interestAmount = t('validation.required');
    } else if (isNaN(parseFloat(interestAmount)) || parseFloat(interestAmount) < 0) {
      newErrors.interestAmount = t('validation.number');
    }

    // Check if principal + interest = amount
    const totalAmount = parseFloat(principalAmount) + parseFloat(interestAmount);
    if (!isNaN(totalAmount) && !isNaN(parseFloat(amount)) && Math.abs(totalAmount - parseFloat(amount)) > 0.01) {
      newErrors.amount = t('loans.invalidPaymentData');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      Alert.alert(t('common.error'), t('validation.fixErrors'));
      return;
    }

    setIsSubmitting(true);
    try {
      const paymentData = {
        id: paymentId,
        loanId,
        amount: parseFloat(amount),
        principalAmount: parseFloat(principalAmount),
        interestAmount: parseFloat(interestAmount),
        paymentDate: paymentDate.toISOString(),
        paymentMethod,
        isScheduled,
        notes,
      };

      const result = await updatePayment(paymentData);
      if (result) {
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error updating payment:', error);
      Alert.alert(t('common.error'), t('loans.invalidPaymentData'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete payment
  const handleDelete = () => {
    Alert.alert(
      t('loans.deletePayment'),
      t('loans.deletePaymentConfirmation'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await deletePayment(paymentId);
              if (success) {
                navigation.goBack();
              }
            } catch (error) {
              console.error('Error deleting payment:', error);
              Alert.alert(t('common.error'), t('loans.deletePaymentError'));
            }
          },
        },
      ]
    );
  };

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
      </View>
    );
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}
      contentContainerStyle={styles.contentContainer}
    >
      {/* Header with delete button */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.editPayment')}
        </Text>
        <TouchableOpacity
          style={[styles.deleteButton, { backgroundColor: isDarkMode ? '#F85149' : '#CF222E' }]}
          onPress={handleDelete}
        >
          <Ionicons name="trash-outline" size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Loan Info */}
      <View style={[styles.loanInfoContainer, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
        <Text style={[styles.loanTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {loan?.title}
        </Text>
        <Text style={[styles.loanAmount, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
          {t('loans.remainingAmount')}: {loan?.remainingAmount.toLocaleString('en-US', { style: 'currency', currency: 'NPR' })}
        </Text>
      </View>

      {/* Amount */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.paymentAmount')} *
        </Text>
        <TextInput
          style={[
            styles.input,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
            errors.amount && styles.inputError,
          ]}
          value={amount}
          onChangeText={setAmount}
          placeholder="0.00"
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          keyboardType="numeric"
        />
        {errors.amount && (
          <Text style={styles.errorText}>{errors.amount}</Text>
        )}
      </View>

      {/* Principal Amount */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.principalAmount')} *
        </Text>
        <TextInput
          style={[
            styles.input,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
            errors.principalAmount && styles.inputError,
          ]}
          value={principalAmount}
          onChangeText={setPrincipalAmount}
          placeholder="0.00"
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          keyboardType="numeric"
        />
        {errors.principalAmount && (
          <Text style={styles.errorText}>{errors.principalAmount}</Text>
        )}
      </View>

      {/* Submit Button */}
      <TouchableOpacity
        style={[
          styles.submitButton,
          { backgroundColor: isDarkMode ? '#238636' : '#2DA44E' },
          isSubmitting && { opacity: 0.7 },
        ]}
        onPress={handleSubmit}
        disabled={isSubmitting}
      >
        {isSubmitting ? (
          <ActivityIndicator size="small" color="#FFFFFF" />
        ) : (
          <Text style={styles.submitButtonText}>{t('loans.editPayment')}</Text>
        )}
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  deleteButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loanInfoContainer: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  loanTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  loanAmount: {
    fontSize: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  labelRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  optionalText: {
    fontSize: 14,
    marginLeft: 8,
  },
  input: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#F85149',
  },
  errorText: {
    color: '#F85149',
    fontSize: 12,
    marginTop: 4,
  },
  dateInput: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  textArea: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    minHeight: 100,
  },
  submitButton: {
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default EditPaymentScreen;
