import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Dimensions,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useLoans } from '../../contexts/LoanContext';
import { formatCurrency, formatDate } from '../../utils/formatters';
import { LineChart } from 'react-native-chart-kit';

interface RouteParams {
  loanId: number;
}

const LoanTimelineScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const route = useRoute();
  const navigation = useNavigation<any>();
  const { loanId } = route.params as RouteParams;
  
  const { getLoanById, getLoanTimeline } = useLoans();
  
  const [isLoading, setIsLoading] = useState(true);
  const [timelineData, setTimelineData] = useState<any>(null);
  const [chartData, setChartData] = useState<any>(null);
  const [loan, setLoan] = useState<any>(null);

  // Load timeline data
  useEffect(() => {
    const loadTimelineData = async () => {
      setIsLoading(true);
      try {
        const loanData = getLoanById(loanId);
        setLoan(loanData);
        
        if (loanData) {
          const timeline = await getLoanTimeline(loanId);
          setTimelineData(timeline);
          
          // Prepare chart data
          if (timeline && timeline.timeline) {
            prepareChartData(timeline.timeline);
          }
        }
      } catch (error) {
        console.error('Error loading timeline data:', error);
        Alert.alert(t('common.error'), t('common.unexpectedError'));
      } finally {
        setIsLoading(false);
      }
    };
    
    loadTimelineData();
  }, [loanId]);

  // Prepare chart data
  const prepareChartData = (timeline: Record<string, number>) => {
    // Sort dates
    const sortedDates = Object.keys(timeline).sort((a, b) => {
      return new Date(a).getTime() - new Date(b).getTime();
    });
    
    // Take at most 12 points for better visualization
    const step = Math.max(1, Math.floor(sortedDates.length / 12));
    const sampledDates = sortedDates.filter((_, index) => index % step === 0);
    
    // Prepare labels and data points
    const labels = sampledDates.map(date => {
      const d = new Date(date);
      return `${d.getMonth() + 1}/${d.getFullYear().toString().substr(2, 2)}`;
    });
    
    const dataPoints = sampledDates.map(date => timeline[date]);
    
    // Calculate cumulative payments
    let cumulativeData = [];
    let sum = 0;
    for (const amount of dataPoints) {
      sum += amount;
      cumulativeData.push(sum);
    }
    
    setChartData({
      labels,
      datasets: [
        {
          data: cumulativeData,
          color: (opacity = 1) => isDarkMode ? `rgba(88, 166, 255, ${opacity})` : `rgba(3, 102, 214, ${opacity})`,
        },
      ],
    });
  };

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
      </View>
    );
  }

  if (!timelineData || !loan) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <Ionicons name="alert-circle-outline" size={48} color={isDarkMode ? '#F85149' : '#CF222E'} />
        <Text style={[styles.errorText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('common.unexpectedError')}
        </Text>
        <TouchableOpacity
          style={[styles.button, { backgroundColor: isDarkMode ? '#58A6FF' : '#0366D6' }]}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.buttonText}>{t('common.back')}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      {/* Loan Summary */}
      <View style={[styles.summaryContainer, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
        <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {loan.title}
        </Text>
        
        <View style={styles.summaryRow}>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('loans.totalPayable')}
            </Text>
            <Text style={[styles.summaryValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {formatCurrency(timelineData.totalAmount)}
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('loans.paidAmount')}
            </Text>
            <Text style={[styles.summaryValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {formatCurrency(timelineData.paidAmount)}
            </Text>
          </View>
        </View>
        
        <View style={styles.summaryRow}>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('loans.remainingAmount')}
            </Text>
            <Text style={[styles.summaryValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {formatCurrency(timelineData.remainingAmount)}
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('loans.predictedCompletionDate')}
            </Text>
            <Text style={[styles.summaryValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {timelineData.predictedCompletionDate ? formatDate(timelineData.predictedCompletionDate) : '--'}
            </Text>
          </View>
        </View>
        
        <View style={styles.progressContainer}>
          <View style={styles.progressBarBackground}>
            <View
              style={[
                styles.progressBar,
                {
                  width: `${Math.min(100, (timelineData.paidAmount / timelineData.totalAmount) * 100)}%`,
                  backgroundColor: isDarkMode ? '#238636' : '#2DA44E'
                }
              ]}
            />
          </View>
          <Text
            style={[
              styles.progressText,
              { color: isDarkMode ? '#8B949E' : '#6E7781' }
            ]}
          >
            {Math.round((timelineData.paidAmount / timelineData.totalAmount) * 100)}% {t('common.completed')}
          </Text>
        </View>
      </View>

      {/* Chart */}
      {chartData && (
        <View style={styles.chartContainer}>
          <Text style={[styles.chartTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('loans.paymentSchedule')}
          </Text>
          <LineChart
            data={chartData}
            width={Dimensions.get('window').width - 32}
            height={220}
            chartConfig={{
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              backgroundGradientFrom: isDarkMode ? '#161B22' : '#F6F8FA',
              backgroundGradientTo: isDarkMode ? '#161B22' : '#F6F8FA',
              decimalPlaces: 0,
              color: (opacity = 1) => isDarkMode ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
              labelColor: (opacity = 1) => isDarkMode ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
              style: {
                borderRadius: 16,
              },
              propsForDots: {
                r: '6',
                strokeWidth: '2',
                stroke: isDarkMode ? '#58A6FF' : '#0366D6',
              },
            }}
            bezier
            style={{
              marginVertical: 8,
              borderRadius: 16,
            }}
          />
        </View>
      )}

      {/* Timeline */}
      <View style={styles.timelineContainer}>
        <Text style={[styles.timelineTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.timeline')}
        </Text>
        
        {Object.keys(timelineData.timeline).length > 0 ? (
          Object.keys(timelineData.timeline)
            .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())
            .map((date, index) => (
              <View
                key={index}
                style={[
                  styles.timelineItem,
                  { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
                ]}
              >
                <View style={styles.timelineDateContainer}>
                  <Text style={[styles.timelineDate, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                    {formatDate(date)}
                  </Text>
                  <View
                    style={[
                      styles.timelineStatus,
                      {
                        backgroundColor: new Date(date) <= new Date()
                          ? isDarkMode ? '#238636' : '#2DA44E'
                          : isDarkMode ? '#8B949E' : '#6E7781',
                      },
                    ]}
                  >
                    <Text style={styles.timelineStatusText}>
                      {new Date(date) <= new Date() ? t('common.paid') : t('common.scheduled')}
                    </Text>
                  </View>
                </View>
                <Text style={[styles.timelineAmount, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                  {formatCurrency(timelineData.timeline[date])}
                </Text>
              </View>
            ))
        ) : (
          <View style={styles.emptyTimelineContainer}>
            <Ionicons
              name="calendar-outline"
              size={48}
              color={isDarkMode ? '#8B949E' : '#6E7781'}
            />
            <Text style={[styles.emptyTimelineText, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('common.noData')}
            </Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 18,
    textAlign: 'center',
    marginVertical: 16,
  },
  button: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  summaryContainer: {
    margin: 16,
    padding: 16,
    borderRadius: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryItem: {
    flex: 1,
  },
  summaryLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  progressContainer: {
    marginTop: 16,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: '#E1E4E8',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressBar: {
    height: '100%',
  },
  progressText: {
    fontSize: 12,
    textAlign: 'right',
  },
  chartContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  timelineContainer: {
    margin: 16,
  },
  timelineTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  timelineItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  timelineDateContainer: {
    flexDirection: 'column',
  },
  timelineDate: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  timelineStatus: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  timelineStatusText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  timelineAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyTimelineContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyTimelineText: {
    fontSize: 16,
    marginTop: 16,
  },
});

export default LoanTimelineScreen;
