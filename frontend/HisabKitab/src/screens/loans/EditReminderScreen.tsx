import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Switch,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useLoans } from '../../contexts/LoanContext';
import DateTimePicker from '@react-native-community/datetimepicker';

interface RouteParams {
  reminderId: number;
  loanId: number;
}

const EditReminderScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const route = useRoute();
  const navigation = useNavigation<any>();
  const { reminderId, loanId } = route.params as RouteParams;

  const { getLoanById, getReminderById, updateReminder, deleteReminder } = useLoans();

  // Form state
  const [reminderDate, setReminderDate] = useState(new Date());
  const [message, setMessage] = useState('');
  const [isActive, setIsActive] = useState(true);

  // UI state
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loan, setLoan] = useState<any>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Load reminder details
  useEffect(() => {
    const loadReminder = async () => {
      setIsLoading(true);
      try {
        const loanData = getLoanById(loanId);
        setLoan(loanData);

        const reminder = await getReminderById(reminderId);
        if (reminder) {
          setReminderDate(new Date(reminder.reminderDate));
          setMessage(reminder.message);
          setIsActive(reminder.isActive);
        } else {
          Alert.alert(t('common.error'), t('common.itemNotFound'));
          navigation.goBack();
        }
      } catch (error) {
        console.error('Error loading reminder:', error);
        Alert.alert(t('common.error'), t('common.unexpectedError'));
        navigation.goBack();
      } finally {
        setIsLoading(false);
      }
    };

    loadReminder();
  }, [reminderId, loanId]);

  // Validate form
  const validateForm = async () => {
    const newErrors: Record<string, string> = {};

    if (!message.trim()) {
      newErrors.message = t('validation.required');
    }

    // Check if reminder date is in the future
    const currentReminder = await getReminderById(reminderId);
    if (reminderDate < new Date() && !currentReminder?.isSent) {
      newErrors.reminderDate = t('validation.futureDate');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    const isValid = await validateForm();
    if (!isValid) {
      Alert.alert(t('common.error'), t('validation.fixErrors'));
      return;
    }

    setIsSubmitting(true);
    try {
      const reminderData = {
        id: reminderId,
        loanId,
        reminderDate: reminderDate.toISOString(),
        message,
        isActive,
      };

      const result = await updateReminder(reminderData);
      if (result) {
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error updating reminder:', error);
      Alert.alert(t('common.error'), t('loans.invalidReminderData'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete reminder
  const handleDelete = () => {
    Alert.alert(
      t('loans.deleteReminder'),
      t('loans.deleteReminderConfirmation'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await deleteReminder(reminderId);
              if (success) {
                navigation.goBack();
              }
            } catch (error) {
              console.error('Error deleting reminder:', error);
              Alert.alert(t('common.error'), t('loans.deleteReminderError'));
            }
          },
        },
      ]
    );
  };

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
      </View>
    );
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}
      contentContainerStyle={styles.contentContainer}
    >
      {/* Header with delete button */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.editReminder')}
        </Text>
        <TouchableOpacity
          style={[styles.deleteButton, { backgroundColor: isDarkMode ? '#F85149' : '#CF222E' }]}
          onPress={handleDelete}
        >
          <Ionicons name="trash-outline" size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Loan Info */}
      <View style={[styles.loanInfoContainer, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
        <Text style={[styles.loanTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {loan?.title}
        </Text>
        <Text style={[styles.loanAmount, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
          {t('loans.remainingAmount')}: {loan?.remainingAmount.toLocaleString('en-US', { style: 'currency', currency: 'NPR' })}
        </Text>
      </View>

      {/* Reminder Date */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.reminderDate')} *
        </Text>
        <TouchableOpacity
          style={[
            styles.dateInput,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
            errors.reminderDate && styles.inputError,
          ]}
          onPress={() => setShowDatePicker(true)}
        >
          <Text style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}>
            {reminderDate.toLocaleDateString()}
          </Text>
          <Ionicons
            name="calendar-outline"
            size={20}
            color={isDarkMode ? '#FFFFFF' : '#000000'}
          />
        </TouchableOpacity>
        {errors.reminderDate && (
          <Text style={styles.errorText}>{errors.reminderDate}</Text>
        )}
        {showDatePicker && (
          <DateTimePicker
            value={reminderDate}
            mode="date"
            display="default"
            onChange={(_, selectedDate) => {
              setShowDatePicker(false);
              if (selectedDate) {
                setReminderDate(selectedDate);
              }
            }}
          />
        )}
      </View>

      {/* Message */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.reminderMessage')} *
        </Text>
        <TextInput
          style={[
            styles.textArea,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
            errors.message && styles.inputError,
          ]}
          value={message}
          onChangeText={setMessage}
          placeholder={t('loans.reminderMessage')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          multiline
          numberOfLines={4}
          textAlignVertical="top"
        />
        {errors.message && (
          <Text style={styles.errorText}>{errors.message}</Text>
        )}
      </View>

      {/* Is Active */}
      <View style={styles.formGroup}>
        <View style={styles.switchContainer}>
          <Text style={[styles.switchLabel, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('loans.isActive')}
          </Text>
          <Switch
            value={isActive}
            onValueChange={setIsActive}
            trackColor={{ false: '#767577', true: isDarkMode ? '#0366D6' : '#0366D6' }}
            thumbColor={isActive ? '#FFFFFF' : '#f4f3f4'}
          />
        </View>
      </View>

      {/* Submit Button */}
      <TouchableOpacity
        style={[
          styles.submitButton,
          { backgroundColor: isDarkMode ? '#238636' : '#2DA44E' },
          isSubmitting && { opacity: 0.7 },
        ]}
        onPress={handleSubmit}
        disabled={isSubmitting}
      >
        {isSubmitting ? (
          <ActivityIndicator size="small" color="#FFFFFF" />
        ) : (
          <Text style={styles.submitButtonText}>{t('loans.editReminder')}</Text>
        )}
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  deleteButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loanInfoContainer: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  loanTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  loanAmount: {
    fontSize: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  dateInput: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#F85149',
  },
  errorText: {
    color: '#F85149',
    fontSize: 12,
    marginTop: 4,
  },
  textArea: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    minHeight: 100,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  submitButton: {
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default EditReminderScreen;
