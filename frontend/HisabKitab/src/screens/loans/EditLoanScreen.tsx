import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Switch,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useLoans } from '../../contexts/LoanContext';
import { useAuth } from '../../contexts/AuthContext';
import { InterestType, FrequencyType } from '../../models/Loan';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import { formatCurrency } from '../../utils/formatters';

interface RouteParams {
  loanId: number;
}

const EditLoanScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const route = useRoute();
  const navigation = useNavigation<any>();
  const { loanId } = route.params as RouteParams;
  
  const { getLoanById, updateLoan, calculateEMI, getInterestTypeOptions, getFrequencyTypeOptions } = useLoans();
  const { user } = useAuth();

  // Form state
  const [title, setTitle] = useState('');
  const [amount, setAmount] = useState('');
  const [interestRate, setInterestRate] = useState('');
  const [interestType, setInterestType] = useState<InterestType>(InterestType.ReducingBalance);
  const [paymentFrequency, setPaymentFrequency] = useState<FrequencyType>(FrequencyType.Monthly);
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [loanType, setLoanType] = useState('given'); // 'given', 'taken', 'external'
  const [externalEntityName, setExternalEntityName] = useState('');
  const [status, setStatus] = useState('Active');
  const [notes, setNotes] = useState('');
  
  // UI state
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [isCalculatingEMI, setIsCalculatingEMI] = useState(false);
  const [calculatedEMI, setCalculatedEMI] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get options for dropdowns
  const interestTypeOptions = getInterestTypeOptions();
  const frequencyTypeOptions = getFrequencyTypeOptions();

  // Load loan details
  useEffect(() => {
    const loadLoan = async () => {
      setIsLoading(true);
      try {
        const loan = getLoanById(loanId);
        if (loan) {
          setTitle(loan.title);
          setAmount(loan.amount.toString());
          setInterestRate(loan.interestRate.toString());
          setInterestType(loan.interestType);
          setPaymentFrequency(loan.paymentFrequency);
          setStartDate(new Date(loan.startDate));
          setEndDate(loan.endDate ? new Date(loan.endDate) : null);
          
          if (loan.lenderUserId) {
            setLoanType('given');
          } else if (loan.borrowerUserId) {
            setLoanType('taken');
          } else {
            setLoanType('external');
          }
          
          setExternalEntityName(loan.externalEntityName || '');
          setStatus(loan.status);
          setNotes(loan.notes || '');
        }
      } catch (error) {
        console.error('Error loading loan:', error);
        Alert.alert(t('common.error'), t('loans.loanNotFound'));
        navigation.goBack();
      } finally {
        setIsLoading(false);
      }
    };
    
    loadLoan();
  }, [loanId]);

  // Calculate EMI when form values change
  useEffect(() => {
    const calculateEMIValue = async () => {
      if (
        amount &&
        !isNaN(parseFloat(amount)) &&
        interestRate &&
        !isNaN(parseFloat(interestRate))
      ) {
        setIsCalculatingEMI(true);
        try {
          const emi = await calculateEMI({
            principal: parseFloat(amount),
            interestRate: parseFloat(interestRate),
            interestType,
            paymentFrequency,
            startDate: startDate.toISOString(),
            endDate: endDate ? endDate.toISOString() : undefined,
          });
          setCalculatedEMI(emi);
        } catch (error) {
          console.error('Error calculating EMI:', error);
        } finally {
          setIsCalculatingEMI(false);
        }
      } else {
        setCalculatedEMI(null);
      }
    };

    calculateEMIValue();
  }, [amount, interestRate, interestType, paymentFrequency, startDate, endDate]);

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!title.trim()) {
      newErrors.title = t('validation.required');
    }

    if (!amount.trim()) {
      newErrors.amount = t('validation.required');
    } else if (isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      newErrors.amount = t('validation.positive');
    }

    if (!interestRate.trim()) {
      newErrors.interestRate = t('validation.required');
    } else if (
      isNaN(parseFloat(interestRate)) ||
      parseFloat(interestRate) < 0 ||
      parseFloat(interestRate) > 100
    ) {
      newErrors.interestRate = t('validation.percentageRange');
    }

    if (loanType === 'external' && !externalEntityName.trim()) {
      newErrors.externalEntityName = t('validation.required');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      Alert.alert(t('common.error'), t('validation.fixErrors'));
      return;
    }

    setIsSubmitting(true);
    try {
      const loanData = {
        id: loanId,
        title,
        amount: parseFloat(amount),
        interestRate: parseFloat(interestRate),
        interestType,
        paymentFrequency,
        startDate: startDate.toISOString(),
        endDate: endDate ? endDate.toISOString() : undefined,
        lenderUserId: loanType === 'given' ? user?.id : undefined,
        borrowerUserId: loanType === 'taken' ? user?.id : undefined,
        isExternalEntity: loanType === 'external',
        externalEntityName: loanType === 'external' ? externalEntityName : undefined,
        status,
        notes,
      };

      const result = await updateLoan(loanData);
      if (result) {
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error updating loan:', error);
      Alert.alert(t('common.error'), t('loans.updateLoanError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
      </View>
    );
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}
      contentContainerStyle={styles.contentContainer}
    >
      {/* Title */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.title')} *
        </Text>
        <TextInput
          style={[
            styles.input,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
            errors.title && styles.inputError,
          ]}
          value={title}
          onChangeText={setTitle}
          placeholder={t('loans.title')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
        />
        {errors.title && (
          <Text style={styles.errorText}>{errors.title}</Text>
        )}
      </View>

      {/* Status */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.status')} *
        </Text>
        <View
          style={[
            styles.pickerContainer,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
        >
          <Picker
            selectedValue={status}
            onValueChange={(itemValue: string) => setStatus(itemValue)}
            style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}
            dropdownIconColor={isDarkMode ? '#FFFFFF' : '#000000'}
          >
            <Picker.Item label={t('loans.statusOptions.active')} value="Active" />
            <Picker.Item label={t('loans.statusOptions.completed')} value="Completed" />
            <Picker.Item label={t('loans.statusOptions.defaulted')} value="Defaulted" />
          </Picker>
        </View>
      </View>

      {/* Amount */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.amount')} *
        </Text>
        <TextInput
          style={[
            styles.input,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
            errors.amount && styles.inputError,
          ]}
          value={amount}
          onChangeText={setAmount}
          placeholder="0.00"
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          keyboardType="numeric"
        />
        {errors.amount && (
          <Text style={styles.errorText}>{errors.amount}</Text>
        )}
      </View>

      {/* Interest Rate */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.interestRate')} (%) *
        </Text>
        <TextInput
          style={[
            styles.input,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', color: isDarkMode ? '#FFFFFF' : '#000000' },
            errors.interestRate && styles.inputError,
          ]}
          value={interestRate}
          onChangeText={setInterestRate}
          placeholder="0.00"
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          keyboardType="numeric"
        />
        {errors.interestRate && (
          <Text style={styles.errorText}>{errors.interestRate}</Text>
        )}
      </View>

      {/* Interest Type */}
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.interestType')} *
        </Text>
        <View
          style={[
            styles.pickerContainer,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
        >
          <Picker
            selectedValue={interestType}
            onValueChange={(itemValue: any) => setInterestType(itemValue as InterestType)}
            style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}
            dropdownIconColor={isDarkMode ? '#FFFFFF' : '#000000'}
          >
            {interestTypeOptions.map((option) => (
              <Picker.Item
                key={option.value}
                label={option.label}
                value={option.value}
              />
            ))}
          </Picker>
        </View>
      </View>

      {/* Submit Button */}
      <TouchableOpacity
        style={[
          styles.submitButton,
          { backgroundColor: isDarkMode ? '#238636' : '#2DA44E' },
          isSubmitting && { opacity: 0.7 },
        ]}
        onPress={handleSubmit}
        disabled={isSubmitting}
      >
        {isSubmitting ? (
          <ActivityIndicator size="small" color="#FFFFFF" />
        ) : (
          <Text style={styles.submitButtonText}>{t('loans.editLoan')}</Text>
        )}
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formGroup: {
    marginBottom: 16,
  },
  labelRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  optionalText: {
    fontSize: 14,
    marginLeft: 8,
  },
  input: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#F85149',
  },
  errorText: {
    color: '#F85149',
    fontSize: 12,
    marginTop: 4,
  },
  pickerContainer: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  dateInput: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  textArea: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    minHeight: 100,
  },
  loanTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  loanTypeButton: {
    flex: 1,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  activeLoanTypeButton: {
    borderWidth: 0,
  },
  loanTypeText: {
    fontWeight: 'bold',
  },
  activeLoanTypeText: {
    color: '#FFFFFF',
  },
  emiContainer: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    alignItems: 'center',
  },
  emiTitle: {
    fontSize: 16,
    marginBottom: 8,
  },
  emiValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  emiPlaceholder: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  submitButton: {
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default EditLoanScreen;
