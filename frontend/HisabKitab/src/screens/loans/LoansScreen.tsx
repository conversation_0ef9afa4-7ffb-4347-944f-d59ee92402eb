import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useLoans } from '../../contexts/LoanContext';
import { Loan } from '../../models/Loan';
import EmptyState from '../../components/EmptyState';
import LoanCard from '../../components/loans/LoanCard';
import FilterButton from '../../components/common/FilterButton';
import SegmentedControl from '../../components/common/SegmentedControl';
import { formatCurrency } from '../../utils/formatters';

const LoansScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();
  const { loans, isLoading, refreshLoans } = useLoans();

  const [activeTab, setActiveTab] = useState('all'); // 'all', 'given', 'taken'
  const [statusFilter, setStatusFilter] = useState('all'); // 'all', 'active', 'completed'
  const [filteredLoans, setFilteredLoans] = useState<Loan[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Filter loans based on active tab and status filter
  useEffect(() => {
    let filtered = [...loans];

    // Filter by loan type (given/taken)
    if (activeTab === 'given') {
      filtered = filtered.filter(loan => loan.lenderUserId !== null);
    } else if (activeTab === 'taken') {
      filtered = filtered.filter(loan => loan.borrowerUserId !== null);
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(loan => loan.status.toLowerCase() === statusFilter);
    }

    setFilteredLoans(filtered);
  }, [loans, activeTab, statusFilter]);

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await refreshLoans();
    setIsRefreshing(false);
  };

  // Navigate to create loan screen
  const handleCreateLoan = () => {
    navigation.navigate('CreateLoan');
  };

  // Navigate to loan details screen
  const handleLoanPress = (loan: Loan) => {
    navigation.navigate('LoanDetails', { loanId: loan.id });
  };

  // Calculate summary statistics
  const calculateSummary = () => {
    const givenLoans = loans.filter(loan => loan.lenderUserId !== null);
    const takenLoans = loans.filter(loan => loan.borrowerUserId !== null);
    
    const totalGiven = givenLoans.reduce((sum, loan) => sum + loan.amount, 0);
    const totalTaken = takenLoans.reduce((sum, loan) => sum + loan.amount, 0);
    
    const activeLoans = loans.filter(loan => loan.status.toLowerCase() === 'active');
    const completedLoans = loans.filter(loan => loan.status.toLowerCase() === 'completed');
    
    return {
      totalGiven,
      totalTaken,
      activeCount: activeLoans.length,
      completedCount: completedLoans.length,
    };
  };

  const summary = calculateSummary();

  // Render loan summary
  const renderSummary = () => {
    return (
      <View style={[styles.summaryContainer, { backgroundColor: isDarkMode ? '#1D3D47' : '#E1F5FE' }]}>
        <View style={styles.summaryRow}>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {t('loans.totalLoansGiven')}
            </Text>
            <Text style={[styles.summaryValue, { color: isDarkMode ? '#58A6FF' : '#0366D6' }]}>
              {formatCurrency(summary.totalGiven)}
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {t('loans.totalLoansTaken')}
            </Text>
            <Text style={[styles.summaryValue, { color: isDarkMode ? '#58A6FF' : '#0366D6' }]}>
              {formatCurrency(summary.totalTaken)}
            </Text>
          </View>
        </View>
        <View style={styles.summaryRow}>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {t('loans.activeLoans')}
            </Text>
            <Text style={[styles.summaryValue, { color: isDarkMode ? '#58A6FF' : '#0366D6' }]}>
              {summary.activeCount}
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {t('loans.completedLoans')}
            </Text>
            <Text style={[styles.summaryValue, { color: isDarkMode ? '#58A6FF' : '#0366D6' }]}>
              {summary.completedCount}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  // Render filter controls
  const renderFilterControls = () => {
    return (
      <View style={styles.filterContainer}>
        <SegmentedControl
          values={[
            { label: t('common.all'), value: 'all' },
            { label: t('loans.loanTypes.given'), value: 'given' },
            { label: t('loans.loanTypes.taken'), value: 'taken' },
          ]}
          selectedValue={activeTab}
          onChange={setActiveTab}
          isDarkMode={isDarkMode}
        />
        <View style={styles.statusFilterContainer}>
          <FilterButton
            label={t('common.all')}
            isActive={statusFilter === 'all'}
            onPress={() => setStatusFilter('all')}
            isDarkMode={isDarkMode}
          />
          <FilterButton
            label={t('loans.statusOptions.active')}
            isActive={statusFilter === 'active'}
            onPress={() => setStatusFilter('active')}
            isDarkMode={isDarkMode}
          />
          <FilterButton
            label={t('loans.statusOptions.completed')}
            isActive={statusFilter === 'completed'}
            onPress={() => setStatusFilter('completed')}
            isDarkMode={isDarkMode}
          />
        </View>
      </View>
    );
  };

  // Render empty state
  const renderEmptyState = () => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
        </View>
      );
    }

    return (
      <EmptyState
        icon="cash-outline"
        title={t('loans.noLoans')}
        message={t('loans.noLoansMessage')}
        buttonTitle={t('loans.addLoan')}
        onButtonPress={handleCreateLoan}
        isDarkMode={isDarkMode}
      />
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      {/* Header with add button */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.dashboard')}
        </Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: isDarkMode ? '#238636' : '#2DA44E' }]}
          onPress={handleCreateLoan}
        >
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Loan summary */}
      {loans.length > 0 && renderSummary()}

      {/* Filter controls */}
      {loans.length > 0 && renderFilterControls()}

      {/* Loan list */}
      <FlatList
        data={filteredLoans}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => (
          <LoanCard loan={item} onPress={() => handleLoanPress(item)} isDarkMode={isDarkMode} />
        )}
        ListEmptyComponent={renderEmptyState}
        contentContainerStyle={loans.length === 0 ? styles.emptyListContainer : styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={['#0366D6']}
            tintColor={isDarkMode ? '#58A6FF' : '#0366D6'}
          />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  summaryContainer: {
    margin: 16,
    padding: 16,
    borderRadius: 8,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryItem: {
    flex: 1,
  },
  summaryLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  filterContainer: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  statusFilterContainer: {
    flexDirection: 'row',
    marginTop: 8,
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default LoansScreen;
