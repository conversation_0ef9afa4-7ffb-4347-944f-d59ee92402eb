import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import apiService from '../services/api';
import dbService, { Family } from '../services/db';

const EditFamilyScreen = ({ route }: any) => {
  const { family } = route.params;
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const router = useRouter();

  const [name, setName] = useState(family.name);
  const [description, setDescription] = useState(family.description || '');
  const [loading, setLoading] = useState(false);

  const handleUpdateFamily = async () => {
    if (!name.trim()) {
      Alert.alert(
        t('common.error'),
        t('family.familyName') + ' ' + t('auth.requiredFields')
      );
      return;
    }

    try {
      setLoading(true);

      const response = await apiService.family.updateFamily(family.id, {
        name: name.trim(),
        description: description.trim() || null,
        settings: family.settings || '{}'
      });

      if (response.data) {
        // Save updated family to local database
        const updatedFamily = {
          ...family,
          name: name.trim(),
          description: description.trim() || '',
          updatedAt: new Date(),
        };

        await dbService.saveFamily(updatedFamily);

        // Navigate back to family details
        router.back();
      } else {
        Alert.alert(
          t('common.error'),
          response.error || t('common.error')
        );
      }
    } catch (error) {
      console.error('Error updating family:', error);
      Alert.alert(
        t('common.error'),
        t('common.error')
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteFamily = () => {
    Alert.alert(
      t('common.delete') + ' ' + t('family.family'),
      t('common.confirmDelete') + ' ' + family.name + '?',
      [
        {
          text: t('common.cancel'),
          style: 'cancel'
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);

              const response = await apiService.family.deleteFamily(family.id);

              if (response.status === 204) {
                // Delete family from local database
                await dbService.deleteFamily(family.id);

                // Navigate back to families list
                router.push('/families' as any);
              } else {
                Alert.alert(
                  t('common.error'),
                  response.error || t('common.error')
                );
              }
            } catch (error) {
              console.error('Error deleting family:', error);
              Alert.alert(
                t('common.error'),
                t('common.error')
              );
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      <ScrollView
        style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}
        contentContainerStyle={styles.contentContainer}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons
              name="arrow-back"
              size={24}
              color={isDarkMode ? '#FFFFFF' : '#000000'}
            />
          </TouchableOpacity>
          <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('family.editFamily')}
          </Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('family.familyName')} *
          </Text>
          <TextInput
            style={[
              styles.input,
              {
                backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                color: isDarkMode ? '#FFFFFF' : '#000000',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
              }
            ]}
            placeholder={t('family.familyName')}
            placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
            value={name}
            onChangeText={setName}
            maxLength={100}
          />

          <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('family.familyDescription')}
          </Text>
          <TextInput
            style={[
              styles.input,
              styles.textArea,
              {
                backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                color: isDarkMode ? '#FFFFFF' : '#000000',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
              }
            ]}
            placeholder={t('family.familyDescription')}
            placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={4}
            maxLength={500}
          />

          <TouchableOpacity
            style={[
              styles.updateButton,
              {
                backgroundColor: isDarkMode ? '#1F6FEB' : '#0969DA',
                opacity: loading ? 0.7 : 1
              }
            ]}
            onPress={handleUpdateFamily}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#FFFFFF" size="small" />
            ) : (
              <Text style={styles.updateButtonText}>
                {t('common.update')}
              </Text>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.deleteButton,
              {
                opacity: loading ? 0.7 : 1
              }
            ]}
            onPress={handleDeleteFamily}
            disabled={loading}
          >
            <Ionicons
              name="trash-outline"
              size={24}
              color="#FFFFFF"
              style={styles.deleteButtonIcon}
            />
            <Text style={styles.deleteButtonText}>
              {t('common.delete')} {t('family.family')}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  formContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    marginBottom: 16,
  },
  textArea: {
    height: 120,
    paddingTop: 12,
    textAlignVertical: 'top',
  },
  updateButton: {
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 16,
  },
  updateButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  deleteButton: {
    height: 48,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 16,
    backgroundColor: '#F85149',
  },
  deleteButtonIcon: {
    marginRight: 8,
  },
  deleteButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default EditFamilyScreen;
