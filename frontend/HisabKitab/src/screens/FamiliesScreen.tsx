import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import apiService from '../services/api';
import dbService, { Family } from '../services/db';

const FamiliesScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { user } = useAuth();
  const router = useRouter();

  const [families, setFamilies] = useState<Family[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchFamilies = async () => {
    try {
      setLoading(true);
      const response = await apiService.family.getFamilies();

      if (response.data) {
        // Save families to local database
        const savedFamilies: Family[] = [];
        for (const family of response.data) {
          const savedFamily = {
            ...family,
            createdAt: new Date(family.createdAt),
            updatedAt: new Date(family.updatedAt),
            isSynced: true
          };
          await dbService.saveFamily(savedFamily);
          savedFamilies.push(savedFamily);
        }
        setFamilies(savedFamilies);
      }
    } catch (error) {
      console.error('Error fetching families:', error);
      // Try to load from local database
      const localFamilies = await dbService.getFamilies();
      setFamilies(localFamilies);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchFamilies();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    fetchFamilies();
  };

  const handleCreateFamily = () => {
    router.push('/family/create' as any);
  };

  const handleJoinFamily = () => {
    router.push('/family/join' as any);
  };

  const handleFamilyPress = (family: Family) => {
    router.push({
      pathname: '/family/[id]' as any,
      params: { id: family.id.toString() }
    });
  };

  const renderFamilyItem = ({ item }: { item: Family }) => (
    <TouchableOpacity
      style={[
        styles.familyCard,
        { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
      ]}
      onPress={() => handleFamilyPress(item)}
    >
      <View style={styles.familyHeader}>
        <Text style={[styles.familyName, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {item.name}
        </Text>
        <Ionicons
          name="chevron-forward"
          size={24}
          color={isDarkMode ? '#8B949E' : '#6E7781'}
        />
      </View>

      {item.description && (
        <Text style={[styles.familyDescription, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
          {item.description}
        </Text>
      )}

      <View style={styles.familyFooter}>
        <Text style={[styles.familyMeta, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
          {t('family.createdBy')}: {item.createdByUsername || 'Unknown'}
        </Text>
        <Text style={[styles.familyMeta, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
          {new Date(item.createdAt).toLocaleDateString()}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Ionicons
        name="people-outline"
        size={64}
        color={isDarkMode ? '#8B949E' : '#6E7781'}
        style={styles.emptyIcon}
      />
      <Text style={[styles.emptyText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
        {t('family.noFamilies')}
      </Text>
      <Text style={[styles.emptySubtext, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
        {t('family.createFamilyPrompt')}
      </Text>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('family.families')}
        </Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: isDarkMode ? '#238636' : '#2DA44E' }]}
            onPress={handleJoinFamily}
          >
            <Ionicons name="enter-outline" size={20} color="#FFFFFF" />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: isDarkMode ? '#1F6FEB' : '#0969DA' }]}
            onPress={handleCreateFamily}
          >
            <Ionicons name="add" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
        </View>
      ) : (
        <FlatList
          data={families}
          renderItem={renderFamilyItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContainer}
          ListEmptyComponent={renderEmptyComponent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#0366D6']}
              tintColor={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  actionButtons: {
    flexDirection: 'row',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  listContainer: {
    padding: 16,
    paddingTop: 0,
  },
  familyCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  familyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  familyName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  familyDescription: {
    fontSize: 14,
    marginBottom: 12,
  },
  familyFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  familyMeta: {
    fontSize: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default FamiliesScreen;
