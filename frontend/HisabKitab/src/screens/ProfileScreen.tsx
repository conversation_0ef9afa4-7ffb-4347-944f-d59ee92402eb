import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import ProtectedRoute from '../components/ProtectedRoute';

const ProfileScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { user, updateProfile, changePassword, changeEmail, isLoading } = useAuth();
  const { language, availableLanguages } = useLanguage();

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState(language);

  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const [newEmail, setNewEmail] = useState('');
  const [passwordForEmail, setPasswordForEmail] = useState('');

  const [activeTab, setActiveTab] = useState('profile');

  // Load user data
  useEffect(() => {
    if (user) {
      setFirstName(user.firstName || '');
      setLastName(user.lastName || '');
      setPhoneNumber(user.phoneNumber || '');
      setSelectedLanguage((user.language || language) as 'en' | 'ne');
      setNewEmail(user.email || '');
    }
  }, [user, language]);

  const handleUpdateProfile = async () => {
    if (!firstName || !lastName) {
      Alert.alert(t('common.error'), t('auth.requiredFields'));
      return;
    }

    const result = await updateProfile({
      firstName,
      lastName,
      phoneNumber,
      language: selectedLanguage
    });

    if (result.success) {
      Alert.alert(t('common.success'), t('profile.updateSuccess'));
    } else {
      Alert.alert(t('common.error'), result.error || t('profile.updateFailed'));
    }
  };

  const handleChangePassword = async () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      Alert.alert(t('common.error'), t('auth.requiredFields'));
      return;
    }

    if (newPassword !== confirmPassword) {
      Alert.alert(t('common.error'), t('auth.passwordMismatch'));
      return;
    }

    const result = await changePassword(currentPassword, newPassword);

    if (result.success) {
      Alert.alert(t('common.success'), t('profile.passwordChangeSuccess'));
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
    } else {
      // Check for specific error messages
      const errorMessage = result.error || '';

      if (errorMessage.toLowerCase().includes('current password') ||
          errorMessage.toLowerCase().includes('password does not match')) {
        Alert.alert(t('common.error'), t('profile.incorrectCurrentPassword'));
      } else if (errorMessage.toLowerCase().includes('complexity')) {
        Alert.alert(t('common.error'), t('profile.passwordComplexity'));
      } else {
        Alert.alert(t('common.error'), errorMessage || t('profile.passwordChangeFailed'));
      }
    }
  };

  const handleChangeEmail = async () => {
    if (!newEmail || !passwordForEmail) {
      Alert.alert(t('common.error'), t('auth.requiredFields'));
      return;
    }

    const result = await changeEmail(newEmail, passwordForEmail);

    if (result.success) {
      Alert.alert(t('common.success'), t('profile.emailChangeSuccess'));
      setPasswordForEmail('');
    } else {
      Alert.alert(t('common.error'), result.error || t('profile.emailChangeFailed'));
    }
  };

  const renderProfileTab = () => (
    <View style={styles.tabContent}>
      <TextInput
        style={[
          styles.input,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            color: isDarkMode ? '#FFFFFF' : '#000000',
            borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
          }
        ]}
        placeholder={t('auth.firstName')}
        placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
        value={firstName}
        onChangeText={setFirstName}
      />

      <TextInput
        style={[
          styles.input,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            color: isDarkMode ? '#FFFFFF' : '#000000',
            borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
          }
        ]}
        placeholder={t('auth.lastName')}
        placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
        value={lastName}
        onChangeText={setLastName}
      />

      <TextInput
        style={[
          styles.input,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            color: isDarkMode ? '#FFFFFF' : '#000000',
            borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
          }
        ]}
        placeholder={t('auth.phoneNumber')}
        placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
        value={phoneNumber}
        onChangeText={setPhoneNumber}
        keyboardType="phone-pad"
      />

      <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
        {t('settings.language')}
      </Text>

      <View style={styles.languageContainer}>
        {availableLanguages.map((lang) => (
          <TouchableOpacity
            key={lang.code}
            style={[
              styles.languageOption,
              {
                backgroundColor: selectedLanguage === lang.code
                  ? (isDarkMode ? '#0366D6' : '#0366D6')
                  : (isDarkMode ? '#161B22' : '#F6F8FA'),
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
              }
            ]}
            onPress={() => setSelectedLanguage(lang.code)}
          >
            <Text
              style={[
                styles.languageText,
                {
                  color: selectedLanguage === lang.code
                    ? '#FFFFFF'
                    : (isDarkMode ? '#FFFFFF' : '#000000')
                }
              ]}
            >
              {lang.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <TouchableOpacity
        style={[styles.button, { opacity: isLoading ? 0.7 : 1 }]}
        onPress={handleUpdateProfile}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="#FFFFFF" />
        ) : (
          <Text style={styles.buttonText}>{t('common.save')}</Text>
        )}
      </TouchableOpacity>
    </View>
  );

  const renderPasswordTab = () => (
    <View style={styles.tabContent}>
      <TextInput
        style={[
          styles.input,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            color: isDarkMode ? '#FFFFFF' : '#000000',
            borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
          }
        ]}
        placeholder={t('profile.currentPassword')}
        placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
        value={currentPassword}
        onChangeText={setCurrentPassword}
        secureTextEntry
      />

      <TextInput
        style={[
          styles.input,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            color: isDarkMode ? '#FFFFFF' : '#000000',
            borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
          }
        ]}
        placeholder={t('profile.newPassword')}
        placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
        value={newPassword}
        onChangeText={setNewPassword}
        secureTextEntry
      />

      <TextInput
        style={[
          styles.input,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            color: isDarkMode ? '#FFFFFF' : '#000000',
            borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
          }
        ]}
        placeholder={t('profile.confirmNewPassword')}
        placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
        value={confirmPassword}
        onChangeText={setConfirmPassword}
        secureTextEntry
      />

      <TouchableOpacity
        style={[styles.button, { opacity: isLoading ? 0.7 : 1 }]}
        onPress={handleChangePassword}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="#FFFFFF" />
        ) : (
          <Text style={styles.buttonText}>{t('profile.changePassword')}</Text>
        )}
      </TouchableOpacity>
    </View>
  );

  const renderEmailTab = () => (
    <View style={styles.tabContent}>
      <TextInput
        style={[
          styles.input,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            color: isDarkMode ? '#FFFFFF' : '#000000',
            borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
          }
        ]}
        placeholder={t('auth.email')}
        placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
        value={newEmail}
        onChangeText={setNewEmail}
        keyboardType="email-address"
        autoCapitalize="none"
      />

      <TextInput
        style={[
          styles.input,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            color: isDarkMode ? '#FFFFFF' : '#000000',
            borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
          }
        ]}
        placeholder={t('auth.password')}
        placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
        value={passwordForEmail}
        onChangeText={setPasswordForEmail}
        secureTextEntry
      />

      <TouchableOpacity
        style={[styles.button, { opacity: isLoading ? 0.7 : 1 }]}
        onPress={handleChangeEmail}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="#FFFFFF" />
        ) : (
          <Text style={styles.buttonText}>{t('profile.changeEmail')}</Text>
        )}
      </TouchableOpacity>
    </View>
  );

  return (
    <ProtectedRoute>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      >
        <ScrollView
          style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}
          contentContainerStyle={styles.contentContainer}
        >
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons
                name="arrow-back"
                size={24}
                color={isDarkMode ? '#FFFFFF' : '#000000'}
              />
            </TouchableOpacity>

            <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {t('profile.profile')}
            </Text>
          </View>

          <View style={styles.userInfo}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>
                {user?.firstName?.charAt(0) || ''}{user?.lastName?.charAt(0) || ''}
              </Text>
            </View>

            <Text style={[styles.username, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {user?.username}
            </Text>

            {user?.roles && user.roles.length > 0 && (
              <View style={styles.rolesContainer}>
                {user.roles.map((role, index) => (
                  <View key={index} style={styles.roleTag}>
                    <Text style={styles.roleText}>{role}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>

          <View style={styles.tabs}>
            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'profile' && styles.activeTab,
                { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }
              ]}
              onPress={() => setActiveTab('profile')}
            >
              <Text
                style={[
                  styles.tabText,
                  { color: activeTab === 'profile'
                    ? (isDarkMode ? '#58A6FF' : '#0366D6')
                    : (isDarkMode ? '#8B949E' : '#6E7781')
                  }
                ]}
              >
                {t('profile.profile')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'password' && styles.activeTab,
                { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }
              ]}
              onPress={() => setActiveTab('password')}
            >
              <Text
                style={[
                  styles.tabText,
                  { color: activeTab === 'password'
                    ? (isDarkMode ? '#58A6FF' : '#0366D6')
                    : (isDarkMode ? '#8B949E' : '#6E7781')
                  }
                ]}
              >
                {t('profile.password')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'email' && styles.activeTab,
                { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }
              ]}
              onPress={() => setActiveTab('email')}
            >
              <Text
                style={[
                  styles.tabText,
                  { color: activeTab === 'email'
                    ? (isDarkMode ? '#58A6FF' : '#0366D6')
                    : (isDarkMode ? '#8B949E' : '#6E7781')
                  }
                ]}
              >
                {t('profile.email')}
              </Text>
            </TouchableOpacity>
          </View>

          {activeTab === 'profile' && renderProfileTab()}
          {activeTab === 'password' && renderPasswordTab()}
          {activeTab === 'email' && renderEmailTab()}
        </ScrollView>
      </KeyboardAvoidingView>
    </ProtectedRoute>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginLeft: 16,
  },
  userInfo: {
    alignItems: 'center',
    marginBottom: 24,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#0366D6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
  },
  username: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  rolesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  roleTag: {
    backgroundColor: '#0366D6',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    marginHorizontal: 4,
    marginBottom: 4,
  },
  roleText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  tabs: {
    flexDirection: 'row',
    marginBottom: 24,
    borderBottomWidth: 1,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#0366D6',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
  },
  tabContent: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 16,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  languageContainer: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  languageOption: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  languageText: {
    fontSize: 14,
    fontWeight: '500',
  },
  button: {
    height: 50,
    backgroundColor: '#0366D6',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ProfileScreen;
