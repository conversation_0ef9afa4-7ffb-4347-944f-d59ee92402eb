import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Share,
} from 'react-native';
import { useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useAnalytics } from '../../contexts/AnalyticsContext';
import { formatCurrency } from '../../utils/formatters';
import ChartContainer from '../../components/analytics/ChartContainer';
// @ts-ignore - MonthYearPicker exists but TypeScript can't find it
const MonthYearPicker = require('../../components/common/MonthYearPicker').default;

const MonthlySummaryScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  // const navigation = useNavigation<any>(); // Unused for now
  const route = useRoute<any>();

  const { getMonthlySummary, isLoading } = useAnalytics();

  const initialMonth = route.params?.month ? new Date(route.params.month) : new Date();
  const [selectedMonth, setSelectedMonth] = useState(initialMonth);
  const [summary, setSummary] = useState<any>(null);

  // Load monthly summary
  useEffect(() => {
    loadMonthlySummary();
  }, [selectedMonth]);

  // Load monthly summary
  const loadMonthlySummary = async () => {
    const data = await getMonthlySummary(selectedMonth);
    if (data) {
      setSummary(data);
    }
  };

  // Handle share
  const handleShare = async () => {
    if (!summary) return;

    const monthName = selectedMonth.toLocaleString('default', { month: 'long' });
    const year = selectedMonth.getFullYear();

    const message = `
${t('analytics.monthlySummary')} - ${monthName} ${year}

${t('analytics.totalIncome')}: ${formatCurrency(summary.totalIncome)}
${t('analytics.totalExpense')}: ${formatCurrency(summary.totalExpense)}
${t('analytics.netSavings')}: ${formatCurrency(summary.netSavings)}
${t('analytics.savingsRate')}: ${summary.savingsRate}%
${t('analytics.topExpenseCategory')}: ${summary.topExpenseCategory}
${t('analytics.topIncomeSource')}: ${summary.topIncomeSource}

${t('analytics.transactionCount')}: ${summary.transactionCount}
${t('analytics.averageExpense')}: ${formatCurrency(summary.averageExpense)}
${t('analytics.largestExpense')}: ${formatCurrency(summary.largestExpense)}

${t('analytics.generatedBy')} Hisab-Kitab
    `;

    try {
      await Share.share({
        message,
        title: `${t('analytics.monthlySummary')} - ${monthName} ${year}`,
      });
    } catch (error) {
      console.error('Error sharing summary:', error);
    }
  };

  // Prepare category data for chart
  const prepareCategoryData = () => {
    if (!summary || !summary.expensesByCategory) return [];

    return summary.expensesByCategory.map((item: any) => ({
      name: item.category,
      value: item.amount,
      color: getRandomColor(),
    }));
  };

  // Prepare income vs expense data for chart
  const prepareIncomeExpenseData = () => {
    if (!summary) return [];

    return [
      {
        name: t('analytics.income'),
        value: summary.totalIncome,
        color: '#3FB950',
      },
      {
        name: t('analytics.expense'),
        value: summary.totalExpense,
        color: '#F85149',
      },
    ];
  };

  // Prepare daily trend data for chart
  const prepareDailyTrendData = () => {
    if (!summary || !summary.dailyTrend) return [];

    return summary.dailyTrend.map((item: any) => ({
      date: item.date,
      income: item.income,
      expense: item.expense,
    }));
  };

  // Get random color for chart
  const getRandomColor = () => {
    const colors = [
      '#F78166', '#58A6FF', '#3FB950', '#F85149', '#8B5CF6',
      '#F59E0B', '#0D9488', '#EC4899', '#6366F1', '#F43F5E',
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  const monthName = selectedMonth.toLocaleString('default', { month: 'long' });
  const year = selectedMonth.getFullYear();

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      {/* Month selector */}
      <View style={styles.monthSelector}>
        <MonthYearPicker
          value={selectedMonth}
          onChange={setSelectedMonth}
          isDarkMode={isDarkMode}
        />
        <TouchableOpacity
          style={styles.shareButton}
          onPress={handleShare}
          disabled={!summary}
        >
          <Ionicons
            name="share-outline"
            size={24}
            color={isDarkMode ? '#58A6FF' : '#0366D6'}
          />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
          </View>
        ) : !summary ? (
          <View style={styles.noDataContainer}>
            <Ionicons
              name="analytics-outline"
              size={48}
              color={isDarkMode ? '#8B949E' : '#6E7781'}
            />
            <Text style={[styles.noDataText, { color: isDarkMode ? '#C9D1D9' : '#24292E' }]}>
              {t('analytics.noDataForMonth')}
            </Text>
          </View>
        ) : (
          <>
            {/* Summary card */}
            <View style={[styles.card, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
              <Text style={[styles.cardTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {`${monthName} ${year} ${t('analytics.summary')}`}
              </Text>

              {/* Main metrics */}
              <View style={styles.metricsGrid}>
                <View style={styles.metricItem}>
                  <Text style={[styles.metricLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                    {t('analytics.totalIncome')}
                  </Text>
                  <Text
                    style={[
                      styles.metricValue,
                      { color: isDarkMode ? '#3FB950' : '#2EA043' },
                    ]}
                  >
                    {formatCurrency(summary.totalIncome)}
                  </Text>
                </View>

                <View style={styles.metricItem}>
                  <Text style={[styles.metricLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                    {t('analytics.totalExpense')}
                  </Text>
                  <Text
                    style={[
                      styles.metricValue,
                      { color: isDarkMode ? '#F85149' : '#D73A49' },
                    ]}
                  >
                    {formatCurrency(summary.totalExpense)}
                  </Text>
                </View>

                <View style={styles.metricItem}>
                  <Text style={[styles.metricLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                    {t('analytics.netSavings')}
                  </Text>
                  <Text
                    style={[
                      styles.metricValue,
                      {
                        color: summary.netSavings >= 0
                          ? isDarkMode ? '#3FB950' : '#2EA043'
                          : isDarkMode ? '#F85149' : '#D73A49',
                      },
                    ]}
                  >
                    {formatCurrency(summary.netSavings)}
                  </Text>
                </View>

                <View style={styles.metricItem}>
                  <Text style={[styles.metricLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                    {t('analytics.savingsRate')}
                  </Text>
                  <Text
                    style={[
                      styles.metricValue,
                      {
                        color: summary.savingsRate >= 0
                          ? isDarkMode ? '#3FB950' : '#2EA043'
                          : isDarkMode ? '#F85149' : '#D73A49',
                      },
                    ]}
                  >
                    {`${summary.savingsRate}%`}
                  </Text>
                </View>
              </View>
            </View>

            {/* Charts */}
            <ChartContainer
              title={t('analytics.incomeVsExpense')}
              type="bar"
              data={prepareIncomeExpenseData()}
              height={200}
            />

            {summary.expensesByCategory && summary.expensesByCategory.length > 0 && (
              <ChartContainer
                title={t('analytics.expensesByCategory')}
                type="pie"
                data={prepareCategoryData()}
                height={200}
              />
            )}

            {summary.dailyTrend && summary.dailyTrend.length > 0 && (
              <ChartContainer
                title={t('analytics.dailyTrend')}
                type="line"
                data={prepareDailyTrendData()}
                height={200}
              />
            )}

            {/* Additional metrics */}
            <View style={[styles.card, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
              <Text style={[styles.cardTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {t('analytics.additionalMetrics')}
              </Text>

              <View style={styles.additionalMetrics}>
                <View style={styles.metricRow}>
                  <Text style={[styles.metricLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                    {t('analytics.transactionCount')}
                  </Text>
                  <Text style={[styles.metricValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                    {summary.transactionCount}
                  </Text>
                </View>

                <View style={styles.metricRow}>
                  <Text style={[styles.metricLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                    {t('analytics.averageExpense')}
                  </Text>
                  <Text style={[styles.metricValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                    {formatCurrency(summary.averageExpense)}
                  </Text>
                </View>

                <View style={styles.metricRow}>
                  <Text style={[styles.metricLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                    {t('analytics.largestExpense')}
                  </Text>
                  <Text style={[styles.metricValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                    {formatCurrency(summary.largestExpense)}
                  </Text>
                </View>

                <View style={styles.metricRow}>
                  <Text style={[styles.metricLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                    {t('analytics.topExpenseCategory')}
                  </Text>
                  <Text style={[styles.metricValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                    {summary.topExpenseCategory}
                  </Text>
                </View>

                <View style={styles.metricRow}>
                  <Text style={[styles.metricLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                    {t('analytics.topIncomeSource')}
                  </Text>
                  <Text style={[styles.metricValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                    {summary.topIncomeSource}
                  </Text>
                </View>
              </View>
            </View>
          </>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  monthSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  shareButton: {
    padding: 8,
  },
  scrollContent: {
    padding: 16,
  },
  loadingContainer: {
    padding: 32,
    alignItems: 'center',
  },
  noDataContainer: {
    padding: 32,
    alignItems: 'center',
  },
  noDataText: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
  card: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricItem: {
    width: '48%',
    marginBottom: 16,
  },
  metricLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: '600',
  },
  additionalMetrics: {
    marginTop: 8,
  },
  metricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
});

export default MonthlySummaryScreen;
