import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import dbService, { Account, Transaction } from '../services/db';
import syncService from '../services/sync';
import { Ionicons } from '@expo/vector-icons';
import AppHeader from '../components/layout/AppHeader';

const HomeScreen = ({ navigation }: any) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { user } = useAuth();

  const [accounts, setAccounts] = useState<Account[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [totalBalance, setTotalBalance] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isSyncing, setIsSyncing] = useState(false);

  useEffect(() => {
    loadData();

    // Subscribe to sync status changes
    const unsubscribe = syncService.subscribeSyncStatus((status) => {
      setIsSyncing(status.isSyncing);
    });

    return () => unsubscribe();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      // Load accounts
      const accountsData = await dbService.getAccounts();
      setAccounts(accountsData);

      // Calculate total balance
      const total = accountsData.reduce((sum, account) => sum + account.balance, 0);
      setTotalBalance(total);

      // Load recent transactions
      const transactionsData = await dbService.getTransactions();
      // Sort by date (newest first) and take only the first 5
      const sortedTransactions = transactionsData
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, 5);
      setTransactions(sortedTransactions);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSync = async () => {
    await syncService.syncData();
    await loadData();
  };

  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <AppHeader title={t('common.appName')} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      <AppHeader title={t('common.appName')} />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
      >
      {/* Welcome section */}
      <View style={styles.welcomeSection}>
        <Text style={[styles.welcomeText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('home.welcome')}, {user?.firstName || user?.username}!
        </Text>

        <TouchableOpacity
          style={[styles.syncButton, { opacity: isSyncing ? 0.7 : 1 }]}
          onPress={handleSync}
          disabled={isSyncing}
        >
          {isSyncing ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Ionicons name="sync" size={20} color="#FFFFFF" />
          )}
        </TouchableOpacity>
      </View>

      {/* Balance card */}
      <View style={[styles.card, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
        <Text style={[styles.cardTitle, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
          {t('home.totalBalance')}
        </Text>
        <Text style={[styles.balanceText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          ₨ {totalBalance.toFixed(2)}
        </Text>

        <View style={styles.accountsRow}>
          <TouchableOpacity
            style={styles.accountButton}
            onPress={() => navigation.navigate('Accounts')}
          >
            <Text style={styles.accountButtonText}>{t('accounts.accounts')}</Text>
          </TouchableOpacity>

          <Text style={[styles.accountCount, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {accounts.length} {accounts.length === 1 ? t('accounts.account') : t('accounts.accounts')}
          </Text>
        </View>
      </View>

      {/* Recent transactions */}
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('home.recentTransactions')}
        </Text>

        <TouchableOpacity onPress={() => navigation.navigate('Transactions')}>
          <Text style={[styles.viewAllText, { color: isDarkMode ? '#58A6FF' : '#0366D6' }]}>
            {t('home.viewAll')}
          </Text>
        </TouchableOpacity>
      </View>

      {transactions.length === 0 ? (
        <Text style={[styles.emptyText, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
          {t('home.noTransactions')}
        </Text>
      ) : (
        transactions.map((transaction) => (
          <View
            key={transaction.id}
            style={[styles.transactionItem, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}
          >
            <View style={styles.transactionIcon}>
              <Ionicons
                name={transaction.type === 'Income' ? 'arrow-down' : transaction.type === 'Expense' ? 'arrow-up' : 'swap-horizontal'}
                size={24}
                color={transaction.type === 'Income' ? '#3FB950' : transaction.type === 'Expense' ? '#F85149' : '#58A6FF'}
              />
            </View>

            <View style={styles.transactionDetails}>
              <Text style={[styles.transactionDescription, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {transaction.description || t(`transactions.${transaction.type.toLowerCase()}`)}
              </Text>

              <Text style={[styles.transactionDate, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {new Date(transaction.date).toLocaleDateString()}
              </Text>
            </View>

            <Text
              style={[
                styles.transactionAmount,
                {
                  color: transaction.type === 'Income'
                    ? '#3FB950'
                    : transaction.type === 'Expense'
                      ? '#F85149'
                      : isDarkMode ? '#FFFFFF' : '#000000'
                }
              ]}
            >
              {transaction.type === 'Income' ? '+' : transaction.type === 'Expense' ? '-' : ''}
              ₨ {transaction.amount.toFixed(2)}
            </Text>
          </View>
        ))
      )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  welcomeSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  syncButton: {
    backgroundColor: '#0366D6',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 16,
    marginBottom: 8,
  },
  balanceText: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  accountsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  accountButton: {
    backgroundColor: '#0366D6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  accountButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  accountCount: {
    fontSize: 14,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  viewAllText: {
    fontSize: 14,
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 24,
    fontSize: 16,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  transactionDate: {
    fontSize: 14,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default HomeScreen;
