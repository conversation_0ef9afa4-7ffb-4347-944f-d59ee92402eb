import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';

const LoginScreen = ({ navigation }: any) => {
  const { t } = useTranslation();
  const { login } = useAuth();
  const { isDarkMode } = useTheme();
  
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const handleLogin = async () => {
    if (!username || !password) {
      Alert.alert(t('common.error'), t('auth.emptyFields'));
      return;
    }
    
    setIsLoading(true);
    
    try {
      const result = await login(username, password);
      
      if (!result.success) {
        Alert.alert(t('common.error'), result.error || t('auth.loginFailed'));
      }
    } catch (error) {
      console.error('Login error:', error);
      Alert.alert(t('common.error'), t('auth.loginFailed'));
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
        {t('common.appName')}
      </Text>
      
      <View style={styles.formContainer}>
        <TextInput
          style={[
            styles.input,
            { 
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              color: isDarkMode ? '#FFFFFF' : '#000000',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
            }
          ]}
          placeholder={t('auth.username')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          value={username}
          onChangeText={setUsername}
          autoCapitalize="none"
        />
        
        <TextInput
          style={[
            styles.input,
            { 
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              color: isDarkMode ? '#FFFFFF' : '#000000',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
            }
          ]}
          placeholder={t('auth.password')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          value={password}
          onChangeText={setPassword}
          secureTextEntry
        />
        
        <TouchableOpacity
          style={[styles.button, { opacity: isLoading ? 0.7 : 1 }]}
          onPress={handleLogin}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#FFFFFF" />
          ) : (
            <Text style={styles.buttonText}>{t('auth.login')}</Text>
          )}
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.registerLink}
          onPress={() => navigation.navigate('Register')}
        >
          <Text style={[styles.registerText, { color: isDarkMode ? '#58A6FF' : '#0366D6' }]}>
            {t('auth.noAccount')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 40,
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
    alignSelf: 'center',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 16,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  button: {
    height: 50,
    backgroundColor: '#0366D6',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  registerLink: {
    marginTop: 20,
    alignItems: 'center',
  },
  registerText: {
    fontSize: 16,
  },
});

export default LoginScreen;
