import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useNotifications, NotificationType } from '../../contexts/NotificationContext';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';
import { useNavigation } from '@react-navigation/native';
import { Picker } from '@react-native-picker/picker';

const CreateScheduledNotificationScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation();
  const { scheduleNotification, isLoading } = useNotifications();

  const [title, setTitle] = useState('');
  const [message, setMessage] = useState('');
  const [scheduledFor, setScheduledFor] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [type, setType] = useState<NotificationType>('Info');
  const [relatedEntityType, setRelatedEntityType] = useState('');
  const [relatedEntityId, setRelatedEntityId] = useState('');
  const [actionUrl, setActionUrl] = useState('');

  const notificationTypes: NotificationType[] = [
    'Info',
    'Success',
    'Warning',
    'Error',
    'Transaction',
    'Loan',
    'Budget',
    'Goal',
    'MonthlySummary',
  ];

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      const newDate = new Date(scheduledFor);
      newDate.setFullYear(selectedDate.getFullYear());
      newDate.setMonth(selectedDate.getMonth());
      newDate.setDate(selectedDate.getDate());
      setScheduledFor(newDate);
    }
  };

  const handleTimeChange = (event: any, selectedTime?: Date) => {
    setShowTimePicker(false);
    if (selectedTime) {
      const newDate = new Date(scheduledFor);
      newDate.setHours(selectedTime.getHours());
      newDate.setMinutes(selectedTime.getMinutes());
      setScheduledFor(newDate);
    }
  };

  const handleSubmit = async () => {
    if (!title.trim()) {
      alert(t('notifications.titleRequired'));
      return;
    }

    if (!message.trim()) {
      alert(t('notifications.messageRequired'));
      return;
    }

    // Ensure scheduled time is in the future
    if (scheduledFor <= new Date()) {
      alert(t('notifications.futureTimeRequired'));
      return;
    }

    const notificationData = {
      title,
      message,
      scheduledFor: scheduledFor.toISOString(),
      type,
      ...(relatedEntityType && { relatedEntityType }),
      ...(relatedEntityId && { relatedEntityId: parseInt(relatedEntityId, 10) }),
      ...(actionUrl && { actionUrl }),
    };

    await scheduleNotification(notificationData);
    navigation.goBack();
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      <ScrollView
        style={[
          styles.container,
          { backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA' },
        ]}
        contentContainerStyle={styles.contentContainer}
      >
        <View style={styles.formGroup}>
          <Text
            style={[
              styles.label,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {t('notifications.title')} *
          </Text>
          <TextInput
            style={[
              styles.input,
              {
                backgroundColor: isDarkMode ? '#1C2128' : '#FFFFFF',
                color: isDarkMode ? '#FFFFFF' : '#000000',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
            value={title}
            onChangeText={setTitle}
            placeholder={t('notifications.enterTitle')}
            placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          />
        </View>

        <View style={styles.formGroup}>
          <Text
            style={[
              styles.label,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {t('notifications.message')} *
          </Text>
          <TextInput
            style={[
              styles.textArea,
              {
                backgroundColor: isDarkMode ? '#1C2128' : '#FFFFFF',
                color: isDarkMode ? '#FFFFFF' : '#000000',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
            value={message}
            onChangeText={setMessage}
            placeholder={t('notifications.enterMessage')}
            placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        <View style={styles.formGroup}>
          <Text
            style={[
              styles.label,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {t('notifications.scheduledDate')} *
          </Text>
          <TouchableOpacity
            style={[
              styles.datePickerButton,
              {
                backgroundColor: isDarkMode ? '#1C2128' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
            onPress={() => setShowDatePicker(true)}
          >
            <Text
              style={{
                color: isDarkMode ? '#FFFFFF' : '#000000',
              }}
            >
              {format(scheduledFor, 'PP')}
            </Text>
            <Ionicons
              name="calendar-outline"
              size={20}
              color={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
          </TouchableOpacity>
          {showDatePicker && (
            <DateTimePicker
              value={scheduledFor}
              mode="date"
              display="default"
              onChange={handleDateChange}
            />
          )}
        </View>

        <View style={styles.formGroup}>
          <Text
            style={[
              styles.label,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {t('notifications.scheduledTime')} *
          </Text>
          <TouchableOpacity
            style={[
              styles.datePickerButton,
              {
                backgroundColor: isDarkMode ? '#1C2128' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
            onPress={() => setShowTimePicker(true)}
          >
            <Text
              style={{
                color: isDarkMode ? '#FFFFFF' : '#000000',
              }}
            >
              {format(scheduledFor, 'p')}
            </Text>
            <Ionicons
              name="time-outline"
              size={20}
              color={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
          </TouchableOpacity>
          {showTimePicker && (
            <DateTimePicker
              value={scheduledFor}
              mode="time"
              display="default"
              onChange={handleTimeChange}
            />
          )}
        </View>

        <View style={styles.formGroup}>
          <Text
            style={[
              styles.label,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {t('notifications.type')}
          </Text>
          <View
            style={[
              styles.pickerContainer,
              {
                backgroundColor: isDarkMode ? '#1C2128' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
          >
            <Picker
              selectedValue={type}
              onValueChange={(itemValue) => setType(itemValue as NotificationType)}
              style={{
                color: isDarkMode ? '#FFFFFF' : '#000000',
              }}
              dropdownIconColor={isDarkMode ? '#FFFFFF' : '#000000'}
            >
              {notificationTypes.map((type) => (
                <Picker.Item
                  key={type}
                  label={type}
                  value={type}
                  color={isDarkMode ? '#FFFFFF' : '#000000'}
                />
              ))}
            </Picker>
          </View>
        </View>

        <View style={styles.formGroup}>
          <Text
            style={[
              styles.label,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {t('notifications.relatedEntityType')} ({t('common.optional')})
          </Text>
          <TextInput
            style={[
              styles.input,
              {
                backgroundColor: isDarkMode ? '#1C2128' : '#FFFFFF',
                color: isDarkMode ? '#FFFFFF' : '#000000',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
            value={relatedEntityType}
            onChangeText={setRelatedEntityType}
            placeholder={t('notifications.enterRelatedEntityType')}
            placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          />
        </View>

        <View style={styles.formGroup}>
          <Text
            style={[
              styles.label,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {t('notifications.relatedEntityId')} ({t('common.optional')})
          </Text>
          <TextInput
            style={[
              styles.input,
              {
                backgroundColor: isDarkMode ? '#1C2128' : '#FFFFFF',
                color: isDarkMode ? '#FFFFFF' : '#000000',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
            value={relatedEntityId}
            onChangeText={setRelatedEntityId}
            placeholder={t('notifications.enterRelatedEntityId')}
            placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
            keyboardType="numeric"
          />
        </View>

        <View style={styles.formGroup}>
          <Text
            style={[
              styles.label,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {t('notifications.actionUrl')} ({t('common.optional')})
          </Text>
          <TextInput
            style={[
              styles.input,
              {
                backgroundColor: isDarkMode ? '#1C2128' : '#FFFFFF',
                color: isDarkMode ? '#FFFFFF' : '#000000',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
            value={actionUrl}
            onChangeText={setActionUrl}
            placeholder={t('notifications.enterActionUrl')}
            placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          />
        </View>

        <TouchableOpacity
          style={[
            styles.submitButton,
            {
              backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
              opacity: isLoading ? 0.7 : 1,
            },
          ]}
          onPress={handleSubmit}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#FFFFFF" size="small" />
          ) : (
            <Text style={styles.submitButtonText}>
              {t('notifications.scheduleNotification')}
            </Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingTop: 12,
    fontSize: 16,
    minHeight: 100,
  },
  datePickerButton: {
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pickerContainer: {
    borderWidth: 1,
    borderRadius: 8,
    overflow: 'hidden',
  },
  submitButton: {
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CreateScheduledNotificationScreen;
