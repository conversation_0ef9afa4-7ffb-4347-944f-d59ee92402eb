import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useNotifications, MonthlySummary } from '../../contexts/NotificationContext';
import { Ionicons } from '@expo/vector-icons';
import { format, subMonths } from 'date-fns';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { BarChart, LineChart } from 'react-native-chart-kit';
import { Dimensions } from 'react-native';

// Define route params type
type MonthlySummaryRouteParams = {
  month?: number;
  year?: number;
};

const screenWidth = Dimensions.get('window').width;

const MonthlySummaryScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation();
  const route = useRoute<RouteProp<Record<string, MonthlySummaryRouteParams>, string>>();
  const { getMonthlySummary, isLoading } = useNotifications();

  // Get current month and year if not provided in route params
  const now = new Date();
  const initialMonth = route.params?.month || now.getMonth() + 1; // 1-12
  const initialYear = route.params?.year || now.getFullYear();

  const [month, setMonth] = useState(initialMonth);
  const [year, setYear] = useState(initialYear);
  const [summary, setSummary] = useState<MonthlySummary | null>(null);
  const [loadingData, setLoadingData] = useState(false);

  useEffect(() => {
    fetchMonthlySummary();
  }, [month, year]);

  const fetchMonthlySummary = async () => {
    setLoadingData(true);
    const data = await getMonthlySummary(month, year);
    setSummary(data);
    setLoadingData(false);
  };

  const handlePreviousMonth = () => {
    const date = new Date(year, month - 1, 1); // month is 0-indexed in Date
    const prevMonth = subMonths(date, 1);
    setMonth(prevMonth.getMonth() + 1); // convert back to 1-indexed
    setYear(prevMonth.getFullYear());
  };

  const handleNextMonth = () => {
    const date = new Date(year, month - 1, 1); // month is 0-indexed in Date
    const nextMonth = new Date(date);
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    setMonth(nextMonth.getMonth() + 1); // convert back to 1-indexed
    setYear(nextMonth.getFullYear());
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getMonthName = (monthNum: number) => {
    const date = new Date();
    date.setMonth(monthNum - 1);
    return format(date, 'MMMM');
  };

  const chartConfig = {
    backgroundGradientFrom: isDarkMode ? '#1C2128' : '#FFFFFF',
    backgroundGradientTo: isDarkMode ? '#1C2128' : '#FFFFFF',
    color: (opacity = 1) => isDarkMode ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
    strokeWidth: 2,
    barPercentage: 0.5,
    useShadowColorFromDataset: false,
    decimalPlaces: 0,
  };

  const renderExpensesByCategoryChart = () => {
    if (!summary || !summary.expensesByCategory || summary.expensesByCategory.length === 0) {
      return null;
    }

    // Sort categories by amount (descending)
    const sortedCategories = [...summary.expensesByCategory].sort((a, b) => b.amount - a.amount);

    // Take top 5 categories for better visualization
    const topCategories = sortedCategories.slice(0, 5);

    const data = {
      labels: topCategories.map(cat => cat.category),
      datasets: [
        {
          data: topCategories.map(cat => cat.amount),
          color: (opacity = 1) => isDarkMode ? `rgba(88, 166, 255, ${opacity})` : `rgba(3, 102, 214, ${opacity})`,
        },
      ],
    };

    return (
      <View style={styles.chartContainer}>
        <Text style={[styles.chartTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('notifications.topExpenseCategories')}
        </Text>
        <BarChart
          data={data}
          width={screenWidth - 32}
          height={220}
          chartConfig={chartConfig}
          verticalLabelRotation={30}
          fromZero
          showValuesOnTopOfBars
          yAxisLabel=""
          yAxisSuffix=""
        />
      </View>
    );
  };

  const renderDailyTrendChart = () => {
    if (!summary || !summary.dailyTrend || summary.dailyTrend.length === 0) {
      return null;
    }

    // Format dates for display
    const formattedDates = summary.dailyTrend.map(item => {
      const date = new Date(item.date);
      return format(date, 'd');
    });

    const data = {
      labels: formattedDates,
      datasets: [
        {
          data: summary.dailyTrend.map(item => item.expense),
          color: (opacity = 1) => isDarkMode ? `rgba(218, 54, 51, ${opacity})` : `rgba(215, 58, 73, ${opacity})`,
          strokeWidth: 2,
        },
        {
          data: summary.dailyTrend.map(item => item.income),
          color: (opacity = 1) => isDarkMode ? `rgba(35, 134, 54, ${opacity})` : `rgba(46, 160, 67, ${opacity})`,
          strokeWidth: 2,
        },
      ],
      legend: [t('common.expense'), t('common.income')],
    };

    return (
      <View style={styles.chartContainer}>
        <Text style={[styles.chartTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('notifications.dailyTrend')}
        </Text>
        <LineChart
          data={data}
          width={screenWidth - 32}
          height={220}
          chartConfig={chartConfig}
          bezier
        />
      </View>
    );
  };

  if (isLoading || loadingData) {
    return (
      <View
        style={[
          styles.loadingContainer,
          { backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA' },
        ]}
      >
        <ActivityIndicator
          size="large"
          color={isDarkMode ? '#58A6FF' : '#0366D6'}
        />
      </View>
    );
  }

  return (
    <ScrollView
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA' },
      ]}
      contentContainerStyle={styles.contentContainer}
    >
      <View style={styles.header}>
        <TouchableOpacity onPress={handlePreviousMonth}>
          <Ionicons
            name="chevron-back"
            size={24}
            color={isDarkMode ? '#FFFFFF' : '#000000'}
          />
        </TouchableOpacity>
        <Text
          style={[
            styles.monthTitle,
            { color: isDarkMode ? '#FFFFFF' : '#000000' },
          ]}
        >
          {getMonthName(month)} {year}
        </Text>
        <TouchableOpacity
          onPress={handleNextMonth}
          disabled={month === now.getMonth() + 1 && year === now.getFullYear()}
        >
          <Ionicons
            name="chevron-forward"
            size={24}
            color={
              month === now.getMonth() + 1 && year === now.getFullYear()
                ? isDarkMode
                  ? '#8B949E'
                  : '#6E7781'
                : isDarkMode
                ? '#FFFFFF'
                : '#000000'
            }
          />
        </TouchableOpacity>
      </View>

      {!summary ? (
        <View style={styles.noDataContainer}>
          <Ionicons
            name="analytics-outline"
            size={64}
            color={isDarkMode ? '#8B949E' : '#6E7781'}
          />
          <Text
            style={[
              styles.noDataText,
              { color: isDarkMode ? '#C9D1D9' : '#24292E' },
            ]}
          >
            {t('notifications.noSummaryData')}
          </Text>
        </View>
      ) : (
        <>
          <View
            style={[
              styles.summaryCard,
              {
                backgroundColor: isDarkMode ? '#1C2128' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
          >
            <View style={styles.summaryRow}>
              <View style={styles.summaryItem}>
                <Text
                  style={[
                    styles.summaryLabel,
                    { color: isDarkMode ? '#8B949E' : '#6E7781' },
                  ]}
                >
                  {t('common.totalIncome')}
                </Text>
                <Text
                  style={[
                    styles.summaryValue,
                    { color: isDarkMode ? '#238636' : '#2EA043' },
                  ]}
                >
                  {formatCurrency(summary.totalIncome)}
                </Text>
              </View>
              <View style={styles.summaryItem}>
                <Text
                  style={[
                    styles.summaryLabel,
                    { color: isDarkMode ? '#8B949E' : '#6E7781' },
                  ]}
                >
                  {t('common.totalExpense')}
                </Text>
                <Text
                  style={[
                    styles.summaryValue,
                    { color: isDarkMode ? '#DA3633' : '#D73A49' },
                  ]}
                >
                  {formatCurrency(summary.totalExpense)}
                </Text>
              </View>
            </View>
            <View style={styles.summaryRow}>
              <View style={styles.summaryItem}>
                <Text
                  style={[
                    styles.summaryLabel,
                    { color: isDarkMode ? '#8B949E' : '#6E7781' },
                  ]}
                >
                  {t('common.netSavings')}
                </Text>
                <Text
                  style={[
                    styles.summaryValue,
                    {
                      color:
                        summary.netSavings >= 0
                          ? isDarkMode
                            ? '#238636'
                            : '#2EA043'
                          : isDarkMode
                          ? '#DA3633'
                          : '#D73A49',
                    },
                  ]}
                >
                  {formatCurrency(summary.netSavings)}
                </Text>
              </View>
              <View style={styles.summaryItem}>
                <Text
                  style={[
                    styles.summaryLabel,
                    { color: isDarkMode ? '#8B949E' : '#6E7781' },
                  ]}
                >
                  {t('common.savingsRate')}
                </Text>
                <Text
                  style={[
                    styles.summaryValue,
                    {
                      color:
                        summary.savingsRate >= 0
                          ? isDarkMode
                            ? '#238636'
                            : '#2EA043'
                          : isDarkMode
                          ? '#DA3633'
                          : '#D73A49',
                    },
                  ]}
                >
                  {summary.savingsRate.toFixed(1)}%
                </Text>
              </View>
            </View>
          </View>

          <View
            style={[
              styles.statsCard,
              {
                backgroundColor: isDarkMode ? '#1C2128' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
          >
            <Text
              style={[
                styles.cardTitle,
                { color: isDarkMode ? '#FFFFFF' : '#000000' },
              ]}
            >
              {t('notifications.keyStats')}
            </Text>
            <View style={styles.statItem}>
              <Text
                style={[
                  styles.statLabel,
                  { color: isDarkMode ? '#8B949E' : '#6E7781' },
                ]}
              >
                {t('notifications.transactionCount')}
              </Text>
              <Text
                style={[
                  styles.statValue,
                  { color: isDarkMode ? '#FFFFFF' : '#000000' },
                ]}
              >
                {summary.transactionCount}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text
                style={[
                  styles.statLabel,
                  { color: isDarkMode ? '#8B949E' : '#6E7781' },
                ]}
              >
                {t('notifications.averageExpense')}
              </Text>
              <Text
                style={[
                  styles.statValue,
                  { color: isDarkMode ? '#FFFFFF' : '#000000' },
                ]}
              >
                {formatCurrency(summary.averageExpense)}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text
                style={[
                  styles.statLabel,
                  { color: isDarkMode ? '#8B949E' : '#6E7781' },
                ]}
              >
                {t('notifications.largestExpense')}
              </Text>
              <Text
                style={[
                  styles.statValue,
                  { color: isDarkMode ? '#FFFFFF' : '#000000' },
                ]}
              >
                {formatCurrency(summary.largestExpense)}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text
                style={[
                  styles.statLabel,
                  { color: isDarkMode ? '#8B949E' : '#6E7781' },
                ]}
              >
                {t('notifications.topExpenseCategory')}
              </Text>
              <Text
                style={[
                  styles.statValue,
                  { color: isDarkMode ? '#FFFFFF' : '#000000' },
                ]}
              >
                {summary.topExpenseCategory}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text
                style={[
                  styles.statLabel,
                  { color: isDarkMode ? '#8B949E' : '#6E7781' },
                ]}
              >
                {t('notifications.topIncomeSource')}
              </Text>
              <Text
                style={[
                  styles.statValue,
                  { color: isDarkMode ? '#FFFFFF' : '#000000' },
                ]}
              >
                {summary.topIncomeSource}
              </Text>
            </View>
          </View>

          {renderExpensesByCategoryChart()}
          {renderDailyTrendChart()}
        </>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  monthTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  summaryCard: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryItem: {
    flex: 1,
  },
  summaryLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  statsCard: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  statItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statLabel: {
    fontSize: 14,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  chartContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#D0D7DE',
    alignItems: 'center',
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  noDataContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  noDataText: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
});

export default MonthlySummaryScreen;
