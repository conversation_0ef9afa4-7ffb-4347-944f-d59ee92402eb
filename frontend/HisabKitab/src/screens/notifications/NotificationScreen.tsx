import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useNotifications } from '../../contexts/NotificationContext';
import { Notification } from '../../models/Notification';
import NotificationItem from '../../components/notifications/NotificationItem';
import EmptyState from '../../components/EmptyState';
import SegmentedControl from '../../components/common/SegmentedControl';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import ScheduledNotificationsScreen from './ScheduledNotificationsScreen';
import MonthlySummaryScreen from './MonthlySummaryScreen';

const Tab = createMaterialTopTabNavigator();

// Regular notifications tab component
const RegularNotificationsTab = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();

  const {
    notifications,
    isLoading,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
  } = useNotifications();

  const [filter, setFilter] = useState('all'); // 'all', 'unread', 'read'
  const [refreshing, setRefreshing] = useState(false);

  // Filter notifications based on selected filter
  const filteredNotifications = notifications.filter((notification) => {
    if (filter === 'unread') return !notification.isRead;
    if (filter === 'read') return notification.isRead;
    return true; // 'all'
  });

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchNotifications();
    setRefreshing(false);
  };

  // Handle notification press
  const handleNotificationPress = async (notification: Notification) => {
    // Mark as read if not already read
    if (!notification.isRead) {
      await markAsRead(notification.id);
    }

    // Navigate based on notification type and referenceId
    if (notification.referenceId) {
      switch (notification.type) {
        case 'Loan':
          navigation.navigate('LoanDetails', { loanId: notification.referenceId });
          break;
        case 'Transaction':
          navigation.navigate('TransactionDetails', { transactionId: notification.referenceId });
          break;
        case 'Family':
          navigation.navigate('FamilyDetails', { familyId: notification.referenceId });
          break;
        case 'Account':
          navigation.navigate('AccountDetails', { accountId: notification.referenceId });
          break;
        default:
          // Do nothing for unknown types
          break;
      }
    }
  };

  // Handle mark as read
  const handleMarkAsRead = async (id: number) => {
    try {
      await markAsRead(id);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    try {
      const success = await markAllAsRead();
      if (success) {
        Alert.alert(t('notifications.allMarkedAsRead'));
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // Handle delete notification
  const handleDeleteNotification = async (id: number) => {
    try {
      await deleteNotification(id);
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  // Render empty state
  const renderEmptyState = () => {
    return (
      <EmptyState
        icon="notifications-outline"
        title={t('notifications.noNotifications')}
        message={
          filter === 'all'
            ? t('notifications.noNotificationsMessage')
            : filter === 'unread'
            ? t('notifications.noUnreadNotificationsMessage')
            : t('notifications.noReadNotificationsMessage')
        }
        isDarkMode={isDarkMode}
      />
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('notifications.regularNotifications')}
        </Text>
        {notifications.length > 0 && (
          <TouchableOpacity
            style={styles.markAllButton}
            onPress={handleMarkAllAsRead}
            disabled={!notifications.some(n => !n.isRead)}
          >
            <Text
              style={[
                styles.markAllText,
                {
                  color: notifications.some(n => !n.isRead)
                    ? isDarkMode
                      ? '#58A6FF'
                      : '#0366D6'
                    : isDarkMode
                    ? '#8B949E'
                    : '#6E7781',
                },
              ]}
            >
              {t('notifications.markAllAsRead')}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Filter */}
      <View style={styles.filterContainer}>
        <SegmentedControl
          values={[
            { label: t('notifications.all'), value: 'all' },
            { label: t('notifications.unread'), value: 'unread' },
            { label: t('notifications.read'), value: 'read' },
          ]}
          selectedValue={filter}
          onChange={setFilter}
          isDarkMode={isDarkMode}
        />
      </View>

      {/* Notifications list */}
      {isLoading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
        </View>
      ) : (
        <FlatList
          data={filteredNotifications}
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => (
            <NotificationItem
              notification={item}
              onPress={handleNotificationPress}
              onMarkAsRead={handleMarkAsRead}
              onDelete={handleDeleteNotification}
              isDarkMode={isDarkMode}
            />
          )}
          contentContainerStyle={
            filteredNotifications.length === 0 ? styles.emptyListContainer : styles.listContainer
          }
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#0366D6']}
              tintColor={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
          }
        />
      )}
    </View>
  );
};

// Main notification screen with tabs
const NotificationScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();

  // Add a button to create scheduled notifications
  React.useLayoutEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <TouchableOpacity
          style={{ marginRight: 16 }}
          onPress={() => navigation.navigate('CreateScheduledNotification')}
        >
          <Ionicons
            name="add-circle-outline"
            size={24}
            color={isDarkMode ? '#FFFFFF' : '#000000'}
          />
        </TouchableOpacity>
      ),
    });
  }, [navigation, isDarkMode]);

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarStyle: {
          backgroundColor: isDarkMode ? '#1C2128' : '#FFFFFF',
        },
        tabBarActiveTintColor: isDarkMode ? '#58A6FF' : '#0366D6',
        tabBarInactiveTintColor: isDarkMode ? '#8B949E' : '#6E7781',
        tabBarIndicatorStyle: {
          backgroundColor: isDarkMode ? '#58A6FF' : '#0366D6',
        },
      }}
    >
      <Tab.Screen
        name="Regular"
        component={RegularNotificationsTab}
        options={{ tabBarLabel: t('notifications.regular') }}
      />
      <Tab.Screen
        name="Scheduled"
        component={ScheduledNotificationsScreen}
        options={{ tabBarLabel: t('notifications.scheduled') }}
      />
      <Tab.Screen
        name="Summary"
        component={MonthlySummaryScreen}
        options={{ tabBarLabel: t('notifications.summary') }}
      />
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  markAllButton: {
    padding: 8,
  },
  markAllText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  filterContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 16,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default NotificationScreen;
