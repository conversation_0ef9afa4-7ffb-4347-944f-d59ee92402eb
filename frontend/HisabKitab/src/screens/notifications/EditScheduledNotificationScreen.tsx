import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Switch,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useNotifications, ScheduledNotification } from '../../contexts/NotificationContext';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';

// Define route params type
type EditNotificationRouteParams = {
  notification: ScheduledNotification;
};

const EditScheduledNotificationScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation();
  const route = useRoute<RouteProp<Record<string, EditNotificationRouteParams>, string>>();
  const { updateScheduledNotification, isLoading } = useNotifications();

  const notification = route.params?.notification;

  const [title, setTitle] = useState('');
  const [message, setMessage] = useState('');
  const [scheduledFor, setScheduledFor] = useState(new Date());
  const [isActive, setIsActive] = useState(true);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);

  useEffect(() => {
    if (notification) {
      setTitle(notification.title);
      setMessage(notification.message);
      setScheduledFor(new Date(notification.scheduledFor));
      setIsActive(notification.isActive);
    }
  }, [notification]);

  const handleDateChange = (_: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      const newDate = new Date(scheduledFor);
      newDate.setFullYear(selectedDate.getFullYear());
      newDate.setMonth(selectedDate.getMonth());
      newDate.setDate(selectedDate.getDate());
      setScheduledFor(newDate);
    }
  };

  const handleTimeChange = (_: any, selectedTime?: Date) => {
    setShowTimePicker(false);
    if (selectedTime) {
      const newDate = new Date(scheduledFor);
      newDate.setHours(selectedTime.getHours());
      newDate.setMinutes(selectedTime.getMinutes());
      setScheduledFor(newDate);
    }
  };

  const handleSubmit = async () => {
    if (!title.trim()) {
      alert(t('notifications.titleRequired'));
      return;
    }

    if (!message.trim()) {
      alert(t('notifications.messageRequired'));
      return;
    }

    // Ensure scheduled time is in the future if not already sent
    if (!notification.isSent && scheduledFor <= new Date()) {
      alert(t('notifications.futureTimeRequired'));
      return;
    }

    const updates = {
      title,
      message,
      scheduledFor: scheduledFor.toISOString(),
      isActive,
    };

    await updateScheduledNotification(notification.id, updates);
    navigation.goBack();
  };

  if (!notification) {
    return (
      <View
        style={[
          styles.loadingContainer,
          { backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA' },
        ]}
      >
        <Text
          style={{
            color: isDarkMode ? '#FFFFFF' : '#000000',
          }}
        >
          {t('common.notFound')}
        </Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      <ScrollView
        style={[
          styles.container,
          { backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA' },
        ]}
        contentContainerStyle={styles.contentContainer}
      >
        <View style={styles.formGroup}>
          <Text
            style={[
              styles.label,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {t('notifications.title')} *
          </Text>
          <TextInput
            style={[
              styles.input,
              {
                backgroundColor: isDarkMode ? '#1C2128' : '#FFFFFF',
                color: isDarkMode ? '#FFFFFF' : '#000000',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
            value={title}
            onChangeText={setTitle}
            placeholder={t('notifications.enterTitle')}
            placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
          />
        </View>

        <View style={styles.formGroup}>
          <Text
            style={[
              styles.label,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {t('notifications.message')} *
          </Text>
          <TextInput
            style={[
              styles.textArea,
              {
                backgroundColor: isDarkMode ? '#1C2128' : '#FFFFFF',
                color: isDarkMode ? '#FFFFFF' : '#000000',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
            value={message}
            onChangeText={setMessage}
            placeholder={t('notifications.enterMessage')}
            placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        <View style={styles.formGroup}>
          <Text
            style={[
              styles.label,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {t('notifications.scheduledDate')} *
          </Text>
          <TouchableOpacity
            style={[
              styles.datePickerButton,
              {
                backgroundColor: isDarkMode ? '#1C2128' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
            onPress={() => setShowDatePicker(true)}
            disabled={notification.isSent}
          >
            <Text
              style={{
                color: isDarkMode ? '#FFFFFF' : '#000000',
                opacity: notification.isSent ? 0.5 : 1,
              }}
            >
              {format(scheduledFor, 'PP')}
            </Text>
            <Ionicons
              name="calendar-outline"
              size={20}
              color={
                notification.isSent
                  ? isDarkMode
                    ? '#8B949E'
                    : '#6E7781'
                  : isDarkMode
                  ? '#58A6FF'
                  : '#0366D6'
              }
            />
          </TouchableOpacity>
          {showDatePicker && !notification.isSent && (
            <DateTimePicker
              value={scheduledFor}
              mode="date"
              display="default"
              onChange={handleDateChange}
            />
          )}
        </View>

        <View style={styles.formGroup}>
          <Text
            style={[
              styles.label,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {t('notifications.scheduledTime')} *
          </Text>
          <TouchableOpacity
            style={[
              styles.datePickerButton,
              {
                backgroundColor: isDarkMode ? '#1C2128' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
            onPress={() => setShowTimePicker(true)}
            disabled={notification.isSent}
          >
            <Text
              style={{
                color: isDarkMode ? '#FFFFFF' : '#000000',
                opacity: notification.isSent ? 0.5 : 1,
              }}
            >
              {format(scheduledFor, 'p')}
            </Text>
            <Ionicons
              name="time-outline"
              size={20}
              color={
                notification.isSent
                  ? isDarkMode
                    ? '#8B949E'
                    : '#6E7781'
                  : isDarkMode
                  ? '#58A6FF'
                  : '#0366D6'
              }
            />
          </TouchableOpacity>
          {showTimePicker && !notification.isSent && (
            <DateTimePicker
              value={scheduledFor}
              mode="time"
              display="default"
              onChange={handleTimeChange}
            />
          )}
        </View>

        <View style={styles.formGroup}>
          <View style={styles.switchContainer}>
            <Text
              style={[
                styles.label,
                { color: isDarkMode ? '#FFFFFF' : '#000000' },
              ]}
            >
              {t('notifications.isActive')}
            </Text>
            <Switch
              value={isActive}
              onValueChange={setIsActive}
              trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
              thumbColor={isActive ? '#58A6FF' : '#f4f3f4'}
              disabled={notification.isSent}
            />
          </View>
          <Text
            style={[
              styles.helperText,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {isActive
              ? t('notifications.activeDescription')
              : t('notifications.inactiveDescription')}
          </Text>
        </View>

        {notification.isSent && (
          <View
            style={[
              styles.sentNotice,
              {
                backgroundColor: isDarkMode ? '#238636' : '#2EA043',
              },
            ]}
          >
            <Ionicons name="checkmark-circle" size={20} color="#FFFFFF" />
            <Text style={styles.sentNoticeText}>
              {t('notifications.alreadySent')}
              {notification.sentAt && ` (${format(new Date(notification.sentAt), 'PPp')})`}
            </Text>
          </View>
        )}

        <TouchableOpacity
          style={[
            styles.submitButton,
            {
              backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
              opacity: isLoading ? 0.7 : 1,
            },
          ]}
          onPress={handleSubmit}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#FFFFFF" size="small" />
          ) : (
            <Text style={styles.submitButtonText}>
              {t('common.save')}
            </Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingTop: 12,
    fontSize: 16,
    minHeight: 100,
  },
  datePickerButton: {
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  helperText: {
    fontSize: 14,
    marginTop: 4,
  },
  sentNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  sentNoticeText: {
    color: '#FFFFFF',
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  submitButton: {
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default EditScheduledNotificationScreen;
