import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Switch,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
// import { useNotifications } from '../../contexts/NotificationContext';
import { useFeatureFlag } from '../../contexts/FeatureFlagContext';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';

const NotificationSettingsScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  // In a real implementation, these would come from the context
  const [settings, setSettings] = useState({
    pushEnabled: false,
    emailEnabled: false,
    inAppEnabled: true,
    transactionNotifications: true,
    loanNotifications: true,
    familyNotifications: true,
    accountNotifications: true,
    budgetNotifications: true,
    savingsNotifications: true,
    reminderNotifications: true,
    systemNotifications: true,
    summaryEnabled: false,
    summaryFrequency: 'weekly',
  });

  // Mock update settings function
  const updateSettings = async (newSettings: any) => {
    setSettings(newSettings);
    return true;
  };
  const { isFeatureEnabled } = useFeatureFlag();

  // Notification settings state
  const [pushEnabled, setPushEnabled] = useState(settings?.pushEnabled || false);
  const [emailEnabled, setEmailEnabled] = useState(settings?.emailEnabled || false);
  const [inAppEnabled, setInAppEnabled] = useState(settings?.inAppEnabled || false);
  const [transactionNotifications, setTransactionNotifications] = useState(settings?.transactionNotifications || false);
  const [loanNotifications, setLoanNotifications] = useState(settings?.loanNotifications || false);
  const [familyNotifications, setFamilyNotifications] = useState(settings?.familyNotifications || false);
  const [accountNotifications, setAccountNotifications] = useState(settings?.accountNotifications || false);
  const [budgetNotifications, setBudgetNotifications] = useState(settings?.budgetNotifications || false);
  const [savingsNotifications, setSavingsNotifications] = useState(settings?.savingsNotifications || false);
  const [reminderNotifications, setReminderNotifications] = useState(settings?.reminderNotifications || false);
  const [systemNotifications, setSystemNotifications] = useState(settings?.systemNotifications || false);
  const [summaryEnabled, setSummaryEnabled] = useState(settings?.summaryEnabled || false);
  const [summaryFrequency, setSummaryFrequency] = useState(settings?.summaryFrequency || 'weekly');

  // Check notification permissions
  const [permissionsGranted, setPermissionsGranted] = useState(false);

  // Check notification permissions on mount
  useEffect(() => {
    checkNotificationPermissions();
  }, []);

  // Check notification permissions
  const checkNotificationPermissions = async () => {
    if (Device.isDevice) {
      const { status } = await Notifications.getPermissionsAsync();
      setPermissionsGranted(status === 'granted');
    }
  };

  // Request notification permissions
  const requestNotificationPermissions = async () => {
    if (Device.isDevice) {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        Alert.alert(
          t('notifications.permissionsDenied'),
          t('notifications.permissionsDeniedMessage')
        );
        return false;
      }

      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }

      setPermissionsGranted(true);
      Alert.alert(
        t('notifications.permissionsGranted'),
        t('notifications.permissionsGrantedMessage')
      );
      return true;
    } else {
      Alert.alert(
        t('common.error'),
        t('notifications.deviceRequired')
      );
      return false;
    }
  };

  // Handle toggle push notifications
  const handleTogglePush = async (value: boolean) => {
    if (value && !permissionsGranted) {
      const granted = await requestNotificationPermissions();
      if (!granted) {
        return;
      }
    }

    setPushEnabled(value);
    await updateSettings({ ...settings, pushEnabled: value });
  };

  // Handle toggle email notifications
  const handleToggleEmail = async (value: boolean) => {
    setEmailEnabled(value);
    await updateSettings({ ...settings, emailEnabled: value });
  };

  // Handle toggle in-app notifications
  const handleToggleInApp = async (value: boolean) => {
    setInAppEnabled(value);
    await updateSettings({ ...settings, inAppEnabled: value });
  };

  // Handle toggle transaction notifications
  const handleToggleTransaction = async (value: boolean) => {
    setTransactionNotifications(value);
    await updateSettings({ ...settings, transactionNotifications: value });
  };

  // Handle toggle loan notifications
  const handleToggleLoan = async (value: boolean) => {
    setLoanNotifications(value);
    await updateSettings({ ...settings, loanNotifications: value });
  };

  // Handle toggle family notifications
  const handleToggleFamily = async (value: boolean) => {
    setFamilyNotifications(value);
    await updateSettings({ ...settings, familyNotifications: value });
  };

  // Handle toggle account notifications
  const handleToggleAccount = async (value: boolean) => {
    setAccountNotifications(value);
    await updateSettings({ ...settings, accountNotifications: value });
  };

  // Handle toggle budget notifications
  const handleToggleBudget = async (value: boolean) => {
    setBudgetNotifications(value);
    await updateSettings({ ...settings, budgetNotifications: value });
  };

  // Handle toggle savings notifications
  const handleToggleSavings = async (value: boolean) => {
    setSavingsNotifications(value);
    await updateSettings({ ...settings, savingsNotifications: value });
  };

  // Handle toggle reminder notifications
  const handleToggleReminder = async (value: boolean) => {
    setReminderNotifications(value);
    await updateSettings({ ...settings, reminderNotifications: value });
  };

  // Handle toggle system notifications
  const handleToggleSystem = async (value: boolean) => {
    setSystemNotifications(value);
    await updateSettings({ ...settings, systemNotifications: value });
  };

  // Handle toggle summary
  const handleToggleSummary = async (value: boolean) => {
    setSummaryEnabled(value);
    await updateSettings({ ...settings, summaryEnabled: value });
  };

  // Handle select summary frequency
  const handleSelectSummaryFrequency = async (frequency: string) => {
    setSummaryFrequency(frequency);
    await updateSettings({ ...settings, summaryFrequency: frequency });
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Notification channels */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('notifications.notificationChannels')}
          </Text>

          <View style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
            <View style={styles.settingInfo}>
              <Ionicons
                name="phone-portrait-outline"
                size={24}
                color={isDarkMode ? '#FFFFFF' : '#000000'}
                style={styles.settingIcon}
              />
              <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {t('notifications.pushNotifications')}
              </Text>
            </View>
            <Switch
              value={pushEnabled}
              onValueChange={handleTogglePush}
              trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
              thumbColor={pushEnabled ? '#58A6FF' : '#f4f3f4'}
            />
          </View>

          {isFeatureEnabled('EmailNotifications') && (
            <View style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
              <View style={styles.settingInfo}>
                <Ionicons
                  name="mail-outline"
                  size={24}
                  color={isDarkMode ? '#FFFFFF' : '#000000'}
                  style={styles.settingIcon}
                />
                <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                  {t('notifications.emailNotifications')}
                </Text>
              </View>
              <Switch
                value={emailEnabled}
                onValueChange={handleToggleEmail}
                trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
                thumbColor={emailEnabled ? '#58A6FF' : '#f4f3f4'}
              />
            </View>
          )}

          <View style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
            <View style={styles.settingInfo}>
              <Ionicons
                name="apps-outline"
                size={24}
                color={isDarkMode ? '#FFFFFF' : '#000000'}
                style={styles.settingIcon}
              />
              <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {t('notifications.inAppNotifications')}
              </Text>
            </View>
            <Switch
              value={inAppEnabled}
              onValueChange={handleToggleInApp}
              trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
              thumbColor={inAppEnabled ? '#58A6FF' : '#f4f3f4'}
            />
          </View>
        </View>

        {/* Notification types */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('notifications.notificationTypes')}
          </Text>

          <View style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
            <View style={styles.settingInfo}>
              <Ionicons
                name="swap-horizontal-outline"
                size={24}
                color={isDarkMode ? '#FFFFFF' : '#000000'}
                style={styles.settingIcon}
              />
              <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {t('notifications.transactionNotifications')}
              </Text>
            </View>
            <Switch
              value={transactionNotifications}
              onValueChange={handleToggleTransaction}
              trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
              thumbColor={transactionNotifications ? '#58A6FF' : '#f4f3f4'}
            />
          </View>

          <View style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
            <View style={styles.settingInfo}>
              <Ionicons
                name="cash-outline"
                size={24}
                color={isDarkMode ? '#FFFFFF' : '#000000'}
                style={styles.settingIcon}
              />
              <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {t('notifications.loanNotifications')}
              </Text>
            </View>
            <Switch
              value={loanNotifications}
              onValueChange={handleToggleLoan}
              trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
              thumbColor={loanNotifications ? '#58A6FF' : '#f4f3f4'}
            />
          </View>

          <View style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
            <View style={styles.settingInfo}>
              <Ionicons
                name="people-outline"
                size={24}
                color={isDarkMode ? '#FFFFFF' : '#000000'}
                style={styles.settingIcon}
              />
              <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {t('notifications.familyNotifications')}
              </Text>
            </View>
            <Switch
              value={familyNotifications}
              onValueChange={handleToggleFamily}
              trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
              thumbColor={familyNotifications ? '#58A6FF' : '#f4f3f4'}
            />
          </View>

          <View style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
            <View style={styles.settingInfo}>
              <Ionicons
                name="wallet-outline"
                size={24}
                color={isDarkMode ? '#FFFFFF' : '#000000'}
                style={styles.settingIcon}
              />
              <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {t('notifications.accountNotifications')}
              </Text>
            </View>
            <Switch
              value={accountNotifications}
              onValueChange={handleToggleAccount}
              trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
              thumbColor={accountNotifications ? '#58A6FF' : '#f4f3f4'}
            />
          </View>

          <View style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
            <View style={styles.settingInfo}>
              <Ionicons
                name="calculator-outline"
                size={24}
                color={isDarkMode ? '#FFFFFF' : '#000000'}
                style={styles.settingIcon}
              />
              <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {t('notifications.budgetNotifications')}
              </Text>
            </View>
            <Switch
              value={budgetNotifications}
              onValueChange={handleToggleBudget}
              trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
              thumbColor={budgetNotifications ? '#58A6FF' : '#f4f3f4'}
            />
          </View>

          <View style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
            <View style={styles.settingInfo}>
              <Ionicons
                name="trending-up-outline"
                size={24}
                color={isDarkMode ? '#FFFFFF' : '#000000'}
                style={styles.settingIcon}
              />
              <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {t('notifications.savingsNotifications')}
              </Text>
            </View>
            <Switch
              value={savingsNotifications}
              onValueChange={handleToggleSavings}
              trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
              thumbColor={savingsNotifications ? '#58A6FF' : '#f4f3f4'}
            />
          </View>

          <View style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
            <View style={styles.settingInfo}>
              <Ionicons
                name="alarm-outline"
                size={24}
                color={isDarkMode ? '#FFFFFF' : '#000000'}
                style={styles.settingIcon}
              />
              <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {t('notifications.reminderNotifications')}
              </Text>
            </View>
            <Switch
              value={reminderNotifications}
              onValueChange={handleToggleReminder}
              trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
              thumbColor={reminderNotifications ? '#58A6FF' : '#f4f3f4'}
            />
          </View>

          <View style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
            <View style={styles.settingInfo}>
              <Ionicons
                name="information-circle-outline"
                size={24}
                color={isDarkMode ? '#FFFFFF' : '#000000'}
                style={styles.settingIcon}
              />
              <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {t('notifications.systemNotifications')}
              </Text>
            </View>
            <Switch
              value={systemNotifications}
              onValueChange={handleToggleSystem}
              trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
              thumbColor={systemNotifications ? '#58A6FF' : '#f4f3f4'}
            />
          </View>
        </View>

        {/* Notification summary */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('notifications.notificationSummary')}
          </Text>

          <View style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
            <View style={styles.settingInfo}>
              <Ionicons
                name="document-text-outline"
                size={24}
                color={isDarkMode ? '#FFFFFF' : '#000000'}
                style={styles.settingIcon}
              />
              <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {t('notifications.enableSummary')}
              </Text>
            </View>
            <Switch
              value={summaryEnabled}
              onValueChange={handleToggleSummary}
              trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
              thumbColor={summaryEnabled ? '#58A6FF' : '#f4f3f4'}
            />
          </View>

          {summaryEnabled && (
            <View style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
              <View style={styles.settingInfo}>
                <Ionicons
                  name="time-outline"
                  size={24}
                  color={isDarkMode ? '#FFFFFF' : '#000000'}
                  style={styles.settingIcon}
                />
                <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                  {t('notifications.summaryFrequency')}
                </Text>
              </View>
              <View style={styles.frequencySelector}>
                <TouchableOpacity
                  style={[
                    styles.frequencyOption,
                    summaryFrequency === 'daily' && {
                      backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
                    },
                  ]}
                  onPress={() => handleSelectSummaryFrequency('daily')}
                >
                  <Text
                    style={[
                      styles.frequencyText,
                      {
                        color:
                          summaryFrequency === 'daily'
                            ? '#FFFFFF'
                            : isDarkMode
                            ? '#C9D1D9'
                            : '#24292E',
                      },
                    ]}
                  >
                    {t('notifications.daily')}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.frequencyOption,
                    summaryFrequency === 'weekly' && {
                      backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
                    },
                  ]}
                  onPress={() => handleSelectSummaryFrequency('weekly')}
                >
                  <Text
                    style={[
                      styles.frequencyText,
                      {
                        color:
                          summaryFrequency === 'weekly'
                            ? '#FFFFFF'
                            : isDarkMode
                            ? '#C9D1D9'
                            : '#24292E',
                      },
                    ]}
                  >
                    {t('notifications.weekly')}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.frequencyOption,
                    summaryFrequency === 'monthly' && {
                      backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
                    },
                  ]}
                  onPress={() => handleSelectSummaryFrequency('monthly')}
                >
                  <Text
                    style={[
                      styles.frequencyText,
                      {
                        color:
                          summaryFrequency === 'monthly'
                            ? '#FFFFFF'
                            : isDarkMode
                            ? '#C9D1D9'
                            : '#24292E',
                      },
                    ]}
                  >
                    {t('notifications.monthly')}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>

        {/* Notification permissions */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('notifications.notificationPermissions')}
          </Text>

          <TouchableOpacity
            style={[
              styles.permissionButton,
              {
                backgroundColor: permissionsGranted
                  ? isDarkMode
                    ? '#238636'
                    : '#2EA043'
                  : isDarkMode
                  ? '#DA3633'
                  : '#D73A49',
              },
            ]}
            onPress={requestNotificationPermissions}
          >
            <Ionicons
              name={permissionsGranted ? 'checkmark-circle-outline' : 'alert-circle-outline'}
              size={24}
              color="#FFFFFF"
            />
            <Text style={styles.permissionButtonText}>
              {permissionsGranted
                ? t('notifications.permissionsGranted')
                : t('notifications.requestPermissions')}
            </Text>
          </TouchableOpacity>

          <Text
            style={[
              styles.permissionText,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {permissionsGranted
              ? t('notifications.permissionsGrantedMessage')
              : t('notifications.permissionsRequiredMessage')}
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingIcon: {
    marginRight: 12,
  },
  settingText: {
    fontSize: 16,
  },
  frequencySelector: {
    flexDirection: 'row',
  },
  frequencyOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    marginLeft: 8,
  },
  frequencyText: {
    fontSize: 14,
  },
  permissionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  permissionText: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default NotificationSettingsScreen;
