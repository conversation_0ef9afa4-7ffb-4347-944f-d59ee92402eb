import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useNotifications } from '../../contexts/NotificationContext';
import { Notification } from '../../models/Notification';
import EmptyState from '../../components/EmptyState';
import SegmentedControl from '../../components/common/SegmentedControl';
import NotificationItem from '../../components/notifications/NotificationItem';
import { useFeatureFlag } from '../../contexts/FeatureFlagContext';

const NotificationCenterScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { notifications, fetchNotifications, markAsRead, deleteNotification, markAllAsRead, isLoading } = useNotifications();

  // This function doesn't exist in the context, we'll implement it locally
  const deleteAllNotifications = async () => {
    // In a real implementation, this would call the API to delete all notifications
    return true;
  };
  const { isFeatureEnabled } = useFeatureFlag();

  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all');
  const [refreshing, setRefreshing] = useState(false);

  // Filter notifications based on selected filter
  const filteredNotifications = notifications.filter((notification: Notification) => {
    if (filter === 'unread') return !notification.isRead;
    if (filter === 'read') return notification.isRead;
    return true; // 'all'
  });

  // Fetch notifications on mount
  useEffect(() => {
    fetchNotifications();
  }, []);

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchNotifications();
    setRefreshing(false);
  };

  // Handle notification press
  const handleNotificationPress = async (notification: Notification) => {
    if (!notification.isRead) {
      await markAsRead(notification.id);
    }

    // Handle navigation based on notification type
    // In the real app, we would navigate to the appropriate screen
    // based on the notification type or reference ID
  };

  // Handle delete notification
  const handleDeleteNotification = async (id: number): Promise<void> => {
    Alert.alert(
      t('notifications.deleteConfirmTitle'),
      t('notifications.deleteConfirmMessage'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            const success = await deleteNotification(id);
            if (success) {
              Alert.alert(t('common.success'), t('notifications.deleteSuccess'));
            }
          },
        },
      ]
    );
  };

  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    if (notifications.filter((n: Notification) => !n.isRead).length === 0) {
      Alert.alert(t('common.info'), t('notifications.allAlreadyRead'));
      return;
    }

    const success = await markAllAsRead();
    if (success) {
      Alert.alert(t('common.success'), t('notifications.markAllReadSuccess'));
    }
  };

  // Handle delete all notifications
  const handleDeleteAllNotifications = () => {
    if (notifications.length === 0) {
      Alert.alert(t('common.info'), t('notifications.noNotifications'));
      return;
    }

    Alert.alert(
      t('notifications.deleteAllConfirmTitle'),
      t('notifications.deleteAllConfirmMessage'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            const success = await deleteAllNotifications();
            if (success) {
              Alert.alert(t('common.success'), t('notifications.deleteAllSuccess'));
            }
          },
        },
      ]
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      {/* Filter controls */}
      <View style={styles.filterContainer}>
        <SegmentedControl
          values={[
            { label: t('notifications.all'), value: 'all' },
            { label: t('notifications.unread'), value: 'unread' },
            { label: t('notifications.read'), value: 'read' },
          ]}
          selectedValue={filter}
          onChange={(value) => {
            setFilter(value as 'all' | 'unread' | 'read');
          }}
          isDarkMode={isDarkMode}
        />
      </View>

      {/* Action buttons */}
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={[
            styles.actionButton,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
          onPress={handleMarkAllAsRead}
        >
          <Ionicons
            name="checkmark-done-outline"
            size={20}
            color={isDarkMode ? '#58A6FF' : '#0366D6'}
          />
          <Text
            style={[
              styles.actionButtonText,
              { color: isDarkMode ? '#58A6FF' : '#0366D6' },
            ]}
          >
            {t('notifications.markAllAsRead')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.actionButton,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
          onPress={handleDeleteAllNotifications}
        >
          <Ionicons
            name="trash-outline"
            size={20}
            color={isDarkMode ? '#F85149' : '#D73A49'}
          />
          <Text
            style={[
              styles.actionButtonText,
              { color: isDarkMode ? '#F85149' : '#D73A49' },
            ]}
          >
            {t('notifications.deleteAll')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Notifications list */}
      {isLoading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
        </View>
      ) : (
        <FlatList
          data={filteredNotifications}
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => (
            <NotificationItem
              notification={item}
              onPress={handleNotificationPress}
              onMarkAsRead={async (id) => {
                // In a real implementation, this would mark the notification as read
                console.log('Mark as read:', id);
                return Promise.resolve();
              }}
              onDelete={handleDeleteNotification}
              isDarkMode={isDarkMode}
            />
          )}
          contentContainerStyle={
            filteredNotifications.length === 0 ? styles.emptyListContainer : styles.listContainer
          }
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#0366D6']}
              tintColor={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
          }
          ListEmptyComponent={
            <EmptyState
              icon="notifications-outline"
              title={t('notifications.noNotifications')}
              message={t('notifications.noNotificationsMessage')}
              isDarkMode={isDarkMode}
            />
          }
        />
      )}

      {/* Settings button - only visible if feature is enabled */}
      {isFeatureEnabled('NotificationSettings') && (
        <TouchableOpacity
          style={[
            styles.settingsButton,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
          onPress={() => {
            // Navigate to notification settings
            // navigation.navigate('NotificationSettings');
          }}
        >
          <Ionicons
            name="settings-outline"
            size={24}
            color={isDarkMode ? '#C9D1D9' : '#24292E'}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  filterContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  actionButtonText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  settingsButton: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
});

export default NotificationCenterScreen;
