import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useNotifications, ScheduledNotification } from '../../contexts/NotificationContext';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { useNavigation } from '@react-navigation/native';

// Define navigation type
type NotificationScreenNavigationProp = {
  navigate: (screen: string, params?: any) => void;
};

const ScheduledNotificationsScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();
  const {
    scheduledNotifications,
    fetchScheduledNotifications,
    deleteScheduledNotification,
    isLoading,
  } = useNotifications();

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchScheduledNotifications();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchScheduledNotifications();
    setRefreshing(false);
  };

  const handleDelete = (id: number) => {
    Alert.alert(
      t('common.confirm'),
      t('notifications.confirmDeleteScheduled'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          onPress: () => deleteScheduledNotification(id),
          style: 'destructive',
        },
      ]
    );
  };

  const handleEdit = (notification: ScheduledNotification) => {
    // Navigate to edit screen with notification data
    navigation.navigate('EditScheduledNotification', { notification });
  };

  const renderNotificationItem = ({ item }: { item: ScheduledNotification }) => {
    const scheduledDate = new Date(item.scheduledFor);
    const isPast = scheduledDate < new Date();
    const isSent = item.isSent;

    return (
      <View
        style={[
          styles.notificationItem,
          {
            backgroundColor: isDarkMode ? '#1C2128' : '#FFFFFF',
            borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
            opacity: isSent || !item.isActive ? 0.7 : 1,
          },
        ]}
      >
        <View style={styles.notificationHeader}>
          <View style={styles.titleContainer}>
            <Text
              style={[
                styles.notificationTitle,
                { color: isDarkMode ? '#FFFFFF' : '#000000' },
              ]}
            >
              {item.title}
            </Text>
            {!item.isActive && (
              <View
                style={[
                  styles.statusBadge,
                  { backgroundColor: isDarkMode ? '#30363D' : '#D0D7DE' },
                ]}
              >
                <Text
                  style={[
                    styles.statusText,
                    { color: isDarkMode ? '#8B949E' : '#57606A' },
                  ]}
                >
                  {t('notifications.inactive')}
                </Text>
              </View>
            )}
            {isSent && (
              <View
                style={[
                  styles.statusBadge,
                  { backgroundColor: isDarkMode ? '#238636' : '#2EA043' },
                ]}
              >
                <Text style={[styles.statusText, { color: '#FFFFFF' }]}>
                  {t('notifications.sent')}
                </Text>
              </View>
            )}
            {isPast && !isSent && (
              <View
                style={[
                  styles.statusBadge,
                  { backgroundColor: isDarkMode ? '#DA3633' : '#D73A49' },
                ]}
              >
                <Text style={[styles.statusText, { color: '#FFFFFF' }]}>
                  {t('notifications.missed')}
                </Text>
              </View>
            )}
          </View>
          <View style={styles.actions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleEdit(item)}
            >
              <Ionicons
                name="pencil"
                size={18}
                color={isDarkMode ? '#58A6FF' : '#0366D6'}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDelete(item.id)}
            >
              <Ionicons
                name="trash-outline"
                size={18}
                color={isDarkMode ? '#DA3633' : '#D73A49'}
              />
            </TouchableOpacity>
          </View>
        </View>
        <Text
          style={[
            styles.notificationMessage,
            { color: isDarkMode ? '#C9D1D9' : '#24292E' },
          ]}
        >
          {item.message}
        </Text>
        <View style={styles.notificationFooter}>
          <View style={styles.scheduleInfo}>
            <Ionicons
              name="calendar-outline"
              size={16}
              color={isDarkMode ? '#8B949E' : '#57606A'}
              style={styles.footerIcon}
            />
            <Text
              style={[
                styles.scheduleText,
                { color: isDarkMode ? '#8B949E' : '#57606A' },
              ]}
            >
              {format(scheduledDate, 'PPp')}
            </Text>
          </View>
          <View style={styles.typeInfo}>
            <Ionicons
              name="information-circle-outline"
              size={16}
              color={isDarkMode ? '#8B949E' : '#57606A'}
              style={styles.footerIcon}
            />
            <Text
              style={[
                styles.typeText,
                { color: isDarkMode ? '#8B949E' : '#57606A' },
              ]}
            >
              {item.type}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Ionicons
        name="notifications-off-outline"
        size={64}
        color={isDarkMode ? '#8B949E' : '#57606A'}
      />
      <Text
        style={[
          styles.emptyText,
          { color: isDarkMode ? '#C9D1D9' : '#24292E' },
        ]}
      >
        {t('notifications.noScheduledNotifications')}
      </Text>
    </View>
  );

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <Text
        style={[
          styles.headerText,
          { color: isDarkMode ? '#FFFFFF' : '#000000' },
        ]}
      >
        {t('notifications.scheduledNotifications')}
      </Text>
      <TouchableOpacity
        style={[
          styles.addButton,
          { backgroundColor: isDarkMode ? '#0D419D' : '#0366D6' },
        ]}
        onPress={() => navigation.navigate('CreateScheduledNotification')}
      >
        <Ionicons name="add" size={24} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );

  if (isLoading && !refreshing) {
    return (
      <View
        style={[
          styles.loadingContainer,
          { backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA' },
        ]}
      >
        <ActivityIndicator
          size="large"
          color={isDarkMode ? '#58A6FF' : '#0366D6'}
        />
      </View>
    );
  }

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA' },
      ]}
    >
      <FlatList
        data={scheduledNotifications}
        renderItem={renderNotificationItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContent}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyComponent}
        onRefresh={handleRefresh}
        refreshing={refreshing}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: 16,
    flexGrow: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationItem: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  titleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    marginRight: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  notificationMessage: {
    fontSize: 14,
    marginBottom: 12,
  },
  notificationFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  scheduleInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  footerIcon: {
    marginRight: 4,
  },
  scheduleText: {
    fontSize: 12,
  },
  typeText: {
    fontSize: 12,
  },
  actions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 4,
    marginLeft: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
});

export default ScheduledNotificationsScreen;
