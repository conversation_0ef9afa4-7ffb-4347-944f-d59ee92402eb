import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';

const CategoriesScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  
  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      <Text style={[styles.text, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
        {t('categories.categories')} - {t('common.comingSoon')}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  text: {
    fontSize: 18,
    textAlign: 'center',
  },
});

export default CategoriesScreen;
