import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { LoanReminder } from '../../models/Loan';
import EmptyState from '../../components/EmptyState';
import SegmentedControl from '../../components/common/SegmentedControl';
import { useLoans } from '../../contexts/LoanContext';

const ReminderManagementScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();
  const { isLoading } = useLoans();

  // Mock data and functions
  // Mock data
  const [reminders, setReminders] = useState<LoanReminder[]>([
    {
      id: 1,
      message: 'Monthly payment for home loan',
      reminderDate: new Date(),
      loanId: 1,
      isActive: true,
      isSent: false,
      createdAt: new Date(),
    },
    {
      id: 2,
      message: 'Monthly payment for car loan',
      reminderDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7), // 7 days from now
      loanId: 2,
      isActive: true,
      isSent: false,
      createdAt: new Date(),
    },
  ]);

  const fetchReminders = async () => {
    // In a real implementation, this would call the API
    console.log('Fetching reminders...');
    // No need to update state since we're using mock data
  };

  const deleteReminder = async (id: number) => {
    // In a real implementation, this would call the API
    console.log('Deleting reminder:', id);
    setReminders(reminders.filter(r => r.id !== id));
    return true;
  };

  const [filter, setFilter] = useState('active'); // 'all', 'active', 'upcoming'
  const [refreshing, setRefreshing] = useState(false);

  // Filter reminders based on selected filter
  const filteredReminders = reminders.filter((reminder) => {
    if (filter === 'active') return reminder.isActive;
    if (filter === 'upcoming') {
      const reminderDate = new Date(reminder.reminderDate);
      const now = new Date();
      return reminder.isActive && reminderDate > now;
    }
    return true; // 'all'
  });

  // Fetch reminders on mount
  useEffect(() => {
    fetchReminders();
  }, []);

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchReminders();
    setRefreshing(false);
  };

  // Handle reminder press
  const handleReminderPress = (reminder: LoanReminder) => {
    navigation.navigate('ReminderDetails', { reminderId: reminder.id });
  };

  // Handle add reminder
  const handleAddReminder = () => {
    navigation.navigate('CreateReminder');
  };

  // Handle delete reminder
  const handleDeleteReminder = async (id: number) => {
    Alert.alert(
      t('reminders.deleteConfirmTitle'),
      t('reminders.deleteConfirmMessage'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            const success = await deleteReminder(id);
            if (success) {
              Alert.alert(t('common.success'), t('reminders.deleteSuccess'));
            }
          },
        },
      ]
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      {/* Filter controls */}
      <View style={styles.filterContainer}>
        <SegmentedControl
          values={[
            { label: t('reminders.all'), value: 'all' },
            { label: t('reminders.active'), value: 'active' },
            { label: t('reminders.upcoming'), value: 'upcoming' },
          ]}
          selectedValue={filter}
          onChange={(value) => {
            setFilter(value as 'all' | 'active' | 'upcoming');
          }}
          isDarkMode={isDarkMode}
        />
      </View>

      {/* Reminders list */}
      {isLoading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
        </View>
      ) : (
        <FlatList
          data={filteredReminders}
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.reminderItem,
                { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
              ]}
              onPress={() => handleReminderPress(item)}
            >
              <View style={styles.reminderContent}>
                <Text
                  style={[
                    styles.reminderMessage,
                    { color: isDarkMode ? '#FFFFFF' : '#000000' },
                  ]}
                >
                  {item.message}
                </Text>
                <Text
                  style={[
                    styles.reminderDate,
                    { color: isDarkMode ? '#8B949E' : '#6E7781' },
                  ]}
                >
                  {new Date(item.reminderDate).toLocaleDateString()}
                </Text>
              </View>
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={() => handleDeleteReminder(item.id)}
              >
                <Ionicons
                  name="trash-outline"
                  size={20}
                  color={isDarkMode ? '#F85149' : '#D73A49'}
                />
              </TouchableOpacity>
            </TouchableOpacity>
          )}
          contentContainerStyle={
            filteredReminders.length === 0 ? styles.emptyListContainer : styles.listContainer
          }
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#0366D6']}
              tintColor={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
          }
          ListEmptyComponent={
            <EmptyState
              icon="notifications-outline"
              title={t('reminders.noReminders')}
              message={t('reminders.noRemindersMessage')}
              isDarkMode={isDarkMode}
            />
          }
        />
      )}

      {/* Add reminder button */}
      <TouchableOpacity
        style={[
          styles.addButton,
          { backgroundColor: isDarkMode ? '#1F6FEB' : '#0366D6' },
        ]}
        onPress={handleAddReminder}
      >
        <Ionicons name="add" size={24} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  filterContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 80, // Space for FAB
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  addButton: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  reminderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  reminderContent: {
    flex: 1,
  },
  reminderMessage: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  reminderDate: {
    fontSize: 14,
  },
  deleteButton: {
    padding: 8,
  },
});

export default ReminderManagementScreen;
