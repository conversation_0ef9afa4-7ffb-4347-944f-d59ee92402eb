import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Switch,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useLoans } from '../../contexts/LoanContext';
// Using native components instead of custom DateTimePicker and LoanSelector
import { LoanReminder } from '../../models/Loan';

const CreateReminderScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();
  const route = useRoute<any>();

  const { isLoading } = useLoans();

  // Mock functions
  const getReminder = async (id: number) => {
    // In a real implementation, this would call the API
    return {
      id,
      message: 'Monthly payment for home loan',
      reminderDate: new Date(),
      isRecurring: true,
      recurringType: 'monthly',
      loanId: 1,
      isActive: true,
      isSent: false,
      createdAt: new Date(),
    };
  };

  const createReminder = async (reminderData: any) => {
    // In a real implementation, this would call the API
    console.log('Creating reminder:', reminderData);
    return true;
  };

  const updateReminder = async (id: number, reminderData: any) => {
    // In a real implementation, this would call the API
    console.log('Updating reminder:', id, reminderData);
    return true;
  };

  const reminderId = route.params?.reminderId;
  const isEditing = !!reminderId;

  const [message, setMessage] = useState('');
  const [reminderDate, setReminderDate] = useState(new Date());
  const [isActive, setIsActive] = useState(true);
  const [selectedLoanId, setSelectedLoanId] = useState<number | null>(null);

  // Load reminder data if editing
  useEffect(() => {
    if (isEditing) {
      loadReminderData();
    }
  }, [isEditing, reminderId]);

  // Load reminder data
  const loadReminderData = async () => {
    const reminder = await getReminder(reminderId);
    if (reminder) {
      setMessage(reminder.message);
      setReminderDate(new Date(reminder.reminderDate));
      setIsActive(reminder.isActive);
      setSelectedLoanId(reminder.loanId);
    }
  };

  // Handle save
  const handleSave = async () => {
    // Validate inputs
    if (!message.trim()) {
      Alert.alert(t('common.error'), t('reminders.messageRequired'));
      return;
    }

    if (!selectedLoanId) {
      Alert.alert(t('common.error'), t('reminders.loanRequired'));
      return;
    }

    const reminderData: Partial<LoanReminder> = {
      message,
      reminderDate,
      isActive,
      loanId: selectedLoanId,
    };

    let success = false;

    if (isEditing) {
      success = await updateReminder(reminderId, reminderData);
    } else {
      success = await createReminder(reminderData);
    }

    if (success) {
      Alert.alert(
        t('common.success'),
        isEditing ? t('reminders.updateSuccess') : t('reminders.createSuccess'),
        [
          {
            text: t('common.ok'),
            onPress: () => navigation.goBack(),
          },
        ]
      );
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Loan selector */}
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: isDarkMode ? '#C9D1D9' : '#24292E' }]}>
            {t('reminders.loan')} *
          </Text>
          <TouchableOpacity
            style={[
              styles.input,
              {
                backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',

                borderColor: isDarkMode ? '#30363D' : '#E1E4E8',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              },
            ]}
            onPress={() => {
              // In a real implementation, this would show a loan selector
              console.log('Show loan selector');
              setSelectedLoanId(1); // Mock selection
            }}
          >
            <Text
              style={{
                fontSize: 16,
                color: isDarkMode ? '#FFFFFF' : '#000000',
              }}
            >
              {selectedLoanId ? `Loan #${selectedLoanId}` : t('reminders.selectLoan')}
            </Text>
            <Ionicons
              name="chevron-down"
              size={20}
              color={isDarkMode ? '#8B949E' : '#6E7781'}
            />
          </TouchableOpacity>
        </View>

        {/* Message input */}
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: isDarkMode ? '#C9D1D9' : '#24292E' }]}>
            {t('reminders.message')} *
          </Text>
          <TextInput
            style={[
              styles.input,
              {
                backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                color: isDarkMode ? '#FFFFFF' : '#000000',
                borderColor: isDarkMode ? '#30363D' : '#E1E4E8',
              },
            ]}
            value={message}
            onChangeText={setMessage}
            placeholder={t('reminders.messagePlaceholder')}
            placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
            multiline
            numberOfLines={3}
          />
        </View>

        {/* Date picker */}
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: isDarkMode ? '#C9D1D9' : '#24292E' }]}>
            {t('reminders.date')} *
          </Text>
          <TouchableOpacity
            style={[
              styles.input,
              {
                backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',

                borderColor: isDarkMode ? '#30363D' : '#E1E4E8',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              },
            ]}
            onPress={() => {
              // In a real implementation, this would show a date picker
              console.log('Show date picker');
            }}
          >
            <Text
              style={{
                fontSize: 16,
                color: isDarkMode ? '#FFFFFF' : '#000000',
              }}
            >
              {reminderDate.toLocaleString()}
            </Text>
            <Ionicons
              name="calendar-outline"
              size={20}
              color={isDarkMode ? '#8B949E' : '#6E7781'}
            />
          </TouchableOpacity>
        </View>

        {/* Active switch */}
        <View style={styles.formGroup}>
          <View style={styles.switchContainer}>
            <Text style={[styles.label, { color: isDarkMode ? '#C9D1D9' : '#24292E' }]}>
              {t('reminders.active')}
            </Text>
            <Switch
              value={isActive}
              onValueChange={setIsActive}
              trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
              thumbColor={isActive ? '#58A6FF' : '#f4f3f4'}
            />
          </View>
        </View>

        {/* Save button */}
        <TouchableOpacity
          style={[
            styles.saveButton,
            { backgroundColor: isDarkMode ? '#238636' : '#2EA043' },
          ]}
          onPress={handleSave}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={styles.saveButtonText}>
              {isEditing ? t('common.update') : t('common.save')}
            </Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  saveButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CreateReminderScreen;
