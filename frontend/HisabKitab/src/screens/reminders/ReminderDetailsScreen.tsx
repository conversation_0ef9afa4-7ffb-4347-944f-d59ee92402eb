import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useLoans } from '../../contexts/LoanContext';
import { LoanReminder } from '../../models/Loan';
import { formatDate } from '../../utils/formatters';
import { scheduleLocalNotification } from '../../services/pushNotifications';

const ReminderDetailsScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();
  const route = useRoute<any>();

  const { isLoading } = useLoans();

  // Mock functions
  const getReminder = async (id: number) => {
    // In a real implementation, this would call the API
    return {
      id,
      title: 'Loan Payment Reminder',
      message: 'Monthly payment for home loan',
      reminderDate: new Date(),
      isRecurring: true,
      recurringType: 'monthly',
      loanId: 1,
      isActive: true,
      isSent: false,
      createdAt: new Date(),
    };
  };

  const getLoan = async (id: number) => {
    // In a real implementation, this would call the API
    return {
      id,
      title: 'Home Loan',
      amount: 100000,
      interestRate: 5,
      startDate: new Date(),
      endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 365 * 30), // 30 years
    };
  };

  const deleteReminder = async (id: number) => {
    // In a real implementation, this would call the API
    console.log('Deleting reminder:', id);
    return true;
  };

  const reminderId = route.params?.reminderId;
  const [reminder, setReminder] = useState<LoanReminder | null>(null);
  const [loanTitle, setLoanTitle] = useState('');

  // Load reminder data
  useEffect(() => {
    loadReminderData();
  }, [reminderId]);

  // Load reminder data
  const loadReminderData = async () => {
    const reminderData = await getReminder(reminderId);
    if (reminderData) {
      setReminder(reminderData);

      // Get loan title
      const loan = await getLoan(reminderData.loanId);
      if (loan) {
        setLoanTitle(loan.title);
      }
    }
  };

  // Handle edit
  const handleEdit = () => {
    navigation.navigate('EditReminder', { reminderId });
  };

  // Handle delete
  const handleDelete = () => {
    Alert.alert(
      t('reminders.deleteConfirmTitle'),
      t('reminders.deleteConfirmMessage'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            const success = await deleteReminder(reminderId);
            if (success) {
              Alert.alert(
                t('common.success'),
                t('reminders.deleteSuccess'),
                [
                  {
                    text: t('common.ok'),
                    onPress: () => navigation.goBack(),
                  },
                ]
              );
            }
          },
        },
      ]
    );
  };

  // Handle schedule notification
  const handleScheduleNotification = async () => {
    if (!reminder) return;

    try {
      await scheduleLocalNotification(
        t('reminders.notificationTitle', { loan: loanTitle }),
        reminder.message,
        { reminderId: reminder.id, loanId: reminder.loanId },
        { date: new Date(reminder.reminderDate) }
      );

      Alert.alert(
        t('common.success'),
        t('reminders.notificationScheduled')
      );
    } catch (error) {
      Alert.alert(
        t('common.error'),
        t('reminders.notificationError')
      );
    }
  };

  if (isLoading || !reminder) {
    return (
      <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
        <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Reminder card */}
        <View
          style={[
            styles.card,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
        >
          {/* Reminder header */}
          <View style={styles.cardHeader}>
            <Ionicons
              name="notifications-outline"
              size={24}
              color={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
            <Text
              style={[
                styles.cardTitle,
                { color: isDarkMode ? '#FFFFFF' : '#000000' },
              ]}
            >
              {t('reminders.reminderDetails')}
            </Text>
          </View>

          {/* Reminder details */}
          <View style={styles.detailsContainer}>
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('reminders.message')}
              </Text>
              <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {reminder.message}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('reminders.date')}
              </Text>
              <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {formatDate(reminder.reminderDate)}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('reminders.loan')}
              </Text>
              <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {loanTitle}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('reminders.status')}
              </Text>
              <Text
                style={[
                  styles.detailValue,
                  {
                    color: reminder.isActive
                      ? isDarkMode ? '#3FB950' : '#2EA043'
                      : isDarkMode ? '#8B949E' : '#6E7781',
                  },
                ]}
              >
                {reminder.isActive ? t('reminders.active') : t('reminders.inactive')}
              </Text>
            </View>
          </View>
        </View>

        {/* Action buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[
              styles.actionButton,
              { backgroundColor: isDarkMode ? '#238636' : '#2EA043' },
            ]}
            onPress={handleScheduleNotification}
          >
            <Ionicons name="notifications" size={20} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>
              {t('reminders.scheduleNotification')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.actionButton,
              { backgroundColor: isDarkMode ? '#0D419D' : '#0366D6' },
            ]}
            onPress={handleEdit}
          >
            <Ionicons name="pencil" size={20} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>
              {t('common.edit')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.actionButton,
              { backgroundColor: isDarkMode ? '#DA3633' : '#D73A49' },
            ]}
            onPress={handleDelete}
          >
            <Ionicons name="trash" size={20} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>
              {t('common.delete')}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
  },
  scrollContent: {
    padding: 16,
  },
  card: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  detailsContainer: {
    marginTop: 8,
  },
  detailRow: {
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  actionButtons: {
    marginTop: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
});

export default ReminderDetailsScreen;
