import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useSync, SyncConflict } from '../../contexts/SyncContext';
import Button from '../../components/ui/Button';

const MergeEditorScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const { resolveConflict } = useSync();

  // Get conflict from route params
  const conflict: SyncConflict = route.params?.conflict;

  // State
  const [mergedData, setMergedData] = useState<string>('');
  const [isValid, setIsValid] = useState<boolean>(true);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [isResolving, setIsResolving] = useState<boolean>(false);

  // Initialize merged data with local data
  useEffect(() => {
    if (conflict) {
      setMergedData(JSON.stringify(conflict.localData, null, 2));
    }
  }, [conflict]);

  // Validate JSON
  const validateJson = (json: string): boolean => {
    try {
      JSON.parse(json);
      setIsValid(true);
      setErrorMessage('');
      return true;
    } catch (error: unknown) {
      setIsValid(false);
      setErrorMessage(error instanceof Error ? error.message : 'Invalid JSON format');
      return false;
    }
  };

  // Handle text change
  const handleTextChange = (text: string) => {
    setMergedData(text);
    validateJson(text);
  };

  // Use local data
  const useLocalData = () => {
    const localData = JSON.stringify(conflict.localData, null, 2);
    setMergedData(localData);
    validateJson(localData);
  };

  // Use remote data
  const useRemoteData = () => {
    const remoteData = JSON.stringify(conflict.remoteData, null, 2);
    setMergedData(remoteData);
    validateJson(remoteData);
  };

  // Handle save
  const handleSave = async () => {
    if (!isValid) {
      Alert.alert(
        t('common.error'),
        t('sync.invalidJson')
      );
      return;
    }

    try {
      setIsResolving(true);

      const parsedData = JSON.parse(mergedData);
      const success = await resolveConflict(conflict.id, 'merge', parsedData);

      if (success) {
        Alert.alert(
          t('common.success'),
          t('sync.conflictResolved'),
          [
            {
              text: t('common.ok'),
              onPress: () => navigation.goBack(),
            },
          ]
        );
      } else {
        Alert.alert(
          t('common.error'),
          t('sync.conflictResolutionFailed')
        );
      }
    } catch (error) {
      console.error('Error resolving conflict:', error);
      Alert.alert(
        t('common.error'),
        t('sync.conflictResolutionError')
      );
    } finally {
      setIsResolving(false);
    }
  };

  // If no conflict provided, show error
  if (!conflict) {
    return (
      <View style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }
      ]}>
        <Text style={[
          styles.errorText,
          { color: isDarkMode ? '#F85149' : '#F85149' }
        ]}>
          {t('sync.noConflictProvided')}
        </Text>

        <Button
          title={t('common.goBack')}
          onPress={() => navigation.goBack()}
          type="primary"
          style={styles.backButton}
        />
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }
      ]}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={100}
    >
      <View style={styles.header}>
        <Text style={[
          styles.title,
          { color: isDarkMode ? '#FFFFFF' : '#24292E' }
        ]}>
          {t('sync.mergeEditor')}
        </Text>

        <Text style={[
          styles.subtitle,
          { color: isDarkMode ? '#8B949E' : '#6E7781' }
        ]}>
          {t('sync.editMergedData')}
        </Text>
      </View>

      <View style={styles.actionsBar}>
        <Button
          title={t('sync.useLocal')}
          onPress={useLocalData}
          type="secondary"
          style={styles.actionButton}
          disabled={isResolving}
        />

        <Button
          title={t('sync.useRemote')}
          onPress={useRemoteData}
          type="secondary"
          style={styles.actionButton}
          disabled={isResolving}
        />
      </View>

      <View style={[
        styles.editorContainer,
        {
          backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
          borderColor: isValid ?
            (isDarkMode ? '#30363D' : '#D0D7DE') :
            '#F85149'
        }
      ]}>
        <ScrollView style={styles.editorScroll}>
          <TextInput
            style={[
              styles.editor,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' }
            ]}
            value={mergedData}
            onChangeText={handleTextChange}
            multiline
            autoCapitalize="none"
            autoCorrect={false}
            spellCheck={false}
            editable={!isResolving}
          />
        </ScrollView>
      </View>

      {!isValid && (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={16} color="#F85149" />
          <Text style={styles.errorText}>
            {errorMessage}
          </Text>
        </View>
      )}

      <View style={styles.footer}>
        <Button
          title={t('common.cancel')}
          onPress={() => navigation.goBack()}
          type="secondary"
          style={styles.footerButton}
          disabled={isResolving}
        />

        <Button
          title={t('common.save')}
          onPress={handleSave}
          type="primary"
          style={styles.footerButton}
          isLoading={isResolving}
          disabled={!isValid || isResolving}
        />
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
  },
  actionsBar: {
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 16,
  },
  actionButton: {
    marginHorizontal: 8,
    minWidth: 120,
  },
  editorContainer: {
    flex: 1,
    margin: 16,
    borderWidth: 1,
    borderRadius: 8,
  },
  editorScroll: {
    flex: 1,
    padding: 8,
  },
  editor: {
    fontFamily: 'monospace',
    fontSize: 14,
    minHeight: '100%',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginBottom: 8,
  },
  errorText: {
    color: '#F85149',
    marginLeft: 8,
    fontSize: 14,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#30363D',
  },
  footerButton: {
    marginLeft: 8,
    minWidth: 100,
  },
  backButton: {
    alignSelf: 'center',
    marginTop: 16,
    minWidth: 120,
  },
});

export default MergeEditorScreen;
