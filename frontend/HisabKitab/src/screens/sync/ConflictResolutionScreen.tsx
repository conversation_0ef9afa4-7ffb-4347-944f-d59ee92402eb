import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useSync, SyncConflict } from '../../contexts/SyncContext';
import Button from '../../components/ui/Button';
import EmptyState from '../../components/ui/EmptyState';
import { formatDate } from '../../utils/formatters';

const ConflictResolutionScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const navigation = useNavigation<any>();
  const { conflicts, resolveConflict } = useSync();
  
  // State
  const [selectedConflict, setSelectedConflict] = useState<SyncConflict | null>(null);
  const [isResolving, setIsResolving] = useState(false);
  const [mergedData, setMergedData] = useState<any>(null);
  
  // Filter unresolved conflicts
  const unresolvedConflicts = conflicts.filter(conflict => !conflict.resolved);
  
  // Get entity type display name
  const getEntityTypeName = (entityType: string): string => {
    switch (entityType) {
      case 'Transaction':
        return t('transactions.transaction');
      case 'Account':
        return t('accounts.account');
      case 'Category':
        return t('categories.category');
      case 'Receipt':
        return t('receipts.receipt');
      default:
        return entityType;
    }
  };
  
  // Get entity name
  const getEntityName = (conflict: SyncConflict): string => {
    const { entityType, localData } = conflict;
    
    switch (entityType) {
      case 'Transaction':
        return localData.description || `${t('transactions.transaction')} #${conflict.entityId}`;
      case 'Account':
        return localData.name || `${t('accounts.account')} #${conflict.entityId}`;
      case 'Category':
        return localData.name || `${t('categories.category')} #${conflict.entityId}`;
      case 'Receipt':
        return localData.merchantName || `${t('receipts.receipt')} #${conflict.entityId}`;
      default:
        return `${entityType} #${conflict.entityId}`;
    }
  };
  
  // Handle conflict selection
  const handleSelectConflict = (conflict: SyncConflict) => {
    setSelectedConflict(conflict);
    
    // Initialize merged data with local data
    setMergedData(JSON.parse(JSON.stringify(conflict.localData)));
  };
  
  // Handle conflict resolution
  const handleResolveConflict = async (resolution: 'local' | 'remote' | 'merge') => {
    if (!selectedConflict) return;
    
    try {
      setIsResolving(true);
      
      let success = false;
      
      if (resolution === 'merge') {
        if (!mergedData) {
          Alert.alert(
            t('common.error'),
            t('sync.noMergedData')
          );
          setIsResolving(false);
          return;
        }
        
        success = await resolveConflict(selectedConflict.id, resolution, mergedData);
      } else {
        success = await resolveConflict(selectedConflict.id, resolution);
      }
      
      if (success) {
        Alert.alert(
          t('common.success'),
          t('sync.conflictResolved')
        );
        
        setSelectedConflict(null);
        setMergedData(null);
      } else {
        Alert.alert(
          t('common.error'),
          t('sync.conflictResolutionFailed')
        );
      }
    } catch (error) {
      console.error('Error resolving conflict:', error);
      Alert.alert(
        t('common.error'),
        t('sync.conflictResolutionError')
      );
    } finally {
      setIsResolving(false);
    }
  };
  
  // Render conflict item
  const renderConflictItem = ({ item }: { item: SyncConflict }) => (
    <TouchableOpacity
      style={[
        styles.conflictItem,
        selectedConflict?.id === item.id && styles.selectedConflictItem,
        { 
          backgroundColor: isDarkMode ? '#161B22' : '#FFFFFF',
          borderColor: selectedConflict?.id === item.id ? 
            (isDarkMode ? '#58A6FF' : '#0366D6') : 
            (isDarkMode ? '#30363D' : '#D0D7DE')
        }
      ]}
      onPress={() => handleSelectConflict(item)}
    >
      <View style={styles.conflictHeader}>
        <Text style={[
          styles.entityType,
          { color: isDarkMode ? '#8B949E' : '#6E7781' }
        ]}>
          {getEntityTypeName(item.entityType)}
        </Text>
        <Text style={[
          styles.conflictDate,
          { color: isDarkMode ? '#8B949E' : '#6E7781' }
        ]}>
          {formatDate(item.createdAt.toString())}
        </Text>
      </View>
      
      <Text style={[
        styles.entityName,
        { color: isDarkMode ? '#FFFFFF' : '#24292E' }
      ]}>
        {getEntityName(item)}
      </Text>
      
      <View style={styles.conflictBadge}>
        <Ionicons name="git-merge" size={12} color="#F0883E" />
        <Text style={styles.conflictBadgeText}>
          {t('sync.conflict')}
        </Text>
      </View>
    </TouchableOpacity>
  );
  
  // Render conflict details
  const renderConflictDetails = () => {
    if (!selectedConflict) {
      return (
        <View style={styles.noSelectionContainer}>
          <Text style={[
            styles.noSelectionText,
            { color: isDarkMode ? '#8B949E' : '#6E7781' }
          ]}>
            {t('sync.selectConflict')}
          </Text>
        </View>
      );
    }
    
    return (
      <View style={styles.detailsContainer}>
        <Text style={[
          styles.detailsTitle,
          { color: isDarkMode ? '#FFFFFF' : '#24292E' }
        ]}>
          {getEntityName(selectedConflict)}
        </Text>
        
        <Text style={[
          styles.detailsSubtitle,
          { color: isDarkMode ? '#8B949E' : '#6E7781' }
        ]}>
          {getEntityTypeName(selectedConflict.entityType)} #{selectedConflict.entityId}
        </Text>
        
        <View style={styles.comparisonContainer}>
          <View style={[
            styles.dataColumn,
            { backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA' }
          ]}>
            <Text style={[
              styles.dataColumnTitle,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' }
            ]}>
              {t('sync.localVersion')}
            </Text>
            
            <ScrollView style={styles.dataScroll}>
              <Text style={[
                styles.dataText,
                { color: isDarkMode ? '#8B949E' : '#6E7781' }
              ]}>
                {JSON.stringify(selectedConflict.localData, null, 2)}
              </Text>
            </ScrollView>
            
            <Button
              title={t('sync.useLocal')}
              onPress={() => handleResolveConflict('local')}
              type="secondary"
              style={styles.actionButton}
              isLoading={isResolving}
              disabled={isResolving}
            />
          </View>
          
          <View style={[
            styles.dataColumn,
            { backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA' }
          ]}>
            <Text style={[
              styles.dataColumnTitle,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' }
            ]}>
              {t('sync.remoteVersion')}
            </Text>
            
            <ScrollView style={styles.dataScroll}>
              <Text style={[
                styles.dataText,
                { color: isDarkMode ? '#8B949E' : '#6E7781' }
              ]}>
                {JSON.stringify(selectedConflict.remoteData, null, 2)}
              </Text>
            </ScrollView>
            
            <Button
              title={t('sync.useRemote')}
              onPress={() => handleResolveConflict('remote')}
              type="secondary"
              style={styles.actionButton}
              isLoading={isResolving}
              disabled={isResolving}
            />
          </View>
        </View>
        
        <Button
          title={t('sync.createMerged')}
          onPress={() => navigation.navigate('MergeEditor', { conflict: selectedConflict })}
          type="primary"
          style={styles.mergeButton}
          icon="git-merge-outline"
          disabled={isResolving}
        />
      </View>
    );
  };
  
  return (
    <View style={[
      styles.container,
      { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }
    ]}>
      <View style={styles.header}>
        <Text style={[
          styles.title,
          { color: isDarkMode ? '#FFFFFF' : '#24292E' }
        ]}>
          {t('sync.conflictResolution')}
        </Text>
        
        <Text style={[
          styles.subtitle,
          { color: isDarkMode ? '#8B949E' : '#6E7781' }
        ]}>
          {unresolvedConflicts.length > 0 ? 
            t('sync.conflictsCount', { count: unresolvedConflicts.length }) : 
            t('sync.noConflicts')}
        </Text>
      </View>
      
      <View style={styles.content}>
        <View style={[
          styles.conflictsList,
          { borderColor: isDarkMode ? '#30363D' : '#D0D7DE' }
        ]}>
          {unresolvedConflicts.length > 0 ? (
            <FlatList
              data={unresolvedConflicts}
              renderItem={renderConflictItem}
              keyExtractor={(item) => item.id.toString()}
              contentContainerStyle={styles.listContent}
            />
          ) : (
            <EmptyState
              icon="checkmark-circle-outline"
              title={t('sync.allConflictsResolved')}
              message={t('sync.noConflictsMessage')}
            />
          )}
        </View>
        
        <View style={styles.detailsPanel}>
          {isResolving ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
              <Text style={[
                styles.loadingText,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' }
              ]}>
                {t('sync.resolvingConflict')}
              </Text>
            </View>
          ) : (
            renderConflictDetails()
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
  },
  content: {
    flex: 1,
    flexDirection: 'row',
  },
  conflictsList: {
    width: '30%',
    borderRightWidth: 1,
  },
  listContent: {
    padding: 8,
  },
  conflictItem: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
  },
  selectedConflictItem: {
    borderWidth: 2,
  },
  conflictHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  entityType: {
    fontSize: 12,
  },
  conflictDate: {
    fontSize: 12,
  },
  entityName: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  conflictBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(240, 136, 62, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  conflictBadgeText: {
    fontSize: 10,
    color: '#F0883E',
    marginLeft: 4,
    fontWeight: 'bold',
  },
  detailsPanel: {
    flex: 1,
    padding: 16,
  },
  noSelectionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noSelectionText: {
    fontSize: 16,
    textAlign: 'center',
  },
  detailsContainer: {
    flex: 1,
  },
  detailsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  detailsSubtitle: {
    fontSize: 14,
    marginBottom: 16,
  },
  comparisonContainer: {
    flex: 1,
    flexDirection: 'row',
    marginBottom: 16,
  },
  dataColumn: {
    flex: 1,
    marginHorizontal: 8,
    borderRadius: 8,
    padding: 12,
  },
  dataColumnTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  dataScroll: {
    flex: 1,
  },
  dataText: {
    fontFamily: 'monospace',
    fontSize: 12,
  },
  actionButton: {
    marginTop: 8,
  },
  mergeButton: {
    alignSelf: 'center',
    minWidth: 200,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
});

export default ConflictResolutionScreen;
