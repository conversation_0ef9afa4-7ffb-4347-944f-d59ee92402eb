import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useSync } from '../../contexts/SyncContext';
import Button from '../../components/ui/Button';
import SyncStatusBadge from '../../components/sync/SyncStatusBadge';
import { formatDate, formatTime } from '../../utils/formatters';
import dbService from '../../services/db';
import { SyncQueue } from '../../services/db';

const SyncStatusScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const navigation = useNavigation<any>();
  const {
    syncStatus,
    lastSyncTime,
    pendingChanges,
    conflicts,
    isOnline,
    syncNow,
    clearSyncQueue
  } = useSync();

  // State
  const [syncQueue, setSyncQueue] = useState<SyncQueue[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);

  // Load sync queue
  const loadSyncQueue = async () => {
    try {
      const queue = await dbService.getSyncQueue();
      setSyncQueue(queue);
    } catch (error) {
      console.error('Error loading sync queue:', error);
    }
  };

  // Load data on mount
  useEffect(() => {
    loadSyncQueue();
  }, [pendingChanges]);

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadSyncQueue();
    setIsRefreshing(false);
  };

  // Handle sync now
  const handleSyncNow = async () => {
    try {
      setIsSyncing(true);
      await syncNow();
      await loadSyncQueue();
    } catch (error) {
      console.error('Error syncing:', error);
      Alert.alert(
        t('common.error'),
        t('sync.syncError')
      );
    } finally {
      setIsSyncing(false);
    }
  };

  // Handle clear sync queue
  const handleClearSyncQueue = () => {
    Alert.alert(
      t('sync.clearSyncQueue'),
      t('sync.clearSyncQueueConfirmation'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.clear'),
          style: 'destructive',
          onPress: async () => {
            try {
              await clearSyncQueue();
              await loadSyncQueue();

              Alert.alert(
                t('common.success'),
                t('sync.syncQueueCleared')
              );
            } catch (error) {
              console.error('Error clearing sync queue:', error);
              Alert.alert(
                t('common.error'),
                t('sync.clearSyncQueueError')
              );
            }
          },
        },
      ]
    );
  };

  // Get entity type display name
  const getEntityTypeName = (entityType: string): string => {
    switch (entityType) {
      case 'Transaction':
        return t('transactions.transaction');
      case 'Account':
        return t('accounts.account');
      case 'Category':
        return t('categories.category');
      case 'Receipt':
        return t('receipts.receipt');
      default:
        return entityType;
    }
  };

  // Get action display name
  const getActionName = (action: string): string => {
    switch (action) {
      case 'Create':
        return t('sync.create');
      case 'Update':
        return t('sync.update');
      case 'Delete':
        return t('sync.delete');
      default:
        return action;
    }
  };

  // Get status color
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'Pending':
        return '#F0883E'; // Orange
      case 'Processing':
        return '#58A6FF'; // Blue
      case 'Completed':
        return '#3FB950'; // Green
      case 'Failed':
        return '#F85149'; // Red
      default:
        return '#8B949E'; // Gray
    }
  };

  // Render sync queue item
  const renderSyncQueueItem = (item: SyncQueue) => (
    <View
      key={item.id}
      style={[
        styles.queueItem,
        { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
      ]}
    >
      <View style={styles.queueItemHeader}>
        <Text style={[
          styles.entityType,
          { color: isDarkMode ? '#8B949E' : '#6E7781' }
        ]}>
          {getEntityTypeName(item.entityType)} #{item.entityId}
        </Text>

        <View style={[
          styles.statusBadge,
          { backgroundColor: getStatusColor(item.status) + '20' }
        ]}>
          <Text style={[
            styles.statusText,
            { color: getStatusColor(item.status) }
          ]}>
            {item.status}
          </Text>
        </View>
      </View>

      <View style={styles.queueItemContent}>
        <View style={styles.actionContainer}>
          <Ionicons
            name={
              item.action === 'Create' ? 'add-circle' :
              item.action === 'Update' ? 'refresh-circle' :
              'trash'
            }
            size={16}
            color={
              item.action === 'Create' ? '#3FB950' :
              item.action === 'Update' ? '#58A6FF' :
              '#F85149'
            }
          />
          <Text style={[
            styles.actionText,
            { color: isDarkMode ? '#FFFFFF' : '#24292E' }
          ]}>
            {getActionName(item.action)}
          </Text>
        </View>

        <Text style={[
          styles.dateText,
          { color: isDarkMode ? '#8B949E' : '#6E7781' }
        ]}>
          {formatDate(item.createdAt.toString())} {formatTime(item.createdAt.toString())}
        </Text>
      </View>

      {item.errorMessage && (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={14} color="#F85149" />
          <Text style={styles.errorText}>
            {item.errorMessage}
          </Text>
        </View>
      )}
    </View>
  );

  return (
    <View style={[
      styles.container,
      { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }
    ]}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[isDarkMode ? '#58A6FF' : '#0366D6']}
            tintColor={isDarkMode ? '#58A6FF' : '#0366D6'}
          />
        }
      >
        <View style={styles.statusSection}>
          <Text style={[
            styles.sectionTitle,
            { color: isDarkMode ? '#FFFFFF' : '#24292E' }
          ]}>
            {t('sync.syncStatus')}
          </Text>

          <View style={styles.statusCard}>
            <SyncStatusBadge showLastSync={true} />

            <View style={styles.statusActions}>
              <Button
                title={t('sync.syncNow')}
                onPress={handleSyncNow}
                type="primary"
                style={styles.actionButton}
                isLoading={isSyncing}
                disabled={!isOnline || isSyncing || syncStatus === 'syncing'}
                icon="sync"
              />

              <Button
                title={t('sync.viewConflicts')}
                onPress={() => navigation.navigate('ConflictResolution')}
                type={conflicts.filter(c => !c.resolved).length > 0 ? 'danger' : 'secondary'}
                style={styles.actionButton}
                icon="git-merge-outline"
                disabled={isSyncing}
              />
            </View>
          </View>
        </View>

        <View style={styles.queueSection}>
          <View style={styles.queueHeader}>
            <Text style={[
              styles.sectionTitle,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' }
            ]}>
              {t('sync.syncQueue')}
            </Text>

            {syncQueue.length > 0 && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={handleClearSyncQueue}
                disabled={isSyncing}
              >
                <Text style={[
                  styles.clearButtonText,
                  { color: isDarkMode ? '#F85149' : '#F85149' }
                ]}>
                  {t('sync.clearQueue')}
                </Text>
              </TouchableOpacity>
            )}
          </View>

          {syncQueue.length > 0 ? (
            <View style={styles.queueList}>
              {syncQueue.map(renderSyncQueueItem)}
            </View>
          ) : (
            <View style={[
              styles.emptyQueue,
              { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
            ]}>
              <Ionicons
                name="checkmark-circle-outline"
                size={48}
                color={isDarkMode ? '#3FB950' : '#3FB950'}
              />
              <Text style={[
                styles.emptyQueueTitle,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' }
              ]}>
                {t('sync.queueEmpty')}
              </Text>
              <Text style={[
                styles.emptyQueueText,
                { color: isDarkMode ? '#8B949E' : '#6E7781' }
              ]}>
                {t('sync.allChangesSynced')}
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  statusSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  statusCard: {
    marginBottom: 16,
  },
  statusActions: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
  actionButton: {
    marginHorizontal: 8,
    minWidth: 140,
  },
  queueSection: {
    flex: 1,
  },
  queueHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  clearButton: {
    padding: 8,
  },
  clearButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  queueList: {
    marginBottom: 16,
  },
  queueItem: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  queueItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  entityType: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  queueItemContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  actionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionText: {
    fontSize: 14,
    marginLeft: 4,
  },
  dateText: {
    fontSize: 12,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(248, 81, 73, 0.2)',
  },
  errorText: {
    fontSize: 12,
    color: '#F85149',
    marginLeft: 4,
  },
  emptyQueue: {
    padding: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  emptyQueueTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 12,
    marginBottom: 4,
  },
  emptyQueueText: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default SyncStatusScreen;
