import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Alert,
  ScrollView,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import Button from '../components/ui/Button';
import ProgressBar from '../components/ui/ProgressBar';
import { migrateToIndexedDB, clearAsyncStorageAfterMigration, MigrationStatus } from '../utils/migrationUtils';

interface MigrationScreenProps {
  onMigrationComplete: () => void;
}

const MigrationScreen: React.FC<MigrationScreenProps> = ({ onMigrationComplete }) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();

  // State
  const [migrationStatus, setMigrationStatus] = useState<MigrationStatus>({
    inProgress: false,
    completed: false,
    error: null,
    progress: 0,
    details: {
      accounts: 0,
      transactions: 0,
      categories: 0,
      receipts: 0,
      syncQueue: 0,
      conflicts: 0,
      settings: 0,
      authTokens: 0,
    },
  });
  const [clearingStorage, setClearingStorage] = useState(false);

  // Start migration automatically
  useEffect(() => {
    startMigration();
  }, []);

  // Start migration
  const startMigration = async () => {
    try {
      setMigrationStatus(prev => ({ ...prev, inProgress: true }));

      const status = await migrateToIndexedDB(updateStatus);

      if (status.completed && !status.error) {
        // Migration successful
        setMigrationStatus(status);
      } else if (status.error) {
        // Migration failed
        Alert.alert(
          t('migration.error'),
          t('migration.errorMessage', { error: status.error }),
          [
            {
              text: t('migration.retry'),
              onPress: startMigration,
            },
            {
              text: t('common.cancel'),
              style: 'cancel',
            },
          ]
        );
      }
    } catch (error: unknown) {
      console.error('Error starting migration:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setMigrationStatus(prev => ({
        ...prev,
        inProgress: false,
        error: errorMessage,
      }));

      Alert.alert(
        t('migration.error'),
        t('migration.errorMessage', { error: errorMessage }),
        [
          {
            text: t('migration.retry'),
            onPress: startMigration,
          },
          {
            text: t('common.cancel'),
            style: 'cancel',
          },
        ]
      );
    }
  };

  // Update migration status
  const updateStatus = (status: MigrationStatus) => {
    setMigrationStatus(status);
  };

  // Clear AsyncStorage after migration
  const handleClearStorage = async () => {
    try {
      setClearingStorage(true);
      await clearAsyncStorageAfterMigration();
      setClearingStorage(false);

      Alert.alert(
        t('migration.success'),
        t('migration.storageCleared'),
        [
          {
            text: t('common.continue'),
            onPress: onMigrationComplete,
          },
        ]
      );
    } catch (error: unknown) {
      console.error('Error clearing storage:', error);
      setClearingStorage(false);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      Alert.alert(
        t('migration.error'),
        t('migration.clearStorageError', { error: errorMessage }),
        [
          {
            text: t('common.ok'),
          },
        ]
      );
    }
  };

  // Skip clearing storage
  const handleSkipClearing = () => {
    onMigrationComplete();
  };

  // Render migration details
  const renderMigrationDetails = () => {
    const { details } = migrationStatus;

    return (
      <View style={styles.detailsContainer}>
        <Text style={[styles.detailsTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
          {t('migration.migrationDetails')}
        </Text>

        <View style={styles.detailsTable}>
          <View style={styles.detailsRow}>
            <Text style={[styles.detailsLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('accounts.accounts')}
            </Text>
            <Text style={[styles.detailsValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {details.accounts}
            </Text>
          </View>

          <View style={styles.detailsRow}>
            <Text style={[styles.detailsLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('transactions.transactions')}
            </Text>
            <Text style={[styles.detailsValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {details.transactions}
            </Text>
          </View>

          <View style={styles.detailsRow}>
            <Text style={[styles.detailsLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('categories.categories')}
            </Text>
            <Text style={[styles.detailsValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {details.categories}
            </Text>
          </View>

          <View style={styles.detailsRow}>
            <Text style={[styles.detailsLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('receipts.receipts')}
            </Text>
            <Text style={[styles.detailsValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {details.receipts}
            </Text>
          </View>

          <View style={styles.detailsRow}>
            <Text style={[styles.detailsLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('sync.syncQueue')}
            </Text>
            <Text style={[styles.detailsValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {details.syncQueue}
            </Text>
          </View>

          <View style={styles.detailsRow}>
            <Text style={[styles.detailsLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('sync.conflicts')}
            </Text>
            <Text style={[styles.detailsValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {details.conflicts}
            </Text>
          </View>

          <View style={styles.detailsRow}>
            <Text style={[styles.detailsLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('settings.settings')}
            </Text>
            <Text style={[styles.detailsValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {details.settings}
            </Text>
          </View>

          <View style={styles.detailsRow}>
            <Text style={[styles.detailsLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('migration.authTokens')}
            </Text>
            <Text style={[styles.detailsValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {details.authTokens}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}
      contentContainerStyle={styles.contentContainer}
    >
      <View style={styles.header}>
        <Ionicons
          name="server-outline"
          size={64}
          color={isDarkMode ? '#58A6FF' : '#0366D6'}
        />

        <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
          {t('migration.title')}
        </Text>

        <Text style={[styles.subtitle, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
          {migrationStatus.completed
            ? t('migration.completed')
            : migrationStatus.inProgress
            ? t('migration.inProgress')
            : t('migration.description')}
        </Text>
      </View>

      <View style={styles.progressContainer}>
        <ProgressBar progress={migrationStatus.progress} />

        <Text style={[styles.progressText, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
          {Math.round(migrationStatus.progress * 100)}%
        </Text>
      </View>

      {migrationStatus.inProgress && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
          <Text style={[styles.loadingText, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
            {t('migration.migrating')}
          </Text>
        </View>
      )}

      {migrationStatus.completed && !migrationStatus.error && (
        <>
          {renderMigrationDetails()}

          <View style={styles.successContainer}>
            <Ionicons
              name="checkmark-circle"
              size={48}
              color="#3FB950"
            />

            <Text style={[styles.successText, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {t('migration.migrationSuccess')}
            </Text>

            <Text style={[styles.successSubtext, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('migration.clearStoragePrompt')}
            </Text>

            <View style={styles.buttonContainer}>
              <Button
                title={t('migration.clearStorage')}
                onPress={handleClearStorage}
                type="primary"
                isLoading={clearingStorage}
                disabled={clearingStorage}
                style={styles.button}
              />

              <Button
                title={t('migration.skipClearing')}
                onPress={handleSkipClearing}
                type="secondary"
                disabled={clearingStorage}
                style={styles.button}
              />
            </View>
          </View>
        </>
      )}

      {migrationStatus.error && !migrationStatus.inProgress && (
        <View style={styles.errorContainer}>
          <Ionicons
            name="alert-circle"
            size={48}
            color="#F85149"
          />

          <Text style={[styles.errorText, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
            {t('migration.migrationFailed')}
          </Text>

          <Text style={[styles.errorSubtext, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {migrationStatus.error}
          </Text>

          <Button
            title={t('migration.retry')}
            onPress={startMigration}
            type="primary"
            style={styles.retryButton}
          />
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 24,
    alignItems: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  progressContainer: {
    width: '100%',
    marginBottom: 24,
  },
  progressText: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
  },
  loadingContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
  },
  detailsContainer: {
    width: '100%',
    marginBottom: 24,
  },
  detailsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  detailsTable: {
    width: '100%',
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  detailsLabel: {
    fontSize: 16,
  },
  detailsValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  successContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  successText: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  successSubtext: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
  },
  button: {
    minWidth: 150,
    margin: 8,
  },
  errorContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  errorText: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  errorSubtext: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    minWidth: 150,
  },
});

export default MigrationScreen;
