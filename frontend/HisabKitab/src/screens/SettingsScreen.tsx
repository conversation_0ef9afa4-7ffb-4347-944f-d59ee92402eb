import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Switch, Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useFeatureFlag } from '../contexts/FeatureFlagContext';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import LanguageSwitcher from '../components/settings/LanguageSwitcher';

const SettingsScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode, setTheme } = useTheme();
  const { } = useLanguage(); // Language settings handled elsewhere
  const { logout, hasRole } = useAuth();
  const { isFeatureEnabled } = useFeatureFlag();

  // Check if user is admin
  const isPlatformAdmin = hasRole('PlatformAdmin');

  const handleLogout = async () => {
    Alert.alert(
      t('auth.logout'),
      t('auth.logoutConfirm'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.yes'),
          onPress: async () => {
            await logout();
          },
        },
      ],
    );
  };

  const handleThemeChange = (value: boolean) => {
    setTheme(value ? 'dark' : 'light');
  };

  // Language change is handled elsewhere

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}
      contentContainerStyle={styles.contentContainer}
    >
      {/* Theme section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('settings.theme')}
        </Text>

        <View style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
          <View style={styles.settingInfo}>
            <Ionicons
              name="moon-outline"
              size={24}
              color={isDarkMode ? '#FFFFFF' : '#000000'}
              style={styles.settingIcon}
            />
            <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {t('settings.darkMode')}
            </Text>
          </View>

          <Switch
            value={isDarkMode}
            onValueChange={handleThemeChange}
            trackColor={{ false: '#767577', true: '#0366D6' }}
            thumbColor="#FFFFFF"
          />
        </View>
      </View>

      {/* Language section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('settings.language')}
        </Text>

        <View style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
          <View style={styles.settingInfo}>
            <Ionicons
              name="globe-outline"
              size={24}
              color={isDarkMode ? '#FFFFFF' : '#000000'}
              style={styles.settingIcon}
            />
            <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {t('settings.language')}
            </Text>
          </View>

          <LanguageSwitcher />
        </View>
      </View>

      {/* Notifications & Reminders section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('settings.notificationsAndReminders')}
        </Text>

        <TouchableOpacity
          style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}
          onPress={() => router.push('/') /* Placeholder navigation */}
        >
          <View style={styles.settingInfo}>
            <Ionicons
              name="notifications-outline"
              size={24}
              color={isDarkMode ? '#FFFFFF' : '#000000'}
              style={styles.settingIcon}
            />
            <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {t('notifications.notificationCenter')}
            </Text>
          </View>

          <Ionicons name="chevron-forward" size={24} color={isDarkMode ? '#8B949E' : '#6E7781'} />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}
          onPress={() => router.push('/') /* Placeholder navigation */}
        >
          <View style={styles.settingInfo}>
            <Ionicons
              name="alarm-outline"
              size={24}
              color={isDarkMode ? '#FFFFFF' : '#000000'}
              style={styles.settingIcon}
            />
            <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {t('reminders.reminderManagement')}
            </Text>
          </View>

          <Ionicons name="chevron-forward" size={24} color={isDarkMode ? '#8B949E' : '#6E7781'} />
        </TouchableOpacity>
      </View>

      {/* Advanced section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('settings.advanced')}
        </Text>

        {/* Feature Management - only visible to platform admins or if feature is enabled */}
        {(isPlatformAdmin || isFeatureEnabled('FeatureManagement')) && (
          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}
            onPress={() => router.push('/') /* Placeholder navigation */}
          >
            <View style={styles.settingInfo}>
              <Ionicons
                name="construct-outline"
                size={24}
                color={isDarkMode ? '#FFFFFF' : '#000000'}
                style={styles.settingIcon}
              />
              <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {t('features.featureManagement')}
              </Text>
            </View>

            <Ionicons name="chevron-forward" size={24} color={isDarkMode ? '#8B949E' : '#6E7781'} />
          </TouchableOpacity>
        )}

        {/* Monthly Summary */}
        <TouchableOpacity
          style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}
          onPress={() => router.push('/') /* Placeholder navigation */}
        >
          <View style={styles.settingInfo}>
            <Ionicons
              name="calendar-outline"
              size={24}
              color={isDarkMode ? '#FFFFFF' : '#000000'}
              style={styles.settingIcon}
            />
            <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {t('analytics.monthlySummary')}
            </Text>
          </View>

          <Ionicons name="chevron-forward" size={24} color={isDarkMode ? '#8B949E' : '#6E7781'} />
        </TouchableOpacity>
      </View>

      {/* Account section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('auth.account')}
        </Text>

        <TouchableOpacity
          style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}
          onPress={() => router.push('/profile')}
        >
          <View style={styles.settingInfo}>
            <Ionicons
              name="person-outline"
              size={24}
              color={isDarkMode ? '#FFFFFF' : '#000000'}
              style={styles.settingIcon}
            />
            <Text style={[styles.settingText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {t('profile.profile')}
            </Text>
          </View>

          <Ionicons name="chevron-forward" size={24} color={isDarkMode ? '#8B949E' : '#6E7781'} />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.settingItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}
          onPress={handleLogout}
        >
          <View style={styles.settingInfo}>
            <Ionicons
              name="log-out-outline"
              size={24}
              color="#F85149"
              style={styles.settingIcon}
            />
            <Text style={[styles.settingText, { color: '#F85149' }]}>
              {t('auth.logout')}
            </Text>
          </View>

          <Ionicons name="chevron-forward" size={24} color={isDarkMode ? '#8B949E' : '#6E7781'} />
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingIcon: {
    marginRight: 16,
  },
  settingText: {
    fontSize: 16,
  },
});

export default SettingsScreen;
