import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import apiService from '../../services/api';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { handleApiError, showSuccessMessage, showConfirmationDialog } from '../../utils/errorHandling';
import PriorityBadge from '../../components/ui/PriorityBadge';

const PrioritiesScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { isPlatformAdmin } = useAuth();
  const navigation = useNavigation<any>();

  const [priorities, setPriorities] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Refresh priorities when screen is focused
  useFocusEffect(
    useCallback(() => {
      let isMounted = true;

      const loadPriorities = async () => {
        try {
          setIsLoading(true);
          const response = await apiService.priorities.getAllPriorities();

          if (!isMounted) return;

          if (response.status === 200 && response.data) {
            setPriorities(response.data);
          } else {
            if (!isMounted) return;
            handleApiError(response, t, 'priorities.fetchPriorityError');
          }
        } catch (error) {
          if (!isMounted) return;
          handleApiError(error, t, 'priorities.fetchPriorityError');
        } finally {
          if (isMounted) {
            setIsLoading(false);
            setIsRefreshing(false);
          }
        }
      };

      loadPriorities();

      // Cleanup function to prevent state updates after unmount
      return () => {
        isMounted = false;
      };
    }, [t])
  );

  // Handle refresh
  const handleRefresh = () => {
    setIsRefreshing(true);

    // Use the same logic as in useFocusEffect but without the isMounted check
    const loadPriorities = async () => {
      try {
        const response = await apiService.priorities.getAllPriorities();

        if (response.status === 200 && response.data) {
          setPriorities(response.data);
        } else {
          handleApiError(response, t, 'priorities.fetchPriorityError');
        }
      } catch (error) {
        handleApiError(error, t, 'priorities.fetchPriorityError');
      } finally {
        setIsLoading(false);
        setIsRefreshing(false);
      }
    };

    loadPriorities();
  };



  // Handle edit priority
  const handleEditPriority = (priorityId: number) => {
    navigation.navigate('EditPriority', { priorityId });
  };

  // Handle delete priority
  const handleDeletePriority = (priorityId: number, priorityName: string) => {
    showConfirmationDialog(
      t,
      'priorities.deletePriority',
      'priorities.deletePriorityConfirm',
      async () => {
        try {
          const response = await apiService.priorities.deletePriority(priorityId);

          if (response.status === 204) {
            // Remove priority from state
            setPriorities(priorities.filter((priority: any) => priority.id !== priorityId));
          } else {
            handleApiError(response, t, 'priorities.deletePriorityError');
          }
        } catch (error) {
          handleApiError(error, t, 'priorities.deletePriorityError');
        }
      },
      { name: priorityName }
    );
  };

  // Render priority item
  const renderPriorityItem = ({ item }: { item: any }) => (
    <Card style={styles.priorityCard}>
      <View style={styles.priorityHeader}>
        <View style={styles.priorityInfo}>
          <PriorityBadge level={item.name} size="large" />
          <Text
            style={[
              styles.priorityName,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
          >
            {t(`priorities.${item.name.toLowerCase()}`)}
          </Text>
        </View>

        {isPlatformAdmin && (
          <View style={styles.priorityActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleEditPriority(item.id)}
            >
              <Ionicons
                name="pencil-outline"
                size={20}
                color={isDarkMode ? '#58A6FF' : '#0366D6'}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDeletePriority(item.id, item.name)}
            >
              <Ionicons
                name="trash-outline"
                size={20}
                color={isDarkMode ? '#F85149' : '#DA3633'}
              />
            </TouchableOpacity>
          </View>
        )}
      </View>

      <View style={styles.priorityDescription}>
        <Text
          style={[
            styles.descriptionText,
            { color: isDarkMode ? '#8B949E' : '#6E7781' },
          ]}
        >
          {item.description || t(`priorities.${item.name.toLowerCase()}Description`)}
        </Text>
      </View>
    </Card>
  );

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons
        name="flag-outline"
        size={64}
        color={isDarkMode ? '#30363D' : '#D0D7DE'}
      />
      <Text
        style={[
          styles.emptyText,
          { color: isDarkMode ? '#8B949E' : '#6E7781' },
        ]}
      >
        {t('priorities.noPriorities')}
      </Text>

      {isPlatformAdmin && (
        <Button
          title={t('priorities.createPriority')}
          onPress={() => navigation.navigate('CreatePriority')}
          icon="add-outline"
          style={styles.createButton}
        />
      )}
    </View>
  );

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
      ]}
    >
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={isDarkMode ? '#58A6FF' : '#0366D6'}
          />
        </View>
      ) : (
        <>
          <FlatList
            data={priorities}
            renderItem={renderPriorityItem}
            keyExtractor={(item) => item.id.toString()}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={renderEmptyState}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={handleRefresh}
                colors={['#0366D6']}
                tintColor={isDarkMode ? '#58A6FF' : '#0366D6'}
              />
            }
          />

          {isPlatformAdmin && priorities.length > 0 && (
            <View style={[
              styles.actionContainer,
              { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }
            ]}>
              <Button
                title={t('priorities.createPriority')}
                onPress={() => navigation.navigate('CreatePriority')}
                icon="add-outline"
                style={styles.createButton}
              />
            </View>
          )}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: 16,
    paddingBottom: 100,
  },
  priorityCard: {
    marginBottom: 12,
  },
  priorityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  priorityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityName: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 12,
  },
  priorityActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
  },
  priorityDescription: {
    marginTop: 8,
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  actionContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#30363D',
  },
  createButton: {
    flex: 1,
  },
});

export default PrioritiesScreen;
