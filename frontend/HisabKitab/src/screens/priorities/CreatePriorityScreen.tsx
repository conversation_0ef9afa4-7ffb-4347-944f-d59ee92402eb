import React, { useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import apiService from '../../services/api';
import PriorityForm from '../../components/priorities/PriorityForm';

const CreatePriorityScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation();

  const [isLoading, setIsLoading] = useState(false);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    try {
      setIsLoading(true);

      const priorityData = {
        name: values.name,
        description: values.description,
        color: values.color,
        level: values.level,
      };

      const response = await apiService.priorities.createPriority(priorityData);

      if (response.status === 201 && response.data) {
        // Navigate back to priorities screen
        navigation.goBack();
      } else {
        Alert.alert(
          t('common.error'),
          response.error || t('priorities.createPriorityError')
        );
      }
    } catch (error) {
      console.error('Error creating priority:', error);
      Alert.alert(
        t('common.error'),
        t('priorities.createPriorityError')
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
      ]}
    >
      <PriorityForm
        onSubmit={handleSubmit}
        isLoading={isLoading}
        mode="create"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default CreatePriorityScreen;
