import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import apiService from '../../services/api';
import PriorityForm from '../../components/priorities/PriorityForm';

// Define interfaces
interface Priority {
  id: number;
  name: string;
  description: string | null;
  color: string;
  level: number;
}

type RouteParams = {
  EditPriority: {
    priorityId: number;
  };
};

const EditPriorityScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();
  const route = useRoute<RouteProp<RouteParams, 'EditPriority'>>();
  const { priorityId } = route.params;

  const [priority, setPriority] = useState<Priority | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch priority
  useEffect(() => {
    const fetchPriority = async () => {
      try {
        setIsLoading(true);
        const response = await apiService.priorities.getPriority(priorityId);

        if (response.status === 200 && response.data) {
          setPriority(response.data);
        } else {
          console.error('Failed to fetch priority:', response.error);
          Alert.alert(
            t('common.error'),
            response.error || t('priorities.fetchPriorityError')
          );
          navigation.goBack();
        }
      } catch (error) {
        console.error('Error fetching priority:', error);
        Alert.alert(
          t('common.error'),
          t('priorities.fetchPriorityError')
        );
        navigation.goBack();
      } finally {
        setIsLoading(false);
      }
    };

    fetchPriority();
  }, [priorityId, navigation, t]);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    try {
      setIsSubmitting(true);

      const priorityData = {
        name: values.name,
        description: values.description,
        color: values.color,
        level: values.level,
      };

      const response = await apiService.priorities.updatePriority(
        priorityId,
        priorityData
      );

      if (response.status === 200 && response.data) {
        // Navigate back to priorities screen
        navigation.goBack();
      } else {
        Alert.alert(
          t('common.error'),
          response.error || t('priorities.updatePriorityError')
        );
      }
    } catch (error) {
      console.error('Error updating priority:', error);
      Alert.alert(
        t('common.error'),
        t('priorities.updatePriorityError')
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading || !priority) {
    return (
      <View
        style={[
          styles.loadingContainer,
          { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
        ]}
      >
        <ActivityIndicator
          size="large"
          color={isDarkMode ? '#58A6FF' : '#0366D6'}
        />
      </View>
    );
  }

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
      ]}
    >
      <PriorityForm
        initialValues={{
          id: priority.id,
          name: priority.name,
          description: priority.description || '',
          color: priority.color || '#4CAF50',
          level: priority.level,
        }}
        onSubmit={handleSubmit}
        isLoading={isSubmitting}
        mode="edit"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default EditPriorityScreen;
