import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Switch,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useFeatureFlag, FeatureFlag } from '../../contexts/FeatureFlagContext';
import { useAuth } from '../../contexts/AuthContext';
import FeatureItem from '../../components/settings/FeatureItem';
import EmptyState from '../../components/EmptyState';

const FeatureManagementScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { features, isLoading, fetchFeatures, updateFeature } = useFeatureFlag();
  const { hasRole } = useAuth();
  
  const [refreshing, setRefreshing] = useState(false);
  
  // Check if user is admin
  const isPlatformAdmin = hasRole('PlatformAdmin');
  
  // Fetch features on mount
  useEffect(() => {
    fetchFeatures();
  }, []);
  
  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchFeatures();
    setRefreshing(false);
  };
  
  // Handle toggle feature
  const handleToggleFeature = async (feature: FeatureFlag) => {
    if (!isPlatformAdmin) {
      Alert.alert(
        t('features.adminRequired'),
        t('features.adminRequiredMessage')
      );
      return;
    }
    
    const success = await updateFeature(feature.id, !feature.isEnabled);
    
    if (success) {
      Alert.alert(
        t('common.success'),
        feature.isEnabled
          ? t('features.disabledSuccess', { name: feature.name })
          : t('features.enabledSuccess', { name: feature.name })
      );
    }
  };
  
  // Handle change enabled for
  const handleChangeEnabledFor = async (feature: FeatureFlag, enabledFor: string) => {
    if (!isPlatformAdmin) {
      Alert.alert(
        t('features.adminRequired'),
        t('features.adminRequiredMessage')
      );
      return;
    }
    
    const success = await updateFeature(feature.id, feature.isEnabled, enabledFor);
    
    if (success) {
      Alert.alert(
        t('common.success'),
        t('features.updatedSuccess', { name: feature.name })
      );
    }
  };
  
  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}>
      {/* Admin notice */}
      {!isPlatformAdmin && (
        <View
          style={[
            styles.adminNotice,
            { backgroundColor: isDarkMode ? '#30363D' : '#E1E4E8' },
          ]}
        >
          <Ionicons
            name="information-circle"
            size={24}
            color={isDarkMode ? '#58A6FF' : '#0366D6'}
          />
          <Text
            style={[
              styles.adminNoticeText,
              { color: isDarkMode ? '#C9D1D9' : '#24292E' },
            ]}
          >
            {t('features.viewOnlyMode')}
          </Text>
        </View>
      )}
      
      {/* Features list */}
      {isLoading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
        </View>
      ) : (
        <FlatList
          data={features}
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => (
            <FeatureItem
              feature={item}
              onToggle={() => handleToggleFeature(item)}
              onChangeEnabledFor={(enabledFor) => handleChangeEnabledFor(item, enabledFor)}
              isAdmin={isPlatformAdmin}
              isDarkMode={isDarkMode}
            />
          )}
          contentContainerStyle={
            features.length === 0 ? styles.emptyListContainer : styles.listContainer
          }
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#0366D6']}
              tintColor={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
          }
          ListEmptyComponent={
            <EmptyState
              icon="construct-outline"
              title={t('features.noFeatures')}
              message={t('features.noFeaturesMessage')}
              isDarkMode={isDarkMode}
            />
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  adminNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
  },
  adminNoticeText: {
    marginLeft: 8,
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 16,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
});

export default FeatureManagementScreen;
