import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import apiService from '../services/api';
import dbService from '../services/db';

const CreateFamilyScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const router = useRouter();

  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);

  const handleCreateFamily = async () => {
    if (!name.trim()) {
      Alert.alert(
        t('common.error'),
        t('family.familyName') + ' ' + t('auth.requiredFields')
      );
      return;
    }

    try {
      setLoading(true);

      const response = await apiService.family.createFamily({
        name: name.trim(),
        description: description.trim() || null,
        settings: '{}'
      });

      if (response.data) {
        // Save family to local database
        const savedFamily = {
          ...response.data,
          createdAt: new Date(response.data.createdAt),
          updatedAt: new Date(response.data.updatedAt),
          isSynced: true
        };
        await dbService.saveFamily(savedFamily);

        // Navigate to family details
        router.replace({
          pathname: '/family/[id]' as any,
          params: { id: savedFamily.id.toString() }
        });
      } else {
        Alert.alert(
          t('common.error'),
          response.error || t('common.error')
        );
      }
    } catch (error) {
      console.error('Error creating family:', error);
      Alert.alert(
        t('common.error'),
        t('common.error')
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      <ScrollView
        style={[styles.container, { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }]}
        contentContainerStyle={styles.contentContainer}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons
              name="arrow-back"
              size={24}
              color={isDarkMode ? '#FFFFFF' : '#000000'}
            />
          </TouchableOpacity>
          <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('family.createFamily')}
          </Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('family.familyName')} *
          </Text>
          <TextInput
            style={[
              styles.input,
              {
                backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                color: isDarkMode ? '#FFFFFF' : '#000000',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
              }
            ]}
            placeholder={t('family.familyName')}
            placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
            value={name}
            onChangeText={setName}
            maxLength={100}
          />

          <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('family.familyDescription')}
          </Text>
          <TextInput
            style={[
              styles.input,
              styles.textArea,
              {
                backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                color: isDarkMode ? '#FFFFFF' : '#000000',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE'
              }
            ]}
            placeholder={t('family.familyDescription')}
            placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={4}
            maxLength={500}
          />

          <TouchableOpacity
            style={[
              styles.createButton,
              {
                backgroundColor: isDarkMode ? '#1F6FEB' : '#0969DA',
                opacity: loading ? 0.7 : 1
              }
            ]}
            onPress={handleCreateFamily}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#FFFFFF" size="small" />
            ) : (
              <Text style={styles.createButtonText}>
                {t('family.createFamily')}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  formContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    marginBottom: 16,
  },
  textArea: {
    height: 120,
    paddingTop: 12,
    textAlignVertical: 'top',
  },
  createButton: {
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  createButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default CreateFamilyScreen;
