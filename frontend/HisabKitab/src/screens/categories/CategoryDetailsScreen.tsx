import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import apiService from '../../services/api';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';

// Import Category model
import { Category } from '../../models/Category';

interface BudgetLimit {
  id: number;
  amount: number;
  period: string;
  currency: string;
  categoryId: number;
}

type RouteParams = {
  CategoryDetails: {
    categoryId: number;
  };
};

const CategoryDetailsScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const { user, isPlatformAdmin, isFamilyAdmin, isFamilyMember } = useAuth();
  const navigation = useNavigation<any>();
  const route = useRoute<RouteProp<RouteParams, 'CategoryDetails'>>();
  const { categoryId } = route.params;

  const [category, setCategory] = useState<Category | null>(null);
  const [parentCategory, setParentCategory] = useState<Category | null>(null);
  const [subcategories, setSubcategories] = useState<Category[]>([]);
  const [budgetLimits, setBudgetLimits] = useState<BudgetLimit[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch category and related data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Fetch category
        const categoryResponse = await apiService.categories.getCategory(categoryId);

        if (categoryResponse.status === 200 && categoryResponse.data) {
          setCategory(categoryResponse.data);

          // Fetch parent category if exists
          if (categoryResponse.data.parentCategoryId) {
            const parentResponse = await apiService.categories.getCategory(
              categoryResponse.data.parentCategoryId
            );

            if (parentResponse.status === 200 && parentResponse.data) {
              setParentCategory(parentResponse.data);
            }
          }

          // Fetch all categories to find subcategories
          const allCategoriesResponse = await apiService.categories.getCategories();

          if (allCategoriesResponse.status === 200 && allCategoriesResponse.data) {
            const subs = allCategoriesResponse.data.filter(
              (cat: Category) => cat.parentCategoryId === categoryId
            );
            setSubcategories(subs);
          }

          // Fetch budget limits
          const budgetResponse = await apiService.categories.getBudgetLimitsByCategory(categoryId);

          if (budgetResponse.status === 200 && budgetResponse.data) {
            setBudgetLimits(budgetResponse.data);
          }
        } else {
          console.error('Failed to fetch category:', categoryResponse.error);
          Alert.alert(
            t('common.error'),
            categoryResponse.error || t('categories.fetchCategoryError')
          );
          navigation.goBack();
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        Alert.alert(
          t('common.error'),
          t('categories.fetchCategoryError')
        );
        navigation.goBack();
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [categoryId, navigation, t]);

  // Handle edit category
  const handleEditCategory = () => {
    navigation.navigate('EditCategory', { categoryId });
  };

  // Handle delete category
  const handleDeleteCategory = () => {
    Alert.alert(
      t('categories.deleteCategory'),
      t('categories.deleteCategoryConfirm', { name: category?.name || '' }),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await apiService.categories.deleteCategory(categoryId);

              if (response.status === 204) {
                navigation.goBack();
              } else {
                Alert.alert(
                  t('common.error'),
                  response.error || t('categories.deleteCategoryError')
                );
              }
            } catch (error) {
              console.error('Error deleting category:', error);
              Alert.alert(
                t('common.error'),
                t('categories.deleteCategoryError')
              );
            }
          },
        },
      ]
    );
  };

  // Handle add budget limit
  const handleAddBudgetLimit = () => {
    navigation.navigate('AddBudgetLimit', { categoryId });
  };

  // Handle edit budget limit
  const handleEditBudgetLimit = (budgetLimitId: number) => {
    navigation.navigate('EditBudgetLimit', { budgetLimitId });
  };

  // Handle delete budget limit
  const handleDeleteBudgetLimit = (budgetLimitId: number) => {
    Alert.alert(
      t('categories.deleteBudgetLimit'),
      t('categories.deleteBudgetLimitConfirm'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await apiService.categories.deleteBudgetLimit(budgetLimitId);

              if (response.status === 204) {
                // Refresh budget limits
                const budgetResponse = await apiService.categories.getBudgetLimitsByCategory(categoryId);

                if (budgetResponse.status === 200 && budgetResponse.data) {
                  setBudgetLimits(budgetResponse.data);
                }
              } else {
                Alert.alert(
                  t('common.error'),
                  response.error || t('categories.deleteBudgetLimitError')
                );
              }
            } catch (error) {
              console.error('Error deleting budget limit:', error);
              Alert.alert(
                t('common.error'),
                t('categories.deleteBudgetLimitError')
              );
            }
          },
        },
      ]
    );
  };

  if (isLoading || !category) {
    return (
      <View
        style={[
          styles.loadingContainer,
          { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
        ]}
      >
        <ActivityIndicator
          size="large"
          color={isDarkMode ? '#58A6FF' : '#0366D6'}
        />
      </View>
    );
  }

  return (
    <ScrollView
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
      ]}
      contentContainerStyle={styles.contentContainer}
    >
      {/* Category header */}
      <View style={styles.header}>
        <View
          style={[
            styles.iconContainer,
            { backgroundColor: category.color || '#808080' },
          ]}
        >
          <Ionicons
            name={(category.icon as any) || 'list-outline'}
            size={32}
            color="#FFFFFF"
          />
        </View>

        <Text
          style={[
            styles.categoryName,
            { color: isDarkMode ? '#FFFFFF' : '#24292E' },
          ]}
        >
          {category.name}
        </Text>

        <View
          style={[
            styles.typeTag,
            {
              backgroundColor:
                (typeof category.type === 'number' ? category.type === 0 : category.type === 'Income')
                  ? isDarkMode
                    ? '#238636'
                    : '#2EA043'
                  : isDarkMode
                  ? '#F85149'
                  : '#DA3633',
            },
          ]}
        >
          <Text style={styles.typeTagText}>
            {t(`categories.${typeof category.type === 'number'
                ? (category.type === 0 ? 'income' : 'expense')
                : (category.type ? String(category.type).toLowerCase() : '')}`)}
          </Text>
        </View>

        {category.isSystem && (
          <View
            style={[
              styles.systemTag,
              {
                backgroundColor: isDarkMode ? '#30363D' : '#F6F8FA',
              },
            ]}
          >
            <Text
              style={[
                styles.systemTagText,
                { color: isDarkMode ? '#8B949E' : '#6E7781' },
              ]}
            >
              {t('categories.system')}
            </Text>
          </View>
        )}
      </View>

      {/* Category details */}
      <Card
        title={t('categories.categoryDetails')}
        style={{ borderColor: isDarkMode ? '#30363D' : '#D0D7DE' }}
      >
        {parentCategory && (
          <View style={styles.detailRow}>
            <Text
              style={[
                styles.detailLabel,
                { color: isDarkMode ? '#8B949E' : '#6E7781' },
              ]}
            >
              {t('categories.parentCategory')}
            </Text>
            <Text
              style={[
                styles.detailValue,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' },
              ]}
            >
              {parentCategory.name}
            </Text>
          </View>
        )}

        <View style={styles.detailRow}>
          <Text
            style={[
              styles.detailLabel,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t('categories.owner')}
          </Text>
          <Text
            style={[
              styles.detailValue,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
          >
            {category.familyId
              ? t('categories.familyCategory')
              : t('categories.personalCategory')}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text
            style={[
              styles.detailLabel,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t('categories.createdAt')}
          </Text>
          <Text
            style={[
              styles.detailValue,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
          >
            {new Date(category.createdAt).toLocaleDateString()}
          </Text>
        </View>
      </Card>

      {/* Subcategories */}
      {subcategories.length > 0 && (
        <Card
          title={t('categories.subcategories')}
          style={{ borderColor: isDarkMode ? '#30363D' : '#D0D7DE' }}
        >
          {subcategories.map((subcategory: Category) => (
            <TouchableOpacity
              key={subcategory.id}
              style={styles.subcategoryItem}
              onPress={() => navigation.navigate('CategoryDetails', { categoryId: subcategory.id })}
            >
              <View style={styles.subcategoryInfo}>
                <View
                  style={[
                    styles.subcategoryIcon,
                    { backgroundColor: subcategory.color || '#808080' },
                  ]}
                >
                  <Ionicons
                    name={(subcategory.icon as any) || 'list-outline'}
                    size={16}
                    color="#FFFFFF"
                  />
                </View>
                <Text
                  style={[
                    styles.subcategoryName,
                    { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                  ]}
                >
                  {subcategory.name}
                </Text>
              </View>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={isDarkMode ? '#8B949E' : '#6E7781'}
              />
            </TouchableOpacity>
          ))}
        </Card>
      )}

      {/* Budget limits */}
      <Card
        title={t('categories.budgetLimits')}
        style={{ borderColor: isDarkMode ? '#30363D' : '#D0D7DE' }}
        rightComponent={
          <TouchableOpacity
            onPress={() => navigation.navigate('BudgetLimits')}
            style={styles.viewAllButton}
          >
            <Text
              style={[
                styles.viewAllText,
                { color: isDarkMode ? '#58A6FF' : '#0366D6' },
              ]}
            >
              {t('categories.viewAllBudgetLimits')}
            </Text>
          </TouchableOpacity>
        }
      >
        {budgetLimits.length > 0 ? (
          budgetLimits.map((budget: BudgetLimit) => (
            <View key={budget.id} style={styles.budgetItem}>
              <View style={styles.budgetInfo}>
                <Text
                  style={[
                    styles.budgetAmount,
                    { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                  ]}
                >
                  {budget.currency} {budget.amount.toFixed(2)}
                </Text>
                <Text
                  style={[
                    styles.budgetPeriod,
                    { color: isDarkMode ? '#8B949E' : '#6E7781' },
                  ]}
                >
                  {t(`categories.${budget.period.toLowerCase()}`)}
                </Text>
              </View>

              {/* Show budget actions only if user has permission */}
              {((!category.familyId && user?.id === category.userId) ||
                 (category.familyId && (isFamilyMember(category.familyId) && isFamilyAdmin)) ||
                 isPlatformAdmin) && (
                <View style={styles.budgetActions}>
                  <TouchableOpacity
                    style={styles.budgetAction}
                    onPress={() => handleEditBudgetLimit(budget.id)}
                  >
                    <Ionicons
                      name="pencil-outline"
                      size={20}
                      color={isDarkMode ? '#58A6FF' : '#0366D6'}
                    />
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.budgetAction}
                    onPress={() => handleDeleteBudgetLimit(budget.id)}
                  >
                    <Ionicons
                      name="trash-outline"
                      size={20}
                      color={isDarkMode ? '#F85149' : '#DA3633'}
                    />
                  </TouchableOpacity>
                </View>
              )}
            </View>
          ))
        ) : (
          <View style={styles.emptyBudget}>
            <Text
              style={[
                styles.emptyBudgetText,
                { color: isDarkMode ? '#8B949E' : '#6E7781' },
              ]}
            >
              {t('categories.noBudgetLimits')}
            </Text>
            {((!category.familyId && user?.id === category.userId) ||
               (category.familyId && (isFamilyMember(category.familyId) && isFamilyAdmin)) ||
               isPlatformAdmin) && (
              <Button
                title={t('categories.addBudgetLimit')}
                onPress={handleAddBudgetLimit}
                variant="outline"
                icon="add-outline"
                size="small"
                style={styles.addBudgetButton}
              />
            )}
          </View>
        )}

        {budgetLimits.length > 0 &&
          ((!category.familyId && user?.id === category.userId) ||
           (category.familyId && (isFamilyMember(category.familyId) && isFamilyAdmin)) ||
           isPlatformAdmin) && (
            <Button
              title={t('categories.addBudgetLimit')}
              onPress={handleAddBudgetLimit}
              variant="outline"
              icon="add-outline"
              style={styles.addBudgetButton}
            />
          )
        }
      </Card>

      {/* Action buttons */}
      {!category.isSystem && (
        <View style={styles.actions}>
          {/* Show edit button only if:
              - It's a personal category owned by the user, or
              - It's a family category and user is a family admin or platform admin, or
              - User is a platform admin
          */}
          {((!category.familyId && user?.id === category.userId) ||
             (category.familyId && (isFamilyMember(category.familyId) && isFamilyAdmin)) ||
             isPlatformAdmin) && (
            <Button
              title={t('common.edit')}
              onPress={handleEditCategory}
              variant="primary"
              icon="pencil-outline"
              style={styles.actionButton}
            />
          )}

          {/* Show delete button only if:
              - It's a personal category owned by the user, or
              - It's a family category and user is a family admin, or
              - User is a platform admin
          */}
          {((!category.familyId && user?.id === category.userId) ||
             (category.familyId && (isFamilyMember(category.familyId) && isFamilyAdmin)) ||
             isPlatformAdmin) && (
            <Button
              title={t('common.delete')}
              onPress={handleDeleteCategory}
              variant="danger"
              icon="trash-outline"
              style={styles.actionButton}
            />
          )}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryName: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  typeTag: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 4,
    marginBottom: 8,
  },
  typeTagText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  systemTag: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 4,
  },
  systemTagText: {
    fontSize: 14,
    fontWeight: '600',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  subcategoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  subcategoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subcategoryIcon: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  subcategoryName: {
    fontSize: 14,
    fontWeight: '600',
  },
  budgetItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  budgetInfo: {
    flex: 1,
  },
  budgetAmount: {
    fontSize: 16,
    fontWeight: '600',
  },
  budgetPeriod: {
    fontSize: 14,
  },
  budgetActions: {
    flexDirection: 'row',
  },
  budgetAction: {
    padding: 8,
  },
  emptyBudget: {
    alignItems: 'center',
    padding: 16,
  },
  emptyBudgetText: {
    fontSize: 14,
    marginBottom: 8,
  },
  addBudgetButton: {
    marginTop: 8,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  viewAllButton: {
    padding: 4,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default CategoryDetailsScreen;
