import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  SectionList
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import apiService from '../../services/api';
import CategoryCard from '../../components/categories/CategoryCard';
import { handleApiError, showSuccessMessage, showConfirmationDialog } from '../../utils/errorHandling';
import { getCategoryTypeString } from '../../utils/typeConversion';

// Import Category model
import { Category } from '../../models/Category';

const CategoriesScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();

  const [activeTab, setActiveTab] = useState('expense'); // 'expense' or 'income'
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch categories when screen is focused
  const fetchCategories = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) {
        setIsLoading(true);
      }

      const response = await apiService.categories.getCategories();

      if (response.status === 200 && response.data) {
        setCategories(response.data);
      } else {
        handleApiError(response, t, 'categories.fetchCategoryError');
      }
    } catch (error) {
      handleApiError(error, t, 'categories.fetchCategoryError');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [t]);

  // Refresh categories when screen is focused
  useFocusEffect(
    useCallback(() => {
      let isMounted = true;

      const load = async () => {
        if (isMounted) {
          await fetchCategories();
        }
      };

      load();

      return () => {
        isMounted = false;
      };
    }, [fetchCategories])
  );

  // Handle refresh
  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchCategories(false);
  };

  // Handle category press
  const handleCategoryPress = (categoryId: number) => {
    navigation.navigate('CategoryDetails', { categoryId });
  };

  // Handle edit category
  const handleEditCategory = (categoryId: number) => {
    navigation.navigate('EditCategory', { categoryId });
  };

  // Handle delete category
  const handleDeleteCategory = (categoryId: number, categoryName: string) => {
    showConfirmationDialog(
      t,
      'categories.deleteCategory',
      'categories.deleteCategoryConfirm',
      async () => {
        try {
          const response = await apiService.categories.deleteCategory(categoryId);

          if (response.status === 204) {
            // Refresh categories after successful deletion
            fetchCategories(false);
            showSuccessMessage(t, 'categories.deleteCategorySuccess');
          } else {
            handleApiError(response, t, 'categories.deleteCategoryError');
          }
        } catch (error) {
          handleApiError(error, t, 'categories.deleteCategoryError');
        }
      },
      { name: categoryName }
    );
  };

  // Filter and organize categories
  const filteredCategories = categories ? categories.filter(
    (category: Category) => {
      // Use utility function to get consistent category type string
      const typeStr = getCategoryTypeString(category.type).toLowerCase();
      return typeStr === activeTab;
    }
  ) : [];

  // Separate into parent categories and subcategories
  const parentCategories = filteredCategories.filter(
    (category: Category) => !category.parentCategoryId
  );

  const subcategories = filteredCategories.filter(
    (category: Category) => category.parentCategoryId
  );

  // Find parent category name
  const getParentCategoryName = (parentId: number) => {
    if (!categories) return '';
    const parent = categories.find((category: Category) => category.id === parentId);
    return parent ? parent.name : '';
  };

  // Render category item
  const renderCategoryItem = ({ item }: { item: Category }) => (
    <CategoryCard
      id={item.id}
      name={item.name}
      type={item.type}
      icon={item.icon}
      color={item.color}
      isSystem={item.isSystem}
      parentCategoryName={item.parentCategoryId ? getParentCategoryName(item.parentCategoryId) : undefined}
      onPress={() => handleCategoryPress(item.id)}
      onEdit={() => handleEditCategory(item.id)}
      onDelete={() => handleDeleteCategory(item.id, item.name)}
    />
  );

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons
        name="list-outline"
        size={64}
        color={isDarkMode ? '#30363D' : '#D0D7DE'}
      />
      <Text
        style={[
          styles.emptyText,
          { color: isDarkMode ? '#8B949E' : '#6E7781' },
        ]}
      >
        {t('categories.noCategories')}
      </Text>
      <TouchableOpacity
        style={[
          styles.addButton,
          { backgroundColor: isDarkMode ? '#238636' : '#2EA043' },
        ]}
        onPress={() => navigation.navigate('CreateCategory')}
      >
        <Ionicons name="add" size={20} color="#FFFFFF" />
        <Text style={styles.addButtonText}>{t('categories.addCategory')}</Text>
      </TouchableOpacity>
    </View>
  );

  // Prepare sections for SectionList
  const sections = [
    {
      title: t('categories.mainCategories'),
      data: parentCategories,
    },
    {
      title: t('categories.subcategories'),
      data: subcategories,
    },
  ].filter(section => section.data.length > 0);

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
      ]}
    >
      {/* Tab selector */}
      <View
        style={[
          styles.tabContainer,
          { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
        ]}
      >
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'expense' && {
              backgroundColor: isDarkMode ? '#30363D' : '#FFFFFF',
              borderBottomColor: isDarkMode ? '#58A6FF' : '#0366D6',
              borderBottomWidth: 2,
            },
          ]}
          onPress={() => setActiveTab('expense')}
        >
          <Text
            style={[
              styles.tabText,
              {
                color:
                  activeTab === 'expense'
                    ? isDarkMode
                      ? '#FFFFFF'
                      : '#24292E'
                    : isDarkMode
                    ? '#8B949E'
                    : '#6E7781',
                fontWeight: activeTab === 'expense' ? '600' : 'normal',
              },
            ]}
          >
            {t('categories.expense')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'income' && {
              backgroundColor: isDarkMode ? '#30363D' : '#FFFFFF',
              borderBottomColor: isDarkMode ? '#58A6FF' : '#0366D6',
              borderBottomWidth: 2,
            },
          ]}
          onPress={() => setActiveTab('income')}
        >
          <Text
            style={[
              styles.tabText,
              {
                color:
                  activeTab === 'income'
                    ? isDarkMode
                      ? '#FFFFFF'
                      : '#24292E'
                    : isDarkMode
                    ? '#8B949E'
                    : '#6E7781',
                fontWeight: activeTab === 'income' ? '600' : 'normal',
              },
            ]}
          >
            {t('categories.income')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Categories list */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={isDarkMode ? '#58A6FF' : '#0366D6'}
          />
        </View>
      ) : filteredCategories.length === 0 ? (
        renderEmptyState()
      ) : (
        <SectionList
          sections={sections}
          renderItem={renderCategoryItem}
          renderSectionHeader={({ section: { title } }) => (
            <View
              style={[
                styles.sectionHeader,
                { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
              ]}
            >
              <Text
                style={[
                  styles.sectionTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                ]}
              >
                {title}
              </Text>
            </View>
          )}
          keyExtractor={(item: Category) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={['#0366D6']}
              tintColor={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
          }
        />
      )}

      {/* Add category button */}
      <TouchableOpacity
        style={[
          styles.floatingButton,
          { backgroundColor: isDarkMode ? '#238636' : '#2EA043' },
        ]}
        onPress={() => navigation.navigate('CreateCategory')}
      >
        <Ionicons name="add" size={24} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  tabText: {
    fontSize: 16,
  },
  listContent: {
    padding: 16,
    paddingBottom: 80,
  },
  sectionHeader: {
    padding: 8,
    marginBottom: 8,
    borderRadius: 4,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 6,
  },
  addButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    marginLeft: 8,
  },
  floatingButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});

export default CategoriesScreen;
