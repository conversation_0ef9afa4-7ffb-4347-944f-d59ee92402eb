import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import apiService from '../../services/api';
import CategoryForm from '../../components/categories/CategoryForm';

type RouteParams = {
  EditCategory: {
    categoryId: number;
  };
};

const EditCategoryScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();
  const route = useRoute<RouteProp<RouteParams, 'EditCategory'>>();
  const { categoryId } = route.params;

  const [category, setCategory] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [families, setFamilies] = useState<any[]>([]);
  const [parentCategories, setParentCategories] = useState<any[]>([]);

  // Fetch category, families, and parent categories
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Fetch category
        const categoryResponse = await apiService.categories.getCategory(categoryId);

        if (categoryResponse.status === 200 && categoryResponse.data) {
          setCategory(categoryResponse.data);
        } else {
          console.error('Failed to fetch category:', categoryResponse.error);
          Alert.alert(
            t('common.error'),
            categoryResponse.error || t('categories.fetchCategoryError')
          );
          navigation.goBack();
        }

        // Fetch families
        const familiesResponse = await apiService.family.getFamilies();

        if (familiesResponse.status === 200 && familiesResponse.data) {
          setFamilies(familiesResponse.data);
        } else {
          console.error('Failed to fetch families:', familiesResponse.error);
        }

        // Fetch categories
        const categoriesResponse = await apiService.categories.getCategories();

        if (categoriesResponse.status === 200 && categoriesResponse.data) {
          // Filter out only parent categories and exclude the current category
          const parents = categoriesResponse.data.filter(
            (cat: any) => !cat.parentCategoryId && cat.id !== categoryId
          );
          setParentCategories(parents);
        } else {
          console.error('Failed to fetch categories:', categoriesResponse.error);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        Alert.alert(
          t('common.error'),
          t('categories.fetchCategoryError')
        );
        navigation.goBack();
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [categoryId, navigation, t]);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    try {
      setIsSubmitting(true);

      const categoryData = {
        name: values.name,
        type: values.type,
        icon: values.icon,
        color: values.color,
        parentCategoryId: values.parentCategoryId,
      };

      const response = await apiService.categories.updateCategory(categoryId, categoryData as any);

      if (response.status === 200 && response.data) {
        // Navigate back to categories screen
        navigation.goBack();
      } else {
        Alert.alert(
          t('common.error'),
          response.error || t('categories.updateCategoryError')
        );
      }
    } catch (error) {
      console.error('Error updating category:', error);
      Alert.alert(
        t('common.error'),
        t('categories.updateCategoryError')
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading || !category) {
    return (
      <View
        style={[
          styles.loadingContainer,
          { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
        ]}
      >
        <ActivityIndicator
          size="large"
          color={isDarkMode ? '#58A6FF' : '#0366D6'}
        />
      </View>
    );
  }

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
      ]}
    >
      <CategoryForm
        initialValues={{
          id: category.id,
          name: category.name,
          type: category.type,
          icon: category.icon,
          color: category.color,
          parentCategoryId: category.parentCategoryId,
          familyId: category.familyId,
        }}
        onSubmit={handleSubmit}
        isLoading={isSubmitting}
        mode="edit"
        families={families}
        parentCategories={parentCategories}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default EditCategoryScreen;
