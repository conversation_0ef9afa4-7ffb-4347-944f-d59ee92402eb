import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import apiService from '../../services/api';
import BudgetLimitForm from '../../components/categories/BudgetLimitForm';

// Import Category model
import { Category } from '../../models/Category';

type RouteParams = {
  AddBudgetLimit: {
    categoryId: number;
  };
};

const AddBudgetLimitScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();
  const route = useRoute<RouteProp<RouteParams, 'AddBudgetLimit'>>();
  const { categoryId } = route.params;

  const [category, setCategory] = useState<Category | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch category
  useEffect(() => {
    const fetchCategory = async () => {
      try {
        setIsLoading(true);
        const response = await apiService.categories.getCategory(categoryId);

        if (response.status === 200 && response.data) {
          setCategory(response.data);
        } else {
          console.error('Failed to fetch category:', response.error);
          Alert.alert(
            t('common.error'),
            response.error || t('categories.fetchCategoryError')
          );
          navigation.goBack();
        }
      } catch (error) {
        console.error('Error fetching category:', error);
        Alert.alert(
          t('common.error'),
          t('categories.fetchCategoryError')
        );
        navigation.goBack();
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategory();
  }, [categoryId, navigation, t]);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    try {
      setIsSubmitting(true);

      const budgetData = {
        amount: values.amount,
        period: values.period,
        categoryId,
        startDate: values.startDate,
        endDate: values.endDate,
        notificationThreshold: values.notificationThreshold,
        rolloverUnused: values.rolloverUnused,
      };

      const response = await apiService.categories.setBudgetLimit(budgetData);

      if (response.status === 201 && response.data) {
        // Navigate back to category details screen
        navigation.goBack();
      } else {
        Alert.alert(
          t('common.error'),
          response.error || t('categories.addBudgetLimitError')
        );
      }
    } catch (error) {
      console.error('Error adding budget limit:', error);
      Alert.alert(
        t('common.error'),
        t('categories.addBudgetLimitError')
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading || !category) {
    return (
      <View
        style={[
          styles.loadingContainer,
          { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
        ]}
      >
        <ActivityIndicator
          size="large"
          color={isDarkMode ? '#58A6FF' : '#0366D6'}
        />
      </View>
    );
  }

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
      ]}
    >
      <BudgetLimitForm
        initialValues={{
          amount: 0,
          period: 'Monthly',
          currency: 'NPR',
          categoryId,
        }}
        onSubmit={handleSubmit}
        isLoading={isSubmitting}
        mode="create"
        categoryName={category.name}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default AddBudgetLimitScreen;
