import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import apiService from '../../services/api';
import BudgetLimitForm from '../../components/categories/BudgetLimitForm';

// Define interfaces
interface BudgetLimit {
  id: number;
  amount: number;
  period: string;
  currency: string;
  categoryId: number;
}

interface Category {
  id: number;
  name: string;
  type: string;
  icon: string;
  color: string;
}

type RouteParams = {
  EditBudgetLimit: {
    budgetLimitId: number;
  };
};

const EditBudgetLimitScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();
  const route = useRoute<RouteProp<RouteParams, 'EditBudgetLimit'>>();
  const { budgetLimitId } = route.params;

  const [budgetLimit, setBudgetLimit] = useState<BudgetLimit | null>(null);
  const [category, setCategory] = useState<Category | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch budget limit and category
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Fetch budget limit
        const budgetResponse = await apiService.categories.getBudgetLimit(budgetLimitId);

        if (budgetResponse.status === 200 && budgetResponse.data) {
          setBudgetLimit(budgetResponse.data);

          // Fetch category
          const categoryResponse = await apiService.categories.getCategory(
            budgetResponse.data.categoryId
          );

          if (categoryResponse.status === 200 && categoryResponse.data) {
            setCategory(categoryResponse.data);
          } else {
            console.error('Failed to fetch category:', categoryResponse.error);
          }
        } else {
          console.error('Failed to fetch budget limit:', budgetResponse.error);
          Alert.alert(
            t('common.error'),
            budgetResponse.error || t('categories.fetchBudgetLimitError')
          );
          navigation.goBack();
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        Alert.alert(
          t('common.error'),
          t('categories.fetchBudgetLimitError')
        );
        navigation.goBack();
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [budgetLimitId, navigation, t]);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    try {
      setIsSubmitting(true);

      const budgetData = {
        amount: values.amount,
        period: values.period,
        categoryId: values.categoryId,
        notificationThreshold: values.notificationThreshold,
        rolloverUnused: values.rolloverUnused,
        endDate: values.endDate,
      };

      const response = await apiService.categories.updateBudgetLimit(
        budgetLimitId,
        budgetData
      );

      if (response.status === 200 && response.data) {
        // Navigate back to category details screen
        navigation.goBack();
      } else {
        Alert.alert(
          t('common.error'),
          response.error || t('categories.updateBudgetLimitError')
        );
      }
    } catch (error) {
      console.error('Error updating budget limit:', error);
      Alert.alert(
        t('common.error'),
        t('categories.updateBudgetLimitError')
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading || !budgetLimit) {
    return (
      <View
        style={[
          styles.loadingContainer,
          { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
        ]}
      >
        <ActivityIndicator
          size="large"
          color={isDarkMode ? '#58A6FF' : '#0366D6'}
        />
      </View>
    );
  }

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
      ]}
    >
      <BudgetLimitForm
        initialValues={{
          id: budgetLimit.id,
          amount: budgetLimit.amount,
          period: budgetLimit.period,
          currency: budgetLimit.currency,
          categoryId: budgetLimit.categoryId,
        }}
        onSubmit={handleSubmit}
        isLoading={isSubmitting}
        mode="edit"
        categoryName={category ? category.name : ''}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default EditBudgetLimitScreen;
