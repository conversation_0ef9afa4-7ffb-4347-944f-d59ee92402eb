import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import apiService from '../../services/api';
import CategoryForm from '../../components/categories/CategoryForm';

const CreateCategoryScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation();

  const [isLoading, setIsLoading] = useState(false);
  const [families, setFamilies] = useState<any[]>([]);
  const [parentCategories, setParentCategories] = useState<any[]>([]);

  // Fetch families and categories
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch families
        const familiesResponse = await apiService.family.getFamilies();

        if (familiesResponse.status === 200 && familiesResponse.data) {
          setFamilies(familiesResponse.data);
        } else {
          console.error('Failed to fetch families:', familiesResponse.error);
        }

        // Fetch categories
        const categoriesResponse = await apiService.categories.getCategories();

        if (categoriesResponse.status === 200 && categoriesResponse.data) {
          // Filter out only parent categories
          const parents = categoriesResponse.data.filter(
            (category: any) => !category.parentCategoryId
          );
          setParentCategories(parents);
        } else {
          console.error('Failed to fetch categories:', categoriesResponse.error);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();
  }, []);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    try {
      setIsLoading(true);

      const categoryData = {
        name: values.name,
        type: values.type,
        icon: values.icon,
        color: values.color,
        parentCategoryId: values.parentCategoryId,
        familyId: values.familyId,
      };

      const response = await apiService.categories.createCategory(categoryData as any);

      if (response.status === 201 && response.data) {
        // Navigate back to categories screen
        navigation.goBack();
      } else {
        Alert.alert(
          t('common.error'),
          response.error || t('categories.createCategoryError')
        );
      }
    } catch (error) {
      console.error('Error creating category:', error);
      Alert.alert(
        t('common.error'),
        t('categories.createCategoryError')
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
      ]}
    >
      <CategoryForm
        onSubmit={handleSubmit}
        isLoading={isLoading}
        mode="create"
        families={families}
        parentCategories={parentCategories}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default CreateCategoryScreen;
