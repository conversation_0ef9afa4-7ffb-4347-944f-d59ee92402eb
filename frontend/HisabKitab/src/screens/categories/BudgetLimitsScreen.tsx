import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import apiService from '../../services/api';
import Card from '../../components/ui/Card';
import { useFetchData } from '../../hooks/useFetchData';
import { handleApiError, showSuccessMessage, showConfirmationDialog } from '../../utils/errorHandling';

// Define interfaces
interface BudgetLimit {
  id: number;
  amount: number;
  period: string;
  currency: string;
  categoryId: number;
  categoryName: string;
  categoryType: string;
  categoryIcon: string;
  categoryColor: string;
}

const BudgetLimitsScreen = () => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const navigation = useNavigation<any>();

  // Use custom hook for fetching budget limits
  const {
    data: budgetLimits = [],
    isLoading,
    isRefreshing,
    refresh: handleRefresh,
    fetchData: refetchBudgetLimits
  } = useFetchData<BudgetLimit[]>(
    () => apiService.categories.getAllBudgetLimits(),
    t,
    'categories.fetchBudgetLimitError',
    []
  );

  // Handle edit budget limit
  const handleEditBudgetLimit = (budgetLimitId: number) => {
    navigation.navigate('EditBudgetLimit', { budgetLimitId });
  };

  // Handle delete budget limit
  const handleDeleteBudgetLimit = (budgetLimitId: number, categoryName: string) => {
    showConfirmationDialog(
      t,
      'categories.deleteBudgetLimit',
      'categories.deleteBudgetLimitConfirm',
      async () => {
        try {
          const response = await apiService.categories.deleteBudgetLimit(budgetLimitId);

          if (response.status === 204) {
            // Refresh budget limits after successful deletion
            refetchBudgetLimits(false);
            showSuccessMessage(t, 'categories.deleteBudgetLimitSuccess');
          } else {
            handleApiError(response, t, 'categories.deleteBudgetLimitError');
          }
        } catch (error) {
          handleApiError(error, t, 'categories.deleteBudgetLimitError');
        }
      },
      { name: categoryName }
    );
  };

  // Handle view category
  const handleViewCategory = (categoryId: number) => {
    navigation.navigate('CategoryDetails', { categoryId });
  };

  // Render budget limit item
  const renderBudgetLimitItem = ({ item }: { item: BudgetLimit }) => (
    <Card style={styles.budgetCard}>
      <View style={styles.budgetHeader}>
        <TouchableOpacity 
          style={styles.categoryInfo}
          onPress={() => handleViewCategory(item.categoryId)}
        >
          <View
            style={[
              styles.categoryIcon,
              { backgroundColor: item.categoryColor || '#808080' },
            ]}
          >
            <Ionicons
              name={(item.categoryIcon as any) || 'list-outline'}
              size={20}
              color="#FFFFFF"
            />
          </View>
          <View style={styles.categoryDetails}>
            <Text
              style={[
                styles.categoryName,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' },
              ]}
            >
              {item.categoryName}
            </Text>
            <View
              style={[
                styles.typeTag,
                {
                  backgroundColor:
                    item.categoryType === 'Income'
                      ? isDarkMode
                        ? '#238636'
                        : '#2EA043'
                      : isDarkMode
                      ? '#F85149'
                      : '#DA3633',
                },
              ]}
            >
              <Text style={styles.typeTagText}>
                {t(`categories.${item.categoryType.toLowerCase()}`)}
              </Text>
            </View>
          </View>
        </TouchableOpacity>

        <View style={styles.budgetInfo}>
          <Text
            style={[
              styles.budgetAmount,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
          >
            {item.currency} {item.amount.toFixed(2)}
          </Text>
          <Text
            style={[
              styles.budgetPeriod,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t(`categories.${item.period.toLowerCase()}`)}
          </Text>
        </View>
      </View>

      <View style={styles.budgetActions}>
        <TouchableOpacity
          style={[
            styles.actionButton,
            { backgroundColor: isDarkMode ? '#30363D' : '#F6F8FA' },
          ]}
          onPress={() => handleEditBudgetLimit(item.id)}
        >
          <Ionicons
            name="pencil-outline"
            size={16}
            color={isDarkMode ? '#58A6FF' : '#0366D6'}
          />
          <Text
            style={[
              styles.actionText,
              { color: isDarkMode ? '#58A6FF' : '#0366D6' },
            ]}
          >
            {t('common.edit')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.actionButton,
            { backgroundColor: isDarkMode ? '#30363D' : '#F6F8FA' },
          ]}
          onPress={() => handleDeleteBudgetLimit(item.id, item.categoryName)}
        >
          <Ionicons
            name="trash-outline"
            size={16}
            color={isDarkMode ? '#F85149' : '#DA3633'}
          />
          <Text
            style={[
              styles.actionText,
              { color: isDarkMode ? '#F85149' : '#DA3633' },
            ]}
          >
            {t('common.delete')}
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons
        name="wallet-outline"
        size={64}
        color={isDarkMode ? '#30363D' : '#D0D7DE'}
      />
      <Text
        style={[
          styles.emptyText,
          { color: isDarkMode ? '#8B949E' : '#6E7781' },
        ]}
      >
        {t('categories.noBudgetLimits')}
      </Text>
      <TouchableOpacity
        style={[
          styles.addButton,
          { backgroundColor: isDarkMode ? '#238636' : '#2EA043' },
        ]}
        onPress={() => navigation.navigate('Categories')}
      >
        <Ionicons name="add" size={20} color="#FFFFFF" />
        <Text style={styles.addButtonText}>{t('categories.addBudgetLimit')}</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
      ]}
    >
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={isDarkMode ? '#58A6FF' : '#0366D6'}
          />
        </View>
      ) : (
        <FlatList
          data={budgetLimits}
          renderItem={renderBudgetLimitItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={['#0366D6']}
              tintColor={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: 16,
    paddingBottom: 80,
  },
  budgetCard: {
    marginBottom: 12,
    padding: 0,
  },
  budgetHeader: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryDetails: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  typeTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  typeTagText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  budgetInfo: {
    alignItems: 'flex-end',
  },
  budgetAmount: {
    fontSize: 18,
    fontWeight: '700',
  },
  budgetPeriod: {
    fontSize: 14,
  },
  budgetActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#30363D',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  actionText: {
    marginLeft: 8,
    fontWeight: '600',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 6,
  },
  addButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default BudgetLimitsScreen;
