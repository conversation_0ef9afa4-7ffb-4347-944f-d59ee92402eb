import React, { useState } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Text,
  Animated,
  Dimensions,
  Platform,
} from 'react-native';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width } = Dimensions.get('window');

const BottomTabBar: React.FC<BottomTabBarProps> = ({
  state,
  descriptors,
  navigation,
}) => {
  const { colors, borderRadius, isDarkMode } = useTheme();
  const insets = useSafeAreaInsets();
  const [fabOpen, setFabOpen] = useState(false);
  const [scaleAnim] = useState(new Animated.Value(1));
  const [rotateAnim] = useState(new Animated.Value(0));

  // Get icon name based on route name
  const getIconName = (routeName: string, isFocused: boolean) => {
    let iconName = '';
    
    switch (routeName) {
      case 'Home':
      case 'Dashboard':
      case 'Overview':
        iconName = isFocused ? 'home' : 'home-outline';
        break;
      case 'Transactions':
        iconName = isFocused ? 'list' : 'list-outline';
        break;
      case 'Loans':
      case 'Goals':
      case 'LoansGoals':
        iconName = isFocused ? 'wallet' : 'wallet-outline';
        break;
      case 'Reports':
      case 'Analytics':
        iconName = isFocused ? 'bar-chart' : 'bar-chart-outline';
        break;
      case 'Settings':
      case 'More':
        iconName = isFocused ? 'settings' : 'settings-outline';
        break;
      default:
        iconName = isFocused ? 'apps' : 'apps-outline';
    }
    
    return iconName;
  };

  // Handle FAB press
  const handleFabPress = () => {
    setFabOpen(!fabOpen);
    
    // Animate scale
    Animated.spring(scaleAnim, {
      toValue: fabOpen ? 1 : 1.1,
      friction: 5,
      tension: 40,
      useNativeDriver: true,
    }).start();
    
    // Animate rotation
    Animated.spring(rotateAnim, {
      toValue: fabOpen ? 0 : 1,
      friction: 5,
      tension: 40,
      useNativeDriver: true,
    }).start();
  };

  // Handle FAB action press
  const handleFabActionPress = (actionType: string) => {
    setFabOpen(false);
    
    // Reset animations
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 5,
      tension: 40,
      useNativeDriver: true,
    }).start();
    
    Animated.spring(rotateAnim, {
      toValue: 0,
      friction: 5,
      tension: 40,
      useNativeDriver: true,
    }).start();
    
    // Navigate based on action type
    switch (actionType) {
      case 'expense':
        navigation.navigate('AddTransaction', { type: 'expense' });
        break;
      case 'income':
        navigation.navigate('AddTransaction', { type: 'income' });
        break;
      case 'transfer':
        navigation.navigate('AddTransaction', { type: 'transfer' });
        break;
      case 'scan':
        navigation.navigate('ScanReceipt');
        break;
      default:
        navigation.navigate('AddTransaction');
    }
  };

  // Calculate rotation for the plus icon
  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '45deg'],
  });

  return (
    <View style={[
      styles.container,
      {
        backgroundColor: isDarkMode ? colors.background.secondary : colors.card,
        borderTopColor: isDarkMode ? colors.border.dark.primary : colors.border.light.primary,
        paddingBottom: insets.bottom > 0 ? insets.bottom : 10,
      }
    ]}>
      {/* FAB Actions Menu (conditionally rendered) */}
      {fabOpen && (
        <View style={styles.fabActionsContainer}>
          <TouchableOpacity
            style={[
              styles.fabAction,
              { backgroundColor: colors.semantic.expense.light }
            ]}
            onPress={() => handleFabActionPress('expense')}
          >
            <Ionicons name="arrow-down" size={20} color="#FFFFFF" />
            <Text style={styles.fabActionText}>Expense</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.fabAction,
              { backgroundColor: colors.semantic.income.light }
            ]}
            onPress={() => handleFabActionPress('income')}
          >
            <Ionicons name="arrow-up" size={20} color="#FFFFFF" />
            <Text style={styles.fabActionText}>Income</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.fabAction,
              { backgroundColor: colors.semantic.transfer.light }
            ]}
            onPress={() => handleFabActionPress('transfer')}
          >
            <Ionicons name="swap-horizontal" size={20} color="#FFFFFF" />
            <Text style={styles.fabActionText}>Transfer</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.fabAction,
              { backgroundColor: colors.accent.start }
            ]}
            onPress={() => handleFabActionPress('scan')}
          >
            <Ionicons name="scan" size={20} color="#FFFFFF" />
            <Text style={styles.fabActionText}>Scan</Text>
          </TouchableOpacity>
        </View>
      )}
      
      {/* Overlay when FAB is open */}
      {fabOpen && (
        <TouchableOpacity
          style={styles.overlay}
          onPress={() => setFabOpen(false)}
          activeOpacity={1}
        />
      )}
      
      {/* Tab Bar Items */}
      <View style={styles.tabBar}>
        {state.routes.map((route, index) => {
          // Skip the middle tab (index 2) as it's reserved for the FAB
          if (index === 2) return null;
          
          const { options } = descriptors[route.key];
          const label = options.tabBarLabel || options.title || route.name;
          const isFocused = state.index === index;
          
          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });
            
            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };
          
          return (
            <TouchableOpacity
              key={route.key}
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              testID={options.tabBarTestID}
              onPress={onPress}
              style={styles.tabItem}
            >
              <Ionicons
                name={getIconName(route.name, isFocused) as any}
                size={24}
                color={isFocused ? colors.primary : isDarkMode ? '#8B949E' : '#6E7781'}
              />
              <Text
                style={[
                  styles.tabLabel,
                  {
                    color: isFocused ? colors.primary : isDarkMode ? '#8B949E' : '#6E7781',
                  },
                ]}
              >
                {label.toString()}
              </Text>
            </TouchableOpacity>
          );
        })}
        
        {/* Center FAB */}
        <View style={styles.fabContainer}>
          <Animated.View
            style={[
              styles.fab,
              {
                backgroundColor: colors.primary,
                transform: [
                  { scale: scaleAnim },
                ],
              },
            ]}
          >
            <TouchableOpacity
              onPress={handleFabPress}
              style={styles.fabButton}
            >
              <Animated.View style={{ transform: [{ rotate }] }}>
                <Ionicons name="add" size={28} color="#FFFFFF" />
              </Animated.View>
            </TouchableOpacity>
          </Animated.View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderTopWidth: 1,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1,
  },
  tabBar: {
    flexDirection: 'row',
    height: 60,
    position: 'relative',
    zIndex: 2,
  },
  tabItem: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabLabel: {
    fontSize: 10,
    marginTop: 2,
  },
  fabContainer: {
    position: 'absolute',
    alignSelf: 'center',
    width: width / 5,
    alignItems: 'center',
    bottom: 5,
    zIndex: 3,
  },
  fab: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 3,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  fabButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fabActionsContainer: {
    position: 'absolute',
    bottom: 80,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    zIndex: 2,
  },
  fabAction: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 12,
    ...Platform.select({
      ios: {
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 3,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  fabActionText: {
    color: '#FFFFFF',
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
  },
});

export default BottomTabBar;
