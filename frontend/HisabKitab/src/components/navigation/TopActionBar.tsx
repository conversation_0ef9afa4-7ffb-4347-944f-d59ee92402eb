import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Platform,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';

interface TopActionBarProps {
  title: string;
  subtitle?: string;
  leftIcon?: string;
  rightIcon?: string;
  onLeftPress?: () => void;
  onRightPress?: () => void;
  showFamilySelector?: boolean;
  transparent?: boolean;
  scrollY?: Animated.Value;
}

const TopActionBar: React.FC<TopActionBarProps> = ({
  title,
  subtitle,
  leftIcon = 'arrow-back',
  rightIcon,
  onLeftPress,
  onRightPress,
  showFamilySelector = false,
  transparent = false,
  scrollY,
}) => {
  const { colors, isDarkMode } = useTheme();
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const [opacity] = useState(new Animated.Value(transparent ? 0 : 1));
  const [translateY] = useState(new Animated.Value(0));

  // Handle left icon press (default to navigation.goBack)
  const handleLeftPress = () => {
    if (onLeftPress) {
      onLeftPress();
    } else {
      navigation.goBack();
    }
  };

  // Animate header based on scroll position
  useEffect(() => {
    if (scrollY && transparent) {
      const headerScrollListener = scrollY.addListener(({ value }) => {
        // Start fading in after 20px of scroll
        const newOpacity = Math.min(1, value / 100);
        opacity.setValue(newOpacity);

        // Parallax effect for title
        const translateYValue = Math.max(-20, -value / 3);
        translateY.setValue(translateYValue);
      });

      return () => {
        scrollY.removeListener(headerScrollListener);
      };
    }
  }, [scrollY, transparent, opacity, translateY]);

  // Get background color based on transparency and scroll position
  const getBackgroundColor = () => {
    if (transparent) {
      return isDarkMode
        ? `rgba(11, 12, 16, ${opacity._value})`
        : `rgba(245, 245, 245, ${opacity._value})`;
    }
    return isDarkMode ? colors.background.dark : colors.background.light;
  };

  // Get border color based on transparency and scroll position
  const getBorderColor = () => {
    if (transparent) {
      return isDarkMode
        ? `rgba(45, 55, 72, ${opacity._value})`
        : `rgba(229, 229, 229, ${opacity._value})`;
    }
    return isDarkMode ? colors.border.dark.primary : colors.border.light.primary;
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: getBackgroundColor(),
          borderBottomColor: getBorderColor(),
          paddingTop: insets.top,
          borderBottomWidth: transparent ? opacity.interpolate({
            inputRange: [0, 1],
            outputRange: [0, 1],
          }) : 1,
        },
      ]}
    >
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent
      />
      <View style={styles.content}>
        {/* Left Icon */}
        {leftIcon && (
          <TouchableOpacity
            style={styles.leftButton}
            onPress={handleLeftPress}
          >
            <Ionicons
              name={leftIcon as any}
              size={24}
              color={isDarkMode ? colors.text.dark.primary : colors.text.light.primary}
            />
          </TouchableOpacity>
        )}

        {/* Title and Subtitle */}
        <Animated.View
          style={[
            styles.titleContainer,
            { transform: [{ translateY }] },
          ]}
        >
          {showFamilySelector ? (
            <TouchableOpacity
              style={styles.familySelector}
              onPress={() => navigation.navigate('FamilySelector')}
            >
              <Text
                style={[
                  styles.title,
                  { color: isDarkMode ? colors.text.dark.primary : colors.text.light.primary },
                ]}
              >
                {title}
              </Text>
              <Ionicons
                name="chevron-down"
                size={16}
                color={isDarkMode ? colors.text.dark.secondary : colors.text.light.secondary}
                style={styles.familySelectorIcon}
              />
            </TouchableOpacity>
          ) : (
            <Text
              style={[
                styles.title,
                { color: isDarkMode ? colors.text.dark.primary : colors.text.light.primary },
              ]}
              numberOfLines={1}
            >
              {title}
            </Text>
          )}

          {subtitle && (
            <Text
              style={[
                styles.subtitle,
                { color: isDarkMode ? colors.text.dark.secondary : colors.text.light.secondary },
              ]}
              numberOfLines={1}
            >
              {subtitle}
            </Text>
          )}
        </Animated.View>

        {/* Right Icon */}
        {rightIcon ? (
          <TouchableOpacity
            style={styles.rightButton}
            onPress={onRightPress}
          >
            <Ionicons
              name={rightIcon as any}
              size={24}
              color={isDarkMode ? colors.text.dark.primary : colors.text.light.primary}
            />
          </TouchableOpacity>
        ) : (
          <View style={styles.rightButton} />
        )}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderBottomWidth: 1,
    zIndex: 10,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 56,
    paddingHorizontal: 16,
  },
  leftButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rightButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 2,
  },
  familySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  familySelectorIcon: {
    marginLeft: 4,
  },
});

export default TopActionBar;
