import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import Card from '../ui/Card';
import { formatCurrency } from '../../utils/formatters';
import { Account } from '../../models/Account';

interface AccountCardProps {
  account: Account;
  onPress: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  showActions?: boolean;
  isSelected?: boolean;
}

const AccountCard: React.FC<AccountCardProps> = ({
  account,
  onPress,
  onEdit,
  onDelete,
  showActions = true,
  isSelected = false,
}) => {
  const { colors, isDarkMode } = useTheme();
  const { t } = useTranslation();

  // Get account type icon
  const getAccountTypeIcon = () => {
    switch (account.accountType?.toLowerCase()) {
      case 'cash':
        return 'cash-outline';
      case 'bank':
        return 'business-outline';
      case 'creditcard':
      case 'credit card':
        return 'card-outline';
      case 'investment':
        return 'trending-up-outline';
      case 'savings':
        return 'wallet-outline';
      case 'loan':
        return 'receipt-outline';
      case 'digital wallet':
      case 'digitalwallet':
        return 'wallet-outline';
      default:
        return 'wallet-outline';
    }
  };

  // Get account type color
  const getAccountTypeColor = () => {
    switch (account.accountType?.toLowerCase()) {
      case 'cash':
        return colors.semantic.income.light;
      case 'bank':
        return colors.primary;
      case 'creditcard':
      case 'credit card':
        return colors.semantic.expense.light;
      case 'investment':
        return colors.semantic.high.light;
      case 'loan':
        return colors.semantic.urgent.light;
      case 'digital wallet':
      case 'digitalwallet':
        return colors.semantic.transfer.light;
      default:
        return colors.primary;
    }
  };

  // Format balance with currency
  const formattedBalance = formatCurrency(account.balance || 0, account.currency || 'NPR');

  return (
    <Card
      style={
        isSelected ?
        {
          borderColor: colors.primary,
          borderWidth: 2,
        } :
        undefined
      }
      onPress={onPress}
      interactive
      variant={isSelected ? 'primary' : 'default'}
      elevation={isSelected ? 'md' : 'sm'}
    >
      <View style={styles.cardContent}>
        {/* Icon and account name */}
        <View style={styles.leftContent}>
          <View
            style={[
              styles.iconContainer,
              { backgroundColor: getAccountTypeColor() },
            ]}
          >
            <Ionicons name={getAccountTypeIcon() as any} size={24} color="#FFFFFF" />
          </View>
          <View style={styles.nameContainer}>
            <Text
              style={[
                styles.accountName,
                { color: isDarkMode ? colors.text.dark.primary : colors.text.light.primary },
              ]}
              numberOfLines={1}
            >
              {account.name}
            </Text>
            <Text
              style={[
                styles.accountType,
                { color: isDarkMode ? colors.text.dark.secondary : colors.text.light.secondary },
              ]}
              numberOfLines={1}
            >
              {account.accountType || t('accounts.defaultType')}
            </Text>
          </View>
        </View>

        {/* Balance */}
        <View style={styles.rightContent}>
          <Text
            style={[
              styles.balance,
              {
                color: account.balance && account.balance < 0
                  ? colors.semantic.expense.light
                  : isDarkMode ? colors.text.dark.primary : colors.text.light.primary
              },
            ]}
          >
            {formattedBalance}
          </Text>

          {account.excludeFromStats && (
            <View
              style={[
                styles.excludedBadge,
                { backgroundColor: colors.warning }
              ]}
            >
              <Text style={styles.excludedText}>{t('accounts.excluded')}</Text>
            </View>
          )}

          {!account.isActive && (
            <View
              style={[
                styles.inactiveBadge,
                { backgroundColor: colors.danger }
              ]}
            >
              <Text style={styles.inactiveText}>{t('accounts.inactive')}</Text>
            </View>
          )}
        </View>
      </View>

      {/* Actions */}
      {showActions && (onEdit || onDelete) && (
        <View style={styles.actionsContainer}>
          {onEdit && (
            <TouchableOpacity
              style={[
                styles.actionButton,
                { backgroundColor: colors.primary },
              ]}
              onPress={onEdit}
            >
              <Ionicons name="create-outline" size={16} color="#FFFFFF" />
              <Text style={styles.actionButtonText}>{t('common.edit')}</Text>
            </TouchableOpacity>
          )}

          {onDelete && (
            <TouchableOpacity
              style={[
                styles.actionButton,
                { backgroundColor: colors.danger },
              ]}
              onPress={onDelete}
            >
              <Ionicons name="trash-outline" size={16} color="#FFFFFF" />
              <Text style={styles.actionButtonText}>{t('common.delete')}</Text>
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* Family sharing info */}
      {account.familyId && (
        <View
          style={[
            styles.familyBadge,
            { backgroundColor: isDarkMode ? colors.border.dark.primary : colors.border.light.primary }
          ]}
        >
          <Ionicons
            name="people-outline"
            size={14}
            color={isDarkMode ? colors.text.dark.secondary : colors.text.light.secondary}
          />
          <Text
            style={[
              styles.familyText,
              { color: isDarkMode ? colors.text.dark.secondary : colors.text.light.secondary }
            ]}
          >
            {t('accounts.familyShared')}
          </Text>
        </View>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  nameContainer: {
    flex: 1,
  },
  accountName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  accountType: {
    fontSize: 14,
  },
  rightContent: {
    alignItems: 'flex-end',
  },
  balance: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  excludedBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    marginBottom: 4,
  },
  excludedText: {
    fontSize: 10,
    color: '#000000',
    fontWeight: '500',
  },
  inactiveBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  inactiveText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    marginLeft: 8,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  familyBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 0,
    marginLeft: 16,
    marginBottom: 12,
    alignSelf: 'flex-start',
  },
  familyText: {
    fontSize: 12,
    marginLeft: 4,
  },

  // Keep these for backward compatibility
  card: {
    marginBottom: 12,
    padding: 0,
  },
  inactiveCard: {
    opacity: 0.7,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  titleContainer: {
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  type: {
    fontSize: 14,
  },
  inactiveTag: {
    backgroundColor: '#F85149',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginLeft: 8,
  },
  inactiveTagText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  sharedTag: {
    backgroundColor: '#0366D6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginLeft: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  sharedIcon: {
    marginRight: 4,
  },
  sharedTagText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  balanceContainer: {
    padding: 16,
    paddingTop: 0,
  },
  balanceLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  balanceAmount: {
    fontSize: 20,
    fontWeight: '700',
  },
  negativeBalance: {
    color: '#F85149',
  },
  actions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#30363D',
  },
  actionText: {
    marginLeft: 8,
    fontWeight: '600',
  },
});

export default AccountCard;
