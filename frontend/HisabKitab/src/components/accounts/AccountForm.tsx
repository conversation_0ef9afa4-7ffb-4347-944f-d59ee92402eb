import React, { useState, forwardRef, useImperativeHandle } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import FormInput from '../ui/FormInput';
import Button from '../ui/Button';
import Dropdown from '../ui/Dropdown';
import { useAuth } from '../../contexts/AuthContext';

interface AccountFormProps {
  initialValues?: {
    id?: number;
    name: string;
    accountType: string;
    initialBalance: number;
    currency: string;
    isActive?: boolean;
    excludeFromStats?: boolean;
    familyId?: number | null;
  };
  onSubmit: (values: any) => void;
  isLoading?: boolean;
  mode?: 'create' | 'edit';
  families?: Array<{ id: number; name: string }>;
}

// Define the ref type
export interface AccountFormRef {
  resetForm: () => void;
}

const AccountForm = forwardRef<AccountFormRef, AccountFormProps>(({
  initialValues = {
    name: '',
    accountType: 'Cash',
    initialBalance: 0,
    currency: 'NPR',
    isActive: true,
    excludeFromStats: false,
    familyId: null,
  },
  onSubmit,
  isLoading = false,
  mode = 'create',
  families = [],
}, ref) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const { isFamilyAdmin } = useAuth();

  // Form state
  const [name, setName] = useState(initialValues.name);
  // Convert string account type to number if needed
  const [accountType, setAccountType] = useState(
    typeof initialValues.accountType === 'string'
      ? getAccountTypeValue(initialValues.accountType)
      : initialValues.accountType
  );
  const [initialBalance, setInitialBalance] = useState(
    initialValues.initialBalance.toString()
  );
  const [currency, setCurrency] = useState(initialValues.currency);
  const [isActive, setIsActive] = useState(initialValues.isActive ?? true);
  const [excludeFromStats, setExcludeFromStats] = useState(
    initialValues.excludeFromStats ?? false
  );
  const [familyId, setFamilyId] = useState<number | null>(
    initialValues.familyId ?? null
  );

  // Helper function to convert string account type to number
  function getAccountTypeValue(type: string): number {
    switch(type) {
      case 'Cash': return 0;
      case 'Bank': return 1;
      case 'CreditCard': return 2;
      case 'Investment': return 3;
      case 'Savings': return 4;
      case 'Loan': return 5;
      case 'Other': return 6;
      default: return 0; // Default to Cash
    }
  }

  // Form validation
  const [touched, setTouched] = useState({
    name: false,
    initialBalance: false,
    currency: false,
  });

  const [errors, setErrors] = useState({
    name: '',
    initialBalance: '',
    currency: '',
  });

  // Account type options (using numeric values to match backend enum)
  const accountTypeOptions = [
    { label: t('accounts.cash'), value: 0 }, // Cash
    { label: t('accounts.bank'), value: 1 }, // Bank
    { label: t('accounts.creditCard'), value: 2 }, // CreditCard
    { label: t('accounts.investment'), value: 3 }, // Investment
    { label: t('accounts.savings'), value: 4 }, // Savings
    { label: t('accounts.loan'), value: 5 }, // Loan
    { label: t('accounts.other'), value: 6 }, // Other
  ];

  // Currency options
  const currencyOptions = [
    { label: 'NPR (₨)', value: 'NPR' },
    { label: 'USD ($)', value: 'USD' },
    { label: 'EUR (€)', value: 'EUR' },
    { label: 'INR (₹)', value: 'INR' },
    { label: 'GBP (£)', value: 'GBP' },
  ];

  // Family options
  const familyOptions = [
    { label: t('accounts.personal'), value: 'null' },
    ...families.map(family => ({
      label: family.name,
      value: family.id,
    })),
  ];

  // Boolean options
  const booleanOptions = [
    { label: t('common.yes'), value: 'true' },
    { label: t('common.no'), value: 'false' },
  ];

  // Validate form
  const validateForm = () => {
    const newErrors = {
      name: '',
      initialBalance: '',
      currency: '',
    };

    // Validate name
    if (!name.trim()) {
      newErrors.name = t('validation.required');
    } else if (name.length < 3) {
      newErrors.name = t('validation.minLength', { length: 3 });
    } else if (name.length > 50) {
      newErrors.name = t('validation.maxLength', { length: 50 });
    }

    // Validate initial balance
    if (!initialBalance.trim()) {
      newErrors.initialBalance = t('validation.required');
    } else if (isNaN(Number(initialBalance))) {
      newErrors.initialBalance = t('validation.number');
    } else {
      const balanceValue = Number(initialBalance);
      // Check if the balance is within a reasonable range
      if (balanceValue > 1000000000) { // 1 billion
        newErrors.initialBalance = t('validation.maxValue', { value: '1,000,000,000' });
      } else if (balanceValue < -1000000000) {
        newErrors.initialBalance = t('validation.minValue', { value: '-1,000,000,000' });
      }
    }

    // Validate currency
    if (!currency.trim()) {
      newErrors.currency = t('validation.required');
    }

    setErrors(newErrors);

    return !Object.values(newErrors).some(error => error);
  };

  // Handle form submission
  const handleSubmit = () => {
    // Mark all fields as touched
    setTouched({
      name: true,
      initialBalance: true,
      currency: true,
    });

    if (validateForm()) {
      const formData = {
        ...(initialValues.id && { id: initialValues.id }),
        name,
        accountType,
        initialBalance: Number(initialBalance),
        currency,
        isActive,
        excludeFromStats,
        familyId,
      };

      onSubmit(formData);
    } else {
      Alert.alert(
        t('common.error'),
        t('validation.fixErrors')
      );
    }
  };

  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    resetForm: () => {
      // Reset form state to initial values
      setName(initialValues.name || '');

      // Handle account type reset
      const defaultType = typeof initialValues.accountType === 'string'
        ? getAccountTypeValue(initialValues.accountType || 'Cash')
        : (initialValues.accountType ?? 0);
      setAccountType(defaultType);

      setInitialBalance((initialValues.initialBalance || 0).toString());
      setCurrency(initialValues.currency || 'NPR');
      setIsActive(initialValues.isActive ?? true);
      setExcludeFromStats(initialValues.excludeFromStats ?? false);
      setFamilyId(initialValues.familyId ?? null);

      // Reset touched state
      setTouched({
        name: false,
        initialBalance: false,
        currency: false,
      });

      // Reset errors
      setErrors({
        name: '',
        initialBalance: '',
        currency: '',
      });
    }
  }));

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
    >
      <FormInput
        label={t('accounts.accountName')}
        value={name}
        onChangeText={setName}
        onBlur={() => setTouched({ ...touched, name: true })}
        error={errors.name}
        touched={touched.name}
        placeholder={t('accounts.accountNamePlaceholder')}
      />

      <Dropdown
        label={t('accounts.accountType')}
        options={accountTypeOptions}
        selectedValue={accountType}
        onValueChange={(value) => typeof value === 'number' ? setAccountType(value) : null}
        valueType="number"
      />

      <FormInput
        label={t('accounts.initialBalance')}
        value={initialBalance}
        onChangeText={setInitialBalance}
        onBlur={() => setTouched({ ...touched, initialBalance: true })}
        error={errors.initialBalance}
        touched={touched.initialBalance}
        keyboardType="numeric"
        placeholder="0.00"
      />

      <Dropdown
        label={t('accounts.currency')}
        options={currencyOptions}
        selectedValue={currency}
        onValueChange={(value) => typeof value === 'string' ? setCurrency(value) : null}
        valueType="string"
      />

      {mode === 'edit' && (
        <Dropdown
          label={t('accounts.isActive')}
          options={booleanOptions}
          selectedValue={isActive ? 'true' : 'false'}
          onValueChange={(value) => typeof value === 'boolean' ? setIsActive(value) : null}
          valueType="boolean"
        />
      )}

      <Dropdown
        label={t('accounts.excludeFromStats')}
        options={booleanOptions}
        selectedValue={excludeFromStats ? 'true' : 'false'}
        onValueChange={(value) => typeof value === 'boolean' ? setExcludeFromStats(value) : null}
        valueType="boolean"
      />

      {isFamilyAdmin && families.length > 0 && (
        <Dropdown
          label={t('accounts.accountOwner')}
          options={familyOptions}
          selectedValue={familyId !== null ? familyId : 'null'}
          onValueChange={(value) => setFamilyId(value === null ? null : Number(value))}
          valueType="null"
        />
      )}

      <View style={styles.buttonContainer}>
        <Button
          title={mode === 'create' ? t('accounts.createAccount') : t('accounts.updateAccount')}
          onPress={handleSubmit}
          isLoading={isLoading}
          fullWidth
        />
      </View>
    </ScrollView>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 40,
  },
});

export default AccountForm;
