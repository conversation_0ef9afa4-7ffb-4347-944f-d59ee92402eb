import React, { ReactNode } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useFeatureFlag } from '../../contexts/FeatureFlagContext';
import { useTheme } from '../../contexts/ThemeContext';

interface FeatureAwareProps {
  featureName: string;
  children: ReactNode;
  fallback?: ReactNode;
  showMessage?: boolean;
}

/**
 * A component that conditionally renders its children based on whether a feature is enabled.
 * If the feature is not enabled, it renders the fallback or a default message.
 */
const FeatureAware: React.FC<FeatureAwareProps> = ({
  featureName,
  children,
  fallback,
  showMessage = true,
}) => {
  const { t } = useTranslation();
  const { isFeatureEnabled } = useFeatureFlag();
  const { isDarkMode } = useTheme();
  
  const isEnabled = isFeatureEnabled(featureName);
  
  if (isEnabled) {
    return <>{children}</>;
  }
  
  if (fallback) {
    return <>{fallback}</>;
  }
  
  if (showMessage) {
    return (
      <View
        style={[
          styles.container,
          { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
        ]}
      >
        <Text
          style={[
            styles.message,
            { color: isDarkMode ? '#C9D1D9' : '#24292E' },
          ]}
        >
          {t('features.notAvailable', { feature: featureName })}
        </Text>
      </View>
    );
  }
  
  return null;
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 16,
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default FeatureAware;
