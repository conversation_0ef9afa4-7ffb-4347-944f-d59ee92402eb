import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

interface SegmentOption {
  label: string;
  value: string;
}

interface SegmentedControlProps {
  values: SegmentOption[];
  selectedValue: string;
  onChange: (value: string) => void;
  isDarkMode: boolean;
}

const SegmentedControl: React.FC<SegmentedControlProps> = ({
  values,
  selectedValue,
  onChange,
  isDarkMode,
}) => {
  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#30363D' : '#E1E4E8' },
      ]}
    >
      {values.map((option) => (
        <TouchableOpacity
          key={option.value}
          style={[
            styles.option,
            selectedValue === option.value && [
              styles.selectedOption,
              { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
            ],
          ]}
          onPress={() => onChange(option.value)}
        >
          <Text
            style={[
              styles.optionText,
              {
                color:
                  selectedValue === option.value
                    ? isDarkMode
                      ? '#FFFFFF'
                      : '#000000'
                    : isDarkMode
                    ? '#8B949E'
                    : '#6E7781',
                fontWeight: selectedValue === option.value ? 'bold' : 'normal',
              },
            ]}
          >
            {option.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    borderRadius: 8,
    padding: 2,
    marginBottom: 8,
  },
  option: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  selectedOption: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  optionText: {
    fontSize: 14,
  },
});

export default SegmentedControl;
