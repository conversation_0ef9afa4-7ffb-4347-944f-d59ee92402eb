import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

interface Tab {
  key: string;
  title: string;
}

interface TabViewProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabKey: string) => void;
  isDarkMode: boolean;
}

const TabView: React.FC<TabViewProps> = ({ tabs, activeTab, onTabChange, isDarkMode }) => {
  return (
    <View style={styles.container}>
      {tabs.map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tab,
            activeTab === tab.key && [
              styles.activeTab,
              { borderBottomColor: isDarkMode ? '#58A6FF' : '#0366D6' },
            ],
          ]}
          onPress={() => onTabChange(tab.key)}
        >
          <Text
            style={[
              styles.tabText,
              {
                color:
                  activeTab === tab.key
                    ? isDarkMode
                      ? '#58A6FF'
                      : '#0366D6'
                    : isDarkMode
                    ? '#8B949E'
                    : '#6E7781',
              },
            ]}
          >
            {tab.title}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontWeight: 'bold',
  },
});

export default TabView;
