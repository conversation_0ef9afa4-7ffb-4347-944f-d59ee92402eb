import React from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  View,
  Text,
  Animated,
  TouchableWithoutFeedback,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';

interface FloatingActionButtonProps {
  onPress: () => void;
  icon?: string;
  color?: string;
  position?: 'bottomRight' | 'bottomLeft' | 'topRight' | 'topLeft';
  size?: 'small' | 'medium' | 'large';
  label?: string;
  actions?: {
    icon: string;
    label: string;
    onPress: () => void;
    color?: string;
  }[];
}

const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  onPress,
  icon = 'add',
  color,
  position = 'bottomRight',
  size = 'medium',
  label,
  actions,
}) => {
  const { isDarkMode } = useTheme();
  const [isOpen, setIsOpen] = React.useState(false);
  const animation = React.useRef(new Animated.Value(0)).current;
  
  // Get button size based on size prop
  const getButtonSize = () => {
    switch (size) {
      case 'small':
        return 48;
      case 'large':
        return 64;
      case 'medium':
      default:
        return 56;
    }
  };
  
  // Get icon size based on button size
  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 20;
      case 'large':
        return 28;
      case 'medium':
      default:
        return 24;
    }
  };
  
  // Get position styles based on position prop
  const getPositionStyles = () => {
    switch (position) {
      case 'bottomLeft':
        return { bottom: 16, left: 16 };
      case 'topRight':
        return { top: 16, right: 16 };
      case 'topLeft':
        return { top: 16, left: 16 };
      case 'bottomRight':
      default:
        return { bottom: 16, right: 16 };
    }
  };
  
  // Toggle actions menu
  const toggleMenu = () => {
    const toValue = isOpen ? 0 : 1;
    
    Animated.spring(animation, {
      toValue,
      friction: 5,
      useNativeDriver: true,
    }).start();
    
    setIsOpen(!isOpen);
  };
  
  // Handle button press
  const handlePress = () => {
    if (actions && actions.length > 0) {
      toggleMenu();
    } else {
      onPress();
    }
  };
  
  // Handle action press
  const handleActionPress = (actionOnPress: () => void) => {
    setIsOpen(false);
    Animated.spring(animation, {
      toValue: 0,
      friction: 5,
      useNativeDriver: true,
    }).start();
    
    actionOnPress();
  };
  
  // Render actions
  const renderActions = () => {
    if (!actions || actions.length === 0 || !isOpen) return null;
    
    return (
      <View style={styles.actionsContainer}>
        {actions.map((action, index) => {
          const actionOpacity = animation.interpolate({
            inputRange: [0, 1],
            outputRange: [0, 1],
          });
          
          const actionTranslate = animation.interpolate({
            inputRange: [0, 1],
            outputRange: [20, 0],
          });
          
          const actionStyle = {
            opacity: actionOpacity,
            transform: [{ translateY: actionTranslate }],
          };
          
          return (
            <Animated.View
              key={index}
              style={[styles.actionContainer, actionStyle]}
            >
              {action.label && (
                <View
                  style={[
                    styles.actionLabel,
                    { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
                  ]}
                >
                  <Text
                    style={[
                      styles.actionLabelText,
                      { color: isDarkMode ? '#FFFFFF' : '#000000' },
                    ]}
                  >
                    {action.label}
                  </Text>
                </View>
              )}
              <TouchableOpacity
                style={[
                  styles.actionButton,
                  {
                    backgroundColor: action.color || (isDarkMode ? '#1F6FEB' : '#0366D6'),
                  },
                ]}
                onPress={() => handleActionPress(action.onPress)}
              >
                <Ionicons
                  name={action.icon as any}
                  size={getIconSize() - 4}
                  color="#FFFFFF"
                />
              </TouchableOpacity>
            </Animated.View>
          );
        })}
      </View>
    );
  };
  
  // Render overlay
  const renderOverlay = () => {
    if (!isOpen) return null;
    
    return (
      <TouchableWithoutFeedback onPress={toggleMenu}>
        <View style={styles.overlay} />
      </TouchableWithoutFeedback>
    );
  };
  
  const buttonSize = getButtonSize();
  const iconSize = getIconSize();
  const positionStyles = getPositionStyles();
  
  return (
    <>
      {renderOverlay()}
      <View
        style={[
          styles.container,
          positionStyles,
        ]}
      >
        {renderActions()}
        <TouchableOpacity
          style={[
            styles.button,
            {
              width: buttonSize,
              height: buttonSize,
              borderRadius: buttonSize / 2,
              backgroundColor: color || (isDarkMode ? '#1F6FEB' : '#0366D6'),
            },
          ]}
          onPress={handlePress}
        >
          <Ionicons
            name={(isOpen && actions && actions.length > 0 ? 'close' : icon) as any}
            size={iconSize}
            color="#FFFFFF"
          />
        </TouchableOpacity>
        {label && (
          <View
            style={[
              styles.label,
              { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
            ]}
          >
            <Text
              style={[
                styles.labelText,
                { color: isDarkMode ? '#FFFFFF' : '#000000' },
              ]}
            >
              {label}
            </Text>
          </View>
        )}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    alignItems: 'center',
    zIndex: 999,
  },
  button: {
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  label: {
    position: 'absolute',
    top: -40,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  labelText: {
    fontSize: 14,
    fontWeight: '500',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 998,
  },
  actionsContainer: {
    position: 'absolute',
    bottom: 70,
    alignItems: 'center',
  },
  actionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  actionButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  actionLabel: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    marginRight: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  actionLabelText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default FloatingActionButton;
