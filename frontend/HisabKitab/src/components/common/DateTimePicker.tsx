import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';

interface DateTimePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  mode?: 'date' | 'time' | 'datetime';
  label?: string;
  placeholder?: string;
  minimumDate?: Date;
  maximumDate?: Date;
  isDarkMode?: boolean;
}

const CustomDateTimePicker: React.FC<DateTimePickerProps> = ({
  value,
  onChange,
  mode = 'date',
  label,
  placeholder,
  minimumDate,
  maximumDate,
  isDarkMode: propIsDarkMode,
}) => {
  const { t } = useTranslation();
  const { isDarkMode: contextIsDarkMode } = useTheme();
  const isDarkMode = propIsDarkMode !== undefined ? propIsDarkMode : contextIsDarkMode;
  
  const [show, setShow] = useState(false);
  const [tempDate, setTempDate] = useState(value);
  const [currentMode, setCurrentMode] = useState<'date' | 'time'>(mode === 'datetime' ? 'date' : mode);
  
  // Format date for display
  const formatDate = (date: Date) => {
    if (mode === 'date') {
      return format(date, 'PPP'); // e.g., "Apr 29, 2023"
    } else if (mode === 'time') {
      return format(date, 'p'); // e.g., "12:00 PM"
    } else {
      return format(date, 'PPp'); // e.g., "Apr 29, 2023, 12:00 PM"
    }
  };
  
  // Handle date change
  const handleChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShow(false);
    }
    
    if (selectedDate) {
      setTempDate(selectedDate);
      
      if (mode === 'datetime' && currentMode === 'date' && Platform.OS === 'ios') {
        setCurrentMode('time');
      } else {
        if (Platform.OS === 'ios' && mode === 'datetime') {
          // For iOS, we need to handle the datetime mode manually
          // by showing the date picker first, then the time picker
          setShow(false);
        }
        
        onChange(selectedDate);
      }
    } else {
      setShow(false);
    }
  };
  
  // Show date picker
  const showDatePicker = () => {
    if (mode === 'datetime') {
      setCurrentMode('date');
    }
    setShow(true);
  };
  
  // Handle confirm on iOS
  const handleIOSConfirm = () => {
    onChange(tempDate);
    setShow(false);
  };
  
  // Handle cancel on iOS
  const handleIOSCancel = () => {
    setShow(false);
  };
  
  // Render iOS modal
  const renderIOSModal = () => {
    return (
      <Modal
        transparent
        visible={show}
        animationType="slide"
      >
        <View style={styles.modalContainer}>
          <View
            style={[
              styles.modalContent,
              { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
            ]}
          >
            <View style={styles.modalHeader}>
              <TouchableOpacity onPress={handleIOSCancel}>
                <Text
                  style={[
                    styles.modalButton,
                    { color: isDarkMode ? '#58A6FF' : '#0366D6' },
                  ]}
                >
                  {t('common.cancel')}
                </Text>
              </TouchableOpacity>
              <Text
                style={[
                  styles.modalTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#000000' },
                ]}
              >
                {mode === 'date'
                  ? t('common.selectDate')
                  : mode === 'time'
                  ? t('common.selectTime')
                  : currentMode === 'date'
                  ? t('common.selectDate')
                  : t('common.selectTime')}
              </Text>
              <TouchableOpacity onPress={handleIOSConfirm}>
                <Text
                  style={[
                    styles.modalButton,
                    { color: isDarkMode ? '#58A6FF' : '#0366D6' },
                  ]}
                >
                  {t('common.confirm')}
                </Text>
              </TouchableOpacity>
            </View>
            <DateTimePicker
              value={tempDate}
              mode={currentMode}
              display="spinner"
              onChange={handleChange}
              minimumDate={minimumDate}
              maximumDate={maximumDate}
              textColor={isDarkMode ? '#FFFFFF' : '#000000'}
            />
          </View>
        </View>
      </Modal>
    );
  };
  
  return (
    <View style={styles.container}>
      {label && (
        <Text
          style={[
            styles.label,
            { color: isDarkMode ? '#C9D1D9' : '#24292E' },
          ]}
        >
          {label}
        </Text>
      )}
      <TouchableOpacity
        style={[
          styles.input,
          { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA', borderColor: isDarkMode ? '#30363D' : '#D0D7DE' },
        ]}
        onPress={showDatePicker}
      >
        <Text
          style={[
            styles.inputText,
            {
              color: value
                ? isDarkMode ? '#FFFFFF' : '#000000'
                : isDarkMode ? '#8B949E' : '#6E7781',
            },
          ]}
        >
          {value ? formatDate(value) : placeholder || t('common.selectDateTime')}
        </Text>
        <Ionicons
          name={
            mode === 'date'
              ? 'calendar-outline'
              : mode === 'time'
              ? 'time-outline'
              : 'calendar-outline'
          }
          size={20}
          color={isDarkMode ? '#8B949E' : '#6E7781'}
        />
      </TouchableOpacity>
      
      {Platform.OS === 'ios' ? (
        renderIOSModal()
      ) : (
        show && (
          <DateTimePicker
            value={tempDate}
            mode={currentMode}
            display="default"
            onChange={handleChange}
            minimumDate={minimumDate}
            maximumDate={maximumDate}
          />
        )
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
  },
  input: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  inputText: {
    fontSize: 16,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalButton: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default CustomDateTimePicker;
