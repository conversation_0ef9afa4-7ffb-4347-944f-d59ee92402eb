import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useFeatureFlag } from '../../contexts/FeatureFlagContext';
import { useAuth } from '../../contexts/AuthContext';

interface FeatureToggleProps {
  featureName: string;
  title: string;
  description?: string;
  icon?: string;
  disabled?: boolean;
  onToggle?: (isEnabled: boolean) => void;
  isDarkMode: boolean;
  showAdminControls?: boolean;
}

const FeatureToggle: React.FC<FeatureToggleProps> = ({
  featureName,
  title,
  description,
  icon = 'toggle-outline',
  disabled = false,
  onToggle,
  isDarkMode,
  showAdminControls = false,
}) => {
  const { t } = useTranslation();
  const { isFeatureEnabled, updateFeature, features, isLoading } = useFeatureFlag();
  const { hasRole } = useAuth();
  
  const [isUpdating, setIsUpdating] = useState(false);
  
  // Check if user is admin
  const isPlatformAdmin = hasRole('PlatformAdmin');
  
  // Get feature enabled state
  const isEnabled = isFeatureEnabled(featureName);
  
  // Get feature details
  const getFeatureDetails = () => {
    return features.find(f => f.name === featureName);
  };
  
  // Handle toggle
  const handleToggle = async (value: boolean) => {
    // If custom toggle handler is provided, use it
    if (onToggle) {
      onToggle(value);
      return;
    }
    
    // Otherwise, update feature flag if user is admin
    if (isPlatformAdmin && showAdminControls) {
      const feature = getFeatureDetails();
      
      if (feature) {
        setIsUpdating(true);
        
        try {
          const success = await updateFeature(feature.id, value);
          
          if (success) {
            Alert.alert(
              t('common.success'),
              value
                ? t('features.enabledSuccess', { name: feature.name })
                : t('features.disabledSuccess', { name: feature.name })
            );
          }
        } catch (error) {
          Alert.alert(t('common.error'), t('features.updateError'));
        } finally {
          setIsUpdating(false);
        }
      } else {
        Alert.alert(t('common.error'), t('features.featureNotFound'));
      }
    } else if (showAdminControls) {
      // If not admin but trying to toggle admin controls
      Alert.alert(
        t('features.adminRequired'),
        t('features.adminRequiredMessage')
      );
    }
  };
  
  // Render admin badge
  const renderAdminBadge = () => {
    if (!showAdminControls || !isPlatformAdmin) return null;
    
    return (
      <View
        style={[
          styles.adminBadge,
          { backgroundColor: isDarkMode ? '#30363D' : '#F6F8FA' },
        ]}
      >
        <Ionicons
          name="shield-checkmark-outline"
          size={12}
          color={isDarkMode ? '#58A6FF' : '#0366D6'}
        />
        <Text
          style={[
            styles.adminBadgeText,
            { color: isDarkMode ? '#58A6FF' : '#0366D6' },
          ]}
        >
          {t('features.admin')}
        </Text>
      </View>
    );
  };
  
  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
      ]}
    >
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Ionicons
            name={icon as any}
            size={24}
            color={
              isEnabled
                ? isDarkMode ? '#58A6FF' : '#0366D6'
                : isDarkMode ? '#8B949E' : '#6E7781'
            }
          />
        </View>
        <View style={styles.textContainer}>
          <View style={styles.titleRow}>
            <Text
              style={[
                styles.title,
                { color: isDarkMode ? '#FFFFFF' : '#000000' },
              ]}
            >
              {title}
            </Text>
            {renderAdminBadge()}
          </View>
          {description && (
            <Text
              style={[
                styles.description,
                { color: isDarkMode ? '#8B949E' : '#6E7781' },
              ]}
            >
              {description}
            </Text>
          )}
        </View>
        <View style={styles.toggleContainer}>
          {isUpdating || isLoading ? (
            <ActivityIndicator
              size="small"
              color={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
          ) : (
            <Switch
              value={isEnabled}
              onValueChange={handleToggle}
              disabled={disabled || (!isPlatformAdmin && showAdminControls)}
              trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
              thumbColor={isEnabled ? '#58A6FF' : '#f4f3f4'}
            />
          )}
        </View>
      </View>
      
      {/* Admin controls */}
      {showAdminControls && isPlatformAdmin && isEnabled && (
        <View
          style={[
            styles.adminControls,
            { borderTopColor: isDarkMode ? '#30363D' : '#D0D7DE' },
          ]}
        >
          <TouchableOpacity
            style={styles.adminControl}
            onPress={() => {
              // Navigate to feature details or show modal
              // This would be implemented in the parent component
              if (onToggle) {
                onToggle(isEnabled);
              }
            }}
          >
            <Ionicons
              name="settings-outline"
              size={16}
              color={isDarkMode ? '#8B949E' : '#6E7781'}
            />
            <Text
              style={[
                styles.adminControlText,
                { color: isDarkMode ? '#8B949E' : '#6E7781' },
              ]}
            >
              {t('features.configure')}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.adminControl}
            onPress={() => {
              // Navigate to feature analytics
              // This would be implemented in the parent component
              if (onToggle) {
                onToggle(isEnabled);
              }
            }}
          >
            <Ionicons
              name="analytics-outline"
              size={16}
              color={isDarkMode ? '#8B949E' : '#6E7781'}
            />
            <Text
              style={[
                styles.adminControlText,
                { color: isDarkMode ? '#8B949E' : '#6E7781' },
              ]}
            >
              {t('features.analytics')}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    marginBottom: 16,
    overflow: 'hidden',
  },
  content: {
    flexDirection: 'row',
    padding: 16,
  },
  iconContainer: {
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
  },
  description: {
    fontSize: 14,
    marginTop: 4,
  },
  toggleContainer: {
    justifyContent: 'center',
    marginLeft: 8,
  },
  adminBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  adminBadgeText: {
    fontSize: 10,
    fontWeight: '500',
    marginLeft: 2,
  },
  adminControls: {
    flexDirection: 'row',
    borderTopWidth: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  adminControl: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  adminControlText: {
    fontSize: 12,
    marginLeft: 4,
  },
});

export default FeatureToggle;
