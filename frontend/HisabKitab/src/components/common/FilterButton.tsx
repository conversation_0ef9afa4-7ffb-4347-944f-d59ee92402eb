import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';

interface FilterButtonProps {
  label: string;
  isActive: boolean;
  onPress: () => void;
  isDarkMode: boolean;
}

const FilterButton: React.FC<FilterButtonProps> = ({ label, isActive, onPress, isDarkMode }) => {
  return (
    <TouchableOpacity
      style={[
        styles.button,
        {
          backgroundColor: isActive
            ? isDarkMode
              ? '#58A6FF'
              : '#0366D6'
            : isDarkMode
            ? '#30363D'
            : '#E1E4E8',
        },
      ]}
      onPress={onPress}
    >
      <Text
        style={[
          styles.label,
          {
            color: isActive ? '#FFFFFF' : isDarkMode ? '#FFFFFF' : '#000000',
          },
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  label: {
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default FilterButton;
