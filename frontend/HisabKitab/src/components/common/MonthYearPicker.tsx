import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';

interface MonthYearPickerProps {
  value: Date;
  onChange: (date: Date) => void;
  minYear?: number;
  maxYear?: number;
  isDarkMode: boolean;
}

const MonthYearPicker: React.FC<MonthYearPickerProps> = ({
  value,
  onChange,
  minYear = 2020,
  maxYear = new Date().getFullYear() + 1,
  isDarkMode,
}) => {
  const { t } = useTranslation();
  
  const [modalVisible, setModalVisible] = useState(false);
  const [tempValue, setTempValue] = useState(value);
  const [view, setView] = useState<'month' | 'year'>('month');
  
  // Get month name
  const getMonthName = (date: Date) => {
    return date.toLocaleString('default', { month: 'long' });
  };
  
  // Handle previous month
  const handlePrevMonth = () => {
    const newDate = new Date(value);
    newDate.setMonth(newDate.getMonth() - 1);
    onChange(newDate);
  };
  
  // Handle next month
  const handleNextMonth = () => {
    const newDate = new Date(value);
    newDate.setMonth(newDate.getMonth() + 1);
    onChange(newDate);
  };
  
  // Handle open modal
  const handleOpenModal = () => {
    setTempValue(new Date(value));
    setModalVisible(true);
  };
  
  // Handle close modal
  const handleCloseModal = () => {
    setModalVisible(false);
  };
  
  // Handle apply
  const handleApply = () => {
    onChange(tempValue);
    setModalVisible(false);
  };
  
  // Handle select month
  const handleSelectMonth = (month: number) => {
    const newDate = new Date(tempValue);
    newDate.setMonth(month);
    setTempValue(newDate);
    setView('year');
  };
  
  // Handle select year
  const handleSelectYear = (year: number) => {
    const newDate = new Date(tempValue);
    newDate.setFullYear(year);
    setTempValue(newDate);
    setView('month');
  };
  
  // Generate months
  const months = Array.from({ length: 12 }, (_, i) => {
    const date = new Date(2000, i, 1);
    return {
      value: i,
      label: date.toLocaleString('default', { month: 'long' }),
    };
  });
  
  // Generate years
  const years = Array.from(
    { length: maxYear - minYear + 1 },
    (_, i) => minYear + i
  );
  
  // Render month item
  const renderMonthItem = ({ item }: { item: { value: number; label: string } }) => (
    <TouchableOpacity
      style={[
        styles.pickerItem,
        tempValue.getMonth() === item.value && {
          backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
        },
      ]}
      onPress={() => handleSelectMonth(item.value)}
    >
      <Text
        style={[
          styles.pickerItemText,
          {
            color:
              tempValue.getMonth() === item.value
                ? '#FFFFFF'
                : isDarkMode
                ? '#C9D1D9'
                : '#24292E',
          },
        ]}
      >
        {item.label}
      </Text>
    </TouchableOpacity>
  );
  
  // Render year item
  const renderYearItem = ({ item }: { item: number }) => (
    <TouchableOpacity
      style={[
        styles.pickerItem,
        tempValue.getFullYear() === item && {
          backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
        },
      ]}
      onPress={() => handleSelectYear(item)}
    >
      <Text
        style={[
          styles.pickerItemText,
          {
            color:
              tempValue.getFullYear() === item
                ? '#FFFFFF'
                : isDarkMode
                ? '#C9D1D9'
                : '#24292E',
          },
        ]}
      >
        {item}
      </Text>
    </TouchableOpacity>
  );
  
  return (
    <View>
      {/* Month selector */}
      <View style={styles.selector}>
        <TouchableOpacity
          style={styles.arrowButton}
          onPress={handlePrevMonth}
        >
          <Ionicons
            name="chevron-back"
            size={24}
            color={isDarkMode ? '#C9D1D9' : '#24292E'}
          />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.dateButton}
          onPress={handleOpenModal}
        >
          <Text
            style={[
              styles.dateText,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {`${getMonthName(value)} ${value.getFullYear()}`}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.arrowButton}
          onPress={handleNextMonth}
        >
          <Ionicons
            name="chevron-forward"
            size={24}
            color={isDarkMode ? '#C9D1D9' : '#24292E'}
          />
        </TouchableOpacity>
      </View>
      
      {/* Month/Year picker modal */}
      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={handleCloseModal}
      >
        <View style={styles.modalContainer}>
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#E1E4E8',
              },
            ]}
          >
            <View style={styles.modalHeader}>
              <TouchableOpacity
                onPress={() => setView(view === 'month' ? 'year' : 'month')}
              >
                <Text
                  style={[
                    styles.modalTitle,
                    { color: isDarkMode ? '#FFFFFF' : '#000000' },
                  ]}
                >
                  {view === 'month'
                    ? t('common.selectMonth')
                    : t('common.selectYear')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={handleCloseModal}>
                <Ionicons
                  name="close"
                  size={24}
                  color={isDarkMode ? '#C9D1D9' : '#24292E'}
                />
              </TouchableOpacity>
            </View>
            
            <View style={styles.pickerContainer}>
              {view === 'month' ? (
                <FlatList
                  data={months}
                  keyExtractor={(item) => item.value.toString()}
                  renderItem={renderMonthItem}
                  numColumns={3}
                  contentContainerStyle={styles.pickerList}
                />
              ) : (
                <FlatList
                  data={years}
                  keyExtractor={(item) => item.toString()}
                  renderItem={renderYearItem}
                  numColumns={3}
                  contentContainerStyle={styles.pickerList}
                />
              )}
            </View>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[
                  styles.footerButton,
                  {
                    backgroundColor: isDarkMode ? '#30363D' : '#E1E4E8',
                  },
                ]}
                onPress={handleCloseModal}
              >
                <Text
                  style={[
                    styles.footerButtonText,
                    { color: isDarkMode ? '#C9D1D9' : '#24292E' },
                  ]}
                >
                  {t('common.cancel')}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.footerButton,
                  {
                    backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
                  },
                ]}
                onPress={handleApply}
              >
                <Text style={[styles.footerButtonText, { color: '#FFFFFF' }]}>
                  {t('common.apply')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  arrowButton: {
    padding: 8,
  },
  dateButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  dateText: {
    fontSize: 16,
    fontWeight: '500',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    borderRadius: 8,
    borderWidth: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  pickerContainer: {
    padding: 16,
  },
  pickerList: {
    paddingBottom: 8,
  },
  pickerItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    margin: 4,
    borderRadius: 4,
  },
  pickerItemText: {
    fontSize: 16,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#30363D',
  },
  footerButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
    marginLeft: 8,
  },
  footerButtonText: {
    fontSize: 16,
  },
});

export default MonthYearPicker;
