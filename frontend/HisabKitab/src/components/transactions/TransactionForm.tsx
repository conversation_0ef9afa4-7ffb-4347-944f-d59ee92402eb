import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, Text, Switch } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import FormInput from '../ui/FormInput';
import Button from '../ui/Button';
import Dropdown from '../ui/Dropdown';
import DatePicker from '../ui/DatePicker';
import {
  validateRequired,
  validatePositiveNumber,
} from '../../utils/validation';
import { formatCurrency } from '../../utils/formatters';
import { TransactionType } from '../../models/Transaction';
import { getCategoryTypeString, isTransferTransaction, getTransferCategoryId } from '../../utils/typeConversion';
import { formatDateForBackend, parseDateFromBackend } from '../../utils/dateUtils';
import TransactionItemList from './TransactionItemList';
import ReceiptImagePicker from './ReceiptImagePicker';

interface TransactionFormProps {
  initialValues?: {
    id?: number;
    amount: number;
    description: string;
    date: Date;
    type: TransactionType;
    accountId: number;
    toAccountId?: number;
    categoryId: number;
    priorityId?: number;
    status?: string;
    receiptImage?: string;
    tags?: string;
    location?: string;
    items?: any[];
  };
  onSubmit: (values: any) => void;
  isLoading?: boolean;
  mode?: 'create' | 'edit';
  accounts?: Array<{ id: number; name: string }>;
  categories?: Array<{ id: number; name: string; type: string }>;
  priorities?: Array<{ id: number; name: string }>;
}

const TransactionForm: React.FC<TransactionFormProps> = ({
  initialValues = {
    amount: 0,
    description: '',
    date: new Date(),
    type: TransactionType.Expense,
    accountId: 0,
    categoryId: 0,
    status: 'Completed',
    items: [],
  },
  onSubmit,
  isLoading = false,
  mode = 'create',
  accounts = [],
  categories = [],
  priorities = [],
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();

  // Form state
  const [amount, setAmount] = useState(initialValues.amount.toString());
  const [description, setDescription] = useState(initialValues.description);
  const [date, setDate] = useState(initialValues.date);
  const [type, setType] = useState(initialValues.type);
  const [accountId, setAccountId] = useState(initialValues.accountId);
  const [toAccountId, setToAccountId] = useState(initialValues.toAccountId);
  const [categoryId, setCategoryId] = useState(initialValues.categoryId);
  const [priorityId, setPriorityId] = useState(initialValues.priorityId);
  const [status, setStatus] = useState(initialValues.status || 'Completed');
  const [receiptImage, setReceiptImage] = useState(initialValues.receiptImage || '');
  const [tags, setTags] = useState(initialValues.tags || '');
  const [location, setLocation] = useState(initialValues.location || '');
  const [items, setItems] = useState(initialValues.items || []);
  const [showItems, setShowItems] = useState(initialValues.items && initialValues.items.length > 0 ? true : false);

  // Form validation
  const [touched, setTouched] = useState({
    amount: false,
    accountId: false,
    categoryId: false,
  });

  const [errors, setErrors] = useState({
    amount: '',
    accountId: '',
    categoryId: '',
  });

  // Validation functions
  const validateAmount = (value: string) => {
    return validateRequired(value, t) || validatePositiveNumber(value, t);
  };

  const validateAccountId = (value: string | number | boolean | null) => {
    if (value === null) return t('validation.required');

    const strValue = value.toString();
    if (type === TransactionType.Transfer && toAccountId === Number(strValue)) {
      return t('validation.differentAccountsRequired');
    }
    return validateRequired(strValue, t);
  };

  const validateCategoryId = (value: string | number | boolean | null) => {
    if (value === null) return t('validation.required');

    const strValue = value.toString();
    if (type !== TransactionType.Transfer) {
      return validateRequired(strValue, t);
    }
    return undefined;
  };

  const validateToAccountId = (value: string | number | boolean | null) => {
    if (value === null) return t('validation.required');

    const strValue = value.toString();
    if (type === TransactionType.Transfer) {
      if (!strValue) {
        return t('validation.toAccountRequired');
      } else if (Number(strValue) === accountId) {
        return t('validation.differentAccountsRequired');
      }
    }
    return undefined;
  };

  // Transaction type options
  const typeOptions = [
    { label: t('transactions.expense'), value: TransactionType.Expense },
    { label: t('transactions.income'), value: TransactionType.Income },
    { label: t('transactions.transfer'), value: TransactionType.Transfer },
  ];

  // Status options
  const statusOptions = [
    { label: t('transactions.completed'), value: 'Completed' },
    { label: t('transactions.pending'), value: 'Pending' },
  ];

  // Filter categories based on transaction type
  const filteredCategories = categories.filter(category => {
    if (type === TransactionType.Income) {
      return getCategoryTypeString(category.type) === 'Income';
    } else if (type === TransactionType.Expense) {
      return getCategoryTypeString(category.type) === 'Expense';
    }
    return true;
  });

  // Format options for dropdowns
  const accountOptions = accounts.map(account => ({
    label: account.name,
    value: account.id,
  }));

  const toAccountOptions = accounts
    .filter(account => account.id !== accountId)
    .map(account => ({
      label: account.name,
      value: account.id,
    }));

  const categoryOptions = filteredCategories.map(category => ({
    label: category.name,
    value: category.id,
  }));

  const priorityOptions = [
    { label: t('common.none'), value: null },
    ...priorities.map(priority => ({
      label: priority.name,
      value: priority.id,
    })),
  ];

  // Validate form
  const validateForm = () => {
    const newErrors = {
      amount: validateRequired(amount, t) || validatePositiveNumber(amount, t),
      accountId: validateRequired(accountId.toString(), t),
      categoryId: type !== TransactionType.Transfer ? validateRequired(categoryId.toString(), t) : '',
    };

    // For transfers, validate that toAccountId is provided and different from accountId
    if (type === TransactionType.Transfer) {
      if (!toAccountId) {
        newErrors.accountId = t('validation.toAccountRequired');
      } else if (toAccountId === accountId) {
        newErrors.accountId = t('validation.differentAccountsRequired');
      }
    }

    // Validate that items total matches transaction amount if items are present
    if (showItems && items.length > 0) {
      const itemsTotal = items.reduce((sum, item) => sum + Number(item.amount) * (Number(item.quantity) || 1), 0);
      const transactionAmount = Number(amount);

      if (Math.abs(itemsTotal - transactionAmount) > 0.01) { // Allow for small rounding errors
        newErrors.amount = t('transactions.itemsTotalMismatch');
      }
    }

    setErrors(newErrors);

    return !Object.values(newErrors).some(error => error);
  };

  // Handle form submission
  const handleSubmit = () => {
    // Mark all fields as touched
    setTouched({
      amount: true,
      accountId: true,
      categoryId: true,
    });

    if (validateForm()) {
      // Get the appropriate category ID for transfers
      const isTransfer = isTransferTransaction(type);
      const transferCategoryId = isTransfer ? getTransferCategoryId(categories) : null;

      // If no Transfer category exists and this is a transfer, show an error
      if (isTransfer && !transferCategoryId) {
        Alert.alert(
          t('common.error'),
          t('transactions.noTransferCategory'),
          [{ text: t('common.ok') }]
        );
        return;
      }

      const formData = {
        ...(initialValues.id && { id: initialValues.id }),
        amount: Number(amount),
        description,
        date: formatDateForBackend(date),
        type,
        accountId,
        toAccountId: isTransfer ? toAccountId : undefined,
        // For transfers, use the transfer category if available, otherwise use 0
        categoryId: isTransfer ? transferCategoryId : categoryId,
        priorityId: priorityId || undefined,
        status,
        receiptImage: receiptImage || undefined,
        tags: tags || undefined,
        location: location || undefined,
        // For transfers, we don't include items
        items: !isTransfer ? items.map(item => ({
          ...(item.id && { id: item.id }),
          name: item.name,
          amount: Number(item.amount),
          quantity: Number(item.quantity || 1),
          categoryId: item.categoryId || undefined,
          notes: item.notes || undefined,
        })) : [],
      };

      onSubmit(formData);
    } else {
      Alert.alert(
        t('common.error'),
        t('validation.fixErrors')
      );
    }
  };

  // Handle transaction type change
  const handleTypeChange = (value: string | number | boolean | null) => {
    if (typeof value === 'number') {
      const newType = value as TransactionType;
      const wasTransfer = isTransferTransaction(type);
      const willBeTransfer = isTransferTransaction(newType);

      setType(newType);

      // Reset category if switching between income/expense
      if ((newType === TransactionType.Income && type === TransactionType.Expense) ||
          (newType === TransactionType.Expense && type === TransactionType.Income)) {
        setCategoryId(0);

        // Keep items but show a warning if there are items
        if (items.length > 0) {
          Alert.alert(
            t('common.notice'),
            t('transactions.typeChangeItemsWarning'),
            [
              {
                text: t('common.keepItems'),
                style: 'default',
              },
              {
                text: t('common.clearItems'),
                style: 'destructive',
                onPress: () => setItems([]),
              },
            ]
          );
        }
      }

      // If switching to/from transfer, clear items
      if (willBeTransfer || wasTransfer) {
        setItems([]);
        setShowItems(false);

        // If switching to transfer, set a transfer category if available
        if (willBeTransfer) {
          const transferCategoryId = getTransferCategoryId(categories);
          if (transferCategoryId) {
            setCategoryId(transferCategoryId);
          }
        }
      }
    }
  };

  // Add transaction item
  const handleAddItem = (item: any) => {
    setItems([...items, item]);
  };

  // Update transaction item
  const handleUpdateItem = (updatedItem: any) => {
    setItems(items.map(item =>
      item.id === updatedItem.id ? updatedItem : item
    ));
  };

  // Remove transaction item
  const handleRemoveItem = (itemId: number) => {
    setItems(items.filter(item => item.id !== itemId));
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
    >
      <Dropdown
        label={t('transactions.type')}
        options={typeOptions}
        selectedValue={type}
        onValueChange={handleTypeChange}
      />

      <FormInput
        label={t('transactions.amount')}
        value={amount}
        onChangeText={setAmount}
        error={errors.amount}
        touched={touched.amount}
        keyboardType="numeric"
        placeholder="0.00"
        required={true}
        validateOnBlur={true}
        onValidate={validateAmount}
      />

      <DatePicker
        label={t('transactions.date')}
        selectedDate={date}
        onDateChange={(newDate) => newDate && setDate(newDate)}
      />

      <Dropdown
        label={t('transactions.account')}
        options={accountOptions}
        selectedValue={accountId}
        onValueChange={(value) => typeof value === 'number' ? setAccountId(value) : null}
        error={errors.accountId}
        touched={touched.accountId}
        required={true}
        validateOnBlur={true}
        onValidate={validateAccountId}
      />

      {type === TransactionType.Transfer && (
        <>
          <View style={styles.transferContainer}>
            <View style={styles.transferIconContainer}>
              <View style={[styles.transferIcon, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
                <Ionicons name="swap-vertical" size={24} color={isDarkMode ? '#58A6FF' : '#0366D6'} />
              </View>
              <View style={styles.transferLine} />
            </View>
            <View style={styles.transferAccountsContainer}>
              <Text style={[styles.transferLabel, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                {t('transactions.from')}
              </Text>
              <Text style={[styles.transferAccountName, { color: isDarkMode ? '#58A6FF' : '#0366D6' }]}>
                {accounts.find(a => a.id === accountId)?.name || t('transactions.selectAccount')}
              </Text>
              <Text style={[styles.transferLabel, { color: isDarkMode ? '#FFFFFF' : '#24292E', marginTop: 16 }]}>
                {t('transactions.to')}
              </Text>
              <Dropdown
                label={t('transactions.toAccount')}
                options={toAccountOptions}
                selectedValue={toAccountId || null}
                onValueChange={(value) => typeof value === 'number' ? setToAccountId(value) : null}
                error={errors.accountId}
                touched={touched.accountId}
                required={true}
                validateOnBlur={true}
                onValidate={validateToAccountId}
              />
            </View>
          </View>
          <Text style={[styles.transferNote, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {t('transactions.transferNote')}
          </Text>
        </>
      )}

      {type !== TransactionType.Transfer && (
        <Dropdown
          label={t('transactions.category')}
          options={categoryOptions}
          selectedValue={categoryId}
          onValueChange={(value) => typeof value === 'number' ? setCategoryId(value) : null}
          error={errors.categoryId}
          touched={touched.categoryId}
          required={true}
          validateOnBlur={true}
          onValidate={validateCategoryId}
        />
      )}

      <FormInput
        label={t('transactions.description')}
        value={description}
        onChangeText={setDescription}
        placeholder={t('transactions.descriptionPlaceholder')}
        multiline
        numberOfLines={3}
        style={styles.textArea}
      />

      <Dropdown
        label={t('transactions.priority')}
        options={priorityOptions}
        selectedValue={priorityId || null}
        onValueChange={(value) => setPriorityId(typeof value === 'number' ? value : undefined)}
      />

      <FormInput
        label={t('transactions.tags')}
        value={tags}
        onChangeText={setTags}
        placeholder={t('transactions.tagsPlaceholder')}
      />

      <FormInput
        label={t('transactions.location')}
        value={location}
        onChangeText={setLocation}
        placeholder={t('transactions.locationPlaceholder')}
      />

      <Dropdown
        label={t('transactions.status')}
        options={statusOptions}
        selectedValue={status}
        onValueChange={(value) => typeof value === 'string' ? setStatus(value) : null}
      />

      {/* Receipt Image Picker */}
      <ReceiptImagePicker
        imageUri={receiptImage}
        onImageSelected={(uri) => setReceiptImage(uri)}
        onImageRemoved={() => setReceiptImage('')}
      />

      {(type === TransactionType.Expense || type === TransactionType.Income) && (
        <>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {t('transactions.items')}
            </Text>
            <View style={styles.itemsHeaderRight}>
              {/* Show total items amount if there are items */}
              {items.length > 0 && (
                <Text style={[styles.itemsTotal, {
                  color: isDarkMode ? '#8B949E' : '#6E7781',
                  marginRight: 8
                }]}>
                  {t('transactions.itemsTotal')}: {formatCurrency(items.reduce((sum, item) => sum + Number(item.amount) * (Number(item.quantity) || 1), 0))}
                </Text>
              )}
              <Switch
                value={showItems}
                onValueChange={setShowItems}
                trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
                thumbColor={showItems ? '#FFFFFF' : '#f4f3f4'}
              />
            </View>
          </View>

          {showItems && (
            <TransactionItemList
              items={items}
              onAddItem={handleAddItem}
              onUpdateItem={handleUpdateItem}
              onRemoveItem={handleRemoveItem}
              categories={categories.filter(c =>
                type === TransactionType.Expense
                  ? c.type === 'Expense'
                  : c.type === 'Income'
              )}
            />
          )}
        </>
      )}

      <View style={styles.buttonContainer}>
        <Button
          title={mode === 'create' ? t('transactions.addTransaction') : t('transactions.updateTransaction')}
          onPress={handleSubmit}
          isLoading={isLoading}
          fullWidth
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    fontSize: 14,
  },
  requiredIndicator: {
    color: '#F85149',
    marginLeft: 4,
    fontSize: 14,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 40,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  itemsHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemsTotal: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Transfer styles
  transferContainer: {
    flexDirection: 'row',
    marginVertical: 16,
    borderWidth: 1,
    borderColor: '#30363D',
    borderRadius: 8,
    padding: 16,
  },
  transferIconContainer: {
    alignItems: 'center',
    marginRight: 16,
  },
  transferIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  transferLine: {
    width: 2,
    flex: 1,
    backgroundColor: '#30363D',
  },
  transferAccountsContainer: {
    flex: 1,
  },
  transferLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  transferAccountName: {
    fontSize: 16,
    fontWeight: '500',
  },
  transferNote: {
    fontSize: 12,
    fontStyle: 'italic',
    marginBottom: 16,
  },
});

export default TransactionForm;
