import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, ScrollView } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import FormInput from '../ui/FormInput';
import Button from '../ui/Button';
import Dropdown from '../ui/Dropdown';
import { Ionicons } from '@expo/vector-icons';
import { validateRequired, validatePositiveNumber } from '../../utils/validation';
import { getCategoryTypeString } from '../../utils/typeConversion';

interface TransactionItem {
  id?: number;
  name: string;
  amount: number | string; // Will be converted to number before sending to backend
  quantity?: number | string; // Will be converted to number before sending to backend
  categoryId?: number;
  notes?: string;
}

interface TransactionItemListProps {
  items: TransactionItem[];
  onAddItem: (item: TransactionItem) => void;
  onUpdateItem: (item: TransactionItem) => void;
  onRemoveItem: (itemId: number) => void;
  categories: Array<{ id: number; name: string; type: string }>;
}

const TransactionItemList: React.FC<TransactionItemListProps> = ({
  items,
  onAddItem,
  onUpdateItem,
  onRemoveItem,
  categories,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();

  // Modal state
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<TransactionItem | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Form state
  const [name, setName] = useState('');
  const [amount, setAmount] = useState('');
  const [quantity, setQuantity] = useState('1');
  const [categoryId, setCategoryId] = useState<number | undefined>(undefined);
  const [notes, setNotes] = useState('');

  // Form validation
  const [touched, setTouched] = useState({
    name: false,
    amount: false,
  });

  const [errors, setErrors] = useState({
    name: '',
    amount: '',
  });

  // Reset form
  const resetForm = () => {
    setName('');
    setAmount('');
    setQuantity('1');
    setCategoryId(undefined);
    setNotes('');
    setTouched({ name: false, amount: false });
    setErrors({ name: '', amount: '' });
    setEditingItem(null);
    setIsEditing(false);
  };

  // Open modal to add new item
  const handleAddNewItem = () => {
    resetForm();
    setModalVisible(true);
  };

  // Open modal to edit existing item
  const handleEditItem = (item: TransactionItem) => {
    setEditingItem(item);
    setName(item.name);
    setAmount(item.amount.toString());
    setQuantity(item.quantity?.toString() || '1');
    setCategoryId(item.categoryId);
    setNotes(item.notes || '');
    setIsEditing(true);
    setModalVisible(true);
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {
      name: validateRequired(name, t),
      amount: validateRequired(amount, t) || validatePositiveNumber(amount, t),
    };

    setErrors(newErrors);

    return !Object.values(newErrors).some(error => error);
  };

  // Handle form submission
  const handleSubmit = () => {
    // Mark all fields as touched
    setTouched({ name: true, amount: true });

    if (validateForm()) {
      const itemData: TransactionItem = {
        ...(editingItem?.id && { id: editingItem.id }),
        name,
        amount: parseFloat(amount),
        quantity: parseInt(quantity || '1', 10),
        categoryId,
        notes,
      };

      if (isEditing && editingItem) {
        onUpdateItem(itemData);
      } else {
        onAddItem(itemData);
      }

      setModalVisible(false);
      resetForm();
    }
  };

  // Format category options for dropdown
  const categoryOptions = [
    { label: t('common.none'), value: null },
    ...categories.map(category => ({
      label: category.name,
      value: category.id,
    })),
  ];

  return (
    <View style={styles.container}>
      {items.length > 0 ? (
        <View style={styles.itemsContainer}>
          {items.map((item, index) => (
            <View
              key={item.id || `new-${index}`}
              style={[
                styles.itemCard,
                {
                  backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                  borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                },
              ]}
            >
              <View style={styles.itemHeader}>
                <Text
                  style={[
                    styles.itemName,
                    { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                  ]}
                >
                  {item.name}
                </Text>
                <View style={styles.itemActions}>
                  <TouchableOpacity
                    onPress={() => handleEditItem(item)}
                    style={styles.actionButton}
                  >
                    <Ionicons
                      name="pencil-outline"
                      size={18}
                      color={isDarkMode ? '#8B949E' : '#6E7781'}
                    />
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => item.id && onRemoveItem(item.id)}
                    style={styles.actionButton}
                  >
                    <Ionicons
                      name="trash-outline"
                      size={18}
                      color="#F85149"
                    />
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.itemDetails}>
                <Text
                  style={[
                    styles.itemDetail,
                    { color: isDarkMode ? '#8B949E' : '#6E7781' },
                  ]}
                >
                  {t('transactions.amount')}: {item.amount}
                </Text>
                <Text
                  style={[
                    styles.itemDetail,
                    { color: isDarkMode ? '#8B949E' : '#6E7781' },
                  ]}
                >
                  {t('transactions.quantity')}: {item.quantity || 1}
                </Text>
                {item.categoryId && (
                  <Text
                    style={[
                      styles.itemDetail,
                      { color: isDarkMode ? '#8B949E' : '#6E7781' },
                    ]}
                  >
                    {t('transactions.category')}: {
                      categories.find(c => c.id === item.categoryId)?.name || ''
                    }
                  </Text>
                )}
                {item.notes && (
                  <Text
                    style={[
                      styles.itemDetail,
                      { color: isDarkMode ? '#8B949E' : '#6E7781' },
                    ]}
                    numberOfLines={2}
                  >
                    {t('transactions.notes')}: {item.notes}
                  </Text>
                )}
              </View>
            </View>
          ))}
        </View>
      ) : (
        <View style={styles.emptyContainer}>
          <Text
            style={[
              styles.emptyText,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t('transactions.noItems')}
          </Text>
        </View>
      )}

      <Button
        title={t('transactions.addItem')}
        onPress={handleAddNewItem}
        variant="secondary"
        icon="add-outline"
        fullWidth
      />

      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text
                style={[
                  styles.modalTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                ]}
              >
                {isEditing
                  ? t('transactions.editItem')
                  : t('transactions.addItem')}
              </Text>
              <TouchableOpacity
                onPress={() => {
                  setModalVisible(false);
                  resetForm();
                }}
              >
                <Ionicons
                  name="close-outline"
                  size={24}
                  color={isDarkMode ? '#8B949E' : '#6E7781'}
                />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              <FormInput
                label={t('transactions.itemName')}
                value={name}
                onChangeText={setName}
                onBlur={() => setTouched({ ...touched, name: true })}
                error={errors.name}
                touched={touched.name}
                placeholder={t('transactions.itemNamePlaceholder')}
              />

              <FormInput
                label={t('transactions.amount')}
                value={amount}
                onChangeText={setAmount}
                onBlur={() => setTouched({ ...touched, amount: true })}
                error={errors.amount}
                touched={touched.amount}
                keyboardType="numeric"
                placeholder="0.00"
              />

              <FormInput
                label={t('transactions.quantity')}
                value={quantity}
                onChangeText={setQuantity}
                keyboardType="numeric"
                placeholder="1"
              />

              <Dropdown
                label={t('transactions.category')}
                options={categoryOptions}
                selectedValue={categoryId ?? null}
                onValueChange={(value) => setCategoryId(typeof value === 'number' ? value : undefined)}
              />

              <FormInput
                label={t('transactions.notes')}
                value={notes}
                onChangeText={setNotes}
                placeholder={t('transactions.notesPlaceholder')}
                multiline
                numberOfLines={3}
                style={styles.textArea}
              />
            </ScrollView>

            <View style={styles.modalFooter}>
              <Button
                title={t('common.cancel')}
                onPress={() => {
                  setModalVisible(false);
                  resetForm();
                }}
                variant="secondary"
                style={styles.footerButton}
              />
              <Button
                title={isEditing ? t('common.update') : t('common.add')}
                onPress={handleSubmit}
                style={styles.footerButton}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
  },
  itemsContainer: {
    marginBottom: 16,
  },
  itemCard: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  itemActions: {
    flexDirection: 'row',
  },
  actionButton: {
    marginLeft: 12,
  },
  itemDetails: {
    marginTop: 4,
  },
  itemDetail: {
    fontSize: 14,
    marginBottom: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderWidth: 1,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalBody: {
    padding: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#30363D',
  },
  footerButton: {
    flex: 1,
    marginHorizontal: 8,
  },
});

export default TransactionItemList;
