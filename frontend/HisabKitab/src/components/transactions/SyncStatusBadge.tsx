import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { SyncStatus } from '../../models/Transaction';

interface SyncStatusBadgeProps {
  status: SyncStatus;
  onSync?: () => void;
  showSyncButton?: boolean;
}

const SyncStatusBadge: React.FC<SyncStatusBadgeProps> = ({
  status,
  onSync,
  showSyncButton = false,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();

  // Get status color and icon
  const getStatusColor = () => {
    switch (status) {
      case SyncStatus.Synced:
        return '#3FB950'; // Green
      case SyncStatus.Pending:
        return '#F0883E'; // Orange
      case SyncStatus.Failed:
        return '#F85149'; // Red
      default:
        return '#8B949E'; // Gray
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case SyncStatus.Synced:
        return 'checkmark-circle-outline';
      case SyncStatus.Pending:
        return 'time-outline';
      case SyncStatus.Failed:
        return 'alert-circle-outline';
      default:
        return 'help-circle-outline';
    }
  };

  const getStatusLabel = () => {
    switch (status) {
      case SyncStatus.Synced:
        return t('sync.synced');
      case SyncStatus.Pending:
        return t('sync.pending');
      case SyncStatus.Failed:
        return t('sync.failed');
      default:
        return t('sync.unknown');
    }
  };

  return (
    <View style={styles.container}>
      <View
        style={[
          styles.badge,
          { backgroundColor: getStatusColor() + '20' }, // Add transparency
        ]}
      >
        <Ionicons
          name={getStatusIcon()}
          size={12}
          color={getStatusColor()}
          style={styles.icon}
        />
        <Text
          style={[
            styles.text,
            { color: getStatusColor() },
          ]}
        >
          {getStatusLabel()}
        </Text>
      </View>
      
      {showSyncButton && status !== SyncStatus.Synced && onSync && (
        <TouchableOpacity
          style={styles.syncButton}
          onPress={onSync}
        >
          <Ionicons
            name="sync-outline"
            size={14}
            color={isDarkMode ? '#58A6FF' : '#0366D6'}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 12,
  },
  icon: {
    marginRight: 4,
  },
  text: {
    fontSize: 10,
    fontWeight: '500',
  },
  syncButton: {
    marginLeft: 4,
    padding: 4,
  },
});

export default SyncStatusBadge;
