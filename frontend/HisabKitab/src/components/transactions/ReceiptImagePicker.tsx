import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  Platform,
  ActivityIndicator,
  Modal,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { useOCR, OCRLanguage } from '../../contexts/OCRContext';
import { ReceiptData } from '../../models/Receipt';
import Button from '../ui/Button';
import ProgressBar from '../ui/ProgressBar';
import { autoCompressImage, getImageFileSize } from '../../utils/imageUtils';

interface ReceiptImagePickerProps {
  imageUri?: string;
  onImageSelected: (uri: string) => void;
  onImageRemoved: () => void;
  onReceiptProcessed?: (receiptData: ReceiptData) => void;
  enableOCR?: boolean;
}

const ReceiptImagePicker: React.FC<ReceiptImagePickerProps> = ({
  imageUri,
  onImageSelected,
  onImageRemoved,
  onReceiptProcessed,
  enableOCR = true,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const {
    isProcessing,
    progress,
    recognizedText,
    extractedData,
    processImage,
    processImageOnServer,
    cancelProcessing,
    clearResults
  } = useOCR();

  const [isLoading, setIsLoading] = useState(false);
  const [showOCRModal, setShowOCRModal] = useState(false);
  const [processingMethod, setProcessingMethod] = useState<'client' | 'server'>('client');
  const [language, setLanguage] = useState<OCRLanguage>('eng');
  const [fileSize, setFileSize] = useState<number>(0);

  // Clear OCR results when component unmounts
  useEffect(() => {
    return () => {
      clearResults();
    };
  }, []);

  // When OCR processing completes, notify parent component
  useEffect(() => {
    if (extractedData && onReceiptProcessed) {
      onReceiptProcessed(extractedData);
    }
  }, [extractedData, onReceiptProcessed]);

  // Request camera permissions
  const requestCameraPermission = async () => {
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          t('common.error'),
          t('receipts.cameraPermissionDenied')
        );
        return false;
      }
      return true;
    }
    return true;
  };

  // Request media library permissions
  const requestMediaLibraryPermission = async () => {
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          t('common.error'),
          t('receipts.mediaLibraryPermissionDenied')
        );
        return false;
      }
      return true;
    }
    return true;
  };

  // Preprocess image for better OCR results
  const preprocessImage = async (uri: string): Promise<string> => {
    try {
      // First, compress the image to reduce file size
      const compressedUri = await autoCompressImage(uri, 1024 * 1024); // Max 1MB

      // Get and store the file size for display
      const size = await getImageFileSize(compressedUri);
      setFileSize(size);

      // Apply image manipulations to improve OCR
      const manipResult = await ImageManipulator.manipulateAsync(
        compressedUri,
        [
          { resize: { width: 1200 } }, // Resize to reasonable dimensions
          // Note: contrast and brightness are not supported in the Action type
          // We'll need to use only the supported operations
        ],
        { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
      );

      return manipResult.uri;
    } catch (error) {
      console.error('Error preprocessing image:', error);
      // Return original if preprocessing fails
      return uri;
    }
  };

  // Take a photo with the camera
  const takePhoto = async () => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) return;

    try {
      setIsLoading(true);
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const processedUri = await preprocessImage(result.assets[0].uri);
        onImageSelected(processedUri);

        // Show OCR options if enabled
        if (enableOCR) {
          setShowOCRModal(true);
        }
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert(
        t('common.error'),
        t('receipts.photoError')
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Pick an image from the media library
  const pickImage = async () => {
    const hasPermission = await requestMediaLibraryPermission();
    if (!hasPermission) return;

    try {
      setIsLoading(true);
      const result = await ImagePicker.launchImageLibraryAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const processedUri = await preprocessImage(result.assets[0].uri);
        onImageSelected(processedUri);

        // Show OCR options if enabled
        if (enableOCR) {
          setShowOCRModal(true);
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(
        t('common.error'),
        t('receipts.imagePickerError')
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Remove the selected image
  const removeImage = () => {
    Alert.alert(
      t('receipts.removeImage'),
      t('receipts.removeImageConfirmation'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.remove'),
          style: 'destructive',
          onPress: () => {
            onImageRemoved();
            clearResults();
          },
        },
      ]
    );
  };

  // Show options for adding an image
  const showImageOptions = () => {
    Alert.alert(
      t('receipts.addReceipt'),
      t('receipts.selectImageSource'),
      [
        {
          text: t('receipts.camera'),
          onPress: takePhoto,
        },
        {
          text: t('receipts.gallery'),
          onPress: pickImage,
        },
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
      ]
    );
  };

  // Process receipt with OCR
  const processReceipt = async () => {
    if (!imageUri) return;

    try {
      setShowOCRModal(false);

      if (processingMethod === 'client') {
        await processImage(imageUri, language);
      } else {
        await processImageOnServer(imageUri, language);
      }
    } catch (error) {
      console.error('Error processing receipt:', error);
      Alert.alert(
        t('common.error'),
        t('receipts.ocrError')
      );
    }
  };

  return (
    <View style={styles.container}>
      <Text style={[styles.label, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
        {t('receipts.receiptImage')}
      </Text>

      <View style={styles.imageContainer}>
        {isLoading || isProcessing ? (
          <View style={[
            styles.imagePlaceholder,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
          ]}>
            <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
            {isProcessing && (
              <View style={styles.progressContainer}>
                <Text style={[styles.progressText, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                  {t('receipts.processingReceipt')} ({Math.round(progress * 100)}%)
                </Text>
                <ProgressBar progress={progress} />
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={cancelProcessing}
                >
                  <Text style={styles.cancelText}>{t('common.cancel')}</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        ) : imageUri ? (
          <View style={styles.imageWrapper}>
            <Image source={{ uri: imageUri }} style={styles.image} />
            <View style={styles.imageActions}>
              <TouchableOpacity
                style={[
                  styles.removeButton,
                  { backgroundColor: isDarkMode ? 'rgba(13, 17, 23, 0.7)' : 'rgba(255, 255, 255, 0.7)' }
                ]}
                onPress={removeImage}
              >
                <Ionicons name="close-circle" size={24} color="#F85149" />
              </TouchableOpacity>

              {enableOCR && (
                <TouchableOpacity
                  style={[
                    styles.ocrButton,
                    { backgroundColor: isDarkMode ? 'rgba(13, 17, 23, 0.7)' : 'rgba(255, 255, 255, 0.7)' }
                  ]}
                  onPress={() => setShowOCRModal(true)}
                >
                  <Ionicons name="scan" size={24} color={isDarkMode ? '#58A6FF' : '#0366D6'} />
                </TouchableOpacity>
              )}
            </View>

            {extractedData && (
              <View style={[
                styles.extractedDataBadge,
                { backgroundColor: isDarkMode ? 'rgba(59, 185, 80, 0.2)' : 'rgba(59, 185, 80, 0.1)' }
              ]}>
                <Ionicons name="checkmark-circle" size={16} color="#3FB950" />
                <Text style={[
                  styles.extractedDataText,
                  { color: isDarkMode ? '#FFFFFF' : '#24292E' }
                ]}>
                  {t('receipts.dataExtracted')}
                </Text>
              </View>
            )}

            {fileSize > 0 && (
              <View style={[
                styles.fileSizeBadge,
                { backgroundColor: isDarkMode ? 'rgba(88, 166, 255, 0.2)' : 'rgba(88, 166, 255, 0.1)' }
              ]}>
                <Ionicons name="document" size={14} color={isDarkMode ? '#58A6FF' : '#0366D6'} />
                <Text style={[
                  styles.fileSizeText,
                  { color: isDarkMode ? '#FFFFFF' : '#24292E' }
                ]}>
                  {(fileSize / 1024).toFixed(0)} KB
                </Text>
              </View>
            )}
          </View>
        ) : (
          <TouchableOpacity
            style={[
              styles.imagePlaceholder,
              { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
            ]}
            onPress={showImageOptions}
          >
            <Ionicons
              name="camera-outline"
              size={32}
              color={isDarkMode ? '#8B949E' : '#6E7781'}
            />
            <Text style={[
              styles.placeholderText,
              { color: isDarkMode ? '#8B949E' : '#6E7781' }
            ]}>
              {t('receipts.addReceiptImage')}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {enableOCR ? (
        <View style={styles.ocrNotice}>
          <Ionicons
            name="scan-outline"
            size={16}
            color={isDarkMode ? '#58A6FF' : '#0366D6'}
            style={styles.infoIcon}
          />
          <Text style={[
            styles.ocrText,
            { color: isDarkMode ? '#8B949E' : '#6E7781' }
          ]}>
            {t('receipts.ocrAvailable')}
          </Text>
        </View>
      ) : (
        <View style={styles.ocrNotice}>
          <Ionicons
            name="information-circle-outline"
            size={16}
            color={isDarkMode ? '#58A6FF' : '#0366D6'}
            style={styles.infoIcon}
          />
          <Text style={[
            styles.ocrText,
            { color: isDarkMode ? '#8B949E' : '#6E7781' }
          ]}>
            {t('receipts.ocrDisabled')}
          </Text>
        </View>
      )}

      {/* OCR Options Modal */}
      <Modal
        visible={showOCRModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowOCRModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[
            styles.modalContent,
            { backgroundColor: isDarkMode ? '#161B22' : '#FFFFFF' }
          ]}>
            <Text style={[
              styles.modalTitle,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' }
            ]}>
              {t('receipts.processReceipt')}
            </Text>

            <Text style={[
              styles.modalSubtitle,
              { color: isDarkMode ? '#8B949E' : '#6E7781' }
            ]}>
              {t('receipts.selectProcessingOptions')}
            </Text>

            <View style={styles.optionSection}>
              <Text style={[
                styles.optionTitle,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' }
              ]}>
                {t('receipts.processingMethod')}
              </Text>

              <View style={styles.optionButtons}>
                <TouchableOpacity
                  style={[
                    styles.optionButton,
                    processingMethod === 'client' && styles.optionButtonSelected,
                    {
                      backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA',
                      borderColor: processingMethod === 'client' ?
                        (isDarkMode ? '#58A6FF' : '#0366D6') :
                        (isDarkMode ? '#30363D' : '#D0D7DE')
                    }
                  ]}
                  onPress={() => setProcessingMethod('client')}
                >
                  <Ionicons
                    name="phone-portrait-outline"
                    size={20}
                    color={processingMethod === 'client' ?
                      (isDarkMode ? '#58A6FF' : '#0366D6') :
                      (isDarkMode ? '#8B949E' : '#6E7781')}
                  />
                  <Text style={[
                    styles.optionButtonText,
                    {
                      color: processingMethod === 'client' ?
                        (isDarkMode ? '#58A6FF' : '#0366D6') :
                        (isDarkMode ? '#8B949E' : '#6E7781')
                    }
                  ]}>
                    {t('receipts.deviceProcessing')}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.optionButton,
                    processingMethod === 'server' && styles.optionButtonSelected,
                    {
                      backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA',
                      borderColor: processingMethod === 'server' ?
                        (isDarkMode ? '#58A6FF' : '#0366D6') :
                        (isDarkMode ? '#30363D' : '#D0D7DE')
                    }
                  ]}
                  onPress={() => setProcessingMethod('server')}
                >
                  <Ionicons
                    name="cloud-outline"
                    size={20}
                    color={processingMethod === 'server' ?
                      (isDarkMode ? '#58A6FF' : '#0366D6') :
                      (isDarkMode ? '#8B949E' : '#6E7781')}
                  />
                  <Text style={[
                    styles.optionButtonText,
                    {
                      color: processingMethod === 'server' ?
                        (isDarkMode ? '#58A6FF' : '#0366D6') :
                        (isDarkMode ? '#8B949E' : '#6E7781')
                    }
                  ]}>
                    {t('receipts.serverProcessing')}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.optionSection}>
              <Text style={[
                styles.optionTitle,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' }
              ]}>
                {t('receipts.language')}
              </Text>

              <View style={styles.optionButtons}>
                <TouchableOpacity
                  style={[
                    styles.optionButton,
                    language === 'eng' && styles.optionButtonSelected,
                    {
                      backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA',
                      borderColor: language === 'eng' ?
                        (isDarkMode ? '#58A6FF' : '#0366D6') :
                        (isDarkMode ? '#30363D' : '#D0D7DE')
                    }
                  ]}
                  onPress={() => setLanguage('eng')}
                >
                  <Text style={[
                    styles.optionButtonText,
                    {
                      color: language === 'eng' ?
                        (isDarkMode ? '#58A6FF' : '#0366D6') :
                        (isDarkMode ? '#8B949E' : '#6E7781')
                    }
                  ]}>
                    {t('common.english')}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.optionButton,
                    language === 'nep' && styles.optionButtonSelected,
                    {
                      backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA',
                      borderColor: language === 'nep' ?
                        (isDarkMode ? '#58A6FF' : '#0366D6') :
                        (isDarkMode ? '#30363D' : '#D0D7DE')
                    }
                  ]}
                  onPress={() => setLanguage('nep')}
                >
                  <Text style={[
                    styles.optionButtonText,
                    {
                      color: language === 'nep' ?
                        (isDarkMode ? '#58A6FF' : '#0366D6') :
                        (isDarkMode ? '#8B949E' : '#6E7781')
                    }
                  ]}>
                    {t('common.nepali')}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.modalActions}>
              <Button
                title={t('common.cancel')}
                onPress={() => setShowOCRModal(false)}
                type="secondary"
                style={styles.modalButton}
              />
              <Button
                title={t('receipts.processReceipt')}
                onPress={processReceipt}
                type="primary"
                style={styles.modalButton}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
  },
  imageContainer: {
    width: '100%',
    height: 200,
    marginBottom: 8,
  },
  imagePlaceholder: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#30363D',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    marginTop: 8,
    fontSize: 14,
  },
  imageWrapper: {
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  imageActions: {
    position: 'absolute',
    top: 8,
    right: 8,
    flexDirection: 'row',
  },
  removeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  ocrButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  ocrNotice: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoIcon: {
    marginRight: 4,
  },
  ocrText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  progressContainer: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    right: 16,
    alignItems: 'center',
  },
  progressText: {
    fontSize: 14,
    marginBottom: 8,
    textAlign: 'center',
  },
  cancelButton: {
    marginTop: 8,
    padding: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(248, 81, 73, 0.2)',
  },
  cancelText: {
    color: '#F85149',
    fontSize: 12,
    fontWeight: 'bold',
  },
  extractedDataBadge: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  extractedDataText: {
    fontSize: 12,
    marginLeft: 4,
    fontWeight: 'bold',
  },
  fileSizeBadge: {
    position: 'absolute',
    bottom: 40,
    left: 8,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  fileSizeText: {
    fontSize: 10,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  modalContent: {
    width: '100%',
    borderRadius: 8,
    padding: 16,
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  modalSubtitle: {
    fontSize: 14,
    marginBottom: 16,
  },
  optionSection: {
    marginBottom: 16,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  optionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  optionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginHorizontal: 4,
  },
  optionButtonSelected: {
    borderWidth: 2,
  },
  optionButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  modalButton: {
    marginLeft: 8,
    minWidth: 100,
  },
});

export default ReceiptImagePicker;
