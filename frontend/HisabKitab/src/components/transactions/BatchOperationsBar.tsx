import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';

interface BatchOperationsBarProps {
  selectedCount: number;
  onDelete: () => void;
  onCategorize: () => void;
  onTag: () => void;
  onCancel: () => void;
  visible: boolean;
}

const BatchOperationsBar: React.FC<BatchOperationsBarProps> = ({
  selectedCount,
  onDelete,
  onCategorize,
  onTag,
  onCancel,
  visible,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const translateY = React.useRef(new Animated.Value(100)).current;

  React.useEffect(() => {
    Animated.timing(translateY, {
      toValue: visible ? 0 : 100,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [visible, translateY]);

  if (!visible) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
          borderTopColor: isDarkMode ? '#30363D' : '#D0D7DE',
          transform: [{ translateY }],
        },
      ]}
    >
      <View style={styles.content}>
        <Text
          style={[
            styles.selectedText,
            { color: isDarkMode ? '#FFFFFF' : '#24292E' },
          ]}
        >
          {t('batch.selected', { count: selectedCount })}
        </Text>

        <View style={styles.actions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={onCategorize}
          >
            <Ionicons
              name="folder-outline"
              size={20}
              color={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
            <Text
              style={[
                styles.actionText,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' },
              ]}
            >
              {t('batch.categorize')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={onTag}
          >
            <Ionicons
              name="pricetag-outline"
              size={20}
              color={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
            <Text
              style={[
                styles.actionText,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' },
              ]}
            >
              {t('batch.tag')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={onDelete}
          >
            <Ionicons
              name="trash-outline"
              size={20}
              color="#F85149"
            />
            <Text
              style={[
                styles.actionText,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' },
              ]}
            >
              {t('batch.delete')}
            </Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={styles.cancelButton}
          onPress={onCancel}
        >
          <Ionicons
            name="close-outline"
            size={24}
            color={isDarkMode ? '#8B949E' : '#6E7781'}
          />
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    paddingBottom: 34, // Extra padding for bottom safe area
    zIndex: 999,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  selectedText: {
    fontSize: 14,
    fontWeight: '500',
  },
  actions: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'center',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  actionText: {
    fontSize: 14,
    marginLeft: 4,
  },
  cancelButton: {
    padding: 4,
  },
});

export default BatchOperationsBar;
