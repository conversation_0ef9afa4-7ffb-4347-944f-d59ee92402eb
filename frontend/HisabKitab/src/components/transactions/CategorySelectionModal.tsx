import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import Button from '../ui/Button';
import { TransactionType } from '../../models/Transaction';

interface Category {
  id: number;
  name: string;
  type: string;
}

interface CategorySelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (categoryId: number) => void;
  categories: Category[];
  transactionType?: TransactionType;
}

const CategorySelectionModal: React.FC<CategorySelectionModalProps> = ({
  visible,
  onClose,
  onSelect,
  categories,
  transactionType,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const [selectedType, setSelectedType] = useState<string | null>(
    transactionType === TransactionType.Income ? 'Income' :
    transactionType === TransactionType.Expense ? 'Expense' : null
  );

  // Filter categories by type
  const filteredCategories = selectedType
    ? categories.filter(c => c.type === selectedType)
    : categories;

  // Render category item
  const renderCategoryItem = ({ item }: { item: Category }) => (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
      ]}
      onPress={() => onSelect(item.id)}
    >
      <Text
        style={[
          styles.categoryName,
          { color: isDarkMode ? '#FFFFFF' : '#24292E' },
        ]}
      >
        {item.name}
      </Text>
      <Ionicons
        name="chevron-forward-outline"
        size={18}
        color={isDarkMode ? '#8B949E' : '#6E7781'}
      />
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View
          style={[
            styles.modalContent,
            {
              backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
            },
          ]}
        >
          <View style={styles.modalHeader}>
            <Text
              style={[
                styles.modalTitle,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' },
              ]}
            >
              {t('categories.selectCategory')}
            </Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons
                name="close-outline"
                size={24}
                color={isDarkMode ? '#8B949E' : '#6E7781'}
              />
            </TouchableOpacity>
          </View>

          <View style={styles.typeSelector}>
            <TouchableOpacity
              style={[
                styles.typeButton,
                selectedType === 'Expense' && {
                  backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
                },
              ]}
              onPress={() => setSelectedType('Expense')}
            >
              <Text
                style={[
                  styles.typeText,
                  {
                    color: selectedType === 'Expense'
                      ? '#FFFFFF'
                      : isDarkMode
                        ? '#8B949E'
                        : '#6E7781',
                  },
                ]}
              >
                {t('transactions.expense')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.typeButton,
                selectedType === 'Income' && {
                  backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
                },
              ]}
              onPress={() => setSelectedType('Income')}
            >
              <Text
                style={[
                  styles.typeText,
                  {
                    color: selectedType === 'Income'
                      ? '#FFFFFF'
                      : isDarkMode
                        ? '#8B949E'
                        : '#6E7781',
                  },
                ]}
              >
                {t('transactions.income')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.typeButton,
                selectedType === null && {
                  backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
                },
              ]}
              onPress={() => setSelectedType(null)}
            >
              <Text
                style={[
                  styles.typeText,
                  {
                    color: selectedType === null
                      ? '#FFFFFF'
                      : isDarkMode
                        ? '#8B949E'
                        : '#6E7781',
                  },
                ]}
              >
                {t('common.all')}
              </Text>
            </TouchableOpacity>
          </View>

          <FlatList
            data={filteredCategories}
            renderItem={renderCategoryItem}
            keyExtractor={(item) => item.id.toString()}
            contentContainerStyle={styles.categoryList}
          />

          <View style={styles.modalFooter}>
            <Button
              title={t('common.cancel')}
              onPress={onClose}
              variant="secondary"
              style={styles.footerButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderWidth: 1,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  typeSelector: {
    flexDirection: 'row',
    padding: 16,
  },
  typeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  typeText: {
    fontSize: 14,
  },
  categoryList: {
    padding: 16,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 16,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#30363D',
  },
  footerButton: {
    minWidth: 100,
  },
});

export default CategorySelectionModal;
