import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  TextInput,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import Button from '../ui/Button';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { TransactionFilterDto } from '../../models/Transaction';
import { formatDate } from '../../utils/formatters';

interface SavedFilter {
  id: string;
  name: string;
  filter: TransactionFilterDto;
  createdAt: string;
}

interface SavedFiltersManagerProps {
  currentFilter: TransactionFilterDto;
  onFilterSelect: (filter: TransactionFilterDto) => void;
}

const SavedFiltersManager: React.FC<SavedFiltersManagerProps> = ({
  currentFilter,
  onFilterSelect,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([]);
  const [filterName, setFilterName] = useState('');
  const [saveMode, setSaveMode] = useState(false);

  // Load saved filters on mount
  useEffect(() => {
    loadSavedFilters();
  }, []);

  // Load saved filters from AsyncStorage
  const loadSavedFilters = async () => {
    try {
      const savedFiltersJson = await AsyncStorage.getItem('savedTransactionFilters');
      if (savedFiltersJson) {
        const filters = JSON.parse(savedFiltersJson) as SavedFilter[];
        setSavedFilters(filters);
      }
    } catch (error) {
      console.error('Error loading saved filters:', error);
    }
  };

  // Save filters to AsyncStorage
  const saveSavedFilters = async (filters: SavedFilter[]) => {
    try {
      await AsyncStorage.setItem('savedTransactionFilters', JSON.stringify(filters));
    } catch (error) {
      console.error('Error saving filters:', error);
    }
  };

  // Save current filter
  const saveCurrentFilter = async () => {
    if (!filterName.trim()) {
      Alert.alert(
        t('common.error'),
        t('filters.nameRequired')
      );
      return;
    }

    // Check if filter with this name already exists
    const existingFilter = savedFilters.find(f => f.name === filterName.trim());
    if (existingFilter) {
      Alert.alert(
        t('common.error'),
        t('filters.nameExists')
      );
      return;
    }

    const newFilter: SavedFilter = {
      id: Date.now().toString(),
      name: filterName.trim(),
      filter: { ...currentFilter },
      createdAt: new Date().toISOString(),
    };

    const updatedFilters = [...savedFilters, newFilter];
    setSavedFilters(updatedFilters);
    await saveSavedFilters(updatedFilters);
    setFilterName('');
    setSaveMode(false);
  };

  // Delete saved filter
  const deleteFilter = async (filterId: string) => {
    Alert.alert(
      t('filters.deleteFilter'),
      t('filters.deleteFilterConfirmation'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            const updatedFilters = savedFilters.filter(f => f.id !== filterId);
            setSavedFilters(updatedFilters);
            await saveSavedFilters(updatedFilters);
          },
        },
      ]
    );
  };

  // Apply saved filter
  const applyFilter = (filter: SavedFilter) => {
    onFilterSelect(filter.filter);
    setModalVisible(false);
  };

  // Get filter summary text
  const getFilterSummary = (filter: TransactionFilterDto) => {
    const parts = [];

    if (filter.startDate && filter.endDate) {
      parts.push(`${formatDate(new Date(filter.startDate))} - ${formatDate(new Date(filter.endDate))}`);
    } else if (filter.startDate) {
      parts.push(`${t('dateRange.from')} ${formatDate(new Date(filter.startDate))}`);
    } else if (filter.endDate) {
      parts.push(`${t('dateRange.until')} ${formatDate(new Date(filter.endDate))}`);
    }

    if (filter.type !== undefined && filter.type !== null) {
      parts.push(t(`transactions.type${filter.type}`));
    }

    if (filter.accountId) {
      parts.push(t('filters.account'));
    }

    if (filter.categoryId) {
      parts.push(t('filters.category'));
    }

    if (filter.priorityId) {
      parts.push(t('filters.priority'));
    }

    if (filter.searchTerm) {
      parts.push(`"${filter.searchTerm}"`);
    }

    return parts.join(' • ');
  };

  // Render filter item
  const renderFilterItem = ({ item }: { item: SavedFilter }) => (
    <View
      style={[
        styles.filterItem,
        { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
      ]}
    >
      <TouchableOpacity
        style={styles.filterContent}
        onPress={() => applyFilter(item)}
      >
        <View style={styles.filterHeader}>
          <Text
            style={[
              styles.filterName,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
          >
            {item.name}
          </Text>
          <Text
            style={[
              styles.filterDate,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {formatDate(new Date(item.createdAt))}
          </Text>
        </View>
        <Text
          style={[
            styles.filterSummary,
            { color: isDarkMode ? '#8B949E' : '#6E7781' },
          ]}
          numberOfLines={2}
        >
          {getFilterSummary(item.filter)}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => deleteFilter(item.id)}
      >
        <Ionicons
          name="trash-outline"
          size={18}
          color="#F85149"
        />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.button,
          { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
        ]}
        onPress={() => setModalVisible(true)}
      >
        <Ionicons
          name="bookmark-outline"
          size={18}
          color={isDarkMode ? '#58A6FF' : '#0366D6'}
          style={styles.buttonIcon}
        />
        <Text
          style={[
            styles.buttonText,
            { color: isDarkMode ? '#FFFFFF' : '#24292E' },
          ]}
        >
          {t('filters.savedFilters')}
        </Text>
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text
                style={[
                  styles.modalTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                ]}
              >
                {t('filters.savedFilters')}
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Ionicons
                  name="close-outline"
                  size={24}
                  color={isDarkMode ? '#8B949E' : '#6E7781'}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              {saveMode ? (
                <View style={styles.saveForm}>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                        borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                        color: isDarkMode ? '#FFFFFF' : '#24292E',
                      },
                    ]}
                    placeholder={t('filters.filterName')}
                    placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
                    value={filterName}
                    onChangeText={setFilterName}
                  />
                  <View style={styles.saveFormButtons}>
                    <Button
                      title={t('common.cancel')}
                      onPress={() => {
                        setSaveMode(false);
                        setFilterName('');
                      }}
                      variant="secondary"
                      style={styles.saveFormButton}
                    />
                    <Button
                      title={t('common.save')}
                      onPress={saveCurrentFilter}
                      style={styles.saveFormButton}
                    />
                  </View>
                </View>
              ) : (
                <Button
                  title={t('filters.saveCurrentFilter')}
                  onPress={() => setSaveMode(true)}
                  icon="bookmark-outline"
                  style={styles.saveButton}
                />
              )}

              {savedFilters.length > 0 ? (
                <FlatList
                  data={savedFilters}
                  renderItem={renderFilterItem}
                  keyExtractor={(item) => item.id}
                  contentContainerStyle={styles.filterList}
                />
              ) : (
                <View style={styles.emptyState}>
                  <Ionicons
                    name="bookmark-outline"
                    size={48}
                    color={isDarkMode ? '#30363D' : '#D0D7DE'}
                  />
                  <Text
                    style={[
                      styles.emptyStateText,
                      { color: isDarkMode ? '#8B949E' : '#6E7781' },
                    ]}
                  >
                    {t('filters.noSavedFilters')}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    fontSize: 14,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderWidth: 1,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalBody: {
    padding: 16,
  },
  saveForm: {
    marginBottom: 16,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    marginBottom: 12,
  },
  saveFormButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  saveFormButton: {
    marginLeft: 8,
    minWidth: 100,
  },
  saveButton: {
    marginBottom: 16,
  },
  filterList: {
    paddingBottom: 16,
  },
  filterItem: {
    flexDirection: 'row',
    borderRadius: 8,
    marginBottom: 12,
    overflow: 'hidden',
  },
  filterContent: {
    flex: 1,
    padding: 12,
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  filterName: {
    fontSize: 16,
    fontWeight: '500',
  },
  filterDate: {
    fontSize: 12,
  },
  filterSummary: {
    fontSize: 14,
  },
  deleteButton: {
    padding: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyStateText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
});

export default SavedFiltersManager;
