import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import Button from '../ui/Button';

interface TagSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (tag: string) => void;
  recentTags: string[];
}

const TagSelectionModal: React.FC<TagSelectionModalProps> = ({
  visible,
  onClose,
  onSelect,
  recentTags,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const [newTag, setNewTag] = useState('');

  // Handle tag selection
  const handleSelectTag = (tag: string) => {
    onSelect(tag);
    onClose();
  };

  // Handle new tag creation
  const handleCreateTag = () => {
    if (newTag.trim()) {
      onSelect(newTag.trim());
      setNewTag('');
      onClose();
    }
  };

  // Render tag item
  const renderTagItem = ({ item }: { item: string }) => (
    <TouchableOpacity
      style={[
        styles.tagItem,
        { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
      ]}
      onPress={() => handleSelectTag(item)}
    >
      <Ionicons
        name="pricetag-outline"
        size={18}
        color={isDarkMode ? '#58A6FF' : '#0366D6'}
        style={styles.tagIcon}
      />
      <Text
        style={[
          styles.tagName,
          { color: isDarkMode ? '#FFFFFF' : '#24292E' },
        ]}
      >
        {item}
      </Text>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View
          style={[
            styles.modalContent,
            {
              backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
            },
          ]}
        >
          <View style={styles.modalHeader}>
            <Text
              style={[
                styles.modalTitle,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' },
              ]}
            >
              {t('tags.selectTag')}
            </Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons
                name="close-outline"
                size={24}
                color={isDarkMode ? '#8B949E' : '#6E7781'}
              />
            </TouchableOpacity>
          </View>

          <View style={styles.newTagContainer}>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                  borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                  color: isDarkMode ? '#FFFFFF' : '#24292E',
                },
              ]}
              placeholder={t('tags.newTag')}
              placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
              value={newTag}
              onChangeText={setNewTag}
              onSubmitEditing={handleCreateTag}
            />
            <Button
              title={t('common.add')}
              onPress={handleCreateTag}
              disabled={!newTag.trim()}
              style={styles.addButton}
            />
          </View>

          {recentTags.length > 0 && (
            <>
              <Text
                style={[
                  styles.sectionTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                ]}
              >
                {t('tags.recentTags')}
              </Text>

              <FlatList
                data={recentTags}
                renderItem={renderTagItem}
                keyExtractor={(item) => item}
                contentContainerStyle={styles.tagList}
              />
            </>
          )}

          <View style={styles.modalFooter}>
            <Button
              title={t('common.cancel')}
              onPress={onClose}
              variant="secondary"
              style={styles.footerButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderWidth: 1,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  newTagContainer: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  input: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    marginRight: 8,
  },
  addButton: {
    minWidth: 80,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    paddingHorizontal: 16,
    marginTop: 8,
  },
  tagList: {
    padding: 16,
  },
  tagItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  tagIcon: {
    marginRight: 8,
  },
  tagName: {
    fontSize: 16,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#30363D',
  },
  footerButton: {
    minWidth: 100,
  },
});

export default TagSelectionModal;
