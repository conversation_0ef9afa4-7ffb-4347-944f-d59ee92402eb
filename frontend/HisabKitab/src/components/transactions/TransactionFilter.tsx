import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, ScrollView } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import Button from '../ui/Button';
import Dropdown from '../ui/Dropdown';
import DateRangeSelector from '../ui/DateRangeSelector';
import FormInput from '../ui/FormInput';
import SavedFiltersManager from './SavedFiltersManager';
import { TransactionType, TransactionFilterDto } from '../../models/Transaction';
import { formatDateForBackend, parseDateFromBackend } from '../../utils/dateUtils';

interface TransactionFilterProps {
  onApplyFilter: (filter: TransactionFilterDto) => void;
  onResetFilter: () => void;
  initialFilter?: TransactionFilterDto;
  accounts?: Array<{ id: number; name: string }>;
  categories?: Array<{ id: number; name: string; type: string }>;
  priorities?: Array<{ id: number; name: string }>;
}

const TransactionFilter: React.FC<TransactionFilterProps> = ({
  onApplyFilter,
  onResetFilter,
  initialFilter = {},
  accounts = [],
  categories = [],
  priorities = [],
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();

  // Modal state
  const [modalVisible, setModalVisible] = useState(false);

  // Filter state
  const [startDate, setStartDate] = useState<Date | undefined>(
    initialFilter.startDate ? new Date(initialFilter.startDate) : undefined
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    initialFilter.endDate ? new Date(initialFilter.endDate) : undefined
  );
  const [type, setType] = useState<TransactionType | undefined>(initialFilter.type);
  const [accountId, setAccountId] = useState<number | undefined>(initialFilter.accountId);
  const [categoryId, setCategoryId] = useState<number | undefined>(initialFilter.categoryId);
  const [priorityId, setPriorityId] = useState<number | undefined>(initialFilter.priorityId);
  const [status, setStatus] = useState<string | undefined>(initialFilter.status);
  const [searchTerm, setSearchTerm] = useState<string | undefined>(initialFilter.searchTerm);
  const [sortBy, setSortBy] = useState<string | undefined>(initialFilter.sortBy || 'date');
  const [ascending, setAscending] = useState<boolean>(initialFilter.ascending || false);

  // Reset filter
  const handleResetFilter = () => {
    setStartDate(undefined);
    setEndDate(undefined);
    setType(undefined);
    setAccountId(undefined);
    setCategoryId(undefined);
    setPriorityId(undefined);
    setStatus(undefined);
    setSearchTerm(undefined);
    setSortBy('date');
    setAscending(false);

    onResetFilter();
    setModalVisible(false);
  };

  // Apply filter
  const handleApplyFilter = () => {
    const filter: TransactionFilterDto = {
      startDate: formatDateForBackend(startDate),
      endDate: formatDateForBackend(endDate),
      type,
      accountId,
      categoryId,
      priorityId,
      status,
      searchTerm,
      sortBy,
      ascending,
    };

    onApplyFilter(filter);
    setModalVisible(false);
  };

  // Transaction type options
  const typeOptions = [
    { label: t('common.all'), value: null },
    { label: t('transactions.expense'), value: TransactionType.Expense },
    { label: t('transactions.income'), value: TransactionType.Income },
    { label: t('transactions.transfer'), value: TransactionType.Transfer },
  ];

  // Status options
  const statusOptions = [
    { label: t('common.all'), value: null },
    { label: t('transactions.completed'), value: 'Completed' },
    { label: t('transactions.pending'), value: 'Pending' },
  ];

  // Sort options
  const sortOptions = [
    { label: t('transactions.sortByDate'), value: 'date' },
    { label: t('transactions.sortByAmount'), value: 'amount' },
    { label: t('transactions.sortByCategory'), value: 'categoryName' },
    { label: t('transactions.sortByAccount'), value: 'accountName' },
  ];

  // Order options
  const orderOptions = [
    { label: t('transactions.ascending'), value: true },
    { label: t('transactions.descending'), value: false },
  ];

  // Format options for dropdowns
  const accountOptions = [
    { label: t('common.all'), value: null },
    ...accounts.map(account => ({
      label: account.name,
      value: account.id,
    })),
  ];

  const categoryOptions = [
    { label: t('common.all'), value: null },
    ...categories.map(category => ({
      label: category.name,
      value: category.id,
    })),
  ];

  const priorityOptions = [
    { label: t('common.all'), value: null },
    ...priorities.map(priority => ({
      label: priority.name,
      value: priority.id,
    })),
  ];

  // Check if filter is active
  const isFilterActive = () => {
    return !!(
      startDate ||
      endDate ||
      type !== undefined ||
      accountId ||
      categoryId ||
      priorityId ||
      status ||
      searchTerm ||
      (sortBy && sortBy !== 'date') ||
      ascending
    );
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.filterButton,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
          },
          isFilterActive() && styles.activeFilterButton,
        ]}
        onPress={() => setModalVisible(true)}
      >
        <Ionicons
          name="filter-outline"
          size={18}
          color={isFilterActive() ? '#58A6FF' : isDarkMode ? '#8B949E' : '#6E7781'}
        />
        <Text
          style={[
            styles.filterButtonText,
            {
              color: isFilterActive()
                ? '#58A6FF'
                : isDarkMode
                  ? '#8B949E'
                  : '#6E7781',
            },
          ]}
        >
          {t('common.filter')}
        </Text>
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text
                style={[
                  styles.modalTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                ]}
              >
                {t('transactions.filterTransactions')}
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Ionicons
                  name="close-outline"
                  size={24}
                  color={isDarkMode ? '#8B949E' : '#6E7781'}
                />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              <DateRangeSelector
                startDate={startDate || null}
                endDate={endDate || null}
                onDateRangeChange={(start, end) => {
                  setStartDate(start || undefined);
                  setEndDate(end || undefined);
                }}
              />

              <SavedFiltersManager
                currentFilter={{
                  startDate: formatDateForBackend(startDate),
                  endDate: formatDateForBackend(endDate),
                  type,
                  accountId,
                  categoryId,
                  priorityId,
                  status,
                  searchTerm,
                  sortBy,
                  ascending,
                }}
                onFilterSelect={(filter) => {
                  setStartDate(parseDateFromBackend(filter.startDate));
                  setEndDate(parseDateFromBackend(filter.endDate));
                  setType(filter.type);
                  setAccountId(filter.accountId);
                  setCategoryId(filter.categoryId);
                  setPriorityId(filter.priorityId);
                  setStatus(filter.status);
                  setSearchTerm(filter.searchTerm);
                  setSortBy(filter.sortBy);
                  setAscending(filter.ascending || false);
                }}
              />

              <Dropdown
                label={t('transactions.type')}
                options={typeOptions}
                selectedValue={type ?? null}
                onValueChange={(value) => setType(typeof value === 'number' ? value as TransactionType : undefined)}
              />

              <Dropdown
                label={t('transactions.account')}
                options={accountOptions}
                selectedValue={accountId ?? null}
                onValueChange={(value) => setAccountId(typeof value === 'number' ? value : undefined)}
              />

              <Dropdown
                label={t('transactions.category')}
                options={categoryOptions}
                selectedValue={categoryId ?? null}
                onValueChange={(value) => setCategoryId(typeof value === 'number' ? value : undefined)}
              />

              <Dropdown
                label={t('transactions.priority')}
                options={priorityOptions}
                selectedValue={priorityId ?? null}
                onValueChange={(value) => setPriorityId(typeof value === 'number' ? value : undefined)}
              />

              <Dropdown
                label={t('transactions.status')}
                options={statusOptions}
                selectedValue={status ?? null}
                onValueChange={(value) => setStatus(typeof value === 'string' ? value : undefined)}
              />

              <FormInput
                label={t('transactions.search')}
                value={searchTerm || ''}
                onChangeText={(text) => setSearchTerm(text || undefined)}
                placeholder={t('transactions.searchPlaceholder')}
              />

              <View style={styles.sortContainer}>
                <View style={styles.sortDropdownContainer}>
                  <Dropdown
                    label={t('transactions.sortBy')}
                    options={sortOptions}
                    selectedValue={sortBy ?? null}
                    onValueChange={(value) => setSortBy(typeof value === 'string' ? value : undefined)}
                  />
                </View>
                <View style={styles.sortDropdownContainer}>
                  <Dropdown
                    label={t('transactions.order')}
                    options={orderOptions}
                    selectedValue={ascending}
                    onValueChange={(value) => setAscending(value as boolean)}
                  />
                </View>
              </View>
            </ScrollView>

            <View style={styles.modalFooter}>
              <Button
                title={t('common.reset')}
                onPress={handleResetFilter}
                variant="secondary"
                style={styles.footerButton}
              />
              <Button
                title={t('common.apply')}
                onPress={handleApplyFilter}
                style={styles.footerButton}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {},
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  activeFilterButton: {
    borderColor: '#58A6FF',
  },
  filterButtonText: {
    marginLeft: 4,
    fontSize: 14,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderWidth: 1,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalBody: {
    padding: 16,
  },
  dateRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  datePickerContainer: {
    width: '48%',
  },
  sortContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  sortDropdownContainer: {
    width: '48%',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#30363D',
  },
  footerButton: {
    flex: 1,
    marginHorizontal: 8,
  },
});

export default TransactionFilter;
