import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  useWindowDimensions,
  Animated,
  PanResponder,
  Platform
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import { formatDate, formatCurrency } from '../../utils/formatters';
import { Transaction, TransactionType, getTransactionTypeIcon, getTransactionTypeColor, getTransactionStatusColor } from '../../models/Transaction';
import SyncStatusBadge from './SyncStatusBadge';
import Card from '../ui/Card';

interface TransactionCardProps {
  transaction: Transaction;
  onPress?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onSync?: () => void;
  showActions?: boolean;
  showSyncStatus?: boolean;
  isSelected?: boolean;
  selectionMode?: boolean;
  onLongPress?: () => void;
}

// Original component implementation
const TransactionCardContent: React.FC<TransactionCardProps & { styles: any }> = ({
  transaction,
  onPress,
  onEdit,
  onDelete,
  onSync,
  showActions = true,
  showSyncStatus = true,
  isSelected = false,
  selectionMode = false,
  onLongPress,
  styles,
}) => {
  const { colors, isDarkMode } = useTheme();
  const { t } = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);
  const pan = useRef(new Animated.ValueXY()).current;
  const opacity = useRef(new Animated.Value(1)).current;

  const {
    // id is not used directly but kept for reference
    amount,
    description,
    date,
    type,
    accountName,
    toAccountName,
    categoryName,
    status,
    items,
    isSynced,
    syncStatus,
  } = transaction;

  // Convert string icon name to a valid Ionicons name
  const typeIcon = getTransactionTypeIcon(type) as any;
  const typeColor = getTransactionTypeColor(type);
  const statusColor = getTransactionStatusColor(status);

  const formattedDate = formatDate(new Date(date));
  const formattedAmount = formatCurrency(amount);

  // Get transaction title
  const getTransactionTitle = () => {
    if (description && description.trim().length > 0) {
      return description;
    }

    if (type === TransactionType.Transfer) {
      return t('transactions.transferBetweenAccounts', {
        from: accountName,
        to: toAccountName,
      });
    }

    return categoryName || t('transactions.uncategorized');
  };

  // Get transaction subtitle
  const getTransactionSubtitle = () => {
    if (type === TransactionType.Transfer) {
      return t('transactions.transfer');
    }

    return `${accountName} • ${categoryName || t('transactions.uncategorized')}`;
  };

  // Get item summary
  const getItemSummary = () => {
    if (!items || items.length === 0) {
      return null;
    }

    if (items.length === 1) {
      return t('transactions.oneItem');
    }

    return t('transactions.multipleItems', { count: items.length });
  };

  // Configure pan responder for swipe actions
  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (_, gestureState) => {
      return Math.abs(gestureState.dx) > 5;
    },
    onPanResponderGrant: () => {
      // Reset the pan value to avoid jumps
      pan.setValue({ x: 0, y: 0 });
    },
    onPanResponderMove: Animated.event([null, { dx: pan.x }], {
      useNativeDriver: false,
    }),
    onPanResponderRelease: (_, gestureState) => {
      pan.flattenOffset();
      const SWIPE_THRESHOLD = 120;

      if (gestureState.dx < -SWIPE_THRESHOLD) {
        // Swiped left (delete)
        Animated.timing(pan, {
          toValue: { x: -Dimensions.get('window').width, y: 0 },
          duration: 250,
          useNativeDriver: false,
        }).start(() => {
          if (onDelete) onDelete();
          // Reset position after animation
          Animated.timing(pan, {
            toValue: { x: 0, y: 0 },
            duration: 0,
            useNativeDriver: false,
          }).start();
        });
      } else if (gestureState.dx > SWIPE_THRESHOLD) {
        // Swiped right (edit)
        Animated.timing(pan, {
          toValue: { x: Dimensions.get('window').width, y: 0 },
          duration: 250,
          useNativeDriver: false,
        }).start(() => {
          if (onEdit) onEdit();
          // Reset position after animation
          Animated.timing(pan, {
            toValue: { x: 0, y: 0 },
            duration: 0,
            useNativeDriver: false,
          }).start();
        });
      } else {
        // Reset position
        Animated.spring(pan, {
          toValue: { x: 0, y: 0 },
          friction: 5,
          useNativeDriver: false,
        }).start();
      }
    },
  });

  // Calculate background colors for swipe actions
  const leftSwipeOpacity = pan.x.interpolate({
    inputRange: [0, 120],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  const rightSwipeOpacity = pan.x.interpolate({
    inputRange: [-120, 0],
    outputRange: [1, 0],
    extrapolate: 'clamp',
  });

  const handleCardPress = () => {
    setIsExpanded(!isExpanded);
    if (onPress) onPress();
  };

  return (
    <Animated.View style={[styles.container, { opacity }]}>
      {/* Swipe action backgrounds */}
      <View style={styles.swipeActionsContainer}>
        <Animated.View
          style={[
            styles.leftSwipeAction,
            {
              backgroundColor: colors.primary,
              opacity: leftSwipeOpacity,
            },
          ]}
        >
          <Ionicons name="create-outline" size={20} color="#FFFFFF" />
          <Text style={styles.swipeActionText}>{t('common.edit')}</Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.rightSwipeAction,
            {
              backgroundColor: colors.danger,
              opacity: rightSwipeOpacity,
            },
          ]}
        >
          <Ionicons name="trash-outline" size={20} color="#FFFFFF" />
          <Text style={styles.swipeActionText}>{t('common.delete')}</Text>
        </Animated.View>
      </View>

      {/* Transaction card */}
      <Animated.View
        style={{ transform: [{ translateX: pan.x }] }}
        {...panResponder.panHandlers}
      >
        <Card
          style={
            isSelected ?
            {
              ...styles.card,
              borderColor: colors.primary,
              borderWidth: 2,
            } :
            styles.card
          }
          onPress={handleCardPress}
          interactive
          variant={isSelected ? 'primary' : 'default'}
          elevation={isSelected ? 'md' : 'sm'}
        >
          <View style={styles.cardContent}>
            {/* Icon and category */}
            <View style={styles.leftContent}>
              <View
                style={[
                  styles.iconContainer,
                  { backgroundColor: typeColor },
                ]}
              >
                <Ionicons name={typeIcon} size={20} color="#FFFFFF" />
              </View>
              <View style={styles.categoryContainer}>
                <Text
                  style={[
                    styles.title,
                    { color: isDarkMode ? colors.text.dark.primary : colors.text.light.primary },
                  ]}
                  numberOfLines={1}
                >
                  {getTransactionTitle()}
                </Text>
                <Text
                  style={[
                    styles.subtitle,
                    { color: isDarkMode ? colors.text.dark.secondary : colors.text.light.secondary },
                  ]}
                  numberOfLines={1}
                >
                  {getTransactionSubtitle()}
                </Text>
              </View>
            </View>

            {/* Amount and date */}
            <View style={styles.rightContent}>
              <Text
                style={[
                  styles.amount,
                  {
                    color: type === TransactionType.Income
                      ? colors.semantic.income.light
                      : type === TransactionType.Expense
                        ? colors.semantic.expense.light
                        : colors.semantic.transfer.light,
                  },
                ]}
              >
                {type === TransactionType.Income ? '+' : type === TransactionType.Expense ? '-' : ''}
                {formattedAmount}
              </Text>
              <Text
                style={[
                  styles.date,
                  { color: isDarkMode ? colors.text.dark.secondary : colors.text.light.secondary },
                ]}
              >
                {formattedDate}
              </Text>
            </View>
          </View>

          {/* Expanded content */}
          {isExpanded && (
            <View style={styles.expandedContent}>
              <View
                style={[
                  styles.divider,
                  { backgroundColor: isDarkMode ? colors.border.dark.primary : colors.border.light.primary },
                ]}
              />

              {/* Status and tags */}
              <View style={styles.tagsContainer}>
                <View
                  style={[
                    styles.statusBadge,
                    { backgroundColor: statusColor + '20' }, // Add transparency
                  ]}
                >
                  <Text
                    style={[
                      styles.statusText,
                      { color: statusColor },
                    ]}
                  >
                    {status}
                  </Text>
                </View>

                {getItemSummary() && (
                  <View
                    style={[
                      styles.itemsBadge,
                      {
                        backgroundColor: isDarkMode ? colors.border.dark.primary : colors.border.light.primary,
                      },
                    ]}
                  >
                    <Text
                      style={[
                        styles.itemsText,
                        { color: isDarkMode ? colors.text.dark.secondary : colors.text.light.secondary },
                      ]}
                    >
                      {getItemSummary()}
                    </Text>
                  </View>
                )}

                {/* Sync status badge */}
                {showSyncStatus && (
                  <SyncStatusBadge
                    status={syncStatus}
                    onSync={onSync}
                    showSyncButton={!isSynced}
                  />
                )}
              </View>

              {/* Action buttons */}
              {showActions && (
                <View style={styles.actionButtons}>
                  {onEdit && (
                    <TouchableOpacity
                      style={[
                        styles.actionButton,
                        { backgroundColor: colors.primary },
                      ]}
                      onPress={onEdit}
                    >
                      <Ionicons name="create-outline" size={16} color="#FFFFFF" />
                      <Text style={styles.actionButtonText}>{t('common.edit')}</Text>
                    </TouchableOpacity>
                  )}

                  {onDelete && (
                    <TouchableOpacity
                      style={[
                        styles.actionButton,
                        { backgroundColor: colors.danger },
                      ]}
                      onPress={onDelete}
                    >
                      <Ionicons name="trash-outline" size={16} color="#FFFFFF" />
                      <Text style={styles.actionButtonText}>{t('common.delete')}</Text>
                    </TouchableOpacity>
                  )}
                </View>
              )}
            </View>
          )}
        </Card>
      </Animated.View>
    </Animated.View>
  );
};

// Create responsive styles based on screen width
const createStyles = (width: number) => {
  // Calculate responsive sizes
  const padding = width < 350 ? 8 : 12;
  const iconSize = width < 350 ? 32 : 40;
  const titleFontSize = width < 350 ? 14 : 16;
  const subtitleFontSize = width < 350 ? 12 : 14;
  const amountFontSize = width < 350 ? 14 : 16;
  const dateFontSize = width < 350 ? 10 : 12;
  const badgeFontSize = width < 350 ? 10 : 12;

  return StyleSheet.create({
    container: {
      marginBottom: 12,
      position: 'relative',
    },
    card: {
      marginBottom: 0,
    },
    cardContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    leftContent: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    iconContainer: {
      width: iconSize,
      height: iconSize,
      borderRadius: iconSize / 2,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    categoryContainer: {
      flex: 1,
    },
    title: {
      fontSize: titleFontSize,
      fontWeight: '500',
      marginBottom: 2,
    },
    subtitle: {
      fontSize: subtitleFontSize,
    },
    rightContent: {
      alignItems: 'flex-end',
      // Allow amount to wrap to next line on very narrow screens
      flexShrink: 0,
    },
    amount: {
      fontSize: amountFontSize,
      fontWeight: '600',
      marginBottom: 2,
    },
    date: {
      fontSize: dateFontSize,
    },
    expandedContent: {
      marginTop: 12,
    },
    divider: {
      height: 1,
      width: '100%',
      marginBottom: 12,
    },
    tagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginBottom: 12,
    },
    statusBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      marginRight: 8,
      marginBottom: 4,
    },
    statusText: {
      fontSize: badgeFontSize,
      fontWeight: '500',
    },
    itemsBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      marginBottom: 4,
      marginRight: 8,
    },
    itemsText: {
      fontSize: badgeFontSize,
    },
    actionButtons: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginTop: 8,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 6,
      paddingHorizontal: 12,
      borderRadius: 16,
      marginLeft: 8,
    },
    actionButtonText: {
      color: '#FFFFFF',
      fontSize: 14,
      fontWeight: '500',
      marginLeft: 4,
    },
    swipeActionsContainer: {
      ...StyleSheet.absoluteFillObject,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    leftSwipeAction: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'flex-end',
      paddingRight: 20,
      borderRadius: 12,
    },
    rightSwipeAction: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'flex-start',
      paddingLeft: 20,
      borderRadius: 12,
    },
    swipeActionText: {
      color: '#FFFFFF',
      fontSize: 14,
      fontWeight: '500',
      marginTop: 4,
    },
    // Keep these for backward compatibility
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
    },
    iconBackground: {
      width: iconSize,
      height: iconSize,
      borderRadius: iconSize / 2,
      alignItems: 'center',
      justifyContent: 'center',
    },
    titleContainer: {
      flex: 1,
      marginRight: 8,
    },
    amountContainer: {
      alignItems: 'flex-end',
      flexShrink: 0,
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: padding,
      flexWrap: 'wrap',
    },
    actionsContainer: {
      flexDirection: 'row',
      marginTop: 4,
    },
  });
};

// This section is replaced by the wrapper component at the end of the file

// Wrapper component that provides responsive styles
const TransactionCard: React.FC<TransactionCardProps> = (props) => {
  const { width } = useWindowDimensions();
  const styles = React.useMemo(() => createStyles(width), [width]);

  return <TransactionCardContent {...props} styles={styles} />;
};

export default TransactionCard;
