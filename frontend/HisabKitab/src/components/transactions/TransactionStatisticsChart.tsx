import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import { BarChart, PieChart } from 'react-native-chart-kit';
import { formatCurrency } from '../../utils/formatters';
import { TransactionType } from '../../models/Transaction';

interface CategorySummary {
  categoryId: number;
  categoryName: string;
  amount: number;
  type: TransactionType;
  color?: string;
}

interface TransactionStatisticsChartProps {
  income: number;
  expense: number;
  balance: number;
  startDate: Date;
  endDate: Date;
  categorySummaries?: CategorySummary[];
  onPeriodChange?: (period: string) => void;
}

const TransactionStatisticsChart: React.FC<TransactionStatisticsChartProps> = ({
  income,
  expense,
  balance,
  startDate,
  endDate,
  categorySummaries = [],
  onPeriodChange,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const [chartType, setChartType] = useState<'bar' | 'pie'>('bar');

  // These variables will be used in a future enhancement to display date range
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const dateRangeString = startDate && endDate ?
    `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}` :
    t('dateRange.allTime');
  const [showType, setShowType] = useState<'income' | 'expense'>('expense');

  const screenWidth = Dimensions.get('window').width - 32; // Padding

  // Chart colors
  const chartColors = [
    '#3FB950', // Green
    '#F85149', // Red
    '#F0883E', // Orange
    '#58A6FF', // Blue
    '#8957E5', // Purple
    '#EC6547', // Coral
    '#2EA043', // Dark Green
    '#F97583', // Pink
    '#79C0FF', // Light Blue
    '#D2A8FF', // Light Purple
  ];

  // Assign colors to categories if not already assigned
  const categoriesWithColors = categorySummaries.map((cat, index) => ({
    ...cat,
    color: cat.color || chartColors[index % chartColors.length],
  }));

  // Filter categories by type
  const filteredCategories = categoriesWithColors.filter(
    cat => cat.type === (showType === 'income' ? TransactionType.Income : TransactionType.Expense)
  );

  // Sort categories by amount (descending)
  const sortedCategories = [...filteredCategories].sort((a, b) => b.amount - a.amount);

  // Prepare data for bar chart
  const barData = {
    labels: [t('transactions.income'), t('transactions.expense'), t('transactions.balance')],
    datasets: [
      {
        data: [income, expense, balance],
        colors: [
          () => '#3FB950', // Green for income
          () => '#F85149', // Red for expense
          () => (balance >= 0 ? '#3FB950' : '#F85149'), // Green/Red for balance
        ],
      },
    ],
  };

  // Prepare data for pie chart
  const pieData = sortedCategories.map(cat => ({
    name: cat.categoryName,
    amount: cat.amount,
    color: cat.color,
    legendFontColor: isDarkMode ? '#FFFFFF' : '#24292E',
    legendFontSize: 12,
  }));

  // Chart configuration
  const chartConfig = {
    backgroundGradientFrom: isDarkMode ? '#0D1117' : '#FFFFFF',
    backgroundGradientTo: isDarkMode ? '#0D1117' : '#FFFFFF',
    decimalPlaces: 0,
    color: (opacity = 1) => isDarkMode ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
    labelColor: (opacity = 1) => isDarkMode ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
    },
    formatYLabel: (value: string) => {
      const num = parseFloat(value);
      if (num >= 1000000) {
        return `${(num / 1000000).toFixed(1)}M`;
      }
      if (num >= 1000) {
        return `${(num / 1000).toFixed(1)}K`;
      }
      return value;
    },
  };

  // Period options
  const periodOptions = [
    { id: 'week', label: t('dateRange.thisWeek') },
    { id: 'month', label: t('dateRange.thisMonth') },
    { id: 'year', label: t('dateRange.thisYear') },
    { id: 'custom', label: t('dateRange.custom') },
  ];

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text
          style={[
            styles.title,
            { color: isDarkMode ? '#FFFFFF' : '#24292E' },
          ]}
        >
          {t('transactions.statistics')}
        </Text>

        {onPeriodChange && (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.periodSelector}
          >
            {periodOptions.map(period => (
              <TouchableOpacity
                key={period.id}
                style={[
                  styles.periodOption,
                  {
                    backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                    borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                  },
                ]}
                onPress={() => onPeriodChange(period.id)}
              >
                <Text
                  style={[
                    styles.periodText,
                    { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                  ]}
                >
                  {period.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        )}
      </View>

      <View style={styles.summaryContainer}>
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {t('transactions.income')}
          </Text>
          <Text style={[styles.summaryValue, { color: '#3FB950' }]}>
            {formatCurrency(income)}
          </Text>
        </View>

        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {t('transactions.expense')}
          </Text>
          <Text style={[styles.summaryValue, { color: '#F85149' }]}>
            {formatCurrency(expense)}
          </Text>
        </View>

        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {t('transactions.balance')}
          </Text>
          <Text
            style={[
              styles.summaryValue,
              {
                color:
                  balance > 0
                    ? '#3FB950'
                    : balance < 0
                      ? '#F85149'
                      : isDarkMode
                        ? '#FFFFFF'
                        : '#24292E',
              },
            ]}
          >
            {formatCurrency(balance)}
          </Text>
        </View>
      </View>

      <View style={styles.chartTypeSelector}>
        <TouchableOpacity
          style={[
            styles.chartTypeButton,
            chartType === 'bar' && {
              backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
            },
          ]}
          onPress={() => setChartType('bar')}
        >
          <Ionicons
            name="bar-chart-outline"
            size={18}
            color={chartType === 'bar' ? '#FFFFFF' : isDarkMode ? '#8B949E' : '#6E7781'}
          />
          <Text
            style={[
              styles.chartTypeText,
              {
                color: chartType === 'bar'
                  ? '#FFFFFF'
                  : isDarkMode
                    ? '#8B949E'
                    : '#6E7781',
              },
            ]}
          >
            {t('charts.barChart')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.chartTypeButton,
            chartType === 'pie' && {
              backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
            },
          ]}
          onPress={() => setChartType('pie')}
        >
          <Ionicons
            name="pie-chart-outline"
            size={18}
            color={chartType === 'pie' ? '#FFFFFF' : isDarkMode ? '#8B949E' : '#6E7781'}
          />
          <Text
            style={[
              styles.chartTypeText,
              {
                color: chartType === 'pie'
                  ? '#FFFFFF'
                  : isDarkMode
                    ? '#8B949E'
                    : '#6E7781',
              },
            ]}
          >
            {t('charts.pieChart')}
          </Text>
        </TouchableOpacity>
      </View>

      {chartType === 'pie' && (
        <View style={styles.typeSelector}>
          <TouchableOpacity
            style={[
              styles.typeButton,
              showType === 'expense' && {
                backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
              },
            ]}
            onPress={() => setShowType('expense')}
          >
            <Text
              style={[
                styles.typeText,
                {
                  color: showType === 'expense'
                    ? '#FFFFFF'
                    : isDarkMode
                      ? '#8B949E'
                      : '#6E7781',
                },
              ]}
            >
              {t('transactions.expense')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.typeButton,
              showType === 'income' && {
                backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
              },
            ]}
            onPress={() => setShowType('income')}
          >
            <Text
              style={[
                styles.typeText,
                {
                  color: showType === 'income'
                    ? '#FFFFFF'
                    : isDarkMode
                      ? '#8B949E'
                      : '#6E7781',
                },
              ]}
            >
              {t('transactions.income')}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      <View style={styles.chartContainer}>
        {chartType === 'bar' ? (
          <BarChart
            data={barData}
            width={screenWidth}
            height={220}
            chartConfig={chartConfig}
            style={styles.chart}
            fromZero
            showValuesOnTopOfBars
            withInnerLines={false}
            yAxisLabel=""
            yAxisSuffix=""
          />
        ) : (
          pieData.length > 0 ? (
            <PieChart
              data={pieData}
              width={screenWidth}
              height={220}
              chartConfig={chartConfig}
              accessor="amount"
              backgroundColor="transparent"
              paddingLeft="15"
              absolute
            />
          ) : (
            <View style={styles.noDataContainer}>
              <Ionicons
                name="pie-chart-outline"
                size={48}
                color={isDarkMode ? '#30363D' : '#D0D7DE'}
              />
              <Text
                style={[
                  styles.noDataText,
                  { color: isDarkMode ? '#8B949E' : '#6E7781' },
                ]}
              >
                {t('charts.noData')}
              </Text>
            </View>
          )
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'column',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  periodSelector: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  periodOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1,
  },
  periodText: {
    fontSize: 12,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  chartTypeSelector: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  chartTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  chartTypeText: {
    fontSize: 12,
    marginLeft: 4,
  },
  typeSelector: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  typeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  typeText: {
    fontSize: 12,
  },
  chartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 220,
  },
  chart: {
    borderRadius: 8,
  },
  noDataContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  noDataText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
});

export default TransactionStatisticsChart;
