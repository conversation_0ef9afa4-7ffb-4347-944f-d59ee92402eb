import React, { useState } from 'react';
import { View, Text, StyleSheet, Modal, ScrollView, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import Button from '../ui/Button';
import { SyncConflict, ConflictResolutionStrategy } from '../../utils/syncUtils';
import { formatDate, formatCurrency } from '../../utils/formatters';
import { Transaction, TransactionType } from '../../models/Transaction';
import SafeTranslation from '../ui/SafeTranslation';

interface SyncConflictResolverProps {
  conflicts: SyncConflict[];
  visible: boolean;
  onClose: () => void;
  onResolveAll: (strategy: ConflictResolutionStrategy) => void;
  onResolveOne: (conflict: SyncConflict, strategy: ConflictResolutionStrategy) => void;
}

const SyncConflictResolver: React.FC<SyncConflictResolverProps> = ({
  conflicts,
  visible,
  onClose,
  onResolveAll,
  onResolveOne,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const [expandedConflict, setExpandedConflict] = useState<number | null>(null);

  if (conflicts.length === 0) {
    return null;
  }

  const toggleExpand = (index: number) => {
    setExpandedConflict(expandedConflict === index ? null : index);
  };

  const renderTransactionDiff = (local: Transaction, remote: Transaction) => {
    const diffs = [];

    // Check type
    if (local.type !== remote.type) {
      diffs.push(
        <View key="type" style={styles.diffRow}>
          <Text style={[styles.diffLabel, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
            <SafeTranslation i18nKey="transactions.type" fallback="Type" />:
          </Text>
          <View style={styles.diffValues}>
            <View style={styles.diffValueContainer}>
              <View style={styles.diffLabelContainer}>
                <Text style={[styles.diffValueLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  <SafeTranslation i18nKey="sync.localVersion" fallback="Local" />:
                </Text>
              </View>
              <Text style={[styles.localValue, { color: '#3FB950' }]}>
                <SafeTranslation
                  i18nKey={`transactions.${local.type === 0 ? 'income' : local.type === 1 ? 'expense' : 'transfer'}`}
                  fallback={local.type === 0 ? 'Income' : local.type === 1 ? 'Expense' : 'Transfer'}
                />
              </Text>
            </View>
            <View style={styles.diffValueContainer}>
              <View style={styles.diffLabelContainer}>
                <Text style={[styles.diffValueLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  <SafeTranslation i18nKey="sync.remoteVersion" fallback="Remote" />:
                </Text>
              </View>
              <Text style={[styles.remoteValue, { color: '#F85149' }]}>
                <SafeTranslation
                  i18nKey={`transactions.${remote.type === 0 ? 'income' : remote.type === 1 ? 'expense' : 'transfer'}`}
                  fallback={remote.type === 0 ? 'Income' : remote.type === 1 ? 'Expense' : 'Transfer'}
                />
              </Text>
            </View>
          </View>
        </View>
      );
    }

    // Check amount
    if (local.amount !== remote.amount) {
      diffs.push(
        <View key="amount" style={styles.diffRow}>
          <Text style={[styles.diffLabel, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
            <SafeTranslation i18nKey="transactions.amount" fallback="Amount" />:
          </Text>
          <View style={styles.diffValues}>
            <View style={styles.diffValueContainer}>
              <View style={styles.diffLabelContainer}>
                <Text style={[styles.diffValueLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  <SafeTranslation i18nKey="sync.localVersion" fallback="Local" />:
                </Text>
              </View>
              <Text style={[styles.localValue, { color: '#3FB950' }]}>
                {formatCurrency(local.amount)}
              </Text>
            </View>
            <View style={styles.diffValueContainer}>
              <View style={styles.diffLabelContainer}>
                <Text style={[styles.diffValueLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  <SafeTranslation i18nKey="sync.remoteVersion" fallback="Remote" />:
                </Text>
              </View>
              <Text style={[styles.remoteValue, { color: '#F85149' }]}>
                {formatCurrency(remote.amount)}
              </Text>
            </View>
          </View>
        </View>
      );
    }

    // Check description
    if (local.description !== remote.description) {
      diffs.push(
        <View key="description" style={styles.diffRow}>
          <Text style={[styles.diffLabel, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
            <SafeTranslation i18nKey="transactions.description" fallback="Description" />:
          </Text>
          <View style={styles.diffValues}>
            <View style={styles.diffValueContainer}>
              <View style={styles.diffLabelContainer}>
                <Text style={[styles.diffValueLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  <SafeTranslation i18nKey="sync.localVersion" fallback="Local" />:
                </Text>
              </View>
              <Text style={[styles.localValue, { color: '#3FB950' }]} numberOfLines={2}>
                {local.description || <SafeTranslation i18nKey="common.none" fallback="None" />}
              </Text>
            </View>
            <View style={styles.diffValueContainer}>
              <View style={styles.diffLabelContainer}>
                <Text style={[styles.diffValueLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  <SafeTranslation i18nKey="sync.remoteVersion" fallback="Remote" />:
                </Text>
              </View>
              <Text style={[styles.remoteValue, { color: '#F85149' }]} numberOfLines={2}>
                {remote.description || <SafeTranslation i18nKey="common.none" fallback="None" />}
              </Text>
            </View>
          </View>
        </View>
      );
    }

    // Check date
    const localDate = new Date(local.date).getTime();
    const remoteDate = new Date(remote.date).getTime();
    if (localDate !== remoteDate) {
      diffs.push(
        <View key="date" style={styles.diffRow}>
          <Text style={[styles.diffLabel, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
            <SafeTranslation i18nKey="transactions.date" fallback="Date" />:
          </Text>
          <View style={styles.diffValues}>
            <View style={styles.diffValueContainer}>
              <View style={styles.diffLabelContainer}>
                <Text style={[styles.diffValueLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  <SafeTranslation i18nKey="sync.localVersion" fallback="Local" />:
                </Text>
              </View>
              <Text style={[styles.localValue, { color: '#3FB950' }]}>
                {formatDate(new Date(local.date))}
              </Text>
            </View>
            <View style={styles.diffValueContainer}>
              <View style={styles.diffLabelContainer}>
                <Text style={[styles.diffValueLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  <SafeTranslation i18nKey="sync.remoteVersion" fallback="Remote" />:
                </Text>
              </View>
              <Text style={[styles.remoteValue, { color: '#F85149' }]}>
                {formatDate(new Date(remote.date))}
              </Text>
            </View>
          </View>
        </View>
      );
    }

    // Check category
    if (local.categoryId !== remote.categoryId || local.categoryName !== remote.categoryName) {
      diffs.push(
        <View key="category" style={styles.diffRow}>
          <Text style={[styles.diffLabel, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
            <SafeTranslation i18nKey="transactions.category" fallback="Category" />:
          </Text>
          <View style={styles.diffValues}>
            <View style={styles.diffValueContainer}>
              <View style={styles.diffLabelContainer}>
                <Text style={[styles.diffValueLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  <SafeTranslation i18nKey="sync.localVersion" fallback="Local" />:
                </Text>
              </View>
              <Text style={[styles.localValue, { color: '#3FB950' }]}>
                {local.categoryName || <SafeTranslation i18nKey="common.none" fallback="None" />}
              </Text>
            </View>
            <View style={styles.diffValueContainer}>
              <View style={styles.diffLabelContainer}>
                <Text style={[styles.diffValueLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  <SafeTranslation i18nKey="sync.remoteVersion" fallback="Remote" />:
                </Text>
              </View>
              <Text style={[styles.remoteValue, { color: '#F85149' }]}>
                {remote.categoryName || <SafeTranslation i18nKey="common.none" fallback="None" />}
              </Text>
            </View>
          </View>
        </View>
      );
    }

    // Check account
    if (local.accountId !== remote.accountId || local.accountName !== remote.accountName) {
      diffs.push(
        <View key="account" style={styles.diffRow}>
          <Text style={[styles.diffLabel, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
            <SafeTranslation i18nKey="transactions.account" fallback="Account" />:
          </Text>
          <View style={styles.diffValues}>
            <View style={styles.diffValueContainer}>
              <View style={styles.diffLabelContainer}>
                <Text style={[styles.diffValueLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  <SafeTranslation i18nKey="sync.localVersion" fallback="Local" />:
                </Text>
              </View>
              <Text style={[styles.localValue, { color: '#3FB950' }]}>
                {local.accountName || <SafeTranslation i18nKey="common.none" fallback="None" />}
              </Text>
            </View>
            <View style={styles.diffValueContainer}>
              <View style={styles.diffLabelContainer}>
                <Text style={[styles.diffValueLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  <SafeTranslation i18nKey="sync.remoteVersion" fallback="Remote" />:
                </Text>
              </View>
              <Text style={[styles.remoteValue, { color: '#F85149' }]}>
                {remote.accountName || <SafeTranslation i18nKey="common.none" fallback="None" />}
              </Text>
            </View>
          </View>
        </View>
      );
    }

    return diffs.length > 0 ? (
      <View style={styles.diffContainer}>{diffs}</View>
    ) : (
      <Text style={[styles.noDiffText, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
        <SafeTranslation i18nKey="sync.noVisibleDifferences" fallback="No visible differences" />
      </Text>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View
          style={[
            styles.modalContent,
            {
              backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
            },
          ]}
        >
          <View style={styles.modalHeader}>
            <Text
              style={[
                styles.modalTitle,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' },
              ]}
            >
              {t('sync.conflictsDetected', { count: conflicts.length })}
            </Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons
                name="close-outline"
                size={24}
                color={isDarkMode ? '#8B949E' : '#6E7781'}
              />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalBody}>
            <Text
              style={[
                styles.modalDescription,
                { color: isDarkMode ? '#8B949E' : '#6E7781' },
              ]}
            >
              {t('sync.conflictsDescription')}
            </Text>

            <View style={styles.resolveAllContainer}>
              <Text
                style={[
                  styles.resolveAllTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                ]}
              >
                {t('sync.resolveAll')}:
              </Text>
              <View style={styles.resolveAllButtons}>
                <Button
                  title={t('sync.keepLocal')}
                  onPress={() => onResolveAll(ConflictResolutionStrategy.KeepLocal)}
                  variant="secondary"
                  size="small"
                  style={styles.resolveButton}
                />
                <Button
                  title={t('sync.keepRemote')}
                  onPress={() => onResolveAll(ConflictResolutionStrategy.KeepRemote)}
                  variant="secondary"
                  size="small"
                  style={styles.resolveButton}
                />
                <Button
                  title={t('sync.keepNewer')}
                  onPress={() => onResolveAll(ConflictResolutionStrategy.MergeKeepNewer)}
                  variant="primary"
                  size="small"
                  style={styles.resolveButton}
                />
              </View>
            </View>

            <View style={styles.conflictsList}>
              {conflicts.map((conflict, index) => (
                <View
                  key={`conflict-${index}`}
                  style={[
                    styles.conflictItem,
                    {
                      backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                      borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                    },
                  ]}
                >
                  <TouchableOpacity
                    style={styles.conflictHeader}
                    onPress={() => toggleExpand(index)}
                  >
                    <View style={styles.conflictTitleContainer}>
                      <Text
                        style={[
                          styles.conflictTitle,
                          { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                        ]}
                        numberOfLines={1}
                      >
                        {conflict.localTransaction.description ||
                         conflict.localTransaction.categoryName ||
                         t('transactions.transaction')}
                      </Text>
                      <Text
                        style={[
                          styles.conflictSubtitle,
                          { color: isDarkMode ? '#8B949E' : '#6E7781' },
                        ]}
                      >
                        {formatDate(new Date(conflict.localTransaction.date))} •
                        {formatCurrency(conflict.localTransaction.amount)}
                      </Text>
                    </View>
                    <Ionicons
                      name={expandedConflict === index ? 'chevron-up' : 'chevron-down'}
                      size={20}
                      color={isDarkMode ? '#8B949E' : '#6E7781'}
                    />
                  </TouchableOpacity>

                  {expandedConflict === index && (
                    <View style={styles.conflictDetails}>
                      <View style={styles.diffHeader}>
                        <Text
                          style={[
                            styles.diffHeaderText,
                            { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                          ]}
                        >
                          {t('sync.differences')}:
                        </Text>
                      </View>

                      {renderTransactionDiff(
                        conflict.localTransaction,
                        conflict.remoteTransaction
                      )}

                      <View style={styles.resolveContainer}>
                        <Button
                          title={t('sync.keepLocal')}
                          onPress={() => onResolveOne(conflict, ConflictResolutionStrategy.KeepLocal)}
                          variant="secondary"
                          size="small"
                          style={styles.resolveButton}
                        />
                        <Button
                          title={t('sync.keepRemote')}
                          onPress={() => onResolveOne(conflict, ConflictResolutionStrategy.KeepRemote)}
                          variant="secondary"
                          size="small"
                          style={styles.resolveButton}
                        />
                        <Button
                          title={t('sync.keepNewer')}
                          onPress={() => onResolveOne(conflict, ConflictResolutionStrategy.MergeKeepNewer)}
                          variant="primary"
                          size="small"
                          style={styles.resolveButton}
                        />
                      </View>
                    </View>
                  )}
                </View>
              ))}
            </View>
          </ScrollView>

          <View style={styles.modalFooter}>
            <Button
              title={t('common.close')}
              onPress={onClose}
              variant="secondary"
              fullWidth
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderWidth: 1,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalBody: {
    padding: 16,
  },
  modalDescription: {
    fontSize: 14,
    marginBottom: 16,
  },
  resolveAllContainer: {
    marginBottom: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  resolveAllTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  resolveAllButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  conflictsList: {
    marginBottom: 16,
  },
  conflictItem: {
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 12,
    overflow: 'hidden',
  },
  conflictHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
  },
  conflictTitleContainer: {
    flex: 1,
    marginRight: 8,
  },
  conflictTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  conflictSubtitle: {
    fontSize: 14,
  },
  conflictDetails: {
    padding: 12,
    borderTopWidth: 1,
    borderTopColor: '#30363D',
  },
  diffHeader: {
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  diffHeaderText: {
    fontSize: 15,
    fontWeight: '500',
  },
  diffContainer: {
    marginBottom: 16,
  },
  diffRow: {
    marginBottom: 16,
    borderLeftWidth: 3,
    borderLeftColor: '#58A6FF',
    paddingLeft: 8,
  },
  diffLabel: {
    fontSize: 15,
    fontWeight: '500',
    marginBottom: 8,
  },
  diffValues: {
    marginLeft: 8,
  },
  diffValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
    padding: 8,
    borderRadius: 4,
  },
  diffLabelContainer: {
    width: 60,
  },
  diffValueLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  localValue: {
    fontSize: 14,
    flex: 1,
  },
  remoteValue: {
    fontSize: 14,
    flex: 1,
  },
  noDiffText: {
    fontSize: 14,
    fontStyle: 'italic',
    marginBottom: 16,
    textAlign: 'center',
    padding: 8,
  },
  resolveContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
    padding: 8,
    borderRadius: 8,
    marginTop: 8,
  },
  resolveButton: {
    marginRight: 8,
    marginBottom: 8,
  },
  modalFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#30363D',
  },
});

export default SyncConflictResolver;
