import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';

interface ApprovalRequestCardProps {
  id: number;
  requesterName: string;
  amount: number;
  description: string;
  categoryName: string;
  status: string;
  requestDate: string;
  responseDate?: string;
  rejectionReason?: string;
  onPress?: () => void;
  onApprove?: () => void;
  onReject?: () => void;
}

const ApprovalRequestCard: React.FC<ApprovalRequestCardProps> = ({
  id,
  requesterName,
  amount,
  description,
  categoryName,
  status,
  requestDate,
  responseDate,
  rejectionReason,
  onPress,
  onApprove,
  onReject,
}) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  // Format dates
  const formattedRequestDate = format(new Date(requestDate), 'MMM d, yyyy');
  const formattedResponseDate = responseDate
    ? format(new Date(responseDate), 'MMM d, yyyy')
    : null;

  // Get status color and icon
  const getStatusInfo = () => {
    switch (status.toLowerCase()) {
      case 'pending':
        return {
          color: '#F59E0B',
          icon: 'time-outline',
          text: t('approval.status.pending'),
        };
      case 'approved':
        return {
          color: '#10B981',
          icon: 'checkmark-circle-outline',
          text: t('approval.status.approved'),
        };
      case 'rejected':
        return {
          color: '#EF4444',
          icon: 'close-circle-outline',
          text: t('approval.status.rejected'),
        };
      default:
        return {
          color: '#9CA3AF',
          icon: 'help-circle-outline',
          text: status,
        };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' },
      ]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.header}>
        <View style={styles.requesterInfo}>
          <Text
            style={[
              styles.requesterName,
              { color: isDarkMode ? '#FFFFFF' : '#111827' },
            ]}
          >
            {requesterName}
          </Text>
          <View style={styles.statusContainer}>
            <Ionicons name={statusInfo.icon as any} size={14} color={statusInfo.color} />
            <Text style={[styles.statusText, { color: statusInfo.color }]}>
              {statusInfo.text}
            </Text>
          </View>
        </View>
        <Text
          style={[
            styles.amount,
            { color: isDarkMode ? '#FFFFFF' : '#111827' },
          ]}
        >
          {amount.toFixed(2)}
        </Text>
      </View>

      <Text
        style={[
          styles.description,
          { color: isDarkMode ? '#D1D5DB' : '#4B5563' },
        ]}
        numberOfLines={2}
      >
        {description}
      </Text>

      <View style={styles.detailsRow}>
        <Text
          style={[
            styles.detailLabel,
            { color: isDarkMode ? '#9CA3AF' : '#6B7280' },
          ]}
        >
          {t('approval.category')}:
        </Text>
        <Text
          style={[
            styles.detailValue,
            { color: isDarkMode ? '#D1D5DB' : '#4B5563' },
          ]}
        >
          {categoryName}
        </Text>
      </View>

      <View style={styles.detailsRow}>
        <Text
          style={[
            styles.detailLabel,
            { color: isDarkMode ? '#9CA3AF' : '#6B7280' },
          ]}
        >
          {t('approval.requestDate')}:
        </Text>
        <Text
          style={[
            styles.detailValue,
            { color: isDarkMode ? '#D1D5DB' : '#4B5563' },
          ]}
        >
          {formattedRequestDate}
        </Text>
      </View>

      {formattedResponseDate && (
        <View style={styles.detailsRow}>
          <Text
            style={[
              styles.detailLabel,
              { color: isDarkMode ? '#9CA3AF' : '#6B7280' },
            ]}
          >
            {t('approval.responseDate')}:
          </Text>
          <Text
            style={[
              styles.detailValue,
              { color: isDarkMode ? '#D1D5DB' : '#4B5563' },
            ]}
          >
            {formattedResponseDate}
          </Text>
        </View>
      )}

      {rejectionReason && (
        <View style={styles.rejectionContainer}>
          <Text
            style={[
              styles.rejectionLabel,
              { color: isDarkMode ? '#9CA3AF' : '#6B7280' },
            ]}
          >
            {t('approval.rejectionReason')}:
          </Text>
          <Text
            style={[
              styles.rejectionReason,
              { color: isDarkMode ? '#F87171' : '#EF4444' },
            ]}
          >
            {rejectionReason}
          </Text>
        </View>
      )}

      {status.toLowerCase() === 'pending' && (onApprove || onReject) && (
        <View style={styles.actionsContainer}>
          {onReject && (
            <TouchableOpacity
              style={[
                styles.actionButton,
                styles.rejectButton,
                { backgroundColor: isDarkMode ? '#4B5563' : '#F3F4F6' },
              ]}
              onPress={onReject}
            >
              <Text
                style={[
                  styles.actionButtonText,
                  { color: isDarkMode ? '#F87171' : '#EF4444' },
                ]}
              >
                {t('approval.reject')}
              </Text>
            </TouchableOpacity>
          )}
          {onApprove && (
            <TouchableOpacity
              style={[
                styles.actionButton,
                styles.approveButton,
                { backgroundColor: '#10B981' },
              ]}
              onPress={onApprove}
            >
              <Text style={[styles.actionButtonText, { color: '#FFFFFF' }]}>
                {t('approval.approve')}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  requesterInfo: {
    flex: 1,
  },
  requesterName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    marginLeft: 4,
  },
  amount: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  description: {
    fontSize: 14,
    marginBottom: 12,
  },
  detailsRow: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  detailLabel: {
    fontSize: 12,
    marginRight: 4,
  },
  detailValue: {
    fontSize: 12,
    fontWeight: '500',
  },
  rejectionContainer: {
    marginTop: 8,
  },
  rejectionLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  rejectionReason: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 12,
  },
  actionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginLeft: 8,
  },
  rejectButton: {
    borderWidth: 1,
    borderColor: '#EF4444',
  },
  approveButton: {},
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default ApprovalRequestCard;
