import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

interface DropdownOption {
  label: string;
  value: string | number | boolean | null;
  type?: 'string' | 'number' | 'boolean' | 'null';
}

interface DropdownProps {
  label?: string;
  options: DropdownOption[];
  selectedValue: string | number | boolean | null;
  onValueChange: (value: string | number | boolean | null) => void;
  placeholder?: string;
  error?: string;
  touched?: boolean;
  valueType?: 'string' | 'number' | 'boolean' | 'null';
  required?: boolean;
  validateOnBlur?: boolean;
  onValidate?: (value: string | number | boolean | null) => string | undefined;
  onBlur?: () => void;
}

const Dropdown: React.FC<DropdownProps> = ({
  label,
  options,
  selectedValue,
  onValueChange,
  placeholder = 'Select an option',
  error,
  touched,
  valueType,
  required = false,
  validateOnBlur = true,
  onValidate,
  onBlur,
}) => {
  const { isDarkMode } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const [localTouched, setLocalTouched] = useState(false);
  const [localError, setLocalError] = useState<string | undefined>(undefined);

  const selectedOption = options.find(option => option.value === selectedValue);

  // Use either provided error/touched or local state
  const effectiveTouched = touched !== undefined ? touched : localTouched;
  const effectiveError = error !== undefined ? error : localError;

  const showError = effectiveError && effectiveTouched;

  // Convert value based on type
  const convertValue = (value: string | number | boolean | null, type?: string): string | number | boolean | null => {
    if (value === null) return null;

    // Use option's type if provided, otherwise use the dropdown's valueType
    const valueTypeToUse = type || valueType;

    if (!valueTypeToUse) return value;

    switch (valueTypeToUse) {
      case 'number':
        return value === null ? null : Number(value);
      case 'boolean':
        if (typeof value === 'string') {
          return value === 'true';
        }
        return Boolean(value);
      case 'null':
        return value === 'null' ? null : value;
      default:
        return String(value);
    }
  };

  const handleSelect = (value: string | number | boolean | null, type?: string) => {
    const convertedValue = convertValue(value, type);
    onValueChange(convertedValue);
    setModalVisible(false);

    // Validate on selection
    if (validateOnBlur && onValidate) {
      setLocalError(onValidate(convertedValue));
    }
    setLocalTouched(true);

    // Call onBlur if provided
    if (onBlur) {
      onBlur();
    }
  };

  return (
    <View style={styles.container}>
      {label && (
        <View style={styles.labelContainer}>
          <Text style={[
            styles.label,
            { color: isDarkMode ? '#8B949E' : '#6E7781' }
          ]}>
            {label}
          </Text>
          {required && (
            <Text style={styles.requiredIndicator}>*</Text>
          )}
        </View>
      )}

      <TouchableOpacity
        style={[
          styles.dropdownButton,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            borderColor: showError
              ? '#F85149'
              : required && !effectiveTouched
                ? '#F0883E' // Orange for required but not touched
                : isDarkMode
                  ? '#30363D'
                  : '#D0D7DE'
          }
        ]}
        onPress={() => {
          setModalVisible(true);
          setLocalTouched(true);
        }}
      >
        <Text
          style={[
            styles.selectedText,
            {
              color: selectedOption
                ? (isDarkMode ? '#FFFFFF' : '#24292E')
                : (isDarkMode ? '#8B949E' : '#6E7781')
            }
          ]}
        >
          {selectedOption ? selectedOption.label : placeholder}
        </Text>
        <Ionicons
          name="chevron-down"
          size={20}
          color={isDarkMode ? '#8B949E' : '#6E7781'}
        />
      </TouchableOpacity>

      {showError && (
        <Text style={styles.errorText}>{error}</Text>
      )}

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setModalVisible(false)}
        >
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
          >
            <FlatList
              data={options}
              keyExtractor={(item) => item.value !== null ? item.value.toString() : 'null'}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.optionItem,
                    item.value === selectedValue && {
                      backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                    },
                  ]}
                  onPress={() => handleSelect(item.value, item.type)}
                >
                  <Text
                    style={[
                      styles.optionText,
                      { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                      item.value === selectedValue && {
                        fontWeight: 'bold',
                        color: isDarkMode ? '#58A6FF' : '#0366D6',
                      },
                    ]}
                  >
                    {item.label}
                  </Text>
                  {item.value === selectedValue && (
                    <Ionicons
                      name="checkmark"
                      size={20}
                      color={isDarkMode ? '#58A6FF' : '#0366D6'}
                    />
                  )}
                </TouchableOpacity>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    width: '100%',
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
  },
  requiredIndicator: {
    color: '#F85149',
    fontSize: 16,
    marginLeft: 4,
  },
  dropdownButton: {
    height: 48,
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectedText: {
    fontSize: 16,
  },
  errorText: {
    color: '#F85149',
    fontSize: 14,
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    maxHeight: '60%',
    borderRadius: 12,
    borderWidth: 1,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  optionText: {
    fontSize: 16,
  },
});

export default Dropdown;
