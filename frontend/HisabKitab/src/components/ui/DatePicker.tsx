import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Platform
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import Button from './Button';
import { formatDate } from '../../utils/formatters';

interface DatePickerProps {
  label?: string;
  selectedDate?: Date;
  onDateChange: (date: Date | undefined) => void;
  error?: string;
  touched?: boolean;
  placeholder?: string;
  minimumDate?: Date;
  maximumDate?: Date;
  mode?: 'date' | 'time' | 'datetime';
  format?: string;
  required?: boolean;
  onBlur?: () => void;
  // Alias for selectedDate to support both naming conventions
  date?: Date;
  // Allow clearing the date
  clearable?: boolean;
}

const DatePicker: React.FC<DatePickerProps> = ({
  label,
  selectedDate,
  date,
  onDateChange,
  error,
  touched,
  placeholder = 'Select a date',
  minimumDate,
  maximumDate,
  mode = 'date',
  required,
  onBlur,
  clearable = false,
}) => {
  // Use date prop if selectedDate is not provided
  const actualDate = selectedDate || date;
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const [tempDate, setTempDate] = useState(actualDate || new Date());

  const showError = error && touched;

  const handleDateChange = (event: any, date?: Date) => {
    if (Platform.OS === 'android') {
      if (event.type === 'dismissed') {
        setModalVisible(false);
        return;
      }
    }

    if (date) {
      setTempDate(date);

      if (Platform.OS === 'ios') {
        // On iOS, we'll confirm the date when the user presses "Done"
      } else {
        // On Android, we'll update the date immediately and close the picker
        onDateChange(date);
        setModalVisible(false);
      }
    }
  };

  const handleConfirm = () => {
    // Make sure we're passing a valid Date object
    if (tempDate instanceof Date) {
      onDateChange(tempDate);
    }
    setModalVisible(false);
    if (onBlur) onBlur();
  };

  const handleCancel = () => {
    // Make sure we're setting a valid Date object
    if (actualDate instanceof Date) {
      setTempDate(actualDate);
    } else {
      setTempDate(new Date());
    }
    setModalVisible(false);
  };

  const formattedDate = actualDate ? formatDate(actualDate) : '';

  return (
    <View style={styles.container}>
      {label && (
        <Text style={[
          styles.label,
          { color: isDarkMode ? '#8B949E' : '#6E7781' }
        ]}>
          {label}{required && <Text style={{ color: '#F85149' }}> *</Text>}
        </Text>
      )}

      <View style={styles.dateButtonContainer}>
        <TouchableOpacity
          style={[
            styles.dateButton,
            {
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              borderColor: showError
                ? '#F85149'
                : isDarkMode
                  ? '#30363D'
                  : '#D0D7DE'
            }
          ]}
          onPress={() => setModalVisible(true)}
        >
          <Text
            style={[
              styles.selectedText,
              {
                color: formattedDate
                  ? (isDarkMode ? '#FFFFFF' : '#24292E')
                  : (isDarkMode ? '#8B949E' : '#6E7781')
              }
            ]}
          >
            {formattedDate || placeholder}
          </Text>
          <Ionicons
            name="calendar-outline"
            size={20}
            color={isDarkMode ? '#8B949E' : '#6E7781'}
          />
        </TouchableOpacity>

        {clearable && actualDate && (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={() => {
              onDateChange(undefined);
              if (onBlur) onBlur();
            }}
          >
            <Ionicons
              name="close-circle"
              size={20}
              color={isDarkMode ? '#8B949E' : '#6E7781'}
            />
          </TouchableOpacity>
        )}
      </View>

      {showError && (
        <Text style={styles.errorText}>{error}</Text>
      )}

      {Platform.OS === 'ios' ? (
        <Modal
          animationType="slide"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => setModalVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View
              style={[
                styles.modalContent,
                {
                  backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
                  borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                },
              ]}
            >
              <Text
                style={[
                  styles.modalTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                ]}
              >
                {t('common.selectDate')}
              </Text>

              <DateTimePicker
                value={tempDate}
                mode={mode}
                display="spinner"
                onChange={handleDateChange}
                minimumDate={minimumDate}
                maximumDate={maximumDate}
                style={styles.datePicker}
                textColor={isDarkMode ? '#FFFFFF' : '#24292E'}
              />

              <View style={styles.modalButtons}>
                <Button
                  title={t('common.cancel')}
                  onPress={handleCancel}
                  variant="secondary"
                  style={styles.modalButton}
                />
                <Button
                  title={t('common.done')}
                  onPress={handleConfirm}
                  style={styles.modalButton}
                />
              </View>
            </View>
          </View>
        </Modal>
      ) : (
        modalVisible && (
          <DateTimePicker
            value={tempDate}
            mode={mode}
            display="default"
            onChange={handleDateChange}
            minimumDate={minimumDate}
            maximumDate={maximumDate}
          />
        )
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
  },
  dateButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 10,
    height: 40,
  },
  clearButton: {
    marginLeft: 8,
    padding: 4,
  },
  selectedText: {
    fontSize: 14,
  },
  errorText: {
    color: '#F85149',
    fontSize: 12,
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderWidth: 1,
    padding: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  datePicker: {
    width: '100%',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 8,
  },
});

export default DatePicker;
