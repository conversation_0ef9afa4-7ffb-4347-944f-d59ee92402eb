import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';

interface ErrorMessageProps {
  message: string;
  onRetry?: () => void;
  fullScreen?: boolean;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({
  message,
  onRetry,
  fullScreen = false,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();

  const containerStyle = [
    styles.container,
    fullScreen && styles.fullScreen,
    { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
  ];

  return (
    <View style={containerStyle}>
      <View style={styles.iconContainer}>
        <Ionicons
          name="alert-circle-outline"
          size={48}
          color="#F85149"
        />
      </View>
      <Text
        style={[
          styles.message,
          { color: isDarkMode ? '#FFFFFF' : '#24292E' },
        ]}
      >
        {message}
      </Text>
      {onRetry && (
        <TouchableOpacity
          style={[
            styles.retryButton,
            { backgroundColor: isDarkMode ? '#0D419D' : '#0366D6' },
          ]}
          onPress={onRetry}
        >
          <Ionicons
            name="refresh-outline"
            size={18}
            color="#FFFFFF"
            style={styles.retryIcon}
          />
          <Text style={styles.retryText}>{t('common.retry')}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 16,
  },
  fullScreen: {
    flex: 1,
    margin: 0,
  },
  iconContainer: {
    marginBottom: 16,
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  retryIcon: {
    marginRight: 8,
  },
  retryText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default ErrorMessage;
