import React from 'react';
import { View, ActivityIndicator, StyleSheet, Text } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';

interface LoadingIndicatorProps {
  fullScreen?: boolean;
  message?: string;
  size?: 'small' | 'large';
}

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  fullScreen = false,
  message,
  size = 'large',
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();

  const containerStyle = [
    styles.container,
    fullScreen && styles.fullScreen,
    { backgroundColor: isDarkMode ? 'rgba(13, 17, 23, 0.7)' : 'rgba(255, 255, 255, 0.7)' },
  ];

  return (
    <View style={containerStyle}>
      <View
        style={[
          styles.loadingBox,
          { backgroundColor: isDarkMode ? '#161B22' : '#FFFFFF' },
        ]}
      >
        <ActivityIndicator
          size={size}
          color={isDarkMode ? '#58A6FF' : '#0366D6'}
          style={styles.indicator}
        />
        {message && (
          <Text
            style={[
              styles.message,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
          >
            {message}
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
  },
  fullScreen: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 999,
  },
  loadingBox: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 120,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  indicator: {
    marginBottom: 8,
  },
  message: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default LoadingIndicator;
