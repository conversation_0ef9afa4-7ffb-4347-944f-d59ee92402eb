import React, { ReactNode, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ViewStyle, TextStyle, Animated, Platform } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

type CardVariant = 'default' | 'primary' | 'accent' | 'success' | 'warning' | 'danger';
type CardElevation = 'none' | 'sm' | 'md' | 'lg';

interface CardProps {
  title?: string;
  children: ReactNode;
  onPress?: () => void;
  style?: ViewStyle;
  titleStyle?: TextStyle;
  contentStyle?: ViewStyle;
  rightIcon?: string;
  onRightIconPress?: () => void;
  rightComponent?: ReactNode;
  leftIcon?: string;
  onLeftIconPress?: () => void;
  footer?: ReactNode;
  variant?: CardVariant;
  elevation?: CardElevation;
  interactive?: boolean;
  fullWidth?: boolean;
  rounded?: boolean;
  bordered?: boolean;
  disabled?: boolean;
}

const Card: React.FC<CardProps> = ({
  title,
  children,
  onPress,
  style,
  titleStyle,
  contentStyle,
  rightIcon,
  onRightIconPress,
  rightComponent,
  leftIcon,
  onLeftIconPress,
  footer,
  variant = 'default',
  elevation = 'sm',
  interactive = false,
  fullWidth = false,
  rounded = false,
  bordered = false,
  disabled = false,
}) => {
  const { colors, borderRadius, isDarkMode } = useTheme();
  // Use default shadow values since shadows might not be available in the theme context yet
  const defaultShadows = {
    light: {
      sm: {
        shadowColor: 'rgba(0, 0, 0, 0.1)',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 2.0,
        elevation: 2,
      },
      md: {
        shadowColor: 'rgba(0, 0, 0, 0.1)',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 4,
      },
      lg: {
        shadowColor: 'rgba(0, 0, 0, 0.1)',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 5.0,
        elevation: 8,
      },
    },
    dark: {
      sm: {
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.4,
        shadowRadius: 3.0,
        elevation: 3,
      },
      md: {
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.5,
        shadowRadius: 5.0,
        elevation: 6,
      },
      lg: {
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowOffset: { width: 0, height: 5 },
        shadowOpacity: 0.6,
        shadowRadius: 7.0,
        elevation: 12,
      },
    },
  };
  const [scaleAnim] = useState(new Animated.Value(1));

  // Get card background color based on variant
  const getBackgroundColor = (): string => {
    switch (variant) {
      case 'primary':
        return colors.primary;
      case 'accent':
        return colors.accent.start;
      case 'success':
        return colors.success;
      case 'warning':
        return colors.warning;
      case 'danger':
        return colors.danger;
      default:
        return isDarkMode ? colors.background.secondary : colors.card;
    }
  };

  // Get card border color based on variant
  const getBorderColor = (): string => {
    if (!bordered && !title && !footer) return 'transparent';

    switch (variant) {
      case 'primary':
        return colors.primary;
      case 'accent':
        return colors.accent.start;
      case 'success':
        return colors.success;
      case 'warning':
        return colors.warning;
      case 'danger':
        return colors.danger;
      default:
        return isDarkMode ? colors.border.dark.primary : colors.border.light.primary;
    }
  };

  // Get card shadow based on elevation
  const getShadow = () => {
    if (elevation === 'none') return {};

    const shadowSet = isDarkMode ? defaultShadows.dark : defaultShadows.light;

    switch (elevation) {
      case 'lg':
        return shadowSet.lg;
      case 'md':
        return shadowSet.md;
      default:
        return shadowSet.sm;
    }
  };

  // Handle press in animation
  const handlePressIn = () => {
    if ((interactive || onPress) && !disabled) {
      Animated.spring(scaleAnim, {
        toValue: 0.98,
        friction: 8,
        tension: 100,
        useNativeDriver: true,
      }).start();
    }
  };

  // Handle press out animation
  const handlePressOut = () => {
    if ((interactive || onPress) && !disabled) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }).start();
    }
  };

  // Get text color based on variant
  const getTextColor = (): string => {
    switch (variant) {
      case 'primary':
      case 'accent':
      case 'success':
      case 'warning':
      case 'danger':
        return colors.text.white;
      default:
        return isDarkMode ? colors.text.dark.primary : colors.text.light.primary;
    }
  };

  // Get icon color based on variant
  const getIconColor = (): string => {
    switch (variant) {
      case 'primary':
      case 'accent':
      case 'success':
      case 'warning':
      case 'danger':
        return colors.text.white;
      default:
        return isDarkMode ? colors.text.dark.secondary : colors.text.light.secondary;
    }
  };

  const cardStyles = [
    styles.card,
    {
      backgroundColor: getBackgroundColor(),
      borderColor: getBorderColor(),
      borderRadius: rounded ? borderRadius.xl : borderRadius.md,
      width: fullWidth ? '100%' : undefined as any, // Type cast to avoid width type issues
      opacity: disabled ? 0.7 : 1,
      ...getShadow(),
    },
    style,
  ];

  const CardContainer = onPress ? TouchableOpacity : View;
  const cardContent = (
    <>
      {title && (
        <View style={[
          styles.titleContainer,
          { borderBottomColor: getBorderColor() }
        ]}>
          {leftIcon && (
            <TouchableOpacity
              onPress={onLeftIconPress}
              disabled={!onLeftIconPress || disabled}
              style={styles.leftIconContainer}
            >
              <Ionicons
                name={leftIcon as any}
                size={20}
                color={getIconColor()}
              />
            </TouchableOpacity>
          )}

          <Text
            style={[
              styles.title,
              { color: getTextColor() },
              titleStyle,
            ]}
          >
            {title}
          </Text>

          {rightComponent ? (
            rightComponent
          ) : rightIcon && (
            <TouchableOpacity
              onPress={onRightIconPress}
              disabled={!onRightIconPress || disabled}
              style={styles.rightIconContainer}
            >
              <Ionicons
                name={rightIcon as any}
                size={20}
                color={getIconColor()}
              />
            </TouchableOpacity>
          )}
        </View>
      )}

      <View style={[styles.content, contentStyle]}>{children}</View>

      {footer && (
        <View style={[
          styles.footer,
          { borderTopColor: getBorderColor() }
        ]}>
          {footer}
        </View>
      )}
    </>
  );

  // If card is interactive, wrap in Animated.View for scale animation
  if (interactive || onPress) {
    return (
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <CardContainer
          style={cardStyles}
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          disabled={disabled || !onPress}
          activeOpacity={0.9}
        >
          {cardContent}
        </CardContainer>
      </Animated.View>
    );
  }

  // Otherwise, render a simple View
  return (
    <CardContainer
      style={cardStyles}
      onPress={onPress}
      disabled={disabled || !onPress}
    >
      {cardContent}
    </CardContainer>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 12, // Increased border radius
    borderWidth: 1,
    marginBottom: 16,
    overflow: 'hidden',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 14, // Slightly more padding
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  leftIconContainer: {
    marginRight: 12, // Increased spacing
  },
  rightIconContainer: {
    marginLeft: 12, // Increased spacing
  },
  content: {
    padding: 16,
  },
  footer: {
    borderTopWidth: 1,
    padding: 14, // Slightly more padding
  },
});

export default Card;
