import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface ProgressBarProps {
  progress: number; // 0 to 1
  height?: number;
  backgroundColor?: string;
  progressColor?: string;
  borderRadius?: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  height = 8,
  backgroundColor,
  progressColor,
  borderRadius = 4,
}) => {
  const { isDarkMode } = useTheme();
  
  // Default colors based on theme
  const defaultBackgroundColor = isDarkMode ? '#30363D' : '#D0D7DE';
  const defaultProgressColor = isDarkMode ? '#58A6FF' : '#0366D6';
  
  // Ensure progress is between 0 and 1
  const clampedProgress = Math.min(Math.max(progress, 0), 1);
  
  return (
    <View 
      style={[
        styles.container, 
        { 
          height, 
          backgroundColor: backgroundColor || defaultBackgroundColor,
          borderRadius,
        }
      ]}
    >
      <View 
        style={[
          styles.progress, 
          { 
            width: `${clampedProgress * 100}%`,
            backgroundColor: progressColor || defaultProgressColor,
            borderRadius,
          }
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    overflow: 'hidden',
  },
  progress: {
    height: '100%',
  },
});

export default ProgressBar;
