/**
 * Standardized form component for the Hisab-Kitab app
 */

import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { useTranslation } from 'react-i18next';
import FormInput from './FormInput';
import Button from './Button';
import ErrorDisplay from './ErrorDisplay';
import { validateForm, ValidationSchema } from '../../utils/formValidation';
import useAppTheme from '../../theme/useAppTheme';
import SafeTranslation from './SafeTranslation';

interface StandardFormProps {
  initialValues: Record<string, any>;
  validationSchema: ValidationSchema;
  onSubmit: (values: Record<string, any>) => void | Promise<void>;
  children?: React.ReactNode;
  submitLabel?: string;
  isLoading?: boolean;
  error?: string;
  successMessage?: string;
  resetOnSubmit?: boolean;
  scrollable?: boolean;
  avoidKeyboard?: boolean;
}

const StandardForm: React.FC<StandardFormProps> = ({
  initialValues,
  validationSchema,
  onSubmit,
  children,
  submitLabel = 'common.save',
  isLoading = false,
  error,
  successMessage,
  resetOnSubmit = false,
  scrollable = true,
  avoidKeyboard = true,
}) => {
  const { t } = useTranslation();
  const theme = useAppTheme();
  
  // Form state
  const [values, setValues] = useState<Record<string, any>>(initialValues);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [formError, setFormError] = useState<string | undefined>(error);
  const [formSuccess, setFormSuccess] = useState<string | undefined>(successMessage);

  // Update form error when prop changes
  useEffect(() => {
    setFormError(error);
  }, [error]);

  // Update form success when prop changes
  useEffect(() => {
    setFormSuccess(successMessage);
  }, [successMessage]);

  // Handle input change
  const handleChange = (name: string, value: any) => {
    setValues((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  // Handle input blur
  const handleBlur = (name: string) => {
    setTouched((prevTouched) => ({
      ...prevTouched,
      [name]: true,
    }));
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Mark all fields as touched
    const allTouched = Object.keys(validationSchema).reduce(
      (acc, key) => ({ ...acc, [key]: true }),
      {}
    );
    setTouched(allTouched);

    // Validate form
    const validationResult = validateForm(values, validationSchema, t);
    setErrors(validationResult.errors);

    // If form is valid, submit
    if (validationResult.isValid) {
      setIsSubmitting(true);
      setFormError(undefined);
      setFormSuccess(undefined);

      try {
        await onSubmit(values);
        
        // Reset form if needed
        if (resetOnSubmit) {
          setValues(initialValues);
          setTouched({});
        }
        
      } catch (error) {
        // Handle submission error
        if (error instanceof Error) {
          setFormError(error.message);
        } else {
          setFormError(t('common.unexpectedError'));
        }
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  // Render form content
  const formContent = (
    <View style={styles.formContainer}>
      {/* Error message */}
      {formError && (
        <ErrorDisplay
          error={formError}
          onDismiss={() => setFormError(undefined)}
          variant="banner"
        />
      )}

      {/* Success message */}
      {formSuccess && (
        <View style={[
          styles.successMessage,
          { 
            backgroundColor: theme.colors.success + '20',
            borderColor: theme.colors.success
          }
        ]}>
          <SafeTranslation
            i18nKey={formSuccess}
            fallback={formSuccess}
            style={{ color: theme.colors.success }}
          />
        </View>
      )}

      {/* Form fields */}
      {children}

      {/* Submit button */}
      <Button
        title={t(submitLabel)}
        onPress={handleSubmit}
        isLoading={isLoading || isSubmitting}
        disabled={isLoading || isSubmitting}
        fullWidth
        style={styles.submitButton}
      />
    </View>
  );

  // Wrap in scroll view if needed
  if (scrollable) {
    if (avoidKeyboard) {
      return (
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidingView}
        >
          <ScrollView
            contentContainerStyle={styles.scrollViewContent}
            keyboardShouldPersistTaps="handled"
          >
            {formContent}
          </ScrollView>
        </KeyboardAvoidingView>
      );
    }
    
    return (
      <ScrollView
        contentContainerStyle={styles.scrollViewContent}
        keyboardShouldPersistTaps="handled"
      >
        {formContent}
      </ScrollView>
    );
  }

  return formContent;
};

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  formContainer: {
    padding: 16,
  },
  successMessage: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 16,
  },
  submitButton: {
    marginTop: 24,
  },
});

export default StandardForm;

// Export a form field component for use with StandardForm
export const FormField: React.FC<{
  name: string;
  label: string;
  placeholder?: string;
  values: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  handleChange: (name: string, value: any) => void;
  handleBlur: (name: string) => void;
  keyboardType?: 'default' | 'numeric' | 'email-address' | 'phone-pad';
  secureTextEntry?: boolean;
  required?: boolean;
  multiline?: boolean;
  numberOfLines?: number;
  rightComponent?: React.ReactElement;
}> = ({
  name,
  label,
  placeholder,
  values,
  errors,
  touched,
  handleChange,
  handleBlur,
  keyboardType = 'default',
  secureTextEntry = false,
  required = false,
  multiline = false,
  numberOfLines = 1,
  rightComponent,
}) => {
  return (
    <FormInput
      label={label}
      value={values[name]?.toString() || ''}
      onChangeText={(text) => handleChange(name, text)}
      onBlur={() => handleBlur(name)}
      error={errors[name]}
      touched={touched[name]}
      placeholder={placeholder}
      keyboardType={keyboardType}
      secureTextEntry={secureTextEntry}
      required={required}
      multiline={multiline}
      numberOfLines={numberOfLines}
      rightComponent={rightComponent}
    />
  );
};
