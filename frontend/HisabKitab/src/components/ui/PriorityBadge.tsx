import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface PriorityBadgeProps {
  level: 'Urgent' | 'High' | 'Normal' | 'Low' | string;
  size?: 'small' | 'medium' | 'large';
  showText?: boolean;
}

const PriorityBadge: React.FC<PriorityBadgeProps> = ({
  level,
  size = 'medium',
  showText = true,
}) => {
  // Get color based on priority level
  const getColor = () => {
    switch (level) {
      case 'Urgent':
        return '#F44336'; // Red
      case 'High':
        return '#FF9800'; // Orange
      case 'Normal':
        return '#4CAF50'; // Green
      case 'Low':
        return '#2196F3'; // Blue
      default:
        return '#9E9E9E'; // Grey
    }
  };

  // Get size dimensions
  const getDimensions = () => {
    switch (size) {
      case 'small':
        return {
          width: 8,
          height: 8,
          fontSize: 10,
          paddingHorizontal: 4,
        };
      case 'medium':
        return {
          width: 12,
          height: 12,
          fontSize: 12,
          paddingHorizontal: 6,
        };
      case 'large':
        return {
          width: 16,
          height: 16,
          fontSize: 14,
          paddingHorizontal: 8,
        };
      default:
        return {
          width: 12,
          height: 12,
          fontSize: 12,
          paddingHorizontal: 6,
        };
    }
  };

  const { width, height, fontSize, paddingHorizontal } = getDimensions();
  const color = getColor();

  return (
    <View style={styles.container}>
      <View
        style={[
          styles.dot,
          {
            width,
            height,
            backgroundColor: color,
          },
        ]}
      />

      {showText && (
        <Text
          style={[
            styles.text,
            {
              fontSize,
              color,
              paddingHorizontal,
            },
          ]}
        >
          {level}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    borderRadius: 50,
  },
  text: {
    fontWeight: '600',
  },
});

export default PriorityBadge;
