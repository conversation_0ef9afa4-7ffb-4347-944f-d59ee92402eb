import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import Button from './Button';

interface EmptyStateProps {
  icon: string;
  title: string;
  message: string;
  buttonTitle?: string;
  actionLabel?: string; // Alias for buttonTitle
  onButtonPress?: () => void;
  onAction?: () => void; // Alias for onButtonPress
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  message,
  buttonTitle,
  actionLabel,
  onButtonPress,
  onAction,
}) => {
  const { isDarkMode } = useTheme();

  // Use aliases if primary props are not provided
  const finalButtonTitle = buttonTitle || actionLabel;
  const finalOnButtonPress = onButtonPress || onAction;

  return (
    <View style={styles.container}>
      <Ionicons
        name={icon as any}
        size={80}
        color={isDarkMode ? '#30363D' : '#D0D7DE'}
        style={styles.icon}
      />
      <Text
        style={[
          styles.title,
          { color: isDarkMode ? '#FFFFFF' : '#24292E' },
        ]}
      >
        {title}
      </Text>
      <Text
        style={[
          styles.message,
          { color: isDarkMode ? '#8B949E' : '#6E7781' },
        ]}
      >
        {message}
      </Text>
      {finalButtonTitle && finalOnButtonPress && (
        <Button
          title={finalButtonTitle}
          onPress={finalOnButtonPress}
          style={styles.button}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  icon: {
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  button: {
    minWidth: 200,
  },
});

export default EmptyState;
