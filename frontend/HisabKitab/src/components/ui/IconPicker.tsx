import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView,
  Modal,
  TextInput
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import Button from './Button';

interface IconPickerProps {
  selectedIcon: string;
  onIconSelected: (icon: string) => void;
  label?: string;
}

// Predefined icons
const ICONS = [
  'wallet-outline',
  'cash-outline',
  'card-outline',
  'home-outline',
  'car-outline',
  'restaurant-outline',
  'cart-outline',
  'medkit-outline',
  'school-outline',
  'airplane-outline',
  'gift-outline',
  'fitness-outline',
  'film-outline',
  'game-controller-outline',
  'book-outline',
  'briefcase-outline',
  'build-outline',
  'bus-outline',
  'cafe-outline',
  'call-outline',
  'camera-outline',
  'desktop-outline',
  'fast-food-outline',
  'football-outline',
  'glasses-outline',
  'headset-outline',
  'heart-outline',
  'leaf-outline',
  'musical-notes-outline',
  'paw-outline',
  'people-outline',
  'person-outline',
  'pizza-outline',
  'planet-outline',
  'shirt-outline',
  'storefront-outline',
  'train-outline',
  'wine-outline',
];

const IconPicker: React.FC<IconPickerProps> = ({
  selectedIcon,
  onIconSelected,
  label,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  const handleIconSelect = (icon: string) => {
    onIconSelected(icon);
    setModalVisible(false);
  };
  
  const filteredIcons = searchQuery
    ? ICONS.filter(icon => icon.toLowerCase().includes(searchQuery.toLowerCase()))
    : ICONS;
  
  return (
    <View style={styles.container}>
      {label && (
        <Text style={[
          styles.label, 
          { color: isDarkMode ? '#8B949E' : '#6E7781' }
        ]}>
          {label}
        </Text>
      )}
      
      <TouchableOpacity
        style={[
          styles.iconButton,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
          },
        ]}
        onPress={() => setModalVisible(true)}
      >
        <View style={styles.selectedIconContainer}>
          <Ionicons
            name={selectedIcon as any}
            size={24}
            color={isDarkMode ? '#FFFFFF' : '#24292E'}
            style={styles.iconPreview}
          />
          <Text
            style={[
              styles.iconText,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
          >
            {selectedIcon}
          </Text>
        </View>
      </TouchableOpacity>
      
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
          >
            <Text
              style={[
                styles.modalTitle,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' },
              ]}
            >
              {t('categories.selectIcon')}
            </Text>
            
            <TextInput
              style={[
                styles.searchInput,
                {
                  backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                  color: isDarkMode ? '#FFFFFF' : '#24292E',
                  borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                },
              ]}
              placeholder={t('common.search')}
              placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            
            <ScrollView style={styles.iconGrid}>
              <View style={styles.iconsContainer}>
                {filteredIcons.map((icon) => (
                  <TouchableOpacity
                    key={icon}
                    style={[
                      styles.iconOption,
                      {
                        backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                        borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                      },
                      selectedIcon === icon && styles.selectedIconOption,
                    ]}
                    onPress={() => handleIconSelect(icon)}
                  >
                    <Ionicons
                      name={icon as any}
                      size={24}
                      color={
                        selectedIcon === icon
                          ? isDarkMode
                            ? '#58A6FF'
                            : '#0366D6'
                          : isDarkMode
                          ? '#FFFFFF'
                          : '#24292E'
                      }
                    />
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
            
            <View style={styles.modalButtons}>
              <Button
                title={t('common.cancel')}
                onPress={() => setModalVisible(false)}
                variant="secondary"
                style={styles.modalButton}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    width: '100%',
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
  },
  iconButton: {
    height: 48,
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    justifyContent: 'center',
  },
  selectedIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconPreview: {
    marginRight: 8,
  },
  iconText: {
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    maxHeight: '80%',
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  searchInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    marginBottom: 16,
  },
  iconGrid: {
    maxHeight: 300,
  },
  iconsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  iconOption: {
    width: 48,
    height: 48,
    borderRadius: 6,
    borderWidth: 1,
    margin: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedIconOption: {
    borderWidth: 2,
    borderColor: '#58A6FF',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
  modalButton: {
    minWidth: 100,
    marginHorizontal: 8,
  },
});

export default IconPicker;
