import React, { ReactNode } from 'react';
import { Text, TextStyle, StyleSheet, Animated, Platform } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { getFontFamilyForLanguage } from '../../utils/fontLoader';

// Define the variant types
type TypographyVariant = 
  | 'h1' 
  | 'h2' 
  | 'h3' 
  | 'bodyLarge' 
  | 'bodySmall' 
  | 'caption' 
  | 'number' 
  | 'hero';

// Define the weight types
type TypographyWeight = 'regular' | 'medium' | 'semibold' | 'bold';

// Define the alignment types
type TypographyAlign = 'left' | 'center' | 'right';

// Define the props for the Typography component
interface TypographyProps {
  variant?: TypographyVariant;
  weight?: TypographyWeight;
  color?: string;
  align?: TypographyAlign;
  style?: TextStyle;
  children: ReactNode;
  numberOfLines?: number;
  uppercase?: boolean;
  animated?: boolean;
  animateOnChange?: boolean;
  nepali?: boolean;
  testID?: string;
}

const Typography: React.FC<TypographyProps> = ({
  variant = 'bodyLarge',
  weight = 'regular',
  color,
  align = 'left',
  style,
  children,
  numberOfLines,
  uppercase = false,
  animated = false,
  animateOnChange = false,
  nepali = false,
  testID,
}) => {
  const { colors, typography, isDarkMode } = useTheme();
  const { i18n } = useTranslation();
  const currentLanguage = i18n.language || 'en';
  
  // Create an animated value for font size animation
  const [animatedFontSize] = React.useState(new Animated.Value(1));
  const [prevChildren, setPrevChildren] = React.useState(children);
  
  // Animate font size when children change (for numbers)
  React.useEffect(() => {
    if (animateOnChange && prevChildren !== children) {
      // Pulse animation for numbers
      Animated.sequence([
        Animated.timing(animatedFontSize, {
          toValue: 1.1,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(animatedFontSize, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
      
      setPrevChildren(children);
    }
  }, [children, animateOnChange, animatedFontSize, prevChildren]);
  
  // Get the font family based on language and weight
  const getFontFamily = () => {
    // Use Nepali font if explicitly requested or if the current language is Nepali
    const languageToUse = nepali ? 'ne' : currentLanguage;
    
    // Get the appropriate font family
    const fontFamily = getFontFamilyForLanguage(languageToUse);
    
    // For web, we need to use the font family name directly
    if (Platform.OS === 'web') {
      return languageToUse === 'ne' ? 'Noto Sans Devanagari' : 'Manrope';
    }
    
    // For native, we need to use the specific font weight
    switch (weight) {
      case 'medium':
        return languageToUse === 'ne' ? 'Noto Sans Devanagari Medium' : 'Manrope-Medium';
      case 'semibold':
        return languageToUse === 'ne' ? 'Noto Sans Devanagari SemiBold' : 'Manrope-SemiBold';
      case 'bold':
        return languageToUse === 'ne' ? 'Noto Sans Devanagari Bold' : 'Manrope-Bold';
      default:
        return fontFamily;
    }
  };
  
  // Get the font size based on variant
  const getFontSize = () => {
    return typography.fontSize[variant];
  };
  
  // Get the line height based on variant
  const getLineHeight = () => {
    return typography.lineHeight[variant];
  };
  
  // Get the font weight based on weight
  const getFontWeight = () => {
    switch (weight) {
      case 'medium':
        return typography.fontWeight.medium;
      case 'semibold':
        return typography.fontWeight.semibold;
      case 'bold':
        return typography.fontWeight.bold;
      default:
        return typography.fontWeight.regular;
    }
  };
  
  // Get the letter spacing based on uppercase and variant
  const getLetterSpacing = () => {
    if (uppercase) {
      return typography.letterSpacing.uppercase;
    }
    
    if (variant === 'caption') {
      return typography.letterSpacing.wide;
    }
    
    return typography.letterSpacing.normal;
  };
  
  // Get the text color
  const getTextColor = () => {
    if (color) return color;
    
    // Default text colors based on dark mode
    return isDarkMode ? colors.text.dark.primary : colors.text.light.primary;
  };
  
  // Create the text style
  const textStyle: TextStyle = {
    fontFamily: getFontFamily(),
    fontSize: getFontSize(),
    lineHeight: getLineHeight(),
    fontWeight: getFontWeight(),
    letterSpacing: getLetterSpacing(),
    color: getTextColor(),
    textAlign: align,
    ...(uppercase && { textTransform: 'uppercase' }),
  };
  
  // If animated, use Animated.Text
  if (animated || animateOnChange) {
    return (
      <Animated.Text
        style={[
          textStyle,
          style,
          { transform: [{ scale: animatedFontSize }] },
        ]}
        numberOfLines={numberOfLines}
        testID={testID}
      >
        {children}
      </Animated.Text>
    );
  }
  
  // Otherwise, use regular Text
  return (
    <Text
      style={[textStyle, style]}
      numberOfLines={numberOfLines}
      testID={testID}
    >
      {children}
    </Text>
  );
};

export default Typography;
