import React from 'react';
import { Text, TextProps } from 'react-native';
import { useTranslation } from 'react-i18next';
import { getSafeTranslation } from '../../utils/translationValidator';

interface SafeTextProps extends TextProps {
  translationKey: string;
  fallback?: string;
  options?: Record<string, any>;
}

/**
 * A component that safely displays translated text with fallback
 */
const SafeText: React.FC<SafeTextProps> = ({
  translationKey,
  fallback,
  options,
  ...textProps
}) => {
  const { t } = useTranslation();
  
  // Get the safe translation
  const safeText = getSafeTranslation(translationKey, fallback, options);
  
  return <Text {...textProps}>{safeText}</Text>;
};

export default SafeText;
