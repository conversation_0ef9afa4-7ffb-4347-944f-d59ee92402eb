import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TextInputProps,
  KeyboardTypeOptions
} from 'react-native';
import useAppTheme from '../../theme/useAppTheme';
import SafeTranslation from './SafeTranslation';

interface FormInputProps extends TextInputProps {
  label: string | React.ReactElement;
  error?: string;
  touched?: boolean;
  keyboardType?: KeyboardTypeOptions;
  rightComponent?: React.ReactElement;
  required?: boolean;
  validateOnBlur?: boolean;
  onValidate?: (value: string) => string | undefined;
}

const FormInput: React.FC<FormInputProps> = ({
  label,
  error,
  touched,
  keyboardType = 'default',
  rightComponent,
  required = false,
  validateOnBlur = true,
  onValidate,
  onBlur,
  ...props
}) => {
  const theme = useAppTheme();
  const [localTouched, setLocalTouched] = useState(false);
  const [localError, setLocalError] = useState<string | undefined>(undefined);

  // Use either provided error/touched or local state
  const effectiveTouched = touched !== undefined ? touched : localTouched;
  const effectiveError = error !== undefined ? error : localError;

  const showError = effectiveError && effectiveTouched;

  // Handle blur event
  const handleBlur = (e: any) => {
    if (validateOnBlur && onValidate && props.value !== undefined) {
      setLocalError(onValidate(props.value.toString()));
    }
    setLocalTouched(true);
    if (onBlur) {
      onBlur(e);
    }
  };

  // Determine border color based on state
  const getBorderColor = () => {
    if (showError) {
      return theme.colors.danger;
    }
    if (required && !effectiveTouched) {
      return theme.colors.warning;
    }
    return theme.colors.border.primary;
  };

  return (
    <View style={styles.container}>
      <View style={styles.labelContainer}>
        {typeof label === 'string' ? (
          <SafeTranslation
            i18nKey={label}
            fallback={label}
            style={[
              styles.label,
              { color: theme.colors.text.secondary }
            ]}
          />
        ) : (
          label
        )}
        {required && (
          <Text style={[styles.requiredIndicator, { color: theme.colors.danger }]}>*</Text>
        )}
      </View>

      <View style={styles.inputContainer}>
        <TextInput
          style={[
            styles.input,
            {
              backgroundColor: theme.colors.background.secondary,
              color: theme.colors.text.primary,
              borderColor: getBorderColor(),
              borderRadius: theme.borderRadius.sm,
              fontSize: theme.typography.fontSize.md,
            },
            rightComponent && styles.inputWithRightComponent
          ]}
          placeholderTextColor={theme.colors.text.tertiary}
          keyboardType={keyboardType}
          onBlur={handleBlur}
          {...props}
        />
        {rightComponent && (
          <View style={styles.rightComponentContainer}>
            {rightComponent}
          </View>
        )}
      </View>

      {showError && (
        <Text style={[styles.errorText, { color: theme.colors.danger }]}>
          {typeof error === 'string' ? (
            <SafeTranslation i18nKey={error} fallback={error} />
          ) : (
            error
          )}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    width: '100%',
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
  },
  requiredIndicator: {
    color: '#F85149',
    fontSize: 16,
    marginLeft: 4,
  },
  inputContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    fontSize: 16,
    flex: 1,
  },
  inputWithRightComponent: {
    paddingRight: 40,
  },
  rightComponentContainer: {
    position: 'absolute',
    right: 12,
    height: '100%',
    justifyContent: 'center',
  },
  errorText: {
    color: '#F85149',
    fontSize: 14,
    marginTop: 4,
  },
});

export default FormInput;
