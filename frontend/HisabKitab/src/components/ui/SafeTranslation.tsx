import React from 'react';
import { Text, TextProps } from 'react-native';
import { useTranslation } from 'react-i18next';

interface SafeTranslationProps extends TextProps {
  i18nKey: string;
  fallback?: string;
  values?: Record<string, any>;
}

/**
 * A component that safely handles translations, preventing raw keys from being displayed
 * If a translation is missing, it will display the fallback text or a formatted version of the key
 */
const SafeTranslation: React.FC<SafeTranslationProps> = ({
  i18nKey,
  fallback,
  values,
  ...props
}) => {
  const { t, i18n } = useTranslation();

  // Check if the key exists in the current language
  const keyExists = i18n.exists(i18nKey);

  // Get the translation
  const translation = keyExists ? t(i18nKey, values) : null;

  // If the translation is the same as the key, it means the translation is missing
  const isMissingTranslation = translation === i18nKey;

  // Format the key for display if needed (convert 'namespace.key' to 'Key')
  const formatKeyForDisplay = (key: string): string => {
    const parts = key.split('.');
    const lastPart = parts[parts.length - 1];
    return lastPart
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/^./, (str) => str.toUpperCase()); // Capitalize first letter
  };

  // Determine what text to display
  const displayText = isMissingTranslation
    ? fallback || formatKeyForDisplay(i18nKey)
    : translation;

  return <Text {...props}>{displayText}</Text>;
};

export default SafeTranslation;
