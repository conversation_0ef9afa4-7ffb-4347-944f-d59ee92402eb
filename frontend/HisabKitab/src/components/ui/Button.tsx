import React, { useState } from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacityProps,
  View,
  Animated,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import useAppTheme from '../../theme/useAppTheme';
import SafeTranslation from './SafeTranslation';
import Typography from './Typography';

interface ButtonProps extends TouchableOpacityProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'outline';
  // Add type as an alias for variant for backward compatibility
  type?: 'primary' | 'secondary' | 'danger' | 'success' | 'outline';
  size?: 'small' | 'medium' | 'large';
  isLoading?: boolean;
  disabled?: boolean;
  icon?: string;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  type,
  size = 'medium',
  isLoading = false,
  disabled = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  style,
  ...props
}) => {
  // Use type prop if provided, otherwise use variant
  const buttonVariant = type || variant;
  const theme = useAppTheme();
  const [scaleAnim] = useState(new Animated.Value(1));

  // Determine background color based on variant and theme
  const getBackgroundColor = () => {
    if (disabled) return theme.colors.border.primary;

    switch (buttonVariant) {
      case 'primary':
        return theme.colors.primary;
      case 'secondary':
        return theme.colors.background.secondary;
      case 'danger':
        return theme.colors.danger;
      case 'success':
        return theme.colors.success;
      case 'outline':
        return 'transparent';
      default:
        return theme.colors.primary;
    }
  };

  // Determine text color based on variant and theme
  const getTextColor = () => {
    if (disabled) return theme.colors.text.tertiary;

    switch (buttonVariant) {
      case 'primary':
      case 'danger':
      case 'success':
        return theme.colors.text.inverse;
      case 'secondary':
        return theme.colors.text.primary;
      case 'outline':
        return theme.colors.primary;
      default:
        return theme.colors.text.inverse;
    }
  };

  // Determine border color for outline variant
  const getBorderColor = () => {
    if (buttonVariant === 'outline') {
      return theme.colors.border.primary;
    }
    return 'transparent';
  };

  // Determine padding based on size
  const getPadding = () => {
    switch (size) {
      case 'small':
        return { paddingVertical: 6, paddingHorizontal: 12 };
      case 'medium':
        return { paddingVertical: 10, paddingHorizontal: 16 };
      case 'large':
        return { paddingVertical: 14, paddingHorizontal: 20 };
      default:
        return { paddingVertical: 10, paddingHorizontal: 16 };
    }
  };

  // Determine font size based on size
  const getFontSize = () => {
    switch (size) {
      case 'small':
        return 14;
      case 'medium':
        return 16;
      case 'large':
        return 18;
      default:
        return 16;
    }
  };

  // Determine icon size based on button size
  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 16;
      case 'medium':
        return 20;
      case 'large':
        return 24;
      default:
        return 20;
    }
  };

  // Handle press in animation
  const handlePressIn = () => {
    if (!disabled && !isLoading) {
      Animated.spring(scaleAnim, {
        toValue: 0.95,
        friction: 8,
        tension: 100,
        useNativeDriver: true,
      }).start();
    }
  };

  // Handle press out animation
  const handlePressOut = () => {
    if (!disabled && !isLoading) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }).start();
    }
  };

  return (
    <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || isLoading}
        style={[
          styles.button,
          {
            backgroundColor: getBackgroundColor(),
            borderColor: getBorderColor(),
            borderWidth: variant === 'outline' ? 1 : 0,
            ...getPadding(),
            width: fullWidth ? '100%' : undefined,
            borderRadius: theme.borderRadius.sm,
          },
          style,
        ]}
        activeOpacity={0.9}
        {...props}
      >
        {isLoading ? (
          <ActivityIndicator size="small" color={getTextColor()} />
        ) : (
          <View style={styles.contentContainer}>
            {icon && iconPosition === 'left' && (
              <Ionicons
                name={icon as any}
                size={getIconSize()}
                color={getTextColor()}
                style={styles.leftIcon}
              />
            )}

            {typeof title === 'string' && title.includes('.') ? (
              <SafeTranslation
                i18nKey={title}
                fallback={title}
                style={[
                  styles.text,
                  {
                    color: getTextColor(),
                    fontSize: getFontSize(),
                    fontWeight: theme.typography.fontWeight.semibold,
                  },
                ]}
              />
            ) : (
              <Typography
                variant={size === 'large' ? 'bodyLarge' : size === 'small' ? 'bodySmall' : 'bodyLarge'}
                weight="semibold"
                color={getTextColor()}
                style={styles.text}
              >
                {title}
              </Typography>
            )}

            {icon && iconPosition === 'right' && (
              <Ionicons
                name={icon as any}
                size={getIconSize()}
                color={getTextColor()}
                style={styles.rightIcon}
              />
            )}
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontWeight: '600',
    textAlign: 'center',
  },
  leftIcon: {
    marginRight: 8,
  },
  rightIcon: {
    marginLeft: 8,
  },
});

export default Button;
