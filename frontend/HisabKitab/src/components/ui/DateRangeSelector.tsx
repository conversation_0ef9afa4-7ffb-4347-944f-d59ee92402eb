import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import DatePicker from './DatePicker';
import Button from './Button';
import { formatDate } from '../../utils/formatters';

interface DateRangePreset {
  id: string;
  label: string;
  getDateRange: () => { startDate: Date | null; endDate: Date | null };
}

interface DateRangeSelectorProps {
  startDate: Date | null;
  endDate: Date | null;
  onDateRangeChange: (startDate: Date | null, endDate: Date | null) => void;
}

const DateRangeSelector: React.FC<DateRangeSelectorProps> = ({
  startDate,
  endDate,
  onDateRangeChange,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const [tempStartDate, setTempStartDate] = useState<Date | null>(startDate);
  const [tempEndDate, setTempEndDate] = useState<Date | null>(endDate);
  const [selectedPresetId, setSelectedPresetId] = useState<string | null>(null);

  // Reset temp dates when props change
  useEffect(() => {
    setTempStartDate(startDate);
    setTempEndDate(endDate);
  }, [startDate, endDate]);

  // Get date range presets
  const getPresets = (): DateRangePreset[] => {
    const today = new Date();

    return [
      {
        id: 'today',
        label: t('dateRange.today'),
        getDateRange: () => {
          const start = new Date(today);
          start.setHours(0, 0, 0, 0);
          const end = new Date(today);
          end.setHours(23, 59, 59, 999);
          return { startDate: start, endDate: end };
        },
      },
      {
        id: 'yesterday',
        label: t('dateRange.yesterday'),
        getDateRange: () => {
          const start = new Date(today);
          start.setDate(start.getDate() - 1);
          start.setHours(0, 0, 0, 0);
          const end = new Date(today);
          end.setDate(end.getDate() - 1);
          end.setHours(23, 59, 59, 999);
          return { startDate: start, endDate: end };
        },
      },
      {
        id: 'thisWeek',
        label: t('dateRange.thisWeek'),
        getDateRange: () => {
          const start = new Date(today);
          start.setDate(start.getDate() - start.getDay());
          start.setHours(0, 0, 0, 0);
          const end = new Date(today);
          end.setDate(end.getDate() + (6 - end.getDay()));
          end.setHours(23, 59, 59, 999);
          return { startDate: start, endDate: end };
        },
      },
      {
        id: 'lastWeek',
        label: t('dateRange.lastWeek'),
        getDateRange: () => {
          const start = new Date(today);
          start.setDate(start.getDate() - start.getDay() - 7);
          start.setHours(0, 0, 0, 0);
          const end = new Date(today);
          end.setDate(end.getDate() - end.getDay() - 1);
          end.setHours(23, 59, 59, 999);
          return { startDate: start, endDate: end };
        },
      },
      {
        id: 'thisMonth',
        label: t('dateRange.thisMonth'),
        getDateRange: () => {
          const start = new Date(today.getFullYear(), today.getMonth(), 1);
          start.setHours(0, 0, 0, 0);
          const end = new Date(today.getFullYear(), today.getMonth() + 1, 0);
          end.setHours(23, 59, 59, 999);
          return { startDate: start, endDate: end };
        },
      },
      {
        id: 'lastMonth',
        label: t('dateRange.lastMonth'),
        getDateRange: () => {
          const start = new Date(today.getFullYear(), today.getMonth() - 1, 1);
          start.setHours(0, 0, 0, 0);
          const end = new Date(today.getFullYear(), today.getMonth(), 0);
          end.setHours(23, 59, 59, 999);
          return { startDate: start, endDate: end };
        },
      },
      {
        id: 'thisYear',
        label: t('dateRange.thisYear'),
        getDateRange: () => {
          const start = new Date(today.getFullYear(), 0, 1);
          start.setHours(0, 0, 0, 0);
          const end = new Date(today.getFullYear(), 11, 31);
          end.setHours(23, 59, 59, 999);
          return { startDate: start, endDate: end };
        },
      },
      {
        id: 'lastYear',
        label: t('dateRange.lastYear'),
        getDateRange: () => {
          const start = new Date(today.getFullYear() - 1, 0, 1);
          start.setHours(0, 0, 0, 0);
          const end = new Date(today.getFullYear() - 1, 11, 31);
          end.setHours(23, 59, 59, 999);
          return { startDate: start, endDate: end };
        },
      },
      {
        id: 'allTime',
        label: t('dateRange.allTime'),
        getDateRange: () => {
          return { startDate: null, endDate: null };
        },
      },
    ];
  };

  const presets = getPresets();

  // Apply preset
  const applyPreset = (presetId: string) => {
    const preset = presets.find(p => p.id === presetId);
    if (preset) {
      const { startDate, endDate } = preset.getDateRange();
      setTempStartDate(startDate);
      setTempEndDate(endDate);
      setSelectedPresetId(presetId);
    }
  };

  // Apply date range
  const applyDateRange = () => {
    onDateRangeChange(tempStartDate, tempEndDate);
    setModalVisible(false);
  };

  // Reset date range
  const resetDateRange = () => {
    setTempStartDate(null);
    setTempEndDate(null);
    setSelectedPresetId('allTime');
  };

  // Get display text
  const getDisplayText = () => {
    if (!startDate && !endDate) {
      return t('dateRange.allTime');
    }

    if (startDate && !endDate) {
      return `${t('dateRange.from')} ${formatDate(startDate as Date)}`;
    }

    if (!startDate && endDate) {
      return `${t('dateRange.until')} ${formatDate(endDate as Date)}`;
    }

    return `${formatDate(startDate as Date)} - ${formatDate(endDate as Date)}`;
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.selector,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
          },
        ]}
        onPress={() => setModalVisible(true)}
      >
        <Ionicons
          name="calendar-outline"
          size={18}
          color={isDarkMode ? '#8B949E' : '#6E7781'}
          style={styles.icon}
        />
        <Text
          style={[
            styles.selectorText,
            { color: isDarkMode ? '#FFFFFF' : '#24292E' },
          ]}
        >
          {getDisplayText()}
        </Text>
        <Ionicons
          name="chevron-down-outline"
          size={18}
          color={isDarkMode ? '#8B949E' : '#6E7781'}
        />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text
                style={[
                  styles.modalTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                ]}
              >
                {t('dateRange.selectDateRange')}
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Ionicons
                  name="close-outline"
                  size={24}
                  color={isDarkMode ? '#8B949E' : '#6E7781'}
                />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              <Text
                style={[
                  styles.sectionTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                ]}
              >
                {t('dateRange.presets')}
              </Text>

              <View style={styles.presetContainer}>
                {presets.map(preset => (
                  <TouchableOpacity
                    key={preset.id}
                    style={[
                      styles.presetButton,
                      {
                        backgroundColor:
                          selectedPresetId === preset.id
                            ? isDarkMode
                              ? '#0D419D'
                              : '#0366D6'
                            : isDarkMode
                              ? '#161B22'
                              : '#F6F8FA',
                        borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                      },
                    ]}
                    onPress={() => applyPreset(preset.id)}
                  >
                    <Text
                      style={[
                        styles.presetText,
                        {
                          color:
                            selectedPresetId === preset.id
                              ? '#FFFFFF'
                              : isDarkMode
                                ? '#FFFFFF'
                                : '#24292E',
                        },
                      ]}
                    >
                      {preset.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <Text
                style={[
                  styles.sectionTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                  { marginTop: 16 },
                ]}
              >
                {t('dateRange.customRange')}
              </Text>

              <View style={styles.datePickerContainer}>
                <View style={styles.datePickerItem}>
                  <DatePicker
                    label={t('dateRange.startDate')}
                    selectedDate={tempStartDate || new Date()}
                    onDateChange={(newDate) => newDate && setTempStartDate(newDate)}
                    placeholder={t('dateRange.selectStartDate')}
                  />
                </View>

                <View style={styles.datePickerItem}>
                  <DatePicker
                    label={t('dateRange.endDate')}
                    selectedDate={tempEndDate || new Date()}
                    onDateChange={(newDate) => newDate && setTempEndDate(newDate)}
                    placeholder={t('dateRange.selectEndDate')}
                    minimumDate={tempStartDate || undefined}
                  />
                </View>
              </View>
            </ScrollView>

            <View style={styles.modalFooter}>
              <Button
                title={t('common.reset')}
                onPress={resetDateRange}
                variant="secondary"
                style={styles.footerButton}
              />
              <Button
                title={t('common.apply')}
                onPress={applyDateRange}
                style={styles.footerButton}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  icon: {
    marginRight: 8,
  },
  selectorText: {
    flex: 1,
    fontSize: 14,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderWidth: 1,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalBody: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  presetContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  presetButton: {
    borderRadius: 8,
    borderWidth: 1,
    padding: 8,
    margin: 4,
  },
  presetText: {
    fontSize: 14,
  },
  datePickerContainer: {
    marginBottom: 16,
  },
  datePickerItem: {
    marginBottom: 12,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#30363D',
  },
  footerButton: {
    flex: 1,
    marginHorizontal: 8,
  },
});

export default DateRangeSelector;
