import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ViewStyle } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import useAppTheme from '../../theme/useAppTheme';
import SafeTranslation from './SafeTranslation';
import { ErrorType } from '../../utils/errorHandling';

interface ErrorDisplayProps {
  error: string | Error | null;
  onDismiss?: () => void;
  variant?: 'inline' | 'banner' | 'toast';
  type?: ErrorType;
  style?: ViewStyle;
  showIcon?: boolean;
}

/**
 * A standardized component for displaying error messages
 */
const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onDismiss,
  variant = 'inline',
  type = ErrorType.UNKNOWN,
  style,
  showIcon = true,
}) => {
  const theme = useAppTheme();
  const { t } = useTranslation();

  // If no error, don't render anything
  if (!error) return null;

  // Extract error message
  const errorMessage = typeof error === 'string' ? error : error.message;

  // Get icon based on error type
  const getIcon = () => {
    switch (type) {
      case ErrorType.NETWORK:
        return 'cloud-offline-outline';
      case ErrorType.VALIDATION:
        return 'alert-circle-outline';
      case ErrorType.AUTHENTICATION:
        return 'lock-closed-outline';
      case ErrorType.AUTHORIZATION:
        return 'shield-outline';
      case ErrorType.NOT_FOUND:
        return 'search-outline';
      case ErrorType.SERVER:
        return 'server-outline';
      case ErrorType.CONFLICT:
        return 'git-merge-outline';
      case ErrorType.OFFLINE:
        return 'wifi-outline';
      case ErrorType.UNKNOWN:
      default:
        return 'warning-outline';
    }
  };

  // Get background color based on error type
  const getBackgroundColor = () => {
    switch (type) {
      case ErrorType.VALIDATION:
        return theme.colors.warning + '20'; // 20% opacity
      case ErrorType.NETWORK:
      case ErrorType.OFFLINE:
        return theme.colors.info + '20';
      default:
        return theme.colors.danger + '20';
    }
  };

  // Get text color based on error type
  const getTextColor = () => {
    switch (type) {
      case ErrorType.VALIDATION:
        return theme.colors.warning;
      case ErrorType.NETWORK:
      case ErrorType.OFFLINE:
        return theme.colors.info;
      default:
        return theme.colors.danger;
    }
  };

  // Determine styles based on variant
  const containerStyle = [
    styles.container,
    variant === 'banner' && styles.bannerContainer,
    variant === 'toast' && styles.toastContainer,
    { backgroundColor: getBackgroundColor() },
    style,
  ];

  const textStyle = [
    styles.errorText,
    { color: theme.colors.text.primary },
  ];

  const iconColor = getTextColor();
  const iconName = getIcon();
  const iconSize = variant === 'inline' ? 16 : 20;

  return (
    <View style={containerStyle}>
      {showIcon && (
        <Ionicons
          name={iconName}
          size={iconSize}
          color={iconColor}
          style={styles.icon}
        />
      )}

      {typeof errorMessage === 'string' && errorMessage.includes('.') ? (
        <SafeTranslation
          i18nKey={errorMessage}
          fallback={errorMessage}
          style={textStyle}
        />
      ) : (
        <Text style={textStyle}>{errorMessage}</Text>
      )}

      {onDismiss && (
        <TouchableOpacity onPress={onDismiss} style={styles.dismissButton}>
          <Ionicons
            name="close-outline"
            size={iconSize}
            color={theme.colors.text.secondary}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 4,
    marginVertical: 8,
    borderLeftWidth: 3,
  },
  bannerContainer: {
    borderRadius: 8,
    padding: 12,
    marginVertical: 0,
    marginBottom: 16,
    borderWidth: 1,
    borderLeftWidth: 4,
  },
  toastContainer: {
    borderRadius: 8,
    padding: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderLeftWidth: 4,
  },
  icon: {
    marginRight: 8,
  },
  errorText: {
    flex: 1,
    fontSize: 14,
  },
  dismissButton: {
    marginLeft: 8,
    padding: 4,
  },
});

export default ErrorDisplay;
