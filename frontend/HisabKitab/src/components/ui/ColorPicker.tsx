import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView,
  Modal
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import Button from './Button';

interface ColorPickerProps {
  selectedColor: string;
  onColorSelected: (color: string) => void;
  label?: string;
}

// Predefined color palette
const COLORS = [
  '#F44336', // Red
  '#E91E63', // Pink
  '#9C27B0', // Purple
  '#673AB7', // Deep Purple
  '#3F51B5', // Indigo
  '#2196F3', // Blue
  '#03A9F4', // Light Blue
  '#00BCD4', // Cyan
  '#009688', // Teal
  '#4CAF50', // Green
  '#8BC34A', // Light Green
  '#CDDC39', // Lime
  '#FFEB3B', // Yellow
  '#FFC107', // Amber
  '#FF9800', // Orange
  '#FF5722', // Deep Orange
  '#795548', // <PERSON>
  '#9E9E9E', // Grey
  '#607D8B', // Blue Grey
  '#000000', // Black
  '#FFFFFF', // White
];

const ColorPicker: React.FC<ColorPickerProps> = ({
  selectedColor,
  onColorSelected,
  label,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  
  const handleColorSelect = (color: string) => {
    onColorSelected(color);
    setModalVisible(false);
  };
  
  return (
    <View style={styles.container}>
      {label && (
        <Text style={[
          styles.label, 
          { color: isDarkMode ? '#8B949E' : '#6E7781' }
        ]}>
          {label}
        </Text>
      )}
      
      <TouchableOpacity
        style={[
          styles.colorButton,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
          },
        ]}
        onPress={() => setModalVisible(true)}
      >
        <View style={styles.selectedColorContainer}>
          <View
            style={[
              styles.colorPreview,
              { backgroundColor: selectedColor },
              selectedColor === '#FFFFFF' && styles.whiteColorBorder,
            ]}
          />
          <Text
            style={[
              styles.colorText,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
          >
            {selectedColor}
          </Text>
        </View>
      </TouchableOpacity>
      
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
          >
            <Text
              style={[
                styles.modalTitle,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' },
              ]}
            >
              {t('categories.selectColor')}
            </Text>
            
            <ScrollView style={styles.colorGrid}>
              <View style={styles.colorsContainer}>
                {COLORS.map((color) => (
                  <TouchableOpacity
                    key={color}
                    style={[
                      styles.colorOption,
                      {
                        backgroundColor: color,
                        borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                        borderWidth: color === '#FFFFFF' ? 1 : 0,
                      },
                      selectedColor === color && styles.selectedColorOption,
                    ]}
                    onPress={() => handleColorSelect(color)}
                  />
                ))}
              </View>
            </ScrollView>
            
            <View style={styles.modalButtons}>
              <Button
                title={t('common.cancel')}
                onPress={() => setModalVisible(false)}
                variant="secondary"
                style={styles.modalButton}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    width: '100%',
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
  },
  colorButton: {
    height: 48,
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    justifyContent: 'center',
  },
  selectedColorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  colorPreview: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 8,
  },
  whiteColorBorder: {
    borderWidth: 1,
    borderColor: '#D0D7DE',
  },
  colorText: {
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    maxHeight: '80%',
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  colorGrid: {
    maxHeight: 300,
  },
  colorsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    margin: 8,
  },
  selectedColorOption: {
    borderWidth: 2,
    borderColor: '#58A6FF',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
  modalButton: {
    minWidth: 100,
    marginHorizontal: 8,
  },
});

export default ColorPicker;
