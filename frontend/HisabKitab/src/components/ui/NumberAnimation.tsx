import React, { useEffect, useRef } from 'react';
import { Animated, TextStyle, ViewStyle } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Typography from './Typography';

interface NumberAnimationProps {
  value: number;
  prefix?: string;
  suffix?: string;
  duration?: number;
  formatValue?: (value: number) => string;
  style?: TextStyle;
  containerStyle?: ViewStyle;
  variant?: 'h1' | 'h2' | 'h3' | 'bodyLarge' | 'bodySmall' | 'caption' | 'number' | 'hero';
  weight?: 'regular' | 'medium' | 'semibold' | 'bold';
  color?: string;
  testID?: string;
}

const NumberAnimation: React.FC<NumberAnimationProps> = ({
  value,
  prefix = '',
  suffix = '',
  duration = 1000,
  formatValue,
  style,
  containerStyle,
  variant = 'number',
  weight = 'bold',
  color,
  testID,
}) => {
  const { colors } = useTheme();
  const animatedValue = useRef(new Animated.Value(0)).current;
  const [displayValue, setDisplayValue] = React.useState('0');
  const prevValue = useRef(0);
  
  // Format the value using the provided formatter or default to toString
  const formatDisplayValue = (val: number): string => {
    if (formatValue) {
      return formatValue(val);
    }
    return val.toString();
  };
  
  // Animate the value when it changes
  useEffect(() => {
    // Don't animate on first render
    if (prevValue.current !== 0) {
      // Determine if value increased or decreased
      const isIncrease = value > prevValue.current;
      
      // Set the starting value
      animatedValue.setValue(prevValue.current);
      
      // Create the animation
      Animated.timing(animatedValue, {
        toValue: value,
        duration,
        useNativeDriver: false,
      }).start();
    } else {
      // On first render, just set the value without animation
      animatedValue.setValue(value);
    }
    
    // Update the previous value
    prevValue.current = value;
    
    // Add listener to update the display value during animation
    const listener = animatedValue.addListener(({ value: val }) => {
      setDisplayValue(formatDisplayValue(val));
    });
    
    // Clean up the listener
    return () => {
      animatedValue.removeListener(listener);
    };
  }, [value, animatedValue, duration, formatValue]);
  
  // Determine text color based on value change
  const getTextColor = () => {
    if (color) return color;
    
    // Default to the provided color or theme color
    return color || colors.text.dark.primary;
  };
  
  return (
    <Animated.View style={containerStyle}>
      <Typography
        variant={variant}
        weight={weight}
        color={getTextColor()}
        style={style}
        animated
        testID={testID}
      >
        {prefix}{displayValue}{suffix}
      </Typography>
    </Animated.View>
  );
};

export default NumberAnimation;
