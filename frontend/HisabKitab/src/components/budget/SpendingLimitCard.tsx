import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import ProgressBar from '../common/ProgressBar';

interface SpendingLimitCardProps {
  id: number;
  memberName: string;
  amount: number;
  spent: number;
  period: string;
  onPress?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
}

const SpendingLimitCard: React.FC<SpendingLimitCardProps> = ({
  id,
  memberName,
  amount,
  spent,
  period,
  onPress,
  onEdit,
  onDelete,
}) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  // Calculate percentage spent
  const percentSpent = amount > 0 ? (spent / amount) * 100 : 0;
  const remaining = amount - spent;
  const isOverLimit = remaining < 0;

  // Determine progress bar color based on percentage spent
  const getProgressColor = () => {
    if (percentSpent >= 100) return '#EF4444'; // Red for over limit
    if (percentSpent >= 80) return '#F59E0B'; // Yellow for approaching limit
    return '#10B981'; // Green for within limit
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' },
      ]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.header}>
        <Text
          style={[
            styles.memberName,
            { color: isDarkMode ? '#FFFFFF' : '#111827' },
          ]}
        >
          {memberName}
        </Text>
        <View style={styles.actions}>
          {onEdit && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={(e) => {
                e.stopPropagation();
                onEdit();
              }}
            >
              <Ionicons
                name="pencil"
                size={18}
                color={isDarkMode ? '#9CA3AF' : '#6B7280'}
              />
            </TouchableOpacity>
          )}
          {onDelete && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={(e) => {
                e.stopPropagation();
                onDelete();
              }}
            >
              <Ionicons
                name="trash"
                size={18}
                color={isDarkMode ? '#9CA3AF' : '#6B7280'}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <View style={styles.amountRow}>
        <Text
          style={[
            styles.amountLabel,
            { color: isDarkMode ? '#9CA3AF' : '#6B7280' },
          ]}
        >
          {t('budget.limit')}:
        </Text>
        <Text
          style={[
            styles.amountValue,
            { color: isDarkMode ? '#FFFFFF' : '#111827' },
          ]}
        >
          {amount.toFixed(2)}
        </Text>
      </View>

      <View style={styles.amountRow}>
        <Text
          style={[
            styles.amountLabel,
            { color: isDarkMode ? '#9CA3AF' : '#6B7280' },
          ]}
        >
          {t('budget.spent')}:
        </Text>
        <Text
          style={[
            styles.amountValue,
            { color: isDarkMode ? '#FFFFFF' : '#111827' },
          ]}
        >
          {spent.toFixed(2)}
        </Text>
      </View>

      <View style={styles.amountRow}>
        <Text
          style={[
            styles.amountLabel,
            { color: isDarkMode ? '#9CA3AF' : '#6B7280' },
          ]}
        >
          {t('budget.remaining')}:
        </Text>
        <Text
          style={[
            styles.amountValue,
            {
              color: isOverLimit
                ? '#EF4444'
                : isDarkMode
                ? '#FFFFFF'
                : '#111827',
            },
          ]}
        >
          {remaining.toFixed(2)}
        </Text>
      </View>

      <View style={styles.progressContainer}>
        <ProgressBar
          progress={Math.min(percentSpent, 100)}
          color={getProgressColor()}
          height={8}
        />
        <View style={styles.percentageRow}>
          <Text
            style={[
              styles.percentageText,
              { color: isDarkMode ? '#9CA3AF' : '#6B7280' },
            ]}
          >
            {Math.round(percentSpent)}% {t('budget.used')}
          </Text>
          <Text
            style={[
              styles.periodText,
              { color: isDarkMode ? '#9CA3AF' : '#6B7280' },
            ]}
          >
            {t(`budget.period.${period.toLowerCase()}`)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  memberName: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  actions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 4,
    marginLeft: 8,
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  amountLabel: {
    fontSize: 14,
  },
  amountValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressContainer: {
    marginTop: 12,
  },
  percentageRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  percentageText: {
    fontSize: 12,
  },
  periodText: {
    fontSize: 12,
  },
});

export default SpendingLimitCard;
