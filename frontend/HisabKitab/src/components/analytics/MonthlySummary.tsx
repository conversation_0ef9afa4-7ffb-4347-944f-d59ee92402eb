import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Share,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useAnalytics } from '../../contexts/AnalyticsContext';
import { formatCurrency, formatDate } from '../../utils/formatters';
import ChartContainer from './ChartContainer';

interface MonthlySummaryProps {
  month?: Date;
  isDarkMode: boolean;
  onViewDetails?: () => void;
}

const MonthlySummary: React.FC<MonthlySummaryProps> = ({
  month = new Date(),
  isDarkMode,
  onViewDetails,
}) => {
  const { t } = useTranslation();
  const { loading: isLoading } = useAnalytics();

  // Mock function for getting monthly summary
  const getMonthlySummary = async (date: Date) => {
    // In a real implementation, this would call the API
    return {
      totalIncome: 5000,
      totalExpense: 3500,
      netSavings: 1500,
      savingsRate: 30,
      topExpenseCategory: 'Food',
      topIncomeSource: 'Salary',
      expensesByCategory: [
        { category: 'Food', amount: 1200 },
        { category: 'Rent', amount: 1500 },
        { category: 'Transportation', amount: 400 },
        { category: 'Entertainment', amount: 300 },
        { category: 'Shopping', amount: 100 },
      ],
    };
  };

  const [summary, setSummary] = useState<any>(null);

  // Load monthly summary
  useEffect(() => {
    loadMonthlySummary();
  }, [month]);

  // Load monthly summary
  const loadMonthlySummary = async () => {
    const data = await getMonthlySummary(month);
    if (data) {
      setSummary(data);
    }
  };

  // Handle share
  const handleShare = async () => {
    if (!summary) return;

    const monthName = month.toLocaleString('default', { month: 'long' });
    const year = month.getFullYear();

    const message = `
${t('analytics.monthlySummary')} - ${monthName} ${year}

${t('analytics.totalIncome')}: ${formatCurrency(summary.totalIncome)}
${t('analytics.totalExpense')}: ${formatCurrency(summary.totalExpense)}
${t('analytics.netSavings')}: ${formatCurrency(summary.netSavings)}
${t('analytics.topExpenseCategory')}: ${summary.topExpenseCategory}
${t('analytics.topIncomeSource')}: ${summary.topIncomeSource}

${t('analytics.generatedBy')} Hisab-Kitab
    `;

    try {
      await Share.share({
        message,
        title: `${t('analytics.monthlySummary')} - ${monthName} ${year}`,
      });
    } catch (error) {
      console.error('Error sharing summary:', error);
    }
  };

  // Prepare category data for chart
  const prepareCategoryData = () => {
    if (!summary || !summary.expensesByCategory) return [];

    return summary.expensesByCategory.map((item: any) => ({
      name: item.category,
      value: item.amount,
      color: getRandomColor(),
    }));
  };

  // Get random color for chart
  const getRandomColor = () => {
    const colors = [
      '#F78166', '#58A6FF', '#3FB950', '#F85149', '#8B5CF6',
      '#F59E0B', '#0D9488', '#EC4899', '#6366F1', '#F43F5E',
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
        <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
      </View>
    );
  }

  if (!summary) {
    return (
      <View style={[styles.container, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
        <Text style={[styles.noDataText, { color: isDarkMode ? '#C9D1D9' : '#24292E' }]}>
          {t('analytics.noDataAvailable')}
        </Text>
      </View>
    );
  }

  const monthName = month.toLocaleString('default', { month: 'long' });
  const year = month.getFullYear();

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {t('analytics.monthlySummary')}
          </Text>
          <Text style={[styles.subtitle, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {`${monthName} ${year}`}
          </Text>
        </View>
        <View style={styles.actions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleShare}
          >
            <Ionicons
              name="share-outline"
              size={20}
              color={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
          </TouchableOpacity>
          {onViewDetails && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={onViewDetails}
            >
              <Ionicons
                name="arrow-forward"
                size={20}
                color={isDarkMode ? '#58A6FF' : '#0366D6'}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Summary metrics */}
      <View style={styles.metricsContainer}>
        <View style={styles.metricRow}>
          <View style={styles.metric}>
            <Text style={[styles.metricLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('analytics.totalIncome')}
            </Text>
            <Text
              style={[
                styles.metricValue,
                { color: isDarkMode ? '#3FB950' : '#2EA043' },
              ]}
            >
              {formatCurrency(summary.totalIncome)}
            </Text>
          </View>
          <View style={styles.metric}>
            <Text style={[styles.metricLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('analytics.totalExpense')}
            </Text>
            <Text
              style={[
                styles.metricValue,
                { color: isDarkMode ? '#F85149' : '#D73A49' },
              ]}
            >
              {formatCurrency(summary.totalExpense)}
            </Text>
          </View>
        </View>
        <View style={styles.metricRow}>
          <View style={styles.metric}>
            <Text style={[styles.metricLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('analytics.netSavings')}
            </Text>
            <Text
              style={[
                styles.metricValue,
                {
                  color: summary.netSavings >= 0
                    ? isDarkMode ? '#3FB950' : '#2EA043'
                    : isDarkMode ? '#F85149' : '#D73A49',
                },
              ]}
            >
              {formatCurrency(summary.netSavings)}
            </Text>
          </View>
          <View style={styles.metric}>
            <Text style={[styles.metricLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('analytics.savingsRate')}
            </Text>
            <Text
              style={[
                styles.metricValue,
                {
                  color: summary.savingsRate >= 0
                    ? isDarkMode ? '#3FB950' : '#2EA043'
                    : isDarkMode ? '#F85149' : '#D73A49',
                },
              ]}
            >
              {`${summary.savingsRate}%`}
            </Text>
          </View>
        </View>
      </View>

      {/* Expense by category chart */}
      {summary.expensesByCategory && summary.expensesByCategory.length > 0 && (
        <ChartContainer
          title={t('analytics.expensesByCategory')}
          type="pie"
          data={prepareCategoryData()}
          height={180}
        />
      )}

      {/* Top categories */}
      <View style={styles.topCategories}>
        <View style={styles.topCategory}>
          <Text style={[styles.topCategoryLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {t('analytics.topExpenseCategory')}
          </Text>
          <Text style={[styles.topCategoryValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {summary.topExpenseCategory}
          </Text>
        </View>
        <View style={styles.topCategory}>
          <Text style={[styles.topCategoryLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {t('analytics.topIncomeSource')}
          </Text>
          <Text style={[styles.topCategoryValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {summary.topIncomeSource}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 14,
  },
  actions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
  },
  metricsContainer: {
    marginBottom: 16,
  },
  metricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  metric: {
    flex: 1,
  },
  metricLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: '600',
  },
  topCategories: {
    marginTop: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  topCategory: {
    flex: 1,
  },
  topCategoryLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  topCategoryValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  noDataText: {
    textAlign: 'center',
    padding: 24,
    fontSize: 16,
  },
});

export default MonthlySummary;
