import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: string;
  iconColor?: string;
  trend?: number;
  onPress?: () => void;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  subtitle,
  icon = 'stats-chart',
  iconColor = '#0366D6',
  trend,
  onPress,
}) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  // Format trend display
  const formatTrend = () => {
    if (trend === undefined) return null;
    
    const isPositive = trend > 0;
    const trendIcon = isPositive ? 'arrow-up' : 'arrow-down';
    const trendColor = isPositive ? '#10B981' : '#EF4444';
    
    return (
      <View style={styles.trendContainer}>
        <Ionicons name={trendIcon} size={14} color={trendColor} />
        <Text style={[styles.trendText, { color: trendColor }]}>
          {Math.abs(trend).toFixed(1)}%
        </Text>
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' },
      ]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.iconContainer}>
        <View style={[styles.iconBackground, { backgroundColor: `${iconColor}20` }]}>
          <Ionicons name={icon as any} size={20} color={iconColor} />
        </View>
      </View>
      <View style={styles.contentContainer}>
        <Text
          style={[
            styles.title,
            { color: isDarkMode ? '#9CA3AF' : '#6B7280' },
          ]}
        >
          {title}
        </Text>
        <View style={styles.valueRow}>
          <Text
            style={[
              styles.value,
              { color: isDarkMode ? '#FFFFFF' : '#111827' },
            ]}
          >
            {value}
          </Text>
          {formatTrend()}
        </View>
        {subtitle && (
          <Text
            style={[
              styles.subtitle,
              { color: isDarkMode ? '#9CA3AF' : '#6B7280' },
            ]}
          >
            {subtitle}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  iconContainer: {
    marginRight: 16,
  },
  iconBackground: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
  },
  title: {
    fontSize: 14,
    marginBottom: 4,
  },
  valueRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  value: {
    fontSize: 20,
    fontWeight: 'bold',
    marginRight: 8,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    fontSize: 12,
    marginLeft: 2,
  },
  subtitle: {
    fontSize: 12,
  },
});

export default MetricCard;
