import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { format, subMonths, startOfMonth, endOfMonth, startOfYear, endOfYear } from 'date-fns';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

interface DateRangePickerProps {
  startDate: string;
  endDate: string;
  onDateRangeChange: (startDate: string, endDate: string) => void;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  startDate,
  endDate,
  onDateRangeChange,
}) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [tempStartDate, setTempStartDate] = useState(startDate);
  const [tempEndDate, setTempEndDate] = useState(endDate);

  // Predefined date ranges
  const today = new Date();
  const thisMonth = {
    start: format(startOfMonth(today), 'yyyy-MM-dd'),
    end: format(endOfMonth(today), 'yyyy-MM-dd'),
  };
  const lastMonth = {
    start: format(startOfMonth(subMonths(today, 1)), 'yyyy-MM-dd'),
    end: format(endOfMonth(subMonths(today, 1)), 'yyyy-MM-dd'),
  };
  const last3Months = {
    start: format(subMonths(today, 3), 'yyyy-MM-dd'),
    end: format(today, 'yyyy-MM-dd'),
  };
  const thisYear = {
    start: format(startOfYear(today), 'yyyy-MM-dd'),
    end: format(endOfYear(today), 'yyyy-MM-dd'),
  };

  // Apply a predefined date range
  const applyPresetRange = (start: string, end: string) => {
    setTempStartDate(start);
    setTempEndDate(end);
    onDateRangeChange(start, end);
    setIsModalVisible(false);
  };

  // Format dates for display
  const formatDateRange = () => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    return `${format(start, 'MMM d, yyyy')} - ${format(end, 'MMM d, yyyy')}`;
  };



  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.dateRangeButton,
          { backgroundColor: isDarkMode ? '#1F2937' : '#F3F4F6' }
        ]}
        onPress={() => setIsModalVisible(true)}
      >
        <Ionicons name="calendar-outline" size={18} color={isDarkMode ? '#FFFFFF' : '#374151'} />
        <Text
          style={[
            styles.dateRangeText,
            { color: isDarkMode ? '#FFFFFF' : '#374151' }
          ]}
        >
          {formatDateRange()}
        </Text>
        <Ionicons name="chevron-down" size={18} color={isDarkMode ? '#FFFFFF' : '#374151'} />
      </TouchableOpacity>

      <Modal
        visible={isModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[
              styles.modalContent,
              { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' }
            ]}
          >
            <View style={styles.modalHeader}>
              <Text
                style={[
                  styles.modalTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#111827' }
                ]}
              >
                {t('analytics.selectDateRange')}
              </Text>
              <TouchableOpacity onPress={() => setIsModalVisible(false)}>
                <Ionicons name="close" size={24} color={isDarkMode ? '#FFFFFF' : '#374151'} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.presetContainer}>
              <TouchableOpacity
                style={[
                  styles.presetOption,
                  { backgroundColor: isDarkMode ? '#374151' : '#F9FAFB' }
                ]}
                onPress={() => applyPresetRange(thisMonth.start, thisMonth.end)}
              >
                <Text
                  style={[
                    styles.presetOptionText,
                    { color: isDarkMode ? '#FFFFFF' : '#374151' }
                  ]}
                >
                  {t('analytics.thisMonth')}
                </Text>
                <Text
                  style={[
                    styles.presetOptionDate,
                    { color: isDarkMode ? '#9CA3AF' : '#6B7280' }
                  ]}
                >
                  {format(new Date(thisMonth.start), 'MMM d')} - {format(new Date(thisMonth.end), 'MMM d, yyyy')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.presetOption,
                  { backgroundColor: isDarkMode ? '#374151' : '#F9FAFB' }
                ]}
                onPress={() => applyPresetRange(lastMonth.start, lastMonth.end)}
              >
                <Text
                  style={[
                    styles.presetOptionText,
                    { color: isDarkMode ? '#FFFFFF' : '#374151' }
                  ]}
                >
                  {t('analytics.lastMonth')}
                </Text>
                <Text
                  style={[
                    styles.presetOptionDate,
                    { color: isDarkMode ? '#9CA3AF' : '#6B7280' }
                  ]}
                >
                  {format(new Date(lastMonth.start), 'MMM d')} - {format(new Date(lastMonth.end), 'MMM d, yyyy')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.presetOption,
                  { backgroundColor: isDarkMode ? '#374151' : '#F9FAFB' }
                ]}
                onPress={() => applyPresetRange(last3Months.start, last3Months.end)}
              >
                <Text
                  style={[
                    styles.presetOptionText,
                    { color: isDarkMode ? '#FFFFFF' : '#374151' }
                  ]}
                >
                  {t('analytics.last3Months')}
                </Text>
                <Text
                  style={[
                    styles.presetOptionDate,
                    { color: isDarkMode ? '#9CA3AF' : '#6B7280' }
                  ]}
                >
                  {format(new Date(last3Months.start), 'MMM d')} - {format(new Date(last3Months.end), 'MMM d, yyyy')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.presetOption,
                  { backgroundColor: isDarkMode ? '#374151' : '#F9FAFB' }
                ]}
                onPress={() => applyPresetRange(thisYear.start, thisYear.end)}
              >
                <Text
                  style={[
                    styles.presetOptionText,
                    { color: isDarkMode ? '#FFFFFF' : '#374151' }
                  ]}
                >
                  {t('analytics.thisYear')}
                </Text>
                <Text
                  style={[
                    styles.presetOptionDate,
                    { color: isDarkMode ? '#9CA3AF' : '#6B7280' }
                  ]}
                >
                  {format(new Date(thisYear.start), 'MMM d')} - {format(new Date(thisYear.end), 'MMM d, yyyy')}
                </Text>
              </TouchableOpacity>
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.cancelButton, { borderColor: isDarkMode ? '#4B5563' : '#D1D5DB' }]}
                onPress={() => setIsModalVisible(false)}
              >
                <Text
                  style={[
                    styles.cancelButtonText,
                    { color: isDarkMode ? '#FFFFFF' : '#374151' }
                  ]}
                >
                  {t('common.cancel')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  dateRangeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  dateRangeText: {
    fontSize: 14,
    marginHorizontal: 8,
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 12,
    padding: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  presetContainer: {
    maxHeight: 300,
    marginBottom: 16,
  },
  presetOption: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  presetOptionText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  presetOptionDate: {
    fontSize: 14,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 14,
  },
});

export default DateRangePicker;
