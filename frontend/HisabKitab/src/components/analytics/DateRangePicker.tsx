import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal } from 'react-native';
import { useTranslation } from 'react-i18next';
import { format, subMonths, startOfMonth, endOfMonth, startOfYear, endOfYear } from 'date-fns';
// @ts-ignore - Missing type definitions for react-native-calendars
import { Calendar } from 'react-native-calendars';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

interface DateRangePickerProps {
  startDate: string;
  endDate: string;
  onDateRangeChange: (startDate: string, endDate: string) => void;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  startDate,
  endDate,
  onDateRangeChange,
}) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [tempStartDate, setTempStartDate] = useState(startDate);
  const [tempEndDate, setTempEndDate] = useState(endDate);
  const [selectingStart, setSelectingStart] = useState(true);

  // Predefined date ranges
  const today = new Date();
  const thisMonth = {
    start: format(startOfMonth(today), 'yyyy-MM-dd'),
    end: format(endOfMonth(today), 'yyyy-MM-dd'),
  };
  const lastMonth = {
    start: format(startOfMonth(subMonths(today, 1)), 'yyyy-MM-dd'),
    end: format(endOfMonth(subMonths(today, 1)), 'yyyy-MM-dd'),
  };
  const last3Months = {
    start: format(subMonths(today, 3), 'yyyy-MM-dd'),
    end: format(today, 'yyyy-MM-dd'),
  };
  const thisYear = {
    start: format(startOfYear(today), 'yyyy-MM-dd'),
    end: format(endOfYear(today), 'yyyy-MM-dd'),
  };

  // Apply a predefined date range
  const applyPresetRange = (start: string, end: string) => {
    setTempStartDate(start);
    setTempEndDate(end);
    onDateRangeChange(start, end);
    setIsModalVisible(false);
  };

  // Handle date selection in the calendar
  const handleDayPress = (day: { dateString: string }) => {
    if (selectingStart) {
      setTempStartDate(day.dateString);
      setTempEndDate(day.dateString);
      setSelectingStart(false);
    } else {
      // Ensure end date is not before start date
      if (day.dateString < tempStartDate) {
        setTempStartDate(day.dateString);
      } else {
        setTempEndDate(day.dateString);
        setSelectingStart(true);
      }
    }
  };

  // Apply custom date range
  const applyCustomRange = () => {
    onDateRangeChange(tempStartDate, tempEndDate);
    setIsModalVisible(false);
  };

  // Format dates for display
  const formatDateRange = () => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    return `${format(start, 'MMM d, yyyy')} - ${format(end, 'MMM d, yyyy')}`;
  };

  // Generate marked dates for the calendar
  const getMarkedDates = () => {
    const markedDates: any = {};

    // Mark start date
    markedDates[tempStartDate] = {
      startingDay: true,
      color: '#0366D6',
      textColor: 'white',
    };

    // Mark end date
    if (tempEndDate !== tempStartDate) {
      markedDates[tempEndDate] = {
        endingDay: true,
        color: '#0366D6',
        textColor: 'white',
      };
    }

    return markedDates;
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.dateRangeButton,
          { backgroundColor: isDarkMode ? '#1F2937' : '#F3F4F6' }
        ]}
        onPress={() => setIsModalVisible(true)}
      >
        <Ionicons name="calendar-outline" size={18} color={isDarkMode ? '#FFFFFF' : '#374151'} />
        <Text
          style={[
            styles.dateRangeText,
            { color: isDarkMode ? '#FFFFFF' : '#374151' }
          ]}
        >
          {formatDateRange()}
        </Text>
        <Ionicons name="chevron-down" size={18} color={isDarkMode ? '#FFFFFF' : '#374151'} />
      </TouchableOpacity>

      <Modal
        visible={isModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[
              styles.modalContent,
              { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' }
            ]}
          >
            <View style={styles.modalHeader}>
              <Text
                style={[
                  styles.modalTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#111827' }
                ]}
              >
                {t('analytics.selectDateRange')}
              </Text>
              <TouchableOpacity onPress={() => setIsModalVisible(false)}>
                <Ionicons name="close" size={24} color={isDarkMode ? '#FFFFFF' : '#374151'} />
              </TouchableOpacity>
            </View>

            <View style={styles.presetContainer}>
              <TouchableOpacity
                style={styles.presetButton}
                onPress={() => applyPresetRange(thisMonth.start, thisMonth.end)}
              >
                <Text
                  style={[
                    styles.presetButtonText,
                    { color: isDarkMode ? '#FFFFFF' : '#374151' }
                  ]}
                >
                  {t('analytics.thisMonth')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.presetButton}
                onPress={() => applyPresetRange(lastMonth.start, lastMonth.end)}
              >
                <Text
                  style={[
                    styles.presetButtonText,
                    { color: isDarkMode ? '#FFFFFF' : '#374151' }
                  ]}
                >
                  {t('analytics.lastMonth')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.presetButton}
                onPress={() => applyPresetRange(last3Months.start, last3Months.end)}
              >
                <Text
                  style={[
                    styles.presetButtonText,
                    { color: isDarkMode ? '#FFFFFF' : '#374151' }
                  ]}
                >
                  {t('analytics.last3Months')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.presetButton}
                onPress={() => applyPresetRange(thisYear.start, thisYear.end)}
              >
                <Text
                  style={[
                    styles.presetButtonText,
                    { color: isDarkMode ? '#FFFFFF' : '#374151' }
                  ]}
                >
                  {t('analytics.thisYear')}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.customRangeContainer}>
              <Text
                style={[
                  styles.customRangeTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#374151' }
                ]}
              >
                {t('analytics.customRange')}
              </Text>
              <Text
                style={[
                  styles.customRangeInstructions,
                  { color: isDarkMode ? '#9CA3AF' : '#6B7280' }
                ]}
              >
                {selectingStart
                  ? t('analytics.selectStartDate')
                  : t('analytics.selectEndDate')}
              </Text>
            </View>

            <Calendar
              onDayPress={handleDayPress}
              markingType="period"
              markedDates={getMarkedDates()}
              theme={{
                calendarBackground: isDarkMode ? '#1F2937' : '#FFFFFF',
                textSectionTitleColor: isDarkMode ? '#FFFFFF' : '#374151',
                dayTextColor: isDarkMode ? '#FFFFFF' : '#374151',
                todayTextColor: '#0366D6',
                selectedDayTextColor: '#FFFFFF',
                monthTextColor: isDarkMode ? '#FFFFFF' : '#111827',
                indicatorColor: '#0366D6',
                textDisabledColor: isDarkMode ? '#4B5563' : '#9CA3AF',
                arrowColor: '#0366D6',
              }}
            />

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.cancelButton, { borderColor: isDarkMode ? '#4B5563' : '#D1D5DB' }]}
                onPress={() => setIsModalVisible(false)}
              >
                <Text
                  style={[
                    styles.cancelButtonText,
                    { color: isDarkMode ? '#FFFFFF' : '#374151' }
                  ]}
                >
                  {t('common.cancel')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.applyButton, { backgroundColor: '#0366D6' }]}
                onPress={applyCustomRange}
              >
                <Text style={styles.applyButtonText}>{t('common.apply')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  dateRangeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  dateRangeText: {
    fontSize: 14,
    marginHorizontal: 8,
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 12,
    padding: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  presetContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  presetButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: 'rgba(3, 102, 214, 0.1)',
    marginRight: 8,
    marginBottom: 8,
  },
  presetButtonText: {
    fontSize: 12,
  },
  customRangeContainer: {
    marginBottom: 16,
  },
  customRangeTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  customRangeInstructions: {
    fontSize: 14,
    marginBottom: 8,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 14,
  },
  applyButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  applyButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
  },
});

export default DateRangePicker;
