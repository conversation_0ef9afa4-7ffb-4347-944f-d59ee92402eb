import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pie<PERSON><PERSON> } from 'react-native-chart-kit';

interface ChartContainerProps {
  title: string;
  subtitle?: string;
  type: 'line' | 'bar' | 'pie';
  data: any;
  height?: number;
  width?: number;
  yAxisSuffix?: string;
  yAxisPrefix?: string;
  chartConfig?: any;
}

const ChartContainer: React.FC<ChartContainerProps> = ({
  title,
  subtitle,
  type,
  data,
  height = 220,
  width = Dimensions.get('window').width - 32,
  yAxisSuffix = '',
  yAxisPrefix = '',
  chartConfig,
}) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  // Default chart configuration
  const defaultChartConfig = {
    backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
    backgroundGradientFrom: isDarkMode ? '#1F2937' : '#FFFFFF',
    backgroundGradientTo: isDarkMode ? '#1F2937' : '#FFFFFF',
    decimalPlaces: 0,
    color: (opacity = 1) => isDarkMode ? `rgba(255, 255, 255, ${opacity})` : `rgba(3, 102, 214, ${opacity})`,
    labelColor: (opacity = 1) => isDarkMode ? `rgba(255, 255, 255, ${opacity})` : `rgba(55, 65, 81, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '4',
      strokeWidth: '2',
      stroke: isDarkMode ? '#58A6FF' : '#0366D6',
    },
    propsForLabels: {
      fontSize: 10,
    },
  };

  // Merge default config with provided config
  const mergedChartConfig = { ...defaultChartConfig, ...chartConfig };

  // Render the appropriate chart type
  const renderChart = () => {
    switch (type) {
      case 'line':
        return (
          <LineChart
            data={data}
            width={width}
            height={height}
            yAxisSuffix={yAxisSuffix}
            // @ts-ignore - yAxisPrefix is not in the type definitions but works in the library
            yAxisPrefix={yAxisPrefix}
            chartConfig={mergedChartConfig}
            bezier
            style={styles.chart}
          />
        );
      case 'bar':
        return (
          <BarChart
            data={data}
            width={width}
            height={height}
            yAxisSuffix={yAxisSuffix}
            // @ts-ignore - yAxisPrefix is not in the type definitions but works in the library
            yAxisPrefix={yAxisPrefix}
            chartConfig={mergedChartConfig}
            style={styles.chart}
          />
        );
      case 'pie':
        return (
          <PieChart
            data={data}
            width={width}
            height={height}
            chartConfig={mergedChartConfig}
            accessor="value"
            backgroundColor="transparent"
            paddingLeft="15"
            absolute
          />
        );
      default:
        return null;
    }
  };

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF' },
      ]}
    >
      <View style={styles.headerContainer}>
        <Text
          style={[
            styles.title,
            { color: isDarkMode ? '#FFFFFF' : '#111827' },
          ]}
        >
          {title}
        </Text>
        {subtitle && (
          <Text
            style={[
              styles.subtitle,
              { color: isDarkMode ? '#9CA3AF' : '#6B7280' },
            ]}
          >
            {subtitle}
          </Text>
        )}
      </View>
      <View style={styles.chartContainer}>{renderChart()}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  headerContainer: {
    marginBottom: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 12,
  },
  chartContainer: {
    alignItems: 'center',
  },
  chart: {
    borderRadius: 16,
  },
});

export default ChartContainer;
