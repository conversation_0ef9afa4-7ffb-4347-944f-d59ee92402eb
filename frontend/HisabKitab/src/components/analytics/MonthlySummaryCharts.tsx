import React from 'react';
import { View, Text, StyleSheet, Dimensions, ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import ChartContainer from './ChartContainer';
import { formatCurrency } from '../../utils/formatters';

// Chart types
export type ChartType = 'income-expense' | 'category-breakdown' | 'savings-trend' | 'budget-usage' | 'account-balance';

interface MonthlySummaryChartsProps {
  month: Date;
  data: any;
  chartTypes?: ChartType[];
}

const MonthlySummaryCharts: React.FC<MonthlySummaryChartsProps> = ({
  month,
  data,
  chartTypes = ['income-expense', 'category-breakdown', 'savings-trend', 'budget-usage', 'account-balance'],
}) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  
  // Format month name
  const formatMonthName = (date: Date) => {
    return date.toLocaleDateString(t('common.locale'), { month: 'long', year: 'numeric' });
  };
  
  // Prepare income vs expense chart data
  const prepareIncomeExpenseData = () => {
    const incomeExpenseData = {
      labels: [t('analytics.income'), t('analytics.expense')],
      datasets: [
        {
          data: [
            data.totalIncome || 0,
            data.totalExpense || 0,
          ],
          color: (opacity = 1) => [
            `rgba(46, 160, 67, ${opacity})`,
            `rgba(248, 81, 73, ${opacity})`,
          ],
          strokeWidth: 2,
        },
      ],
    };
    
    return incomeExpenseData;
  };
  
  // Prepare category breakdown chart data
  const prepareCategoryBreakdownData = () => {
    if (!data.categoryBreakdown || data.categoryBreakdown.length === 0) {
      return {
        labels: [t('analytics.noData')],
        data: [1],
        colors: ['#8B949E'],
      };
    }
    
    // Sort categories by amount
    const sortedCategories = [...data.categoryBreakdown].sort((a, b) => b.amount - a.amount);
    
    // Take top 5 categories and group the rest as "Other"
    const topCategories = sortedCategories.slice(0, 5);
    const otherCategories = sortedCategories.slice(5);
    
    const otherAmount = otherCategories.reduce((sum, cat) => sum + cat.amount, 0);
    
    const chartData = topCategories.map(cat => ({
      name: cat.name,
      value: cat.amount,
      color: cat.color || getRandomColor(),
      legendFontColor: isDarkMode ? '#FFFFFF' : '#000000',
    }));
    
    if (otherAmount > 0) {
      chartData.push({
        name: t('analytics.other'),
        value: otherAmount,
        color: '#8B949E',
        legendFontColor: isDarkMode ? '#FFFFFF' : '#000000',
      });
    }
    
    return chartData;
  };
  
  // Prepare savings trend chart data
  const prepareSavingsTrendData = () => {
    if (!data.savingsTrend || data.savingsTrend.length === 0) {
      return {
        labels: [t('analytics.noData')],
        datasets: [
          {
            data: [0],
            color: (opacity = 1) => `rgba(88, 166, 255, ${opacity})`,
            strokeWidth: 2,
          },
        ],
      };
    }
    
    const labels = data.savingsTrend.map((item: any) => item.label);
    const values = data.savingsTrend.map((item: any) => item.value);
    
    return {
      labels,
      datasets: [
        {
          data: values,
          color: (opacity = 1) => `rgba(88, 166, 255, ${opacity})`,
          strokeWidth: 2,
        },
      ],
    };
  };
  
  // Prepare budget usage chart data
  const prepareBudgetUsageData = () => {
    if (!data.budgetUsage || data.budgetUsage.length === 0) {
      return {
        labels: [t('analytics.noData')],
        datasets: [
          {
            data: [0],
            color: (opacity = 1) => `rgba(240, 136, 62, ${opacity})`,
            strokeWidth: 2,
          },
        ],
      };
    }
    
    const labels = data.budgetUsage.map((item: any) => item.category);
    const values = data.budgetUsage.map((item: any) => 
      (item.spent / item.budget) * 100
    );
    
    return {
      labels,
      datasets: [
        {
          data: values,
          color: (opacity = 1) => `rgba(240, 136, 62, ${opacity})`,
          strokeWidth: 2,
        },
      ],
    };
  };
  
  // Prepare account balance chart data
  const prepareAccountBalanceData = () => {
    if (!data.accountBalances || data.accountBalances.length === 0) {
      return {
        labels: [t('analytics.noData')],
        datasets: [
          {
            data: [0],
            color: (opacity = 1) => `rgba(31, 111, 235, ${opacity})`,
            strokeWidth: 2,
          },
        ],
      };
    }
    
    const labels = data.accountBalances.map((item: any) => item.name);
    const values = data.accountBalances.map((item: any) => item.balance);
    
    return {
      labels,
      datasets: [
        {
          data: values,
          color: (opacity = 1) => `rgba(31, 111, 235, ${opacity})`,
          strokeWidth: 2,
        },
      ],
    };
  };
  
  // Helper function to get random color
  const getRandomColor = () => {
    const colors = [
      '#58A6FF', '#F85149', '#3FB950', '#F0883E', '#8957E5',
      '#D29922', '#1F6FEB', '#DB61A2', '#6E7781', '#238636',
    ];
    
    return colors[Math.floor(Math.random() * colors.length)];
  };
  
  // Render income vs expense chart
  const renderIncomeExpenseChart = () => {
    if (!chartTypes.includes('income-expense')) return null;
    
    const chartData = prepareIncomeExpenseData();
    
    return (
      <ChartContainer
        title={t('analytics.incomeVsExpense')}
        subtitle={formatMonthName(month)}
        type="bar"
        data={chartData}
        yAxisSuffix=""
        yAxisPrefix={data.currencySymbol || '$'}
      />
    );
  };
  
  // Render category breakdown chart
  const renderCategoryBreakdownChart = () => {
    if (!chartTypes.includes('category-breakdown')) return null;
    
    const chartData = prepareCategoryBreakdownData();
    
    return (
      <ChartContainer
        title={t('analytics.categoryBreakdown')}
        subtitle={formatMonthName(month)}
        type="pie"
        data={chartData}
      />
    );
  };
  
  // Render savings trend chart
  const renderSavingsTrendChart = () => {
    if (!chartTypes.includes('savings-trend')) return null;
    
    const chartData = prepareSavingsTrendData();
    
    return (
      <ChartContainer
        title={t('analytics.savingsTrend')}
        subtitle={formatMonthName(month)}
        type="line"
        data={chartData}
        yAxisSuffix=""
        yAxisPrefix={data.currencySymbol || '$'}
      />
    );
  };
  
  // Render budget usage chart
  const renderBudgetUsageChart = () => {
    if (!chartTypes.includes('budget-usage')) return null;
    
    const chartData = prepareBudgetUsageData();
    
    return (
      <ChartContainer
        title={t('analytics.budgetUsage')}
        subtitle={formatMonthName(month)}
        type="bar"
        data={chartData}
        yAxisSuffix="%"
      />
    );
  };
  
  // Render account balance chart
  const renderAccountBalanceChart = () => {
    if (!chartTypes.includes('account-balance')) return null;
    
    const chartData = prepareAccountBalanceData();
    
    return (
      <ChartContainer
        title={t('analytics.accountBalances')}
        subtitle={formatMonthName(month)}
        type="bar"
        data={chartData}
        yAxisSuffix=""
        yAxisPrefix={data.currencySymbol || '$'}
      />
    );
  };
  
  // Render summary cards
  const renderSummaryCards = () => {
    return (
      <View style={styles.summaryCardsContainer}>
        <View
          style={[
            styles.summaryCard,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
        >
          <Text
            style={[
              styles.summaryCardTitle,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t('analytics.totalIncome')}
          </Text>
          <Text
            style={[
              styles.summaryCardValue,
              { color: isDarkMode ? '#3FB950' : '#2EA043' },
            ]}
          >
            {formatCurrency(data.totalIncome || 0, data.currencyCode || 'USD')}
          </Text>
        </View>
        
        <View
          style={[
            styles.summaryCard,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
        >
          <Text
            style={[
              styles.summaryCardTitle,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t('analytics.totalExpense')}
          </Text>
          <Text
            style={[
              styles.summaryCardValue,
              { color: isDarkMode ? '#F85149' : '#CF222E' },
            ]}
          >
            {formatCurrency(data.totalExpense || 0, data.currencyCode || 'USD')}
          </Text>
        </View>
        
        <View
          style={[
            styles.summaryCard,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
        >
          <Text
            style={[
              styles.summaryCardTitle,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t('analytics.netSavings')}
          </Text>
          <Text
            style={[
              styles.summaryCardValue,
              {
                color:
                  (data.totalIncome || 0) - (data.totalExpense || 0) >= 0
                    ? isDarkMode ? '#3FB950' : '#2EA043'
                    : isDarkMode ? '#F85149' : '#CF222E',
              },
            ]}
          >
            {formatCurrency(
              (data.totalIncome || 0) - (data.totalExpense || 0),
              data.currencyCode || 'USD'
            )}
          </Text>
        </View>
      </View>
    );
  };
  
  return (
    <ScrollView
      contentContainerStyle={styles.container}
      showsVerticalScrollIndicator={false}
    >
      {renderSummaryCards()}
      {renderIncomeExpenseChart()}
      {renderCategoryBreakdownChart()}
      {renderSavingsTrendChart()}
      {renderBudgetUsageChart()}
      {renderAccountBalanceChart()}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingBottom: 16,
  },
  summaryCardsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryCard: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  summaryCardTitle: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryCardValue: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default MonthlySummaryCharts;
