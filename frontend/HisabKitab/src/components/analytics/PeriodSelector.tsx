import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';

interface PeriodSelectorProps {
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
  periods?: string[];
}

const PeriodSelector: React.FC<PeriodSelectorProps> = ({
  selectedPeriod,
  onPeriodChange,
  periods = ['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Yearly'],
}) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {periods.map((period) => (
          <TouchableOpacity
            key={period}
            style={[
              styles.periodButton,
              selectedPeriod === period && styles.selectedPeriodButton,
              {
                backgroundColor: isDarkMode
                  ? selectedPeriod === period
                    ? '#0366D6'
                    : '#1F2937'
                  : selectedPeriod === period
                  ? '#0366D6'
                  : '#F3F4F6',
              },
            ]}
            onPress={() => onPeriodChange(period)}
          >
            <Text
              style={[
                styles.periodText,
                selectedPeriod === period && styles.selectedPeriodText,
                {
                  color: isDarkMode
                    ? selectedPeriod === period
                      ? '#FFFFFF'
                      : '#D1D5DB'
                    : selectedPeriod === period
                    ? '#FFFFFF'
                    : '#374151',
                },
              ]}
            >
              {t(`analytics.period.${period.toLowerCase()}`)}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  scrollContent: {
    paddingVertical: 4,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  selectedPeriodButton: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  periodText: {
    fontSize: 14,
  },
  selectedPeriodText: {
    fontWeight: '500',
  },
});

export default PeriodSelector;
