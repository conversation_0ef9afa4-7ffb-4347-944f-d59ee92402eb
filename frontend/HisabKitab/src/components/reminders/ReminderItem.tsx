import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { LoanReminder } from '../../models/Loan';
import { formatDate } from '../../utils/formatters';

interface ReminderItemProps {
  reminder: LoanReminder;
  onPress: (reminder: LoanReminder) => void;
  onDelete: (id: number) => Promise<void>;
  isDarkMode: boolean;
}

const ReminderItem: React.FC<ReminderItemProps> = ({
  reminder,
  onPress,
  onDelete,
  isDarkMode,
}) => {
  const { t } = useTranslation();

  // Handle delete
  const handleDelete = async (e: any) => {
    e.stopPropagation();
    await onDelete(reminder.id);
  };

  // Get status color
  const getStatusColor = () => {
    if (!reminder.isActive) {
      return isDarkMode ? '#8B949E' : '#6E7781'; // Inactive
    }
    
    const reminderDate = new Date(reminder.reminderDate);
    const now = new Date();
    const diffDays = Math.floor((reminderDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return '#F85149'; // Overdue
    } else if (diffDays < 3) {
      return '#F78166'; // Soon
    } else {
      return '#3FB950'; // Upcoming
    }
  };

  // Get status text
  const getStatusText = () => {
    if (!reminder.isActive) {
      return t('reminders.inactive');
    }
    
    const reminderDate = new Date(reminder.reminderDate);
    const now = new Date();
    const diffDays = Math.floor((reminderDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return t('reminders.overdue');
    } else if (diffDays === 0) {
      return t('reminders.today');
    } else if (diffDays === 1) {
      return t('reminders.tomorrow');
    } else if (diffDays < 7) {
      return t('reminders.thisWeek');
    } else {
      return t('reminders.upcoming');
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
          borderLeftColor: getStatusColor(),
        },
      ]}
      onPress={() => onPress(reminder)}
    >
      <View style={styles.iconContainer}>
        <Ionicons
          name="notifications-outline"
          size={24}
          color={isDarkMode ? '#8B949E' : '#6E7781'}
        />
      </View>
      <View style={styles.content}>
        <Text
          style={[
            styles.title,
            { color: isDarkMode ? '#FFFFFF' : '#000000' },
          ]}
        >
          {reminder.message}
        </Text>
        <View style={styles.detailsRow}>
          <Text
            style={[
              styles.date,
              { color: isDarkMode ? '#C9D1D9' : '#24292E' },
            ]}
          >
            {formatDate(reminder.reminderDate)}
          </Text>
          <View style={styles.statusContainer}>
            <Text
              style={[
                styles.status,
                { color: getStatusColor() },
              ]}
            >
              {getStatusText()}
            </Text>
          </View>
        </View>
      </View>
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={handleDelete}
      >
        <Ionicons
          name="trash-outline"
          size={20}
          color={isDarkMode ? '#F85149' : '#D73A49'}
        />
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 16,
    marginVertical: 8,
    borderRadius: 8,
    borderLeftWidth: 4,
  },
  iconContainer: {
    marginRight: 12,
    justifyContent: 'center',
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  date: {
    fontSize: 14,
  },
  statusContainer: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  status: {
    fontSize: 12,
    fontWeight: '500',
  },
  deleteButton: {
    padding: 8,
    justifyContent: 'center',
  },
});

export default ReminderItem;
