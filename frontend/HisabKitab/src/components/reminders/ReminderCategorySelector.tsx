import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';

// Reminder category types
export type ReminderCategoryType = 
  | 'bill'
  | 'loan'
  | 'personal'
  | 'family'
  | 'savings'
  | 'budget'
  | 'transaction'
  | 'account'
  | 'other';

// Reminder category interface
export interface ReminderCategory {
  id: string;
  name: string;
  type: ReminderCategoryType;
  icon: string;
  color: string;
}

// Default reminder categories
export const DEFAULT_REMINDER_CATEGORIES: ReminderCategory[] = [
  {
    id: 'bill',
    name: 'reminders.categories.bill',
    type: 'bill',
    icon: 'receipt-outline',
    color: '#F85149',
  },
  {
    id: 'loan',
    name: 'reminders.categories.loan',
    type: 'loan',
    icon: 'cash-outline',
    color: '#58A6FF',
  },
  {
    id: 'personal',
    name: 'reminders.categories.personal',
    type: 'personal',
    icon: 'person-outline',
    color: '#8957E5',
  },
  {
    id: 'family',
    name: 'reminders.categories.family',
    type: 'family',
    icon: 'people-outline',
    color: '#3FB950',
  },
  {
    id: 'savings',
    name: 'reminders.categories.savings',
    type: 'savings',
    icon: 'trending-up-outline',
    color: '#F0883E',
  },
  {
    id: 'budget',
    name: 'reminders.categories.budget',
    type: 'budget',
    icon: 'calculator-outline',
    color: '#D29922',
  },
  {
    id: 'transaction',
    name: 'reminders.categories.transaction',
    type: 'transaction',
    icon: 'swap-horizontal-outline',
    color: '#2EA043',
  },
  {
    id: 'account',
    name: 'reminders.categories.account',
    type: 'account',
    icon: 'wallet-outline',
    color: '#1F6FEB',
  },
  {
    id: 'other',
    name: 'reminders.categories.other',
    type: 'other',
    icon: 'ellipsis-horizontal-outline',
    color: '#6E7781',
  },
];

interface ReminderCategorySelectorProps {
  selectedCategory: ReminderCategory;
  onSelectCategory: (category: ReminderCategory) => void;
  customCategories?: ReminderCategory[];
  allowCustom?: boolean;
}

const ReminderCategorySelector: React.FC<ReminderCategorySelectorProps> = ({
  selectedCategory,
  onSelectCategory,
  customCategories = [],
  allowCustom = false,
}) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  
  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddingCustom, setIsAddingCustom] = useState(false);
  const [customName, setCustomName] = useState('');
  const [customIcon, setCustomIcon] = useState('ellipsis-horizontal-outline');
  const [customColor, setCustomColor] = useState('#6E7781');
  
  // Combine default and custom categories
  const allCategories = [...DEFAULT_REMINDER_CATEGORIES, ...customCategories];
  
  // Filter categories based on search query
  const filteredCategories = allCategories.filter(category => 
    t(category.name).toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  // Handle select category
  const handleSelectCategory = (category: ReminderCategory) => {
    onSelectCategory(category);
    setModalVisible(false);
  };
  
  // Handle add custom category
  const handleAddCustomCategory = () => {
    if (!customName.trim()) return;
    
    const newCategory: ReminderCategory = {
      id: `custom-${Date.now()}`,
      name: customName.trim(),
      type: 'other',
      icon: customIcon,
      color: customColor,
    };
    
    onSelectCategory(newCategory);
    setModalVisible(false);
    setIsAddingCustom(false);
    setCustomName('');
  };
  
  // Render category item
  const renderCategoryItem = ({ item }: { item: ReminderCategory }) => (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        {
          backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
          borderColor: item.id === selectedCategory.id
            ? item.color
            : isDarkMode ? '#30363D' : '#D0D7DE',
        },
      ]}
      onPress={() => handleSelectCategory(item)}
    >
      <View
        style={[
          styles.categoryIcon,
          { backgroundColor: item.color },
        ]}
      >
        <Ionicons
          name={item.icon as any}
          size={20}
          color="#FFFFFF"
        />
      </View>
      <Text
        style={[
          styles.categoryName,
          { color: isDarkMode ? '#FFFFFF' : '#000000' },
        ]}
      >
        {item.id.startsWith('custom-') ? item.name : t(item.name)}
      </Text>
      {item.id === selectedCategory.id && (
        <Ionicons
          name="checkmark"
          size={20}
          color={item.color}
          style={styles.checkIcon}
        />
      )}
    </TouchableOpacity>
  );
  
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.selector,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
          },
        ]}
        onPress={() => setModalVisible(true)}
      >
        <View
          style={[
            styles.selectedCategoryIcon,
            { backgroundColor: selectedCategory.color },
          ]}
        >
          <Ionicons
            name={selectedCategory.icon as any}
            size={20}
            color="#FFFFFF"
          />
        </View>
        <Text
          style={[
            styles.selectedCategoryName,
            { color: isDarkMode ? '#FFFFFF' : '#000000' },
          ]}
        >
          {selectedCategory.id.startsWith('custom-')
            ? selectedCategory.name
            : t(selectedCategory.name)}
        </Text>
        <Ionicons
          name="chevron-down"
          size={20}
          color={isDarkMode ? '#8B949E' : '#6E7781'}
        />
      </TouchableOpacity>
      
      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View
          style={[
            styles.modalContainer,
            { backgroundColor: isDarkMode ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.3)' },
          ]}
        >
          <View
            style={[
              styles.modalContent,
              { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text
                style={[
                  styles.modalTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#000000' },
                ]}
              >
                {t('reminders.selectCategory')}
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => {
                  setModalVisible(false);
                  setIsAddingCustom(false);
                }}
              >
                <Ionicons
                  name="close"
                  size={24}
                  color={isDarkMode ? '#8B949E' : '#6E7781'}
                />
              </TouchableOpacity>
            </View>
            
            <TextInput
              style={[
                styles.searchInput,
                {
                  backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                  color: isDarkMode ? '#FFFFFF' : '#000000',
                  borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                },
              ]}
              placeholder={t('common.search')}
              placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            
            <FlatList
              data={filteredCategories}
              renderItem={renderCategoryItem}
              keyExtractor={item => item.id}
              contentContainerStyle={styles.categoriesList}
              showsVerticalScrollIndicator={false}
              ListFooterComponent={
                allowCustom && !isAddingCustom ? (
                  <TouchableOpacity
                    style={[
                      styles.addCustomButton,
                      {
                        backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                        borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                      },
                    ]}
                    onPress={() => setIsAddingCustom(true)}
                  >
                    <Ionicons
                      name="add-circle-outline"
                      size={20}
                      color={isDarkMode ? '#58A6FF' : '#0366D6'}
                    />
                    <Text
                      style={[
                        styles.addCustomText,
                        { color: isDarkMode ? '#58A6FF' : '#0366D6' },
                      ]}
                    >
                      {t('reminders.addCustomCategory')}
                    </Text>
                  </TouchableOpacity>
                ) : null
              }
            />
            
            {isAddingCustom && (
              <View
                style={[
                  styles.customCategoryForm,
                  {
                    backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                    borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                  },
                ]}
              >
                <Text
                  style={[
                    styles.customCategoryTitle,
                    { color: isDarkMode ? '#FFFFFF' : '#000000' },
                  ]}
                >
                  {t('reminders.addCustomCategory')}
                </Text>
                
                <TextInput
                  style={[
                    styles.customCategoryInput,
                    {
                      backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
                      color: isDarkMode ? '#FFFFFF' : '#000000',
                      borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                    },
                  ]}
                  placeholder={t('reminders.categoryName')}
                  placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
                  value={customName}
                  onChangeText={setCustomName}
                />
                
                <View style={styles.customCategoryActions}>
                  <TouchableOpacity
                    style={[
                      styles.cancelButton,
                      {
                        backgroundColor: isDarkMode ? '#21262D' : '#F6F8FA',
                        borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                      },
                    ]}
                    onPress={() => setIsAddingCustom(false)}
                  >
                    <Text
                      style={[
                        styles.cancelButtonText,
                        { color: isDarkMode ? '#FFFFFF' : '#000000' },
                      ]}
                    >
                      {t('common.cancel')}
                    </Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[
                      styles.addButton,
                      {
                        backgroundColor: isDarkMode ? '#238636' : '#2EA043',
                        opacity: !customName.trim() ? 0.5 : 1,
                      },
                    ]}
                    onPress={handleAddCustomCategory}
                    disabled={!customName.trim()}
                  >
                    <Text style={styles.addButtonText}>
                      {t('common.add')}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  selectedCategoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  selectedCategoryName: {
    fontSize: 16,
    flex: 1,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingTop: 16,
    paddingBottom: 32,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  searchInput: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 16,
  },
  categoriesList: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 2,
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryName: {
    fontSize: 16,
    flex: 1,
  },
  checkIcon: {
    marginLeft: 8,
  },
  addCustomButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderStyle: 'dashed',
    justifyContent: 'center',
  },
  addCustomText: {
    fontSize: 16,
    marginLeft: 8,
  },
  customCategoryForm: {
    margin: 16,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  customCategoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  customCategoryInput: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    fontSize: 16,
    marginBottom: 16,
  },
  customCategoryActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  addButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FFFFFF',
  },
});

export default ReminderCategorySelector;
