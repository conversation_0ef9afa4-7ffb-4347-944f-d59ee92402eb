import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface EmptyStateProps {
  icon: string;
  title: string;
  message: string;
  buttonTitle?: string;
  onButtonPress?: () => void;
  isDarkMode: boolean;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  message,
  buttonTitle,
  onButtonPress,
  isDarkMode,
}) => {
  return (
    <View style={styles.container}>
      <Ionicons
        name={icon as any}
        size={64}
        color={isDarkMode ? '#8B949E' : '#6E7781'}
        style={styles.icon}
      />
      <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
        {title}
      </Text>
      <Text style={[styles.message, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
        {message}
      </Text>
      {buttonTitle && onButtonPress && (
        <TouchableOpacity
          style={[
            styles.button,
            { backgroundColor: isDarkMode ? '#238636' : '#2DA44E' },
          ]}
          onPress={onButtonPress}
        >
          <Text style={styles.buttonText}>{buttonTitle}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  icon: {
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  message: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
  },
  button: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
});

export default EmptyState;
