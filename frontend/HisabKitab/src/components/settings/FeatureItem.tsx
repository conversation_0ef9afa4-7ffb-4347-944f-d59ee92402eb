import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Switch,
  Modal,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { FeatureFlag } from '../../contexts/FeatureFlagContext';

interface FeatureItemProps {
  feature: FeatureFlag;
  onToggle: () => void;
  onChangeEnabledFor: (enabledFor: string) => void;
  isAdmin: boolean;
  isDarkMode: boolean;
}

const FeatureItem: React.FC<FeatureItemProps> = ({
  feature,
  onToggle,
  onChangeEnabledFor,
  isAdmin,
  isDarkMode,
}) => {
  const { t } = useTranslation();
  
  const [modalVisible, setModalVisible] = useState(false);
  
  // Parse enabled for
  const parseEnabledFor = (): string => {
    try {
      const enabledFor = JSON.parse(feature.enabledFor);
      
      if (enabledFor === 'all') {
        return 'all';
      } else if (enabledFor === 'beta') {
        return 'beta';
      } else if (Array.isArray(enabledFor)) {
        return 'specific';
      } else {
        return 'all';
      }
    } catch (error) {
      return 'all';
    }
  };
  
  // Get enabled for text
  const getEnabledForText = (): string => {
    const enabledFor = parseEnabledFor();
    
    switch (enabledFor) {
      case 'all':
        return t('features.allUsers');
      case 'beta':
        return t('features.betaUsers');
      case 'specific':
        return t('features.specificUsers');
      default:
        return t('features.allUsers');
    }
  };
  
  // Handle select enabled for
  const handleSelectEnabledFor = (enabledFor: string) => {
    onChangeEnabledFor(JSON.stringify(enabledFor));
    setModalVisible(false);
  };
  
  // Enabled for options
  const enabledForOptions = [
    { value: 'all', label: t('features.allUsers') },
    { value: 'beta', label: t('features.betaUsers') },
    { value: 'specific', label: t('features.specificUsers') },
  ];
  
  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
      ]}
    >
      <View style={styles.header}>
        <Text
          style={[
            styles.title,
            { color: isDarkMode ? '#FFFFFF' : '#000000' },
          ]}
        >
          {feature.name}
        </Text>
        <Switch
          value={feature.isEnabled}
          onValueChange={onToggle}
          disabled={!isAdmin}
          trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
          thumbColor={feature.isEnabled ? '#58A6FF' : '#f4f3f4'}
        />
      </View>
      
      <Text
        style={[
          styles.description,
          { color: isDarkMode ? '#C9D1D9' : '#24292E' },
        ]}
      >
        {feature.description}
      </Text>
      
      <View style={styles.footer}>
        <Text
          style={[
            styles.lastUpdated,
            { color: isDarkMode ? '#8B949E' : '#6E7781' },
          ]}
        >
          {t('features.lastUpdated', {
            date: new Date(feature.lastUpdatedAt).toLocaleDateString(),
          })}
        </Text>
        
        {feature.isEnabled && (
          <TouchableOpacity
            style={styles.enabledForButton}
            onPress={() => isAdmin && setModalVisible(true)}
            disabled={!isAdmin}
          >
            <Text
              style={[
                styles.enabledForText,
                { color: isDarkMode ? '#58A6FF' : '#0366D6' },
              ]}
            >
              {getEnabledForText()}
            </Text>
            {isAdmin && (
              <Ionicons
                name="chevron-down"
                size={16}
                color={isDarkMode ? '#58A6FF' : '#0366D6'}
              />
            )}
          </TouchableOpacity>
        )}
      </View>
      
      {/* Enabled for modal */}
      <Modal
        visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#E1E4E8',
              },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text
                style={[
                  styles.modalTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#000000' },
                ]}
              >
                {t('features.selectEnabledFor')}
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Ionicons
                  name="close"
                  size={24}
                  color={isDarkMode ? '#C9D1D9' : '#24292E'}
                />
              </TouchableOpacity>
            </View>
            
            <View style={styles.optionsContainer}>
              {enabledForOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.optionItem,
                    parseEnabledFor() === option.value && {
                      backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
                    },
                  ]}
                  onPress={() => handleSelectEnabledFor(option.value)}
                >
                  <Text
                    style={[
                      styles.optionText,
                      {
                        color:
                          parseEnabledFor() === option.value
                            ? '#FFFFFF'
                            : isDarkMode
                            ? '#C9D1D9'
                            : '#24292E',
                      },
                    ]}
                  >
                    {option.label}
                  </Text>
                  {parseEnabledFor() === option.value && (
                    <Ionicons name="checkmark" size={20} color="#FFFFFF" />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  description: {
    fontSize: 14,
    marginBottom: 12,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastUpdated: {
    fontSize: 12,
  },
  enabledForButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  enabledForText: {
    fontSize: 14,
    marginRight: 4,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    borderRadius: 8,
    borderWidth: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  optionsContainer: {
    padding: 16,
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  optionText: {
    fontSize: 16,
  },
});

export default FeatureItem;
