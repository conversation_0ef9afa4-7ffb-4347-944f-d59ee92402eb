import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import i18n from 'i18next';

interface TranslationPreviewProps {
  translationKey: string;
  defaultText?: string;
  onEdit?: (key: string, value: string, language: string) => Promise<boolean>;
  showEditControls?: boolean;
}

const TranslationPreview: React.FC<TranslationPreviewProps> = ({
  translationKey,
  defaultText,
  onEdit,
  showEditControls = false,
}) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  
  const [isEditing, setIsEditing] = useState(false);
  const [editedText, setEditedText] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  // Get available languages
  const languages = Object.keys(i18n.options.resources || {});
  
  // Get translations for all languages
  const getTranslations = () => {
    const translations: Record<string, string> = {};
    
    languages.forEach(lang => {
      const translation = i18n.getFixedT(lang)(translationKey);
      translations[lang] = translation !== translationKey ? translation : defaultText || '';
    });
    
    return translations;
  };
  
  // Handle edit button press
  const handleEditPress = (language: string) => {
    const translations = getTranslations();
    setEditedText(translations[language] || '');
    setSelectedLanguage(language);
    setIsEditing(true);
  };
  
  // Handle save button press
  const handleSavePress = async () => {
    if (!onEdit) return;
    
    setIsLoading(true);
    
    try {
      const success = await onEdit(translationKey, editedText, selectedLanguage);
      
      if (success) {
        setIsEditing(false);
      }
    } catch (error) {
      console.error('Error saving translation:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle cancel button press
  const handleCancelPress = () => {
    setIsEditing(false);
  };
  
  // Render language preview
  const renderLanguagePreview = (language: string) => {
    const translations = getTranslations();
    const translation = translations[language] || '';
    
    return (
      <View
        key={language}
        style={[
          styles.languageContainer,
          { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
        ]}
      >
        <View style={styles.languageHeader}>
          <Text
            style={[
              styles.languageCode,
              { color: isDarkMode ? '#58A6FF' : '#0366D6' },
            ]}
          >
            {language.toUpperCase()}
          </Text>
          {showEditControls && (
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => handleEditPress(language)}
            >
              <Ionicons
                name="pencil-outline"
                size={16}
                color={isDarkMode ? '#8B949E' : '#6E7781'}
              />
            </TouchableOpacity>
          )}
        </View>
        <Text
          style={[
            styles.translationText,
            { color: isDarkMode ? '#FFFFFF' : '#000000' },
          ]}
        >
          {translation || (
            <Text style={{ fontStyle: 'italic', color: isDarkMode ? '#8B949E' : '#6E7781' }}>
              {t('translations.noTranslation')}
            </Text>
          )}
        </Text>
      </View>
    );
  };
  
  // Render edit form
  const renderEditForm = () => {
    if (!isEditing) return null;
    
    return (
      <View
        style={[
          styles.editForm,
          { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
        ]}
      >
        <View style={styles.editHeader}>
          <Text
            style={[
              styles.editTitle,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {t('translations.editTranslation', { language: selectedLanguage.toUpperCase() })}
          </Text>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleCancelPress}
          >
            <Ionicons
              name="close-outline"
              size={24}
              color={isDarkMode ? '#8B949E' : '#6E7781'}
            />
          </TouchableOpacity>
        </View>
        
        <Text
          style={[
            styles.keyLabel,
            { color: isDarkMode ? '#8B949E' : '#6E7781' },
          ]}
        >
          {t('translations.key')}
        </Text>
        <View
          style={[
            styles.keyContainer,
            { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
          ]}
        >
          <Text
            style={[
              styles.keyText,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {translationKey}
          </Text>
        </View>
        
        <Text
          style={[
            styles.translationLabel,
            { color: isDarkMode ? '#8B949E' : '#6E7781' },
          ]}
        >
          {t('translations.translation')}
        </Text>
        <TextInput
          style={[
            styles.translationInput,
            {
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              color: isDarkMode ? '#FFFFFF' : '#000000',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
            },
          ]}
          value={editedText}
          onChangeText={setEditedText}
          multiline
          placeholder={t('translations.enterTranslation')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
        />
        
        <View style={styles.editActions}>
          <TouchableOpacity
            style={[
              styles.cancelButton,
              {
                backgroundColor: isDarkMode ? '#21262D' : '#F6F8FA',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
            onPress={handleCancelPress}
            disabled={isLoading}
          >
            <Text
              style={[
                styles.cancelButtonText,
                { color: isDarkMode ? '#FFFFFF' : '#000000' },
              ]}
            >
              {t('common.cancel')}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.saveButton,
              {
                backgroundColor: isDarkMode ? '#238636' : '#2EA043',
                opacity: isLoading ? 0.7 : 1,
              },
            ]}
            onPress={handleSavePress}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Text style={styles.saveButtonText}>
                {t('common.save')}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text
          style={[
            styles.keyTitle,
            { color: isDarkMode ? '#FFFFFF' : '#000000' },
          ]}
        >
          {translationKey}
        </Text>
      </View>
      
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.languagesContainer}
      >
        {languages.map(language => renderLanguagePreview(language))}
      </ScrollView>
      
      {renderEditForm()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  header: {
    marginBottom: 8,
  },
  keyTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  languagesContainer: {
    paddingBottom: 8,
  },
  languageContainer: {
    padding: 12,
    borderRadius: 8,
    marginRight: 12,
    width: 200,
  },
  languageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  languageCode: {
    fontSize: 12,
    fontWeight: '600',
  },
  editButton: {
    padding: 4,
  },
  translationText: {
    fontSize: 14,
  },
  editForm: {
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
  },
  editHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  editTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  keyLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  keyContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  keyText: {
    fontSize: 14,
    fontFamily: 'monospace',
  },
  translationLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  translationInput: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    minHeight: 100,
    textAlignVertical: 'top',
    fontSize: 14,
  },
  editActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 80,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FFFFFF',
  },
});

export default TranslationPreview;
