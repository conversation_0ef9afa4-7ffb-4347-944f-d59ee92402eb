import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useLanguage, LanguageType } from '../../contexts/LanguageContext';
import { useTheme } from '../../contexts/ThemeContext';

interface LanguageOption {
  code: LanguageType;
  name: string;
  localName: string;
  flag: string; // Changed to string for text placeholder
}

const LanguageSwitcher: React.FC = () => {
  const { t } = useTranslation();
  const { language, setLanguage, availableLanguages } = useLanguage();
  const { isDarkMode } = useTheme();
  
  const [modalVisible, setModalVisible] = useState(false);
  
  // Language options with flag placeholders
  const languageOptions: LanguageOption[] = [
    {
      code: 'en',
      name: 'English',
      localName: 'English',
      flag: 'EN', // Text placeholder instead of image
    },
    {
      code: 'ne',
      name: 'Nepali',
      localName: 'नेपाली',
      flag: 'NE', // Text placeholder instead of image
    },
  ];
  
  // Get current language option
  const getCurrentLanguage = () => {
    return languageOptions.find((option) => option.code === language) || languageOptions[0];
  };
  
  // Handle language selection
  const handleSelectLanguage = async (code: LanguageType) => {
    await setLanguage(code);
    setModalVisible(false);
  };
  
  // Render language item
  const renderLanguageItem = ({ item }: { item: LanguageOption }) => (
    <TouchableOpacity
      style={[
        styles.languageItem,
        language === item.code && {
          backgroundColor: isDarkMode ? '#0D419D' : '#0366D6',
        },
      ]}
      onPress={() => handleSelectLanguage(item.code)}
    >
      <View style={[styles.flag, { backgroundColor: isDarkMode ? '#30363D' : '#E1E4E8' }]}>
        <Text style={[styles.flagText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {item.flag}
        </Text>
      </View>
      <View style={styles.languageInfo}>
        <Text
          style={[
            styles.languageName,
            {
              color:
                language === item.code
                  ? '#FFFFFF'
                  : isDarkMode
                  ? '#FFFFFF'
                  : '#000000',
            },
          ]}
        >
          {item.name}
        </Text>
        <Text
          style={[
            styles.localName,
            {
              color:
                language === item.code
                  ? '#FFFFFF'
                  : isDarkMode
                  ? '#C9D1D9'
                  : '#24292E',
            },
          ]}
        >
          {item.localName}
        </Text>
      </View>
      {language === item.code && (
        <Ionicons name="checkmark-circle" size={24} color="#FFFFFF" />
      )}
    </TouchableOpacity>
  );
  
  const currentLanguage = getCurrentLanguage();
  
  return (
    <View>
      {/* Language selector button */}
      <TouchableOpacity
        style={[
          styles.languageButton,
          { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
        ]}
        onPress={() => setModalVisible(true)}
      >
        <View style={[styles.buttonFlag, { backgroundColor: isDarkMode ? '#30363D' : '#E1E4E8' }]}>
          <Text style={[styles.buttonFlagText, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {currentLanguage.flag}
          </Text>
        </View>
        <Text
          style={[
            styles.buttonText,
            { color: isDarkMode ? '#FFFFFF' : '#000000' },
          ]}
        >
          {currentLanguage.name}
        </Text>
        <Ionicons
          name="chevron-down"
          size={20}
          color={isDarkMode ? '#8B949E' : '#6E7781'}
        />
      </TouchableOpacity>
      
      {/* Language selection modal */}
      <Modal
        visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#E1E4E8',
              },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text
                style={[
                  styles.modalTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#000000' },
                ]}
              >
                {t('settings.selectLanguage')}
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Ionicons
                  name="close"
                  size={24}
                  color={isDarkMode ? '#C9D1D9' : '#24292E'}
                />
              </TouchableOpacity>
            </View>
            
            <FlatList
              data={languageOptions}
              keyExtractor={(item) => item.code}
              renderItem={renderLanguageItem}
              contentContainerStyle={styles.languageList}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  languageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  buttonFlag: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonFlagText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  buttonText: {
    flex: 1,
    fontSize: 16,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    borderRadius: 8,
    borderWidth: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  languageList: {
    padding: 16,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
  },
  flag: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  flagText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  languageInfo: {
    flex: 1,
  },
  languageName: {
    fontSize: 16,
    fontWeight: '500',
  },
  localName: {
    fontSize: 14,
    marginTop: 4,
  },
});

export default LanguageSwitcher;
