import React, { useEffect } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { router } from 'expo-router';
import { useTheme } from '../contexts/ThemeContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requireAdmin = false 
}) => {
  const { isAuthenticated, isLoading, isPlatformAdmin, isFamilyAdmin } = useAuth();
  const { isDarkMode } = useTheme();
  
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      // Redirect to login if not authenticated
      router.replace('/auth/login');
    } else if (!isLoading && requireAdmin && !isPlatformAdmin && !isFamilyAdmin) {
      // Redirect to home if admin access is required but user is not an admin
      router.replace('/');
    }
  }, [isLoading, isAuthenticated, requireAdmin, isPlatformAdmin, isFamilyAdmin]);
  
  if (isLoading) {
    return (
      <View style={[
        styles.container, 
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }
      ]}>
        <ActivityIndicator size="large" color={isDarkMode ? '#58A6FF' : '#0366D6'} />
      </View>
    );
  }
  
  if (!isAuthenticated) {
    return null; // Will redirect in useEffect
  }
  
  if (requireAdmin && !isPlatformAdmin && !isFamilyAdmin) {
    return null; // Will redirect in useEffect
  }
  
  return <>{children}</>;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ProtectedRoute;
