import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { formatCurrency } from '../../utils/formatters';

interface GoalProgressBarProps {
  currentAmount: number;
  targetAmount: number;
  isOnTrack?: boolean;
  daysRemaining?: number;
  currency?: string;
  showDetails?: boolean;
}

const GoalProgressBar: React.FC<GoalProgressBarProps> = ({
  currentAmount,
  targetAmount,
  isOnTrack = true,
  daysRemaining,
  currency = 'NPR',
  showDetails = true,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  
  // Calculate progress percentage
  const progress = targetAmount > 0 ? (currentAmount / targetAmount) * 100 : 0;
  const remainingAmount = targetAmount - currentAmount;
  
  // Determine progress bar color based on progress and whether it's on track
  const getProgressBarColor = () => {
    if (progress >= 100) {
      return isDarkMode ? '#56D364' : '#2EA043'; // Green for completed
    }
    
    if (isOnTrack) {
      return isDarkMode ? '#58A6FF' : '#0366D6'; // Blue for on track
    }
    
    return isDarkMode ? '#F85149' : '#DA3633'; // Red for behind schedule
  };
  
  return (
    <View style={styles.container}>
      {/* Progress bar */}
      <View style={styles.progressBarContainer}>
        <View
          style={[
            styles.progressBarBackground,
            { backgroundColor: isDarkMode ? '#30363D' : '#E1E4E8' }
          ]}
        >
          <View
            style={[
              styles.progressBar,
              {
                width: `${Math.min(100, progress)}%`,
                backgroundColor: getProgressBarColor()
              }
            ]}
          />
        </View>
        
        {/* Progress percentage */}
        <Text
          style={[
            styles.progressText,
            { color: isDarkMode ? '#8B949E' : '#6E7781' }
          ]}
        >
          {Math.round(progress)}% {t('common.completed')}
        </Text>
      </View>
      
      {/* Details section */}
      {showDetails && (
        <View style={styles.detailsContainer}>
          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('savings.currentAmount')}
            </Text>
            <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {formatCurrency(currentAmount, currency)}
            </Text>
          </View>
          
          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('savings.remainingAmount')}
            </Text>
            <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {formatCurrency(remainingAmount, currency)}
            </Text>
          </View>
          
          {daysRemaining !== undefined && (
            <View style={styles.detailItem}>
              <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('savings.daysRemaining')}
              </Text>
              <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
                {daysRemaining}
              </Text>
            </View>
          )}
          
          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('savings.targetAmount')}
            </Text>
            <Text style={[styles.detailValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {formatCurrency(targetAmount, currency)}
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  progressBarContainer: {
    marginBottom: 8,
  },
  progressBarBackground: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressBar: {
    height: '100%',
  },
  progressText: {
    fontSize: 12,
    textAlign: 'right',
  },
  detailsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  detailItem: {
    width: '48%',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default GoalProgressBar;
