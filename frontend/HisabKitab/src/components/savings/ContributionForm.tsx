import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Text } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import FormInput from '../ui/FormInput';
import Button from '../ui/Button';
import DatePicker from '../ui/DatePicker';
import { SavingsContribution } from '../../models/SavingsGoal';

interface ContributionFormProps {
  initialValues?: Partial<SavingsContribution>;
  onSubmit: (values: any) => void;
  isLoading?: boolean;
  savingsGoalId: number;
  mode: 'create' | 'edit';
}

const ContributionForm: React.FC<ContributionFormProps> = ({
  initialValues = {},
  onSubmit,
  isLoading = false,
  savingsGoalId,
  mode,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();

  // Form state
  const [amount, setAmount] = useState(initialValues.amount?.toString() || '');
  const [date, setDate] = useState(initialValues.date ? new Date(initialValues.date) : new Date());
  const [notes, setNotes] = useState(initialValues.notes || '');

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) {
      newErrors.amount = t('validation.positive');
    }

    setErrors(newErrors);
    setTouched(Object.keys(newErrors).reduce((acc, key) => ({ ...acc, [key]: true }), touched));

    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (validateForm()) {
      const formData = {
        id: initialValues.id,
        savingsGoalId,
        amount: Number(amount),
        date: date.toISOString(),
        notes: notes.trim() || undefined,
      };

      onSubmit(formData);
    }
  };

  return (
    <ScrollView
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }
      ]}
      contentContainerStyle={styles.contentContainer}
    >
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
          {t('savings.contribution')}
        </Text>

        <FormInput
          label={t('savings.contributionAmount')}
          value={amount}
          onChangeText={setAmount}
          keyboardType="numeric"
          error={errors.amount}
          touched={touched.amount}
          onBlur={() => setTouched({ ...touched, amount: true })}
          required
        />

        <DatePicker
          label={t('savings.contributionDate')}
          selectedDate={date}
          onDateChange={(newDate) => newDate && setDate(newDate)}
          maximumDate={new Date()}
          required
        />

        <FormInput
          label={t('savings.contributionNotes')}
          value={notes}
          onChangeText={setNotes}
          placeholder={t('savings.contributionNotesPlaceholder')}
          multiline
          numberOfLines={3}
        />
      </View>

      {/* Submit Button */}
      <Button
        title={mode === 'create' ? t('savings.addContribution') : t('common.update')}
        onPress={handleSubmit}
        isLoading={isLoading}
        fullWidth
        style={styles.submitButton}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  submitButton: {
    marginTop: 16,
    marginBottom: 32,
  },
});

export default ContributionForm;
