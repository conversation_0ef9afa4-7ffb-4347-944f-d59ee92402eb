import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Switch, Text } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import FormInput from '../ui/FormInput';
import Button from '../ui/Button';
import Dropdown from '../ui/Dropdown';
import DatePicker from '../ui/DatePicker';
import { WishlistItem, WishlistItemStatus } from '../../models/WishlistItem';
import { SavingsGoal } from '../../models/SavingsGoal';
// Define mock interfaces for Priority and Family
// These are already defined in the actual model files, but TypeScript can't find them
interface Priority {
  id: number;
  name: string;
  description?: string;
  color?: string;
  order: number;
  isSystem: boolean;
  createdAt: string;
  updatedAt?: string;
}

interface Family {
  id: number;
  name: string;
  description?: string;
  createdByUserId: number;
  createdByUsername?: string;
  createdAt: string;
  updatedAt?: string;
  memberCount: number;
  isUserAdmin: boolean;
}

interface WishlistItemFormProps {
  initialValues?: Partial<WishlistItem>;
  onSubmit: (values: any) => void;
  isLoading?: boolean;
  priorities: Priority[];
  families: Family[];
  savingsGoals: SavingsGoal[];
  mode: 'create' | 'edit';
}

const WishlistItemForm: React.FC<WishlistItemFormProps> = ({
  initialValues = {},
  onSubmit,
  isLoading = false,
  priorities,
  families,
  savingsGoals,
  mode,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();

  // Form state
  const [title, setTitle] = useState(initialValues.title || '');
  const [description, setDescription] = useState(initialValues.description || '');
  const [estimatedPrice, setEstimatedPrice] = useState(initialValues.estimatedPrice?.toString() || '');
  const [productUrl, setProductUrl] = useState(initialValues.productUrl || '');
  const [imageUrl, setImageUrl] = useState(initialValues.imageUrl || '');
  const [targetPurchaseDate, setTargetPurchaseDate] = useState(
    initialValues.targetPurchaseDate ? new Date(initialValues.targetPurchaseDate) : null
  );
  const [isShared, setIsShared] = useState(initialValues.isShared || false);
  const [familyId, setFamilyId] = useState<number | null>(initialValues.familyId || null);
  const [priorityId, setPriorityId] = useState<number | null>(initialValues.priorityId || null);
  const [linkedSavingsGoalId, setLinkedSavingsGoalId] = useState<number | null>(initialValues.linkedSavingsGoalId || null);
  const [status, setStatus] = useState<WishlistItemStatus>(initialValues.status || 'Pending');

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Update familyId when isShared changes
  useEffect(() => {
    if (!isShared) {
      setFamilyId(null);
    } else if (families.length > 0 && !familyId) {
      setFamilyId(families[0].id);
    }
  }, [isShared, families, familyId]);

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!title.trim()) {
      newErrors.title = t('validation.required');
    }

    if (!estimatedPrice || isNaN(Number(estimatedPrice)) || Number(estimatedPrice) <= 0) {
      newErrors.estimatedPrice = t('validation.positive');
    }

    if (productUrl && !isValidUrl(productUrl)) {
      newErrors.productUrl = t('validation.url');
    }

    if (imageUrl && !isValidUrl(imageUrl)) {
      newErrors.imageUrl = t('validation.url');
    }

    if (isShared && !familyId) {
      newErrors.familyId = t('validation.required');
    }

    setErrors(newErrors);
    setTouched(Object.keys(newErrors).reduce((acc, key) => ({ ...acc, [key]: true }), touched));

    return Object.keys(newErrors).length === 0;
  };

  // Validate URL
  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  };

  // Handle form submission
  const handleSubmit = () => {
    if (validateForm()) {
      const formData = {
        id: initialValues.id,
        title,
        description: description.trim() || undefined,
        estimatedPrice: Number(estimatedPrice),
        productUrl: productUrl.trim() || undefined,
        imageUrl: imageUrl.trim() || undefined,
        targetPurchaseDate: targetPurchaseDate ? targetPurchaseDate.toISOString() : undefined,
        isShared,
        familyId: isShared ? familyId : null,
        priorityId: priorityId || null,
        linkedSavingsGoalId: linkedSavingsGoalId || null,
        status,
      };

      onSubmit(formData);
    }
  };

  // Prepare dropdown options
  const familyOptions = families.map(family => ({
    label: family.name,
    value: family.id,
  }));

  const priorityOptions = [
    { label: t('common.none'), value: null },
    ...priorities.map(priority => ({
      label: priority.name,
      value: priority.id,
    })),
  ];

  const savingsGoalOptions = [
    { label: t('common.none'), value: null },
    ...savingsGoals
      .filter(goal => goal.status === 'Active')
      .map(goal => ({
        label: goal.title,
        value: goal.id,
      })),
  ];

  const statusOptions = [
    { label: t('wishlist.statusOptions.pending'), value: 'Pending' },
    { label: t('wishlist.statusOptions.purchased'), value: 'Purchased' },
    { label: t('wishlist.statusOptions.abandoned'), value: 'Abandoned' },
  ];

  return (
    <ScrollView
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }
      ]}
      contentContainerStyle={styles.contentContainer}
    >
      {/* Basic Information */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
          {t('common.basicInformation')}
        </Text>

        <FormInput
          label={t('wishlist.title')}
          value={title}
          onChangeText={setTitle}
          placeholder={t('wishlist.titlePlaceholder')}
          error={errors.title}
          touched={touched.title}
          onBlur={() => setTouched({ ...touched, title: true })}
          required
        />

        <FormInput
          label={t('wishlist.description')}
          value={description}
          onChangeText={setDescription}
          placeholder={t('wishlist.descriptionPlaceholder')}
          multiline
          numberOfLines={3}
        />

        <FormInput
          label={t('wishlist.estimatedPrice')}
          value={estimatedPrice}
          onChangeText={setEstimatedPrice}
          keyboardType="numeric"
          error={errors.estimatedPrice}
          touched={touched.estimatedPrice}
          onBlur={() => setTouched({ ...touched, estimatedPrice: true })}
          required
        />

        <FormInput
          label={t('wishlist.productUrl')}
          value={productUrl}
          onChangeText={setProductUrl}
          placeholder={t('wishlist.productUrlPlaceholder')}
          keyboardType="url"
          error={errors.productUrl}
          touched={touched.productUrl}
          onBlur={() => setTouched({ ...touched, productUrl: true })}
        />

        <FormInput
          label={t('wishlist.imageUrl')}
          value={imageUrl}
          onChangeText={setImageUrl}
          placeholder={t('wishlist.imageUrlPlaceholder')}
          keyboardType="url"
          error={errors.imageUrl}
          touched={touched.imageUrl}
          onBlur={() => setTouched({ ...touched, imageUrl: true })}
        />

        <DatePicker
          label={t('wishlist.targetPurchaseDate')}
          selectedDate={targetPurchaseDate || new Date()}
          onDateChange={(newDate) => newDate && setTargetPurchaseDate(newDate)}
          minimumDate={new Date()}
        />

        <Dropdown
          label={t('wishlist.priority')}
          options={priorityOptions}
          selectedValue={priorityId}
          onValueChange={(value) => setPriorityId(value as number | null)}
          placeholder={t('common.select')}
        />

        {mode === 'edit' && (
          <Dropdown
            label={t('wishlist.status')}
            options={statusOptions}
            selectedValue={status}
            onValueChange={(value) => setStatus(value as WishlistItemStatus)}
            placeholder={t('common.select')}
            required
          />
        )}
      </View>

      {/* Sharing Options */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
          {t('common.sharingOptions')}
        </Text>

        <View style={styles.switchContainer}>
          <Text style={[styles.switchLabel, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
            {t('wishlist.isShared')}
          </Text>
          <Switch
            value={isShared}
            onValueChange={setIsShared}
            trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
            thumbColor={isShared ? isDarkMode ? '#58A6FF' : '#0366D6' : '#f4f3f4'}
          />
        </View>

        {isShared && (
          <Dropdown
            label={t('family.family')}
            options={familyOptions}
            selectedValue={familyId}
            onValueChange={(value) => setFamilyId(value as number)}
            placeholder={t('common.select')}
            error={errors.familyId}
            touched={touched.familyId}
            onBlur={() => setTouched({ ...touched, familyId: true })}
            required={isShared}
          />
        )}
      </View>

      {/* Linked Savings Goal */}
      {savingsGoalOptions.length > 1 && (
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
            {t('wishlist.linkedSavingsGoal')}
          </Text>

          <Dropdown
            label={t('wishlist.selectGoal')}
            options={savingsGoalOptions}
            selectedValue={linkedSavingsGoalId}
            onValueChange={(value) => setLinkedSavingsGoalId(value as number | null)}
            placeholder={t('common.select')}
          />
        </View>
      )}

      {/* Submit Button */}
      <Button
        title={mode === 'create' ? t('wishlist.createItem') : t('wishlist.updateItem')}
        onPress={handleSubmit}
        isLoading={isLoading}
        fullWidth
        style={styles.submitButton}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchLabel: {
    fontSize: 16,
  },
  submitButton: {
    marginTop: 16,
    marginBottom: 32,
  },
});

export default WishlistItemForm;
