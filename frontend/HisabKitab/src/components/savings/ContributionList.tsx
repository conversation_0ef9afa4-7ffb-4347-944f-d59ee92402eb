import React from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import { SavingsContribution } from '../../models/SavingsGoal';
import { formatCurrency, formatDate } from '../../utils/formatters';
import EmptyState from '../ui/EmptyState';

interface ContributionListProps {
  contributions: SavingsContribution[];
  onAddContribution?: () => void;
  onEditContribution?: (contribution: SavingsContribution) => void;
  onDeleteContribution?: (contribution: SavingsContribution) => void;
  currency?: string;
}

const ContributionList: React.FC<ContributionListProps> = ({
  contributions,
  onAddContribution,
  onEditContribution,
  onDeleteContribution,
  currency = 'NPR',
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  
  // Render a contribution item
  const renderContributionItem = ({ item }: { item: SavingsContribution }) => {
    return (
      <View
        style={[
          styles.contributionItem,
          { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
        ]}
      >
        <View style={styles.contributionDetails}>
          <View style={styles.contributionHeader}>
            <Text style={[styles.contributionAmount, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {formatCurrency(item.amount, currency)}
            </Text>
            
            <View style={styles.contributionActions}>
              {onEditContribution && (
                <TouchableOpacity
                  onPress={() => onEditContribution(item)}
                  style={styles.actionButton}
                >
                  <Ionicons
                    name="pencil-outline"
                    size={18}
                    color={isDarkMode ? '#8B949E' : '#6E7781'}
                  />
                </TouchableOpacity>
              )}
              
              {onDeleteContribution && (
                <TouchableOpacity
                  onPress={() => onDeleteContribution(item)}
                  style={styles.actionButton}
                >
                  <Ionicons
                    name="trash-outline"
                    size={18}
                    color={isDarkMode ? '#F85149' : '#DA3633'}
                  />
                </TouchableOpacity>
              )}
            </View>
          </View>
          
          <View style={styles.contributionInfo}>
            <View style={styles.infoItem}>
              <Ionicons
                name="calendar-outline"
                size={14}
                color={isDarkMode ? '#8B949E' : '#6E7781'}
                style={styles.infoIcon}
              />
              <Text style={[styles.infoText, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {formatDate(item.date)}
              </Text>
            </View>
            
            {item.userName && (
              <View style={styles.infoItem}>
                <Ionicons
                  name="person-outline"
                  size={14}
                  color={isDarkMode ? '#8B949E' : '#6E7781'}
                  style={styles.infoIcon}
                />
                <Text style={[styles.infoText, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                  {item.userName}
                </Text>
              </View>
            )}
          </View>
          
          {item.notes && (
            <Text style={[styles.notes, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {item.notes}
            </Text>
          )}
        </View>
      </View>
    );
  };
  
  // Render header with add button
  const renderHeader = () => {
    return (
      <View style={styles.header}>
        <Text style={[styles.title, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
          {t('savings.contributions')}
        </Text>
        
        {onAddContribution && (
          <TouchableOpacity
            style={[
              styles.addButton,
              { backgroundColor: isDarkMode ? '#238636' : '#2DA44E' }
            ]}
            onPress={onAddContribution}
          >
            <Ionicons name="add" size={16} color="#FFFFFF" />
            <Text style={styles.addButtonText}>
              {t('savings.addContribution')}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };
  
  // Render empty state
  const renderEmptyState = () => {
    return (
      <EmptyState
        icon="cash-outline"
        title={t('savings.noContributions')}
        message={t('savings.noContributionsMessage')}
        buttonTitle={onAddContribution ? t('savings.addContribution') : undefined}
        onButtonPress={onAddContribution}
      />
    );
  };
  
  return (
    <View style={styles.container}>
      {renderHeader()}
      
      <FlatList
        data={contributions}
        renderItem={renderContributionItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyState}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  addButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  listContent: {
    paddingBottom: 16,
  },
  contributionItem: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  contributionDetails: {
    flex: 1,
  },
  contributionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  contributionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  contributionActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 4,
    marginLeft: 8,
  },
  contributionInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 4,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  infoIcon: {
    marginRight: 4,
  },
  infoText: {
    fontSize: 12,
  },
  notes: {
    fontSize: 14,
    marginTop: 4,
  },
});

export default ContributionList;
