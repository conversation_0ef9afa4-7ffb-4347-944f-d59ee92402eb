import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import { WishlistItem } from '../../models/WishlistItem';
import { formatCurrency, formatDate } from '../../utils/formatters';

interface WishlistItemCardProps {
  item: WishlistItem;
  onPress?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onLinkToGoal?: () => void;
  showActions?: boolean;
}

const WishlistItemCard: React.FC<WishlistItemCardProps> = ({
  item,
  onPress,
  onEdit,
  onDelete,
  onLinkToGoal,
  showActions = true,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  
  // Get status badge color
  const getStatusColor = () => {
    switch (item.status) {
      case 'Purchased':
        return isDarkMode ? '#56D364' : '#2EA043'; // Green
      case 'Abandoned':
        return isDarkMode ? '#F85149' : '#DA3633'; // Red
      default:
        return isDarkMode ? '#58A6FF' : '#0366D6'; // Blue for Pending
    }
  };
  
  // Get priority icon and color
  const getPriorityIcon = () => {
    if (!item.priority) return null;
    
    let iconName = 'flag-outline';
    let color = '#8B949E';
    
    switch (item.priority.name) {
      case 'Urgent':
        iconName = 'flag';
        color = isDarkMode ? '#F85149' : '#DA3633'; // Red
        break;
      case 'High':
        iconName = 'flag';
        color = isDarkMode ? '#F0883E' : '#E36209'; // Orange
        break;
      case 'Normal':
        iconName = 'flag-outline';
        color = isDarkMode ? '#58A6FF' : '#0366D6'; // Blue
        break;
      case 'Low':
        iconName = 'flag-outline';
        color = isDarkMode ? '#8B949E' : '#6E7781'; // Gray
        break;
    }
    
    return { iconName, color };
  };
  
  const priorityIcon = getPriorityIcon();
  
  return (
    <TouchableOpacity
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
      ]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.contentContainer}>
        {/* Image */}
        {item.imageUrl ? (
          <Image
            source={{ uri: item.imageUrl }}
            style={styles.image}
            resizeMode="cover"
          />
        ) : (
          <View
            style={[
              styles.imagePlaceholder,
              { backgroundColor: isDarkMode ? '#30363D' : '#E1E4E8' }
            ]}
          >
            <Ionicons
              name="image-outline"
              size={24}
              color={isDarkMode ? '#8B949E' : '#6E7781'}
            />
          </View>
        )}
        
        <View style={styles.detailsContainer}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.titleContainer}>
              <Text
                style={[
                  styles.title,
                  { color: isDarkMode ? '#FFFFFF' : '#24292E' }
                ]}
                numberOfLines={1}
              >
                {item.title}
              </Text>
              
              {/* Status badge */}
              <View
                style={[
                  styles.statusBadge,
                  { backgroundColor: getStatusColor() }
                ]}
              >
                <Text style={styles.statusText}>
                  {t(`wishlist.statusOptions.${item.status.toLowerCase()}`)}
                </Text>
              </View>
            </View>
            
            {/* Actions */}
            {showActions && (
              <View style={styles.actions}>
                {priorityIcon && (
                  <Ionicons
                    name={priorityIcon.iconName as any}
                    size={18}
                    color={priorityIcon.color}
                    style={styles.priorityIcon}
                  />
                )}
                
                {onLinkToGoal && !item.linkedSavingsGoalId && (
                  <TouchableOpacity
                    onPress={onLinkToGoal}
                    style={styles.actionButton}
                  >
                    <Ionicons
                      name="link-outline"
                      size={18}
                      color={isDarkMode ? '#8B949E' : '#6E7781'}
                    />
                  </TouchableOpacity>
                )}
                
                {onEdit && (
                  <TouchableOpacity
                    onPress={onEdit}
                    style={styles.actionButton}
                  >
                    <Ionicons
                      name="pencil-outline"
                      size={18}
                      color={isDarkMode ? '#8B949E' : '#6E7781'}
                    />
                  </TouchableOpacity>
                )}
                
                {onDelete && (
                  <TouchableOpacity
                    onPress={onDelete}
                    style={styles.actionButton}
                  >
                    <Ionicons
                      name="trash-outline"
                      size={18}
                      color={isDarkMode ? '#F85149' : '#DA3633'}
                    />
                  </TouchableOpacity>
                )}
              </View>
            )}
          </View>
          
          {/* Price */}
          <Text
            style={[
              styles.price,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' }
            ]}
          >
            {formatCurrency(item.estimatedPrice)}
          </Text>
          
          {/* Description */}
          {item.description && (
            <Text
              style={[
                styles.description,
                { color: isDarkMode ? '#8B949E' : '#6E7781' }
              ]}
              numberOfLines={2}
            >
              {item.description}
            </Text>
          )}
          
          {/* Footer */}
          <View style={styles.footer}>
            {item.targetPurchaseDate && (
              <View style={styles.footerItem}>
                <Ionicons
                  name="calendar-outline"
                  size={14}
                  color={isDarkMode ? '#8B949E' : '#6E7781'}
                  style={styles.footerIcon}
                />
                <Text
                  style={[
                    styles.footerText,
                    { color: isDarkMode ? '#8B949E' : '#6E7781' }
                  ]}
                >
                  {formatDate(item.targetPurchaseDate)}
                </Text>
              </View>
            )}
            
            <View style={styles.footerItem}>
              <Ionicons
                name={item.isShared ? 'people-outline' : 'person-outline'}
                size={14}
                color={isDarkMode ? '#8B949E' : '#6E7781'}
                style={styles.footerIcon}
              />
              <Text
                style={[
                  styles.footerText,
                  { color: isDarkMode ? '#8B949E' : '#6E7781' }
                ]}
              >
                {item.isShared ? t('wishlist.shared') : t('wishlist.personal')}
              </Text>
            </View>
            
            {item.linkedSavingsGoalId && (
              <View style={styles.footerItem}>
                <Ionicons
                  name="link-outline"
                  size={14}
                  color={isDarkMode ? '#8B949E' : '#6E7781'}
                  style={styles.footerIcon}
                />
                <Text
                  style={[
                    styles.footerText,
                    { color: isDarkMode ? '#8B949E' : '#6E7781' }
                  ]}
                >
                  {t('wishlist.linkedSavingsGoal')}
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  contentContainer: {
    flexDirection: 'row',
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 4,
    marginRight: 12,
  },
  imagePlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 4,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  detailsContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  titleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 4,
    marginLeft: 8,
  },
  priorityIcon: {
    marginRight: 4,
  },
  price: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    marginBottom: 8,
  },
  footer: {
    flexDirection: 'row',
    marginTop: 4,
    flexWrap: 'wrap',
  },
  footerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  footerIcon: {
    marginRight: 4,
  },
  footerText: {
    fontSize: 12,
  },
});

export default WishlistItemCard;
