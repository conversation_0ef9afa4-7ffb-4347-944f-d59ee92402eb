import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { LineChart } from 'react-native-chart-kit';
import { SavingsGoalForecast } from '../../models/SavingsGoal';
import { formatCurrency, formatDate } from '../../utils/formatters';

interface ForecastTimelineProps {
  forecast: SavingsGoalForecast;
  currency?: string;
}

const ForecastTimeline: React.FC<ForecastTimelineProps> = ({
  forecast,
  currency = 'NPR',
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const screenWidth = Dimensions.get('window').width - 32; // Adjust for padding
  
  // Prepare data for the chart
  const prepareChartData = () => {
    if (!forecast.timelineData || forecast.timelineData.length === 0) {
      return {
        labels: [],
        datasets: [
          {
            data: [0],
            color: () => (isDarkMode ? '#58A6FF' : '#0366D6'),
          },
        ],
      };
    }
    
    // Get a subset of dates for labels (to avoid overcrowding)
    const timelineLength = forecast.timelineData.length;
    const maxLabels = 6;
    const step = Math.max(1, Math.floor(timelineLength / maxLabels));
    
    const labels = forecast.timelineData
      .filter((_, index) => index % step === 0 || index === timelineLength - 1)
      .map(item => {
        const date = new Date(item.date);
        return `${date.getMonth() + 1}/${date.getDate()}`;
      });
    
    const data = forecast.timelineData.map(item => item.amount);
    
    return {
      labels,
      datasets: [
        {
          data,
          color: () => (isDarkMode ? '#58A6FF' : '#0366D6'),
          strokeWidth: 2,
        },
      ],
    };
  };
  
  const chartData = prepareChartData();
  
  // Chart configuration
  const chartConfig = {
    backgroundGradientFrom: isDarkMode ? '#161B22' : '#F6F8FA',
    backgroundGradientTo: isDarkMode ? '#161B22' : '#F6F8FA',
    decimalPlaces: 0,
    color: (opacity = 1) => isDarkMode ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
    labelColor: (opacity = 1) => isDarkMode ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '4',
      strokeWidth: '2',
      stroke: isDarkMode ? '#58A6FF' : '#0366D6',
    },
    formatYLabel: (value: string) => {
      const num = parseFloat(value);
      if (num >= 1000000) {
        return `${(num / 1000000).toFixed(1)}M`;
      }
      if (num >= 1000) {
        return `${(num / 1000).toFixed(1)}K`;
      }
      return value;
    },
  };
  
  return (
    <View style={styles.container}>
      {/* Summary section */}
      <View
        style={[
          styles.summaryContainer,
          { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
        ]}
      >
        <View style={styles.summaryRow}>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('savings.projectedCompletionDate')}
            </Text>
            <Text style={[styles.summaryValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {formatDate(forecast.projectedCompletionDate)}
            </Text>
          </View>
          
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('savings.isAchievable')}
            </Text>
            <Text
              style={[
                styles.summaryValue,
                {
                  color: forecast.isAchievable
                    ? (isDarkMode ? '#56D364' : '#2EA043')
                    : (isDarkMode ? '#F85149' : '#DA3633')
                }
              ]}
            >
              {forecast.isAchievable ? t('common.yes') : t('common.no')}
            </Text>
          </View>
        </View>
        
        <View style={styles.summaryRow}>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('savings.monthlyContributionNeeded')}
            </Text>
            <Text style={[styles.summaryValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {formatCurrency(forecast.monthlyContributionNeeded, currency)}
            </Text>
          </View>
          
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('savings.weeklyContributionNeeded')}
            </Text>
            <Text style={[styles.summaryValue, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
              {formatCurrency(forecast.weeklyContributionNeeded, currency)}
            </Text>
          </View>
        </View>
      </View>
      
      {/* Chart section */}
      <View style={styles.chartContainer}>
        <Text style={[styles.chartTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
          {t('savings.forecastTimeline')}
        </Text>
        
        <LineChart
          data={chartData}
          width={screenWidth}
          height={220}
          chartConfig={chartConfig}
          bezier
          style={styles.chart}
          withDots={chartData.labels.length < 10}
          withInnerLines={false}
          withOuterLines={true}
          withVerticalLines={false}
          withHorizontalLines={true}
          yAxisLabel=""
          yAxisSuffix=""
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  summaryContainer: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryItem: {
    flex: 1,
  },
  summaryLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  chartContainer: {
    marginTop: 8,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  chart: {
    borderRadius: 8,
    paddingRight: 16,
  },
});

export default ForecastTimeline;
