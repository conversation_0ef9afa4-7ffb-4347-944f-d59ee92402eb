import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import { SavingsGoal } from '../../models/SavingsGoal';
import GoalProgressBar from './GoalProgressBar';
import { formatDate } from '../../utils/formatters';

interface SavingsGoalCardProps {
  goal: SavingsGoal;
  onPress?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  showActions?: boolean;
}

const SavingsGoalCard: React.FC<SavingsGoalCardProps> = ({
  goal,
  onPress,
  onEdit,
  onDelete,
  showActions = true,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  
  // Get status badge color
  const getStatusColor = () => {
    switch (goal.status) {
      case 'Achieved':
        return isDarkMode ? '#56D364' : '#2EA043'; // Green
      case 'Abandoned':
        return isDarkMode ? '#F85149' : '#DA3633'; // Red
      default:
        return isDarkMode ? '#58A6FF' : '#0366D6'; // Blue for Active
    }
  };
  
  // Get priority icon and color
  const getPriorityIcon = () => {
    if (!goal.priority) return null;
    
    let iconName = 'flag-outline';
    let color = '#8B949E';
    
    switch (goal.priority.name) {
      case 'Urgent':
        iconName = 'flag';
        color = isDarkMode ? '#F85149' : '#DA3633'; // Red
        break;
      case 'High':
        iconName = 'flag';
        color = isDarkMode ? '#F0883E' : '#E36209'; // Orange
        break;
      case 'Normal':
        iconName = 'flag-outline';
        color = isDarkMode ? '#58A6FF' : '#0366D6'; // Blue
        break;
      case 'Low':
        iconName = 'flag-outline';
        color = isDarkMode ? '#8B949E' : '#6E7781'; // Gray
        break;
    }
    
    return { iconName, color };
  };
  
  const priorityIcon = getPriorityIcon();
  
  return (
    <TouchableOpacity
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
      ]}
      onPress={onPress}
      disabled={!onPress}
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text
            style={[
              styles.title,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' }
            ]}
            numberOfLines={1}
          >
            {goal.title}
          </Text>
          
          {/* Status badge */}
          <View
            style={[
              styles.statusBadge,
              { backgroundColor: getStatusColor() }
            ]}
          >
            <Text style={styles.statusText}>
              {t(`savings.statusOptions.${goal.status.toLowerCase()}`)}
            </Text>
          </View>
        </View>
        
        {/* Actions */}
        {showActions && (
          <View style={styles.actions}>
            {priorityIcon && (
              <Ionicons
                name={priorityIcon.iconName as any}
                size={18}
                color={priorityIcon.color}
                style={styles.priorityIcon}
              />
            )}
            
            {onEdit && (
              <TouchableOpacity
                onPress={onEdit}
                style={styles.actionButton}
              >
                <Ionicons
                  name="pencil-outline"
                  size={18}
                  color={isDarkMode ? '#8B949E' : '#6E7781'}
                />
              </TouchableOpacity>
            )}
            
            {onDelete && (
              <TouchableOpacity
                onPress={onDelete}
                style={styles.actionButton}
              >
                <Ionicons
                  name="trash-outline"
                  size={18}
                  color={isDarkMode ? '#F85149' : '#DA3633'}
                />
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>
      
      {/* Description */}
      {goal.description && (
        <Text
          style={[
            styles.description,
            { color: isDarkMode ? '#8B949E' : '#6E7781' }
          ]}
          numberOfLines={2}
        >
          {goal.description}
        </Text>
      )}
      
      {/* Progress bar */}
      <GoalProgressBar
        currentAmount={goal.currentAmount}
        targetAmount={goal.targetAmount}
        isOnTrack={goal.isOnTrack}
        daysRemaining={goal.daysRemaining}
        showDetails={false}
      />
      
      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.footerItem}>
          <Ionicons
            name="calendar-outline"
            size={14}
            color={isDarkMode ? '#8B949E' : '#6E7781'}
            style={styles.footerIcon}
          />
          <Text
            style={[
              styles.footerText,
              { color: isDarkMode ? '#8B949E' : '#6E7781' }
            ]}
          >
            {formatDate(goal.targetDate)}
          </Text>
        </View>
        
        <View style={styles.footerItem}>
          <Ionicons
            name={goal.isShared ? 'people-outline' : 'person-outline'}
            size={14}
            color={isDarkMode ? '#8B949E' : '#6E7781'}
            style={styles.footerIcon}
          />
          <Text
            style={[
              styles.footerText,
              { color: isDarkMode ? '#8B949E' : '#6E7781' }
            ]}
          >
            {goal.isShared ? t('savings.shared') : t('savings.personal')}
          </Text>
        </View>
        
        {goal.autoContribute && (
          <View style={styles.footerItem}>
            <Ionicons
              name="repeat-outline"
              size={14}
              color={isDarkMode ? '#8B949E' : '#6E7781'}
              style={styles.footerIcon}
            />
            <Text
              style={[
                styles.footerText,
                { color: isDarkMode ? '#8B949E' : '#6E7781' }
              ]}
            >
              {t('savings.autoContribute')}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 4,
    marginLeft: 8,
  },
  priorityIcon: {
    marginRight: 4,
  },
  description: {
    fontSize: 14,
    marginBottom: 12,
  },
  footer: {
    flexDirection: 'row',
    marginTop: 12,
    flexWrap: 'wrap',
  },
  footerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  footerIcon: {
    marginRight: 4,
  },
  footerText: {
    fontSize: 12,
  },
});

export default SavingsGoalCard;
