import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Switch, Text } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import FormInput from '../ui/FormInput';
import Button from '../ui/Button';
import Dropdown from '../ui/Dropdown';
import DatePicker from '../ui/DatePicker';
import { SavingsGoal, SavingsGoalStatus } from '../../models/SavingsGoal';
import { FrequencyType } from '../../models/Transaction';
// Define interfaces locally to avoid import issues
interface Priority {
  id: number;
  name: string;
  description?: string;
  color?: string;
  order: number;
  isSystem: boolean;
  createdAt: string;
  updatedAt?: string;
}

interface Family {
  id: number;
  name: string;
  description?: string;
  createdByUserId: number;
  createdByUsername?: string;
  createdAt: string;
  updatedAt?: string;
  memberCount: number;
  isUserAdmin: boolean;
}

interface SavingsGoalFormProps {
  initialValues?: Partial<SavingsGoal>;
  onSubmit: (values: any) => void;
  isLoading?: boolean;
  priorities: Priority[];
  families: Family[];
  mode: 'create' | 'edit';
}

const SavingsGoalForm: React.FC<SavingsGoalFormProps> = ({
  initialValues = {},
  onSubmit,
  isLoading = false,
  priorities,
  families,
  mode,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();

  // Form state
  const [title, setTitle] = useState(initialValues.title || '');
  const [description, setDescription] = useState(initialValues.description || '');
  const [targetAmount, setTargetAmount] = useState(initialValues.targetAmount?.toString() || '');
  const [startDate, setStartDate] = useState(initialValues.startDate ? new Date(initialValues.startDate) : new Date());
  const [targetDate, setTargetDate] = useState(initialValues.targetDate ? new Date(initialValues.targetDate) : new Date(new Date().setMonth(new Date().getMonth() + 6)));
  const [isShared, setIsShared] = useState(initialValues.isShared || false);
  const [familyId, setFamilyId] = useState<number | null>(initialValues.familyId || null);
  const [priorityId, setPriorityId] = useState<number | null>(initialValues.priorityId || null);
  const [status, setStatus] = useState<SavingsGoalStatus>(initialValues.status || 'Active');
  const [autoContribute, setAutoContribute] = useState(initialValues.autoContribute || false);
  const [autoContributeAmount, setAutoContributeAmount] = useState(initialValues.autoContributeAmount?.toString() || '');
  const [autoContributeFrequency, setAutoContributeFrequency] = useState<FrequencyType | null>(initialValues.autoContributeFrequency || FrequencyType.Monthly);

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Update familyId when isShared changes
  useEffect(() => {
    if (!isShared) {
      setFamilyId(null);
    } else if (families.length > 0 && !familyId) {
      setFamilyId(families[0].id);
    }
  }, [isShared, families, familyId]);

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!title.trim()) {
      newErrors.title = t('validation.required');
    }

    if (!targetAmount || isNaN(Number(targetAmount)) || Number(targetAmount) <= 0) {
      newErrors.targetAmount = t('validation.positive');
    }

    if (targetDate <= startDate) {
      newErrors.targetDate = t('validation.targetDateAfterStart');
    }

    if (isShared && !familyId) {
      newErrors.familyId = t('validation.required');
    }

    if (autoContribute) {
      if (!autoContributeAmount || isNaN(Number(autoContributeAmount)) || Number(autoContributeAmount) <= 0) {
        newErrors.autoContributeAmount = t('validation.positive');
      }

      if (!autoContributeFrequency) {
        newErrors.autoContributeFrequency = t('validation.required');
      }
    }

    setErrors(newErrors);
    setTouched(Object.keys(newErrors).reduce((acc, key) => ({ ...acc, [key]: true }), touched));

    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (validateForm()) {
      const formData = {
        id: initialValues.id,
        title,
        description: description.trim() || undefined,
        targetAmount: Number(targetAmount),
        startDate: startDate.toISOString(),
        targetDate: targetDate.toISOString(),
        isShared,
        familyId: isShared ? familyId : null,
        priorityId: priorityId || null,
        status,
        autoContribute,
        autoContributeAmount: autoContribute ? Number(autoContributeAmount) : null,
        autoContributeFrequency: autoContribute ? autoContributeFrequency : null,
      };

      onSubmit(formData);
    }
  };

  // Prepare dropdown options
  const familyOptions = families.map(family => ({
    label: family.name,
    value: family.id,
  }));

  const priorityOptions = [
    { label: t('common.none'), value: null },
    ...priorities.map(priority => ({
      label: priority.name,
      value: priority.id,
    })),
  ];

  const statusOptions = [
    { label: t('savings.statusOptions.active'), value: 'Active' },
    { label: t('savings.statusOptions.achieved'), value: 'Achieved' },
    { label: t('savings.statusOptions.abandoned'), value: 'Abandoned' },
  ];

  const frequencyOptions = [
    { label: t('loans.frequencyTypes.daily'), value: FrequencyType.Daily },
    { label: t('loans.frequencyTypes.weekly'), value: FrequencyType.Weekly },
    { label: t('loans.frequencyTypes.monthly'), value: FrequencyType.Monthly },
    { label: t('loans.frequencyTypes.quarterly'), value: FrequencyType.Quarterly },
    { label: t('loans.frequencyTypes.yearly'), value: FrequencyType.Yearly },
  ];

  return (
    <ScrollView
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }
      ]}
      contentContainerStyle={styles.contentContainer}
    >
      {/* Basic Information */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
          {t('common.basicInformation')}
        </Text>

        <FormInput
          label={t('savings.title')}
          value={title}
          onChangeText={setTitle}
          placeholder={t('savings.titlePlaceholder')}
          error={errors.title}
          touched={touched.title}
          onBlur={() => setTouched({ ...touched, title: true })}
          required
        />

        <FormInput
          label={t('savings.description')}
          value={description}
          onChangeText={setDescription}
          placeholder={t('savings.descriptionPlaceholder')}
          multiline
          numberOfLines={3}
        />

        <FormInput
          label={t('savings.targetAmount')}
          value={targetAmount}
          onChangeText={setTargetAmount}
          keyboardType="numeric"
          error={errors.targetAmount}
          touched={touched.targetAmount}
          onBlur={() => setTouched({ ...touched, targetAmount: true })}
          required
        />

        <DatePicker
          label={t('savings.startDate')}
          selectedDate={startDate}
          onDateChange={(newDate) => newDate && setStartDate(newDate)}
          minimumDate={new Date(new Date().setDate(new Date().getDate() - 30))}
          required
        />

        <DatePicker
          label={t('savings.targetDate')}
          selectedDate={targetDate}
          onDateChange={(newDate) => newDate && setTargetDate(newDate)}
          minimumDate={new Date(new Date().setDate(new Date().getDate() + 1))}
          error={errors.targetDate}
          touched={touched.targetDate}
          onBlur={() => setTouched({ ...touched, targetDate: true })}
          required
        />

        <Dropdown
          label={t('savings.priority')}
          options={priorityOptions}
          selectedValue={priorityId}
          onValueChange={(value) => setPriorityId(value as number | null)}
          placeholder={t('common.select')}
        />

        {mode === 'edit' && (
          <Dropdown
            label={t('savings.status')}
            options={statusOptions}
            selectedValue={status}
            onValueChange={(value) => setStatus(value as SavingsGoalStatus)}
            placeholder={t('common.select')}
            required
          />
        )}
      </View>

      {/* Sharing Options */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
          {t('common.sharingOptions')}
        </Text>

        <View style={styles.switchContainer}>
          <Text style={[styles.switchLabel, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
            {t('savings.isShared')}
          </Text>
          <Switch
            value={isShared}
            onValueChange={setIsShared}
            trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
            thumbColor={isShared ? isDarkMode ? '#58A6FF' : '#0366D6' : '#f4f3f4'}
          />
        </View>

        {isShared && (
          <Dropdown
            label={t('family.family')}
            options={familyOptions}
            selectedValue={familyId}
            onValueChange={(value) => setFamilyId(value as number)}
            placeholder={t('common.select')}
            error={errors.familyId}
            touched={touched.familyId}
            onBlur={() => setTouched({ ...touched, familyId: true })}
            required={isShared}
          />
        )}
      </View>

      {/* Auto Contribution */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
          {t('savings.autoContribute')}
        </Text>

        <View style={styles.switchContainer}>
          <Text style={[styles.switchLabel, { color: isDarkMode ? '#FFFFFF' : '#24292E' }]}>
            {t('savings.autoContribute')}
          </Text>
          <Switch
            value={autoContribute}
            onValueChange={setAutoContribute}
            trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
            thumbColor={autoContribute ? isDarkMode ? '#58A6FF' : '#0366D6' : '#f4f3f4'}
          />
        </View>

        {autoContribute && (
          <>
            <FormInput
              label={t('savings.autoContributeAmount')}
              value={autoContributeAmount}
              onChangeText={setAutoContributeAmount}
              keyboardType="numeric"
              error={errors.autoContributeAmount}
              touched={touched.autoContributeAmount}
              onBlur={() => setTouched({ ...touched, autoContributeAmount: true })}
              required={autoContribute}
            />

            <Dropdown
              label={t('savings.autoContributeFrequency')}
              options={frequencyOptions}
              selectedValue={autoContributeFrequency}
              onValueChange={(value) => setAutoContributeFrequency(value as FrequencyType)}
              placeholder={t('common.select')}
              error={errors.autoContributeFrequency}
              touched={touched.autoContributeFrequency}
              onBlur={() => setTouched({ ...touched, autoContributeFrequency: true })}
              required={autoContribute}
            />
          </>
        )}
      </View>

      {/* Submit Button */}
      <Button
        title={mode === 'create' ? t('savings.createGoal') : t('savings.updateGoal')}
        onPress={handleSubmit}
        isLoading={isLoading}
        fullWidth
        style={styles.submitButton}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchLabel: {
    fontSize: 16,
  },
  submitButton: {
    marginTop: 16,
    marginBottom: 32,
  },
});

export default SavingsGoalForm;
