import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  ActivityIndicator
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import apiService from '../../services/api';
import PriorityBadge from '../ui/PriorityBadge';

interface Priority {
  id: number;
  name: string;
  description?: string;
  color?: string;
}

interface PrioritySelectorProps {
  selectedPriorityId: number | null;
  onPrioritySelected: (priorityId: number, priorityName: string) => void;
  label?: string;
  error?: string;
  touched?: boolean;
}

const PrioritySelector: React.FC<PrioritySelectorProps> = ({
  selectedPriorityId,
  onPrioritySelected,
  label,
  error,
  touched,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();

  const [modalVisible, setModalVisible] = useState(false);
  const [priorities, setPriorities] = useState<Priority[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPriority, setSelectedPriority] = useState<Priority | null>(null);

  const showError = error && touched;

  // Fetch priorities
  useEffect(() => {
    const fetchPriorities = async () => {
      try {
        setIsLoading(true);
        const response = await apiService.priorities.getAllPriorities();

        if (response.status === 200 && response.data) {
          setPriorities(response.data);

          // Set selected priority
          if (selectedPriorityId) {
            const priority = response.data.find((p: Priority) => p.id === selectedPriorityId);
            if (priority) {
              setSelectedPriority(priority);
            }
          }
        } else {
          console.error('Failed to fetch priorities:', response.error);
        }
      } catch (error) {
        console.error('Error fetching priorities:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPriorities();
  }, [selectedPriorityId]);

  // Handle priority selection
  const handleSelectPriority = (priority: Priority) => {
    setSelectedPriority(priority);
    onPrioritySelected(priority.id, priority.name);
    setModalVisible(false);
  };

  return (
    <View style={styles.container}>
      {label && (
        <Text style={[
          styles.label,
          { color: isDarkMode ? '#8B949E' : '#6E7781' }
        ]}>
          {label}
        </Text>
      )}

      <TouchableOpacity
        style={[
          styles.selectorButton,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            borderColor: showError
              ? '#F85149'
              : isDarkMode
                ? '#30363D'
                : '#D0D7DE'
          }
        ]}
        onPress={() => setModalVisible(true)}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator
            size="small"
            color={isDarkMode ? '#58A6FF' : '#0366D6'}
          />
        ) : selectedPriority ? (
          <View style={styles.selectedPriorityContainer}>
            <PriorityBadge
              level={selectedPriority.name}
              size="medium"
            />
            <Text
              style={[
                styles.selectedPriorityText,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' },
              ]}
            >
              {t(`priorities.${selectedPriority.name.toLowerCase()}`)}
            </Text>
          </View>
        ) : (
          <Text
            style={[
              styles.placeholderText,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t('priorities.selectPriority')}
          </Text>
        )}

        <Ionicons
          name="chevron-down"
          size={20}
          color={isDarkMode ? '#8B949E' : '#6E7781'}
        />
      </TouchableOpacity>

      {showError && (
        <Text style={styles.errorText}>{error}</Text>
      )}

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setModalVisible(false)}
        >
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
              },
            ]}
          >
            <Text
              style={[
                styles.modalTitle,
                { color: isDarkMode ? '#FFFFFF' : '#24292E' },
              ]}
            >
              {t('priorities.selectPriority')}
            </Text>

            <FlatList
              data={priorities}
              keyExtractor={(item) => item.id.toString()}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.priorityItem,
                    selectedPriority && selectedPriority.id === item.id && {
                      backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                    },
                  ]}
                  onPress={() => handleSelectPriority(item)}
                >
                  <View style={styles.priorityInfo}>
                    <PriorityBadge
                      level={item.name}
                      size="medium"
                    />
                    <Text
                      style={[
                        styles.priorityName,
                        { color: isDarkMode ? '#FFFFFF' : '#24292E' },
                      ]}
                    >
                      {t(`priorities.${item.name.toLowerCase()}`)}
                    </Text>
                  </View>

                  {selectedPriority && selectedPriority.id === item.id && (
                    <Ionicons
                      name="checkmark-circle"
                      size={20}
                      color={isDarkMode ? '#58A6FF' : '#0366D6'}
                    />
                  )}
                </TouchableOpacity>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    width: '100%',
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
  },
  selectorButton: {
    height: 48,
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectedPriorityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedPriorityText: {
    fontSize: 16,
    marginLeft: 8,
  },
  placeholderText: {
    fontSize: 16,
  },
  errorText: {
    color: '#F85149',
    fontSize: 14,
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    maxHeight: '60%',
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  priorityItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  priorityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityName: {
    fontSize: 16,
    marginLeft: 8,
  },
});

export default PrioritySelector;
