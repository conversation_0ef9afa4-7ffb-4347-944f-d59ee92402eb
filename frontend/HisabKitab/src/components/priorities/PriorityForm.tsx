import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import FormInput from '../ui/FormInput';
import Button from '../ui/Button';
import ColorPicker from '../ui/ColorPicker';
import Dropdown from '../ui/Dropdown';

interface PriorityFormProps {
  initialValues?: {
    id?: number;
    name: string;
    description: string;
    color: string;
    level?: number;
  };
  onSubmit: (values: any) => void;
  isLoading?: boolean;
  mode?: 'create' | 'edit';
}

const PriorityForm: React.FC<PriorityFormProps> = ({
  initialValues = {
    name: '',
    description: '',
    color: '#4CAF50',
    level: 3, // Default to Normal
  },
  onSubmit,
  isLoading = false,
  mode = 'create',
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();

  // Priority level options
  const levelOptions = [
    { label: t('priorities.urgent'), value: 1 }, // Urgent
    { label: t('priorities.high'), value: 2 }, // High
    { label: t('priorities.normal'), value: 3 }, // Normal
    { label: t('priorities.low'), value: 4 }, // Low
    { label: t('priorities.custom'), value: 5 }, // Custom (for additional levels)
  ];

  // Form state
  const [name, setName] = useState(initialValues.name);
  const [description, setDescription] = useState(initialValues.description);
  const [color, setColor] = useState(initialValues.color);
  const [level, setLevel] = useState(initialValues.level || 3);

  // Form validation
  const [touched, setTouched] = useState({
    name: false,
    description: false,
    level: false,
  });

  const [errors, setErrors] = useState({
    name: '',
    description: '',
    level: '',
  });

  // Validate form
  const validateForm = () => {
    const newErrors = {
      name: '',
      description: '',
      level: '',
    };

    if (!name.trim()) {
      newErrors.name = t('validation.required');
    } else if (name.length < 2) {
      newErrors.name = t('validation.minLength', { length: 2 });
    }

    if (!description.trim()) {
      newErrors.description = t('validation.required');
    }

    if (!level) {
      newErrors.level = t('validation.required');
    }

    setErrors(newErrors);

    return !Object.values(newErrors).some((error) => error);
  };

  // Handle form submission
  const handleSubmit = () => {
    // Mark all fields as touched
    setTouched({
      name: true,
      description: true,
      level: true,
    });

    if (validateForm()) {
      const formData = {
        ...(initialValues.id && { id: initialValues.id }),
        name,
        description,
        color,
        level,
      };

      onSubmit(formData);
    } else {
      Alert.alert(t('common.error'), t('validation.fixErrors'));
    }
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
    >
      <FormInput
        label={t('priorities.priorityName')}
        value={name}
        onChangeText={setName}
        onBlur={() => setTouched({ ...touched, name: true })}
        error={errors.name}
        touched={touched.name}
        placeholder={t('priorities.priorityNamePlaceholder')}
      />

      <FormInput
        label={t('priorities.description')}
        value={description}
        onChangeText={setDescription}
        onBlur={() => setTouched({ ...touched, description: true })}
        error={errors.description}
        touched={touched.description}
        placeholder={t('priorities.descriptionPlaceholder')}
        multiline
        numberOfLines={4}
        style={styles.textArea}
      />

      <Dropdown
        label={t('priorities.level')}
        options={levelOptions}
        selectedValue={level}
        onValueChange={(value) => typeof value === 'number' ? setLevel(value) : null}
        valueType="number"
        error={errors.level}
        touched={touched.level}
      />

      <ColorPicker
        label={t('priorities.color')}
        selectedColor={color}
        onColorSelected={setColor}
      />

      <View style={styles.buttonContainer}>
        <Button
          title={
            mode === 'create'
              ? t('priorities.createPriority')
              : t('priorities.updatePriority')
          }
          onPress={handleSubmit}
          isLoading={isLoading}
          fullWidth
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 40,
  },
});

export default PriorityForm;
