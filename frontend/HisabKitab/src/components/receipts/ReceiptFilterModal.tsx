import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  TextInput,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { ReceiptFilterOptions } from '../../models/Receipt';
import Button from '../ui/Button';
import DatePicker from '../ui/DatePicker';

interface ReceiptFilterModalProps {
  visible: boolean;
  onClose: () => void;
  onApply: (filter: ReceiptFilterOptions) => void;
  initialFilter?: ReceiptFilterOptions;
}

const ReceiptFilterModal: React.FC<ReceiptFilterModalProps> = ({
  visible,
  onClose,
  onApply,
  initialFilter = {},
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  
  // State
  const [startDate, setStartDate] = useState<Date | undefined>(
    initialFilter.startDate ? new Date(initialFilter.startDate) : undefined
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    initialFilter.endDate ? new Date(initialFilter.endDate) : undefined
  );
  const [minAmount, setMinAmount] = useState(
    initialFilter.minAmount ? initialFilter.minAmount.toString() : ''
  );
  const [maxAmount, setMaxAmount] = useState(
    initialFilter.maxAmount ? initialFilter.maxAmount.toString() : ''
  );
  const [merchantName, setMerchantName] = useState(initialFilter.merchantName || '');
  const [isProcessed, setIsProcessed] = useState<boolean | undefined>(initialFilter.isProcessed);
  
  // Reset filter
  const resetFilter = () => {
    setStartDate(undefined);
    setEndDate(undefined);
    setMinAmount('');
    setMaxAmount('');
    setMerchantName('');
    setIsProcessed(undefined);
  };
  
  // Apply filter
  const applyFilter = () => {
    const filter: ReceiptFilterOptions = {};
    
    if (startDate) {
      filter.startDate = startDate.toISOString().split('T')[0];
    }
    
    if (endDate) {
      filter.endDate = endDate.toISOString().split('T')[0];
    }
    
    if (minAmount && !isNaN(Number(minAmount))) {
      filter.minAmount = Number(minAmount);
    }
    
    if (maxAmount && !isNaN(Number(maxAmount))) {
      filter.maxAmount = Number(maxAmount);
    }
    
    if (merchantName.trim()) {
      filter.merchantName = merchantName.trim();
    }
    
    if (isProcessed !== undefined) {
      filter.isProcessed = isProcessed;
    }
    
    onApply(filter);
  };
  
  // Update filter when initialFilter changes
  useEffect(() => {
    if (visible) {
      setStartDate(initialFilter.startDate ? new Date(initialFilter.startDate) : undefined);
      setEndDate(initialFilter.endDate ? new Date(initialFilter.endDate) : undefined);
      setMinAmount(initialFilter.minAmount ? initialFilter.minAmount.toString() : '');
      setMaxAmount(initialFilter.maxAmount ? initialFilter.maxAmount.toString() : '');
      setMerchantName(initialFilter.merchantName || '');
      setIsProcessed(initialFilter.isProcessed);
    }
  }, [visible, initialFilter]);
  
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[
          styles.modalContent,
          { backgroundColor: isDarkMode ? '#161B22' : '#FFFFFF' }
        ]}>
          <View style={styles.modalHeader}>
            <Text style={[
              styles.modalTitle,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' }
            ]}>
              {t('receipts.filterReceipts')}
            </Text>
            
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <Ionicons
                name="close"
                size={24}
                color={isDarkMode ? '#8B949E' : '#6E7781'}
              />
            </TouchableOpacity>
          </View>
          
          <View style={styles.filterSection}>
            <Text style={[
              styles.sectionTitle,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' }
            ]}>
              {t('receipts.dateRange')}
            </Text>
            
            <View style={styles.dateRangeContainer}>
              <View style={styles.datePickerContainer}>
                <Text style={[
                  styles.dateLabel,
                  { color: isDarkMode ? '#8B949E' : '#6E7781' }
                ]}>
                  {t('common.from')}
                </Text>
                <DatePicker
                  date={startDate}
                  onDateChange={setStartDate}
                  mode="date"
                  placeholder={t('common.selectDate')}
                  clearable={true}
                />
              </View>
              
              <View style={styles.datePickerContainer}>
                <Text style={[
                  styles.dateLabel,
                  { color: isDarkMode ? '#8B949E' : '#6E7781' }
                ]}>
                  {t('common.to')}
                </Text>
                <DatePicker
                  date={endDate}
                  onDateChange={setEndDate}
                  mode="date"
                  placeholder={t('common.selectDate')}
                  clearable={true}
                />
              </View>
            </View>
          </View>
          
          <View style={styles.filterSection}>
            <Text style={[
              styles.sectionTitle,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' }
            ]}>
              {t('receipts.amountRange')}
            </Text>
            
            <View style={styles.amountRangeContainer}>
              <View style={styles.amountInputContainer}>
                <Text style={[
                  styles.amountLabel,
                  { color: isDarkMode ? '#8B949E' : '#6E7781' }
                ]}>
                  {t('common.min')}
                </Text>
                <TextInput
                  style={[
                    styles.amountInput,
                    { 
                      backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA',
                      color: isDarkMode ? '#FFFFFF' : '#24292E',
                      borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                    }
                  ]}
                  value={minAmount}
                  onChangeText={setMinAmount}
                  keyboardType="numeric"
                  placeholder="0"
                  placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
                />
              </View>
              
              <View style={styles.amountInputContainer}>
                <Text style={[
                  styles.amountLabel,
                  { color: isDarkMode ? '#8B949E' : '#6E7781' }
                ]}>
                  {t('common.max')}
                </Text>
                <TextInput
                  style={[
                    styles.amountInput,
                    { 
                      backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA',
                      color: isDarkMode ? '#FFFFFF' : '#24292E',
                      borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                    }
                  ]}
                  value={maxAmount}
                  onChangeText={setMaxAmount}
                  keyboardType="numeric"
                  placeholder="∞"
                  placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
                />
              </View>
            </View>
          </View>
          
          <View style={styles.filterSection}>
            <Text style={[
              styles.sectionTitle,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' }
            ]}>
              {t('receipts.merchant')}
            </Text>
            
            <TextInput
              style={[
                styles.merchantInput,
                { 
                  backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA',
                  color: isDarkMode ? '#FFFFFF' : '#24292E',
                  borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                }
              ]}
              value={merchantName}
              onChangeText={setMerchantName}
              placeholder={t('receipts.merchantNamePlaceholder')}
              placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
            />
          </View>
          
          <View style={styles.filterSection}>
            <Text style={[
              styles.sectionTitle,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' }
            ]}>
              {t('receipts.processingStatus')}
            </Text>
            
            <View style={styles.statusOptions}>
              <TouchableOpacity
                style={[
                  styles.statusOption,
                  isProcessed === undefined && styles.statusOptionSelected,
                  { 
                    backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA',
                    borderColor: isProcessed === undefined ? 
                      (isDarkMode ? '#58A6FF' : '#0366D6') : 
                      (isDarkMode ? '#30363D' : '#D0D7DE')
                  }
                ]}
                onPress={() => setIsProcessed(undefined)}
              >
                <Text style={[
                  styles.statusOptionText,
                  { 
                    color: isProcessed === undefined ? 
                      (isDarkMode ? '#58A6FF' : '#0366D6') : 
                      (isDarkMode ? '#8B949E' : '#6E7781')
                  }
                ]}>
                  {t('common.all')}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.statusOption,
                  isProcessed === true && styles.statusOptionSelected,
                  { 
                    backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA',
                    borderColor: isProcessed === true ? 
                      (isDarkMode ? '#58A6FF' : '#0366D6') : 
                      (isDarkMode ? '#30363D' : '#D0D7DE')
                  }
                ]}
                onPress={() => setIsProcessed(true)}
              >
                <Text style={[
                  styles.statusOptionText,
                  { 
                    color: isProcessed === true ? 
                      (isDarkMode ? '#58A6FF' : '#0366D6') : 
                      (isDarkMode ? '#8B949E' : '#6E7781')
                  }
                ]}>
                  {t('receipts.processed')}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.statusOption,
                  isProcessed === false && styles.statusOptionSelected,
                  { 
                    backgroundColor: isDarkMode ? '#0D1117' : '#F6F8FA',
                    borderColor: isProcessed === false ? 
                      (isDarkMode ? '#58A6FF' : '#0366D6') : 
                      (isDarkMode ? '#30363D' : '#D0D7DE')
                  }
                ]}
                onPress={() => setIsProcessed(false)}
              >
                <Text style={[
                  styles.statusOptionText,
                  { 
                    color: isProcessed === false ? 
                      (isDarkMode ? '#58A6FF' : '#0366D6') : 
                      (isDarkMode ? '#8B949E' : '#6E7781')
                  }
                ]}>
                  {t('receipts.pending')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          
          <View style={styles.modalActions}>
            <Button
              title={t('common.reset')}
              onPress={resetFilter}
              type="secondary"
              style={styles.actionButton}
            />
            <Button
              title={t('common.apply')}
              onPress={applyFilter}
              type="primary"
              style={styles.actionButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  filterSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  dateRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  datePickerContainer: {
    flex: 1,
    marginHorizontal: 4,
  },
  dateLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  amountRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  amountInputContainer: {
    flex: 1,
    marginHorizontal: 4,
  },
  amountLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  amountInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  merchantInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  statusOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statusOption: {
    flex: 1,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  statusOptionSelected: {
    borderWidth: 2,
  },
  statusOptionText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  actionButton: {
    minWidth: 100,
    marginLeft: 8,
  },
});

export default ReceiptFilterModal;
