/**
 * Receipt viewer component for the Hisab-Kitab app
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  Modal,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
// import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import * as Sharing from 'expo-sharing';
import useAppTheme from '../../theme/useAppTheme';
import Button from '../ui/Button';
import SafeTranslation from '../ui/SafeTranslation';
import { Transaction } from '../../models/Transaction';
import { formatDate, formatCurrency } from '../../utils/formatters';

interface ReceiptViewerProps {
  transaction: Transaction;
  receiptUrl: string;
  onClose: () => void;
  onDelete?: () => void;
}

const ReceiptViewer: React.FC<ReceiptViewerProps> = ({
  transaction,
  receiptUrl,
  onClose,
  onDelete,
}) => {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const screenWidth = Dimensions.get('window').width;

  // State
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Save receipt to gallery
  const handleSaveToGallery = async () => {
    try {
      setIsLoading(true);

      // Request permission
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== 'granted') {
        alert(t('receipts.galleryPermissionRequired'));
        return;
      }

      // Save to gallery
      const asset = await MediaLibrary.createAssetAsync(receiptUrl);
      await MediaLibrary.createAlbumAsync('Hisab-Kitab Receipts', asset, false);

      // Show success message
      alert(t('receipts.savedToGallery'));
    } catch (error) {
      console.error('Error saving to gallery:', error);
      alert(t('receipts.saveFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  // Share receipt
  const handleShare = async () => {
    try {
      setIsLoading(true);

      // Check if sharing is available
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        alert(t('receipts.sharingNotAvailable'));
        return;
      }

      // Share the receipt
      await Sharing.shareAsync(receiptUrl);
    } catch (error) {
      console.error('Error sharing receipt:', error);
      alert(t('receipts.shareFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  // Render transaction details
  const renderTransactionDetails = () => {
    return (
      <View style={styles.detailsContainer}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
          <SafeTranslation i18nKey="receipts.transactionDetails" fallback="Transaction Details" />
        </Text>

        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: theme.colors.text.secondary }]}>
            <SafeTranslation i18nKey="transactions.description" fallback="Description" />:
          </Text>
          <Text style={[styles.detailValue, { color: theme.colors.text.primary }]}>
            {transaction.description || t('common.none')}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: theme.colors.text.secondary }]}>
            <SafeTranslation i18nKey="transactions.amount" fallback="Amount" />:
          </Text>
          <Text style={[styles.detailValue, { color: theme.colors.semantic.expense }]}>
            {formatCurrency(transaction.amount)}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: theme.colors.text.secondary }]}>
            <SafeTranslation i18nKey="transactions.date" fallback="Date" />:
          </Text>
          <Text style={[styles.detailValue, { color: theme.colors.text.primary }]}>
            {formatDate(new Date(transaction.date))}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: theme.colors.text.secondary }]}>
            <SafeTranslation i18nKey="transactions.category" fallback="Category" />:
          </Text>
          <Text style={[styles.detailValue, { color: theme.colors.text.primary }]}>
            {transaction.categoryName || t('common.none')}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: theme.colors.text.secondary }]}>
            <SafeTranslation i18nKey="transactions.account" fallback="Account" />:
          </Text>
          <Text style={[styles.detailValue, { color: theme.colors.text.primary }]}>
            {transaction.accountName || t('common.none')}
          </Text>
        </View>
      </View>
    );
  };

  // Render items if available
  const renderItems = () => {
    if (!transaction.items || transaction.items.length === 0) {
      return null;
    }

    return (
      <View style={styles.itemsContainer}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
          <SafeTranslation i18nKey="transactions.items" fallback="Items" />
        </Text>

        <View style={[styles.itemsHeader, { borderBottomColor: theme.colors.border.primary }]}>
          <Text style={[styles.itemHeaderText, styles.itemName, { color: theme.colors.text.secondary }]}>
            <SafeTranslation i18nKey="transactions.itemName" fallback="Item" />
          </Text>
          <Text style={[styles.itemHeaderText, styles.itemQuantity, { color: theme.colors.text.secondary }]}>
            <SafeTranslation i18nKey="transactions.quantity" fallback="Qty" />
          </Text>
          <Text style={[styles.itemHeaderText, styles.itemPrice, { color: theme.colors.text.secondary }]}>
            <SafeTranslation i18nKey="transactions.price" fallback="Price" />
          </Text>
          <Text style={[styles.itemHeaderText, styles.itemAmount, { color: theme.colors.text.secondary }]}>
            <SafeTranslation i18nKey="transactions.amount" fallback="Amount" />
          </Text>
        </View>

        {transaction.items.map((item, index) => (
          <View
            key={index}
            style={[
              styles.itemRow,
              { borderBottomColor: theme.colors.border.tertiary },
              index === transaction.items!.length - 1 && styles.lastItemRow,
            ]}
          >
            <Text style={[styles.itemText, styles.itemName, { color: theme.colors.text.primary }]}>
              {item.name}
            </Text>
            <Text style={[styles.itemText, styles.itemQuantity, { color: theme.colors.text.primary }]}>
              {item.quantity}
            </Text>
            <Text style={[styles.itemText, styles.itemPrice, { color: theme.colors.text.primary }]}>
              {formatCurrency(item.amount / item.quantity)}
            </Text>
            <Text style={[styles.itemText, styles.itemAmount, { color: theme.colors.text.primary }]}>
              {formatCurrency(item.amount)}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  // Render full screen image modal
  const renderFullScreenImage = () => {
    return (
      <Modal
        visible={isFullScreen}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsFullScreen(false)}
      >
        <View style={styles.fullScreenContainer}>
          <TouchableOpacity
            style={styles.closeFullScreenButton}
            onPress={() => setIsFullScreen(false)}
          >
            <Ionicons
              name="close-circle"
              size={32}
              color="#FFFFFF"
            />
          </TouchableOpacity>

          <Image
            source={{ uri: receiptUrl }}
            style={styles.fullScreenImage}
            resizeMode="contain"
          />
        </View>
      </Modal>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background.primary }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text.primary }]}>
          <SafeTranslation i18nKey="receipts.receiptDetails" fallback="Receipt Details" />
        </Text>

        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Ionicons
            name="close-outline"
            size={24}
            color={theme.colors.text.primary}
          />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollContainer}>
        <View style={styles.imageContainer}>
          <TouchableOpacity onPress={() => setIsFullScreen(true)}>
            <Image
              source={{ uri: receiptUrl }}
              style={[styles.image, { width: screenWidth - 32 }]}
              resizeMode="contain"
            />

            <View style={styles.zoomIconContainer}>
              <Ionicons
                name="expand-outline"
                size={24}
                color="#FFFFFF"
              />
            </View>
          </TouchableOpacity>
        </View>

        {renderTransactionDetails()}
        {renderItems()}
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title={t('receipts.saveToGallery')}
          icon="save-outline"
          onPress={handleSaveToGallery}
          disabled={isLoading}
          style={styles.footerButton}
          variant="secondary"
        />

        <Button
          title={t('receipts.share')}
          icon="share-outline"
          onPress={handleShare}
          disabled={isLoading}
          style={styles.footerButton}
          variant="secondary"
        />

        {onDelete && (
          <Button
            title={t('common.delete')}
            icon="trash-outline"
            onPress={onDelete}
            disabled={isLoading}
            style={styles.footerButton}
            variant="danger"
          />
        )}
      </View>

      {isLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      )}

      {renderFullScreenImage()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E4E8',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  scrollContainer: {
    flex: 1,
  },
  imageContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  image: {
    height: 300,
    borderRadius: 8,
  },
  zoomIconContainer: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 16,
    padding: 4,
  },
  detailsContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E4E8',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  detailLabel: {
    width: 100,
    fontSize: 14,
    fontWeight: '500',
  },
  detailValue: {
    flex: 1,
    fontSize: 14,
  },
  itemsContainer: {
    padding: 16,
  },
  itemsHeader: {
    flexDirection: 'row',
    paddingBottom: 8,
    borderBottomWidth: 1,
    marginBottom: 8,
  },
  itemHeaderText: {
    fontSize: 12,
    fontWeight: '500',
  },
  itemRow: {
    flexDirection: 'row',
    paddingVertical: 8,
    borderBottomWidth: 1,
  },
  lastItemRow: {
    borderBottomWidth: 0,
  },
  itemText: {
    fontSize: 14,
  },
  itemName: {
    flex: 2,
  },
  itemQuantity: {
    flex: 0.5,
    textAlign: 'center',
  },
  itemPrice: {
    flex: 1,
    textAlign: 'right',
  },
  itemAmount: {
    flex: 1,
    textAlign: 'right',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E1E4E8',
  },
  footerButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenImage: {
    width: '100%',
    height: '80%',
  },
  closeFullScreenButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 1,
  },
});

export default ReceiptViewer;
