/**
 * Receipt scanner component for the Hisab-Kitab app
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { Camera } from 'expo-camera';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import useAppTheme from '../../theme/useAppTheme';
import Button from '../ui/Button';
import ErrorDisplay from '../ui/ErrorDisplay';
import SafeTranslation from '../ui/SafeTranslation';
import {
  SUPPORTED_LANGUAGES,
  isLanguageAvailable,
  downloadLanguage,
  extractTextFromImage,
  extractReceiptData,
} from '../../utils/receiptScanning';
import { Transaction, TransactionType } from '../../models/Transaction';
import { ErrorType } from '../../utils/errorHandling';

interface ReceiptScannerProps {
  onScanComplete: (transaction: Partial<Transaction>) => void;
  onCancel: () => void;
}

const ReceiptScanner: React.FC<ReceiptScannerProps> = ({
  onScanComplete,
  onCancel,
}) => {
  const { t } = useTranslation();
  const theme = useAppTheme();

  // State
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState('eng');
  const [availableLanguages, setAvailableLanguages] = useState<string[]>([]);
  const [cameraPermission, setCameraPermission] = useState<boolean | null>(null);
  const [galleryPermission, setGalleryPermission] = useState<boolean | null>(null);

  // Check permissions and available languages on mount
  useEffect(() => {
    (async () => {
      // Check camera permission
      const { status: cameraStatus } = await Camera.requestCameraPermissionsAsync();
      setCameraPermission(cameraStatus === 'granted');

      // Check gallery permission
      const { status: galleryStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      setGalleryPermission(galleryStatus === 'granted');

      // Check available languages
      checkAvailableLanguages();
    })();
  }, []);

  // Check which OCR languages are available
  const checkAvailableLanguages = async () => {
    try {
      const languages: string[] = [];
      for (const lang of SUPPORTED_LANGUAGES) {
        const isAvailable = await isLanguageAvailable(lang.code);
        if (isAvailable) {
          languages.push(lang.code);
        }
      }
      setAvailableLanguages(languages);
    } catch (error) {
      console.error('Error checking available languages:', error);
    }
  };

  // Download a language
  const handleDownloadLanguage = async (langCode: string) => {
    try {
      setIsProcessing(true);
      setError(null);

      const success = await downloadLanguage(langCode);
      if (success) {
        setAvailableLanguages([...availableLanguages, langCode]);
        setSelectedLanguage(langCode);
        Alert.alert(
          t('receipts.languageDownloaded'),
          t('receipts.languageDownloadedMessage', { language: getLanguageName(langCode) })
        );
      } else {
        setError(t('receipts.languageDownloadFailed'));
      }
    } catch (error) {
      setError(t('receipts.languageDownloadFailed'));
      console.error('Error downloading language:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Get language name from code
  const getLanguageName = (code: string): string => {
    const language = SUPPORTED_LANGUAGES.find(lang => lang.code === code);
    return language ? language.name : code;
  };

  // Take a photo with the camera
  const handleTakePhoto = async () => {
    try {
      if (!cameraPermission) {
        setError(t('receipts.cameraPermissionRequired'));
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setImageUri(result.assets[0].uri);
        setError(null);
      }
    } catch (error) {
      setError(t('receipts.cameraError'));
      console.error('Error taking photo:', error);
    }
  };

  // Pick an image from the gallery
  const handlePickImage = async () => {
    try {
      if (!galleryPermission) {
        setError(t('receipts.galleryPermissionRequired'));
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setImageUri(result.assets[0].uri);
        setError(null);
      }
    } catch (error) {
      setError(t('receipts.galleryError'));
      console.error('Error picking image:', error);
    }
  };

  // Process the image with OCR
  const handleProcessImage = async () => {
    if (!imageUri) {
      setError(t('receipts.noImageSelected'));
      return;
    }

    try {
      setIsProcessing(true);
      setError(null);

      // Check if selected language is available
      if (!availableLanguages.includes(selectedLanguage)) {
        // Ask user if they want to download the language
        Alert.alert(
          t('receipts.languageNotAvailable'),
          t('receipts.downloadLanguageQuestion', { language: getLanguageName(selectedLanguage) }),
          [
            {
              text: t('common.cancel'),
              style: 'cancel',
              onPress: () => setIsProcessing(false),
            },
            {
              text: t('common.download'),
              onPress: async () => {
                await handleDownloadLanguage(selectedLanguage);
                setIsProcessing(false);
              },
            },
          ]
        );
        return;
      }

      // Extract text from image
      const text = await extractTextFromImage(imageUri, selectedLanguage);

      // Extract structured data from text
      const data = extractReceiptData(text);

      // Create transaction from extracted data
      if (data.total) {
        const transaction: Partial<Transaction> = {
          type: TransactionType.Expense,
          amount: data.total,
          description: data.merchant || t('receipts.receipt'),
          date: data.date ? new Date(data.date).toISOString() : new Date().toISOString(),
          receiptImage: imageUri,
          items: data.items?.map(item => ({
            id: 0,
            transactionId: 0,
            name: item.name,
            quantity: item.quantity,
            amount: item.quantity * item.price,
            createdAt: new Date().toISOString(),
          })) || [],
        };

        onScanComplete(transaction);
      } else {
        setError(t('receipts.noDataExtracted'));
      }
    } catch (error) {
      setError(t('receipts.processingError'));
      console.error('Error processing image:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Render language selection
  const renderLanguageSelection = () => {
    return (
      <View style={styles.languageContainer}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
          <SafeTranslation i18nKey="receipts.selectLanguage" fallback="Select Language" />
        </Text>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.languageScroll}>
          {SUPPORTED_LANGUAGES.map(lang => (
            <TouchableOpacity
              key={lang.code}
              style={[
                styles.languageButton,
                {
                  backgroundColor: selectedLanguage === lang.code
                    ? theme.colors.primary
                    : theme.colors.background.secondary,
                  borderColor: theme.colors.border.primary,
                },
              ]}
              onPress={() => setSelectedLanguage(lang.code)}
            >
              <Text
                style={[
                  styles.languageText,
                  {
                    color: selectedLanguage === lang.code
                      ? theme.colors.text.inverse
                      : theme.colors.text.primary,
                  },
                ]}
              >
                {lang.name}
              </Text>

              {!availableLanguages.includes(lang.code) && (
                <TouchableOpacity
                  style={styles.downloadButton}
                  onPress={() => handleDownloadLanguage(lang.code)}
                >
                  <Ionicons
                    name="cloud-download-outline"
                    size={16}
                    color={theme.colors.primary}
                  />
                </TouchableOpacity>
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text.primary }]}>
          <SafeTranslation i18nKey="receipts.scanReceipt" fallback="Scan Receipt" />
        </Text>

        <TouchableOpacity onPress={onCancel} style={styles.closeButton}>
          <Ionicons
            name="close-outline"
            size={24}
            color={theme.colors.text.primary}
          />
        </TouchableOpacity>
      </View>

      {error && (
        <ErrorDisplay
          error={error}
          type={ErrorType.VALIDATION}
          variant="banner"
          onDismiss={() => setError(null)}
        />
      )}

      {renderLanguageSelection()}

      <View style={styles.imageContainer}>
        {imageUri ? (
          <Image
            source={{ uri: imageUri }}
            style={styles.image}
            resizeMode="contain"
          />
        ) : (
          <View style={[styles.placeholderImage, { backgroundColor: theme.colors.background.secondary }]}>
            <Ionicons
              name="receipt-outline"
              size={64}
              color={theme.colors.text.tertiary}
            />
            <Text style={[styles.placeholderText, { color: theme.colors.text.tertiary }]}>
              <SafeTranslation i18nKey="receipts.noImageSelected" fallback="No image selected" />
            </Text>
          </View>
        )}
      </View>

      <View style={styles.buttonContainer}>
        <Button
          title={t('receipts.takePhoto')}
          icon="camera-outline"
          onPress={handleTakePhoto}
          disabled={isProcessing}
          style={styles.button}
        />

        <Button
          title={t('receipts.pickImage')}
          icon="image-outline"
          onPress={handlePickImage}
          disabled={isProcessing}
          style={styles.button}
        />

        <Button
          title={t('receipts.processImage')}
          icon="scan-outline"
          onPress={handleProcessImage}
          disabled={!imageUri || isProcessing}
          isLoading={isProcessing}
          style={styles.processButton}
          variant="primary"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  languageContainer: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  languageScroll: {
    flexDirection: 'row',
  },
  languageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1,
  },
  languageText: {
    fontSize: 14,
    fontWeight: '500',
  },
  downloadButton: {
    marginLeft: 4,
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  placeholderText: {
    marginTop: 8,
    fontSize: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  button: {
    flex: 1,
    marginHorizontal: 4,
  },
  processButton: {
    marginTop: 8,
    width: '100%',
  },
});

export default ReceiptScanner;
