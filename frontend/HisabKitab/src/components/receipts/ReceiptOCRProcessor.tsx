import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { ReceiptData, ReceiptItem } from '../../models/Receipt';
import { formatCurrency } from '../../utils/formatters';
import Button from '../ui/Button';
import Dropdown from '../ui/Dropdown';
import DatePicker from '../ui/DatePicker';

interface ReceiptOCRProcessorProps {
  receiptData: ReceiptData;
  onSave: (updatedData: ReceiptData) => void;
  onCancel: () => void;
  categories?: Array<{ id: number; name: string; type: string }>;
}

const ReceiptOCRProcessor: React.FC<ReceiptOCRProcessorProps> = ({
  receiptData,
  onSave,
  onCancel,
  categories = [],
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();

  // State for editable receipt data
  const [merchantName, setMerchantName] = useState(receiptData.merchantName || '');
  const [date, setDate] = useState<Date | undefined>(
    receiptData.date ? new Date(receiptData.date) : new Date()
  );
  const [totalAmount, setTotalAmount] = useState(
    receiptData.totalAmount ? receiptData.totalAmount.toString() : '0'
  );
  const [items, setItems] = useState<ReceiptItem[]>(receiptData.items || []);
  const [showRawText, setShowRawText] = useState(false);

  // Handle date change with proper typing
  const handleDateChange = (newDate: Date | undefined) => {
    setDate(newDate || new Date());
  };

  // Add a new empty item
  const addItem = () => {
    setItems([...items, { name: '', amount: 0 }]);
  };

  // Update an item
  const updateItem = (index: number, field: keyof ReceiptItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    setItems(updatedItems);
  };

  // Remove an item
  const removeItem = (index: number) => {
    const updatedItems = [...items];
    updatedItems.splice(index, 1);
    setItems(updatedItems);
  };

  // Calculate total from items
  const calculateTotal = () => {
    return items.reduce((sum, item) => sum + Number(item.amount), 0);
  };

  // Update total when items change
  useEffect(() => {
    const calculatedTotal = calculateTotal();
    if (calculatedTotal > 0) {
      setTotalAmount(calculatedTotal.toString());
    }
  }, [items]);

  // Handle save
  const handleSave = () => {
    // Validate data
    if (!merchantName.trim()) {
      Alert.alert(
        t('common.error'),
        t('receipts.merchantNameRequired')
      );
      return;
    }

    if (isNaN(Number(totalAmount)) || Number(totalAmount) <= 0) {
      Alert.alert(
        t('common.error'),
        t('receipts.invalidAmount')
      );
      return;
    }

    // Validate items
    const invalidItems = items.filter(
      item => !item.name.trim() || isNaN(Number(item.amount)) || Number(item.amount) <= 0
    );

    if (invalidItems.length > 0) {
      Alert.alert(
        t('common.error'),
        t('receipts.invalidItems')
      );
      return;
    }

    // Create updated receipt data
    const updatedData: ReceiptData = {
      ...receiptData,
      merchantName,
      date: date ? date.toISOString() : new Date().toISOString(),
      totalAmount: Number(totalAmount),
      items: items.map(item => ({
        ...item,
        amount: Number(item.amount),
        quantity: item.quantity ? Number(item.quantity) : 1,
      })),
    };

    onSave(updatedData);
  };

  return (
    <ScrollView
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }
      ]}
      contentContainerStyle={styles.contentContainer}
    >
      <Text style={[
        styles.title,
        { color: isDarkMode ? '#FFFFFF' : '#24292E' }
      ]}>
        {t('receipts.processExtractedData')}
      </Text>

      <View style={styles.formGroup}>
        <Text style={[
          styles.label,
          { color: isDarkMode ? '#FFFFFF' : '#24292E' }
        ]}>
          {t('receipts.merchantName')}
        </Text>
        <TextInput
          style={[
            styles.input,
            {
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              color: isDarkMode ? '#FFFFFF' : '#24292E',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
            }
          ]}
          value={merchantName}
          onChangeText={setMerchantName}
          placeholder={t('receipts.merchantNamePlaceholder')}
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
        />
      </View>

      <View style={styles.formGroup}>
        <Text style={[
          styles.label,
          { color: isDarkMode ? '#FFFFFF' : '#24292E' }
        ]}>
          {t('receipts.date')}
        </Text>
        <DatePicker
          date={date}
          onDateChange={handleDateChange}
          mode="date"
        />
      </View>

      <View style={styles.formGroup}>
        <Text style={[
          styles.label,
          { color: isDarkMode ? '#FFFFFF' : '#24292E' }
        ]}>
          {t('receipts.totalAmount')}
        </Text>
        <TextInput
          style={[
            styles.input,
            {
              backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
              color: isDarkMode ? '#FFFFFF' : '#24292E',
              borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
            }
          ]}
          value={totalAmount}
          onChangeText={setTotalAmount}
          keyboardType="numeric"
          placeholder="0.00"
          placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
        />
      </View>

      <View style={styles.itemsContainer}>
        <View style={styles.itemsHeader}>
          <Text style={[
            styles.itemsTitle,
            { color: isDarkMode ? '#FFFFFF' : '#24292E' }
          ]}>
            {t('receipts.items')}
          </Text>
          <TouchableOpacity
            style={styles.addItemButton}
            onPress={addItem}
          >
            <Ionicons
              name="add-circle"
              size={24}
              color={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
          </TouchableOpacity>
        </View>

        {items.length === 0 ? (
          <Text style={[
            styles.noItems,
            { color: isDarkMode ? '#8B949E' : '#6E7781' }
          ]}>
            {t('receipts.noItems')}
          </Text>
        ) : (
          items.map((item, index) => (
            <View key={index} style={styles.itemRow}>
              <View style={styles.itemDetails}>
                <TextInput
                  style={[
                    styles.itemInput,
                    {
                      backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                      color: isDarkMode ? '#FFFFFF' : '#24292E',
                      borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                    }
                  ]}
                  value={item.name}
                  onChangeText={(value) => updateItem(index, 'name', value)}
                  placeholder={t('receipts.itemNamePlaceholder')}
                  placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
                />

                <View style={styles.itemAmountContainer}>
                  <TextInput
                    style={[
                      styles.itemAmountInput,
                      {
                        backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
                        color: isDarkMode ? '#FFFFFF' : '#24292E',
                        borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
                      }
                    ]}
                    value={item.amount.toString()}
                    onChangeText={(value) => updateItem(index, 'amount', value)}
                    keyboardType="numeric"
                    placeholder="0.00"
                    placeholderTextColor={isDarkMode ? '#8B949E' : '#6E7781'}
                  />

                  <TouchableOpacity
                    style={styles.removeItemButton}
                    onPress={() => removeItem(index)}
                  >
                    <Ionicons
                      name="trash-outline"
                      size={20}
                      color="#F85149"
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {categories.length > 0 && (
                <Dropdown
                  label={t('receipts.category')}
                  options={categories.map(c => ({ value: c.id.toString(), label: c.name }))}
                  selectedValue={item.categoryId?.toString() || ''}
                  onValueChange={(value) => updateItem(index, 'categoryId', Number(value))}
                  placeholder={t('receipts.selectCategory')}
                />
              )}
            </View>
          ))
        )}
      </View>

      <TouchableOpacity
        style={styles.rawTextToggle}
        onPress={() => setShowRawText(!showRawText)}
      >
        <Ionicons
          name={showRawText ? 'chevron-up' : 'chevron-down'}
          size={16}
          color={isDarkMode ? '#8B949E' : '#6E7781'}
        />
        <Text style={[
          styles.rawTextToggleText,
          { color: isDarkMode ? '#8B949E' : '#6E7781' }
        ]}>
          {showRawText ? t('receipts.hideRawText') : t('receipts.showRawText')}
        </Text>
      </TouchableOpacity>

      {showRawText && (
        <View style={[
          styles.rawTextContainer,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            borderColor: isDarkMode ? '#30363D' : '#D0D7DE',
          }
        ]}>
          <Text style={[
            styles.rawText,
            { color: isDarkMode ? '#8B949E' : '#6E7781' }
          ]}>
            {receiptData.rawText || t('receipts.noRawText')}
          </Text>
        </View>
      )}

      <View style={styles.actions}>
        <Button
          title={t('common.cancel')}
          onPress={onCancel}
          type="secondary"
          style={styles.actionButton}
        />
        <Button
          title={t('common.save')}
          onPress={handleSave}
          type="primary"
          style={styles.actionButton}
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
  },
  input: {
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 14,
  },
  itemsContainer: {
    marginBottom: 16,
  },
  itemsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  itemsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  addItemButton: {
    padding: 4,
  },
  noItems: {
    fontStyle: 'italic',
    textAlign: 'center',
    marginVertical: 16,
  },
  itemRow: {
    marginBottom: 12,
    borderRadius: 8,
    padding: 8,
  },
  itemDetails: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  itemInput: {
    flex: 2,
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 14,
    marginRight: 8,
  },
  itemAmountContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemAmountInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 14,
  },
  removeItemButton: {
    padding: 8,
    marginLeft: 4,
  },
  rawTextToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  rawTextToggleText: {
    fontSize: 14,
    marginLeft: 4,
  },
  rawTextContainer: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  rawText: {
    fontSize: 12,
    fontFamily: 'monospace',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionButton: {
    minWidth: 100,
    marginLeft: 8,
  },
});

export default ReceiptOCRProcessor;
