import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { Loan } from '../../models/Loan';
import { formatCurrency, formatDate } from '../../utils/formatters';

interface LoanCardProps {
  loan: Loan;
  onPress: () => void;
  isDarkMode: boolean;
}

const LoanCard: React.FC<LoanCardProps> = ({ loan, onPress, isDarkMode }) => {
  const { t } = useTranslation();

  // Determine if the loan is given or taken
  const isLoanGiven = loan.lenderUserId !== null;
  const isLoanTaken = loan.borrowerUserId !== null;
  const isExternalEntity = loan.isExternalEntity;

  // Get status color
  const getStatusColor = () => {
    switch (loan.status.toLowerCase()) {
      case 'active':
        return isDarkMode ? '#238636' : '#2DA44E';
      case 'completed':
        return isDarkMode ? '#58A6FF' : '#0366D6';
      case 'defaulted':
        return isDarkMode ? '#F85149' : '#CF222E';
      default:
        return isDarkMode ? '#8B949E' : '#6E7781';
    }
  };

  // Get loan type icon
  const getLoanTypeIcon = () => {
    if (isLoanGiven) {
      return 'arrow-up-outline';
    } else if (isLoanTaken) {
      return 'arrow-down-outline';
    } else {
      return 'business-outline';
    }
  };

  // Format loan status
  const formatStatus = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return t('loans.statusOptions.active');
      case 'completed':
        return t('loans.statusOptions.completed');
      case 'defaulted':
        return t('loans.statusOptions.defaulted');
      default:
        return status;
    }
  };

  // Get loan type label
  const getLoanTypeLabel = () => {
    if (isLoanGiven) {
      return t('loans.loanTypes.given');
    } else if (isLoanTaken) {
      return t('loans.loanTypes.taken');
    } else {
      return t('loans.loanTypes.external');
    }
  };

  // Get counterparty name
  const getCounterpartyName = () => {
    if (isExternalEntity) {
      return loan.externalEntityName || '';
    } else if (isLoanGiven) {
      return loan.borrowerUsername || '';
    } else {
      return loan.lenderUsername || '';
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
      ]}
      onPress={onPress}
    >
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Ionicons
            name={getLoanTypeIcon()}
            size={20}
            color={isLoanGiven ? '#F85149' : '#2DA44E'}
            style={styles.icon}
          />
          <Text
            style={[
              styles.title,
              { color: isDarkMode ? '#FFFFFF' : '#000000' }
            ]}
            numberOfLines={1}
          >
            {loan.title}
          </Text>
        </View>
        <View
          style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor() }
          ]}
        >
          <Text style={styles.statusText}>
            {formatStatus(loan.status)}
          </Text>
        </View>
      </View>

      <View style={styles.infoContainer}>
        <View style={styles.infoRow}>
          <Text
            style={[
              styles.infoLabel,
              { color: isDarkMode ? '#8B949E' : '#6E7781' }
            ]}
          >
            {t('loans.amount')}:
          </Text>
          <Text
            style={[
              styles.infoValue,
              { color: isDarkMode ? '#FFFFFF' : '#000000' }
            ]}
          >
            {formatCurrency(loan.amount)}
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Text
            style={[
              styles.infoLabel,
              { color: isDarkMode ? '#8B949E' : '#6E7781' }
            ]}
          >
            {t('loans.interestRate')}:
          </Text>
          <Text
            style={[
              styles.infoValue,
              { color: isDarkMode ? '#FFFFFF' : '#000000' }
            ]}
          >
            {loan.interestRate}%
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Text
            style={[
              styles.infoLabel,
              { color: isDarkMode ? '#8B949E' : '#6E7781' }
            ]}
          >
            {isLoanGiven ? t('loans.borrower') : t('loans.lender')}:
          </Text>
          <Text
            style={[
              styles.infoValue,
              { color: isDarkMode ? '#FFFFFF' : '#000000' }
            ]}
            numberOfLines={1}
          >
            {getCounterpartyName()}
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Text
            style={[
              styles.infoLabel,
              { color: isDarkMode ? '#8B949E' : '#6E7781' }
            ]}
          >
            {t('loans.startDate')}:
          </Text>
          <Text
            style={[
              styles.infoValue,
              { color: isDarkMode ? '#FFFFFF' : '#000000' }
            ]}
          >
            {formatDate(loan.startDate)}
          </Text>
        </View>
      </View>

      <View style={styles.footer}>
        <View style={styles.progressContainer}>
          <View style={styles.progressBarBackground}>
            <View
              style={[
                styles.progressBar,
                {
                  width: `${Math.min(100, (loan.paidAmount / loan.totalPayableAmount) * 100)}%`,
                  backgroundColor: isDarkMode ? '#238636' : '#2DA44E'
                }
              ]}
            />
          </View>
          <Text
            style={[
              styles.progressText,
              { color: isDarkMode ? '#8B949E' : '#6E7781' }
            ]}
          >
            {Math.round((loan.paidAmount / loan.totalPayableAmount) * 100)}% {t('common.completed')}
          </Text>
        </View>
        <View style={styles.typeContainer}>
          <Text
            style={[
              styles.typeText,
              { color: isDarkMode ? '#8B949E' : '#6E7781' }
            ]}
          >
            {getLoanTypeLabel()}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  icon: {
    marginRight: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  infoContainer: {
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  infoLabel: {
    fontSize: 14,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  progressContainer: {
    flex: 1,
  },
  progressBarBackground: {
    height: 4,
    backgroundColor: '#E1E4E8',
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressBar: {
    height: '100%',
  },
  progressText: {
    fontSize: 12,
  },
  typeContainer: {
    marginLeft: 8,
  },
  typeText: {
    fontSize: 12,
    fontWeight: '500',
  },
});

export default LoanCard;
