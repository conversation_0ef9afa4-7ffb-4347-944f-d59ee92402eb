import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useLoans } from '../../contexts/LoanContext';
import { Loan } from '../../models/Loan';
import { formatCurrency } from '../../utils/formatters';

interface LoanSelectorProps {
  selectedLoanId: number | null;
  onSelectLoan: (loanId: number) => void;
  isDarkMode: boolean;
}

const LoanSelector: React.FC<LoanSelectorProps> = ({
  selectedLoanId,
  onSelectLoan,
  isDarkMode,
}) => {
  const { t } = useTranslation();
  const { loans, isLoading } = useLoans();

  // Mock fetchLoans function
  const fetchLoans = async () => {
    // In a real implementation, this would call the API
    console.log('Fetching loans...');
  };

  const [modalVisible, setModalVisible] = useState(false);
  const [selectedLoan, setSelectedLoan] = useState<Loan | null>(null);

  // Fetch loans on mount
  useEffect(() => {
    fetchLoans();
  }, []);

  // Update selected loan when selectedLoanId changes
  useEffect(() => {
    if (selectedLoanId) {
      const loan = loans.find((l: Loan) => l.id === selectedLoanId);
      if (loan) {
        setSelectedLoan(loan);
      }
    }
  }, [selectedLoanId, loans]);

  // Handle loan selection
  const handleSelectLoan = (loan: Loan) => {
    setSelectedLoan(loan);
    onSelectLoan(loan.id);
    setModalVisible(false);
  };

  // Render loan item
  const renderLoanItem = ({ item }: { item: Loan }) => (
    <TouchableOpacity
      style={[
        styles.loanItem,
        {
          backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
          borderColor: isDarkMode ? '#30363D' : '#E1E4E8',
        },
      ]}
      onPress={() => handleSelectLoan(item)}
    >
      <View style={styles.loanInfo}>
        <Text
          style={[
            styles.loanTitle,
            { color: isDarkMode ? '#FFFFFF' : '#000000' },
          ]}
        >
          {item.title}
        </Text>
        <Text
          style={[
            styles.loanAmount,
            { color: isDarkMode ? '#C9D1D9' : '#24292E' },
          ]}
        >
          {formatCurrency(item.amount)}
        </Text>
      </View>
      {selectedLoan?.id === item.id && (
        <Ionicons
          name="checkmark-circle"
          size={24}
          color={isDarkMode ? '#58A6FF' : '#0366D6'}
        />
      )}
    </TouchableOpacity>
  );

  return (
    <View>
      {/* Selected loan display */}
      <TouchableOpacity
        style={[
          styles.selector,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            borderColor: isDarkMode ? '#30363D' : '#E1E4E8',
          },
        ]}
        onPress={() => setModalVisible(true)}
      >
        {selectedLoan ? (
          <View style={styles.selectedLoan}>
            <Text
              style={[
                styles.selectedLoanTitle,
                { color: isDarkMode ? '#FFFFFF' : '#000000' },
              ]}
            >
              {selectedLoan.title}
            </Text>
            <Text
              style={[
                styles.selectedLoanAmount,
                { color: isDarkMode ? '#C9D1D9' : '#24292E' },
              ]}
            >
              {formatCurrency(selectedLoan.amount)}
            </Text>
          </View>
        ) : (
          <Text
            style={[
              styles.placeholder,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t('reminders.selectLoan')}
          </Text>
        )}
        <Ionicons
          name="chevron-down"
          size={20}
          color={isDarkMode ? '#8B949E' : '#6E7781'}
        />
      </TouchableOpacity>

      {/* Loan selection modal */}
      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF',
                borderColor: isDarkMode ? '#30363D' : '#E1E4E8',
              },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text
                style={[
                  styles.modalTitle,
                  { color: isDarkMode ? '#FFFFFF' : '#000000' },
                ]}
              >
                {t('reminders.selectLoan')}
              </Text>
              <TouchableOpacity
                onPress={() => setModalVisible(false)}
              >
                <Ionicons
                  name="close"
                  size={24}
                  color={isDarkMode ? '#C9D1D9' : '#24292E'}
                />
              </TouchableOpacity>
            </View>

            {isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator
                  size="large"
                  color={isDarkMode ? '#58A6FF' : '#0366D6'}
                />
              </View>
            ) : (
              <FlatList
                data={loans}
                keyExtractor={(item) => item.id.toString()}
                renderItem={renderLoanItem}
                contentContainerStyle={styles.loanList}
              />
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  selector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
  },
  selectedLoan: {
    flex: 1,
  },
  selectedLoanTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  selectedLoanAmount: {
    fontSize: 14,
    marginTop: 4,
  },
  placeholder: {
    fontSize: 16,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderWidth: 1,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
  },
  loanList: {
    padding: 16,
  },
  loanItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  loanInfo: {
    flex: 1,
  },
  loanTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  loanAmount: {
    fontSize: 14,
    marginTop: 4,
  },
});

export default LoanSelector;
