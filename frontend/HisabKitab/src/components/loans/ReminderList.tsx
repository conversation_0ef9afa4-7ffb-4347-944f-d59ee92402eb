import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { LoanReminder } from '../../models/Loan';
import { formatDate } from '../../utils/formatters';
import EmptyState from '../EmptyState';

interface ReminderListProps {
  reminders: LoanReminder[];
  onReminderPress: (reminder: LoanReminder) => void;
  onAddReminder: () => void;
  isDarkMode: boolean;
}

const ReminderList: React.FC<ReminderListProps> = ({
  reminders,
  onReminderPress,
  onAddReminder,
  isDarkMode,
}) => {
  const { t } = useTranslation();

  // Render reminder item
  const renderReminderItem = ({ item }: { item: LoanReminder }) => {
    return (
      <TouchableOpacity
        style={[
          styles.reminderItem,
          { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
        ]}
        onPress={() => onReminderPress(item)}
      >
        <View style={styles.reminderHeader}>
          <Text
            style={[
              styles.reminderDate,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {formatDate(item.reminderDate)}
          </Text>
          <View
            style={[
              styles.statusBadge,
              {
                backgroundColor: item.isActive
                  ? item.isSent
                    ? isDarkMode
                      ? '#58A6FF'
                      : '#0366D6'
                    : isDarkMode
                    ? '#238636'
                    : '#2DA44E'
                  : isDarkMode
                  ? '#8B949E'
                  : '#6E7781',
              },
            ]}
          >
            <Text style={styles.statusText}>
              {item.isActive
                ? item.isSent
                  ? t('loans.isSent')
                  : t('loans.isActive')
                : t('common.inactive')}
            </Text>
          </View>
        </View>

        <Text
          style={[
            styles.reminderMessage,
            { color: isDarkMode ? '#FFFFFF' : '#000000' },
          ]}
        >
          {item.message}
        </Text>

        {item.isSent && item.sentAt && (
          <Text
            style={[
              styles.sentAtText,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t('loans.sentAt')}: {formatDate(item.sentAt)}
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  // Render empty state
  const renderEmptyState = () => {
    return (
      <EmptyState
        icon="notifications-outline"
        title={t('loans.noReminders')}
        message={t('loans.noRemindersMessage')}
        buttonTitle={t('loans.addReminder')}
        onButtonPress={onAddReminder}
        isDarkMode={isDarkMode}
      />
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text
          style={[
            styles.headerTitle,
            { color: isDarkMode ? '#FFFFFF' : '#000000' },
          ]}
        >
          {t('loans.reminders')}
        </Text>
        <TouchableOpacity
          style={[
            styles.addButton,
            { backgroundColor: isDarkMode ? '#238636' : '#2DA44E' },
          ]}
          onPress={onAddReminder}
        >
          <Ionicons name="add" size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <FlatList
        data={reminders}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderReminderItem}
        ListEmptyComponent={renderEmptyState}
        contentContainerStyle={
          reminders.length === 0 ? styles.emptyListContainer : styles.listContainer
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    paddingBottom: 16,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  reminderItem: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  reminderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  reminderDate: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  reminderMessage: {
    fontSize: 14,
    marginBottom: 8,
  },
  sentAtText: {
    fontSize: 12,
  },
});

export default ReminderList;
