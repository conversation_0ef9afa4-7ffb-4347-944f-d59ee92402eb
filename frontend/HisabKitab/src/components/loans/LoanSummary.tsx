import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Loan } from '../../models/Loan';
import { formatCurrency, formatDate } from '../../utils/formatters';

interface LoanSummaryProps {
  loan: Loan;
  isDarkMode: boolean;
}

const LoanSummary: React.FC<LoanSummaryProps> = ({ loan, isDarkMode }) => {
  const { t } = useTranslation();

  // Format interest type
  const formatInterestType = (type: number) => {
    switch (type) {
      case 0:
        return t('loans.interestTypes.flat');
      case 1:
        return t('loans.interestTypes.reducingBalance');
      case 2:
        return t('loans.interestTypes.compound');
      default:
        return '';
    }
  };

  // Format payment frequency
  const formatPaymentFrequency = (frequency: number) => {
    switch (frequency) {
      case 0:
        return t('loans.frequencyTypes.daily');
      case 1:
        return t('loans.frequencyTypes.weekly');
      case 2:
        return t('loans.frequencyTypes.monthly');
      case 3:
        return t('loans.frequencyTypes.quarterly');
      case 4:
        return t('loans.frequencyTypes.yearly');
      default:
        return '';
    }
  };

  return (
    <View style={styles.container}>
      {/* Basic Information */}
      <View style={[styles.section, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
        <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.loanDetailsForm')}
        </Text>
        
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {t('loans.amount')}:
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {formatCurrency(loan.amount)}
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {t('loans.interestRate')}:
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {loan.interestRate}%
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {t('loans.interestType')}:
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {formatInterestType(loan.interestType)}
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {t('loans.paymentFrequency')}:
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {formatPaymentFrequency(loan.paymentFrequency)}
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {t('loans.startDate')}:
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {formatDate(loan.startDate)}
          </Text>
        </View>
        
        {loan.endDate && (
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('loans.endDate')}:
            </Text>
            <Text style={[styles.infoValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {formatDate(loan.endDate)}
            </Text>
          </View>
        )}
        
        {loan.lenderUserId && (
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('loans.lender')}:
            </Text>
            <Text style={[styles.infoValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {loan.lenderUsername || ''}
            </Text>
          </View>
        )}
        
        {loan.borrowerUserId && (
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('loans.borrower')}:
            </Text>
            <Text style={[styles.infoValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {loan.borrowerUsername || ''}
            </Text>
          </View>
        )}
        
        {loan.isExternalEntity && (
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('loans.externalEntityName')}:
            </Text>
            <Text style={[styles.infoValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {loan.externalEntityName || ''}
            </Text>
          </View>
        )}
        
        {loan.notes && (
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('loans.notes')}:
            </Text>
            <Text style={[styles.infoValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {loan.notes}
            </Text>
          </View>
        )}
      </View>

      {/* Payment Information */}
      <View style={[styles.section, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
        <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.paymentDetails')}
        </Text>
        
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {t('loans.totalPayable')}:
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {formatCurrency(loan.totalPayableAmount)}
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {t('loans.paidAmount')}:
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {formatCurrency(loan.paidAmount)}
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {t('loans.remainingAmount')}:
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {formatCurrency(loan.remainingAmount)}
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
            {t('loans.monthlyEMI')}:
          </Text>
          <Text style={[styles.infoValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
            {formatCurrency(loan.monthlyEMI)}
          </Text>
        </View>
        
        {loan.predictedCompletionDate && (
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('loans.predictedCompletionDate')}:
            </Text>
            <Text style={[styles.infoValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {formatDate(loan.predictedCompletionDate)}
            </Text>
          </View>
        )}
      </View>

      {/* Progress Bar */}
      <View style={[styles.section, { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }]}>
        <Text style={[styles.sectionTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
          {t('loans.paymentBreakdown')}
        </Text>
        
        <View style={styles.progressContainer}>
          <View style={styles.progressBarBackground}>
            <View
              style={[
                styles.progressBar,
                {
                  width: `${Math.min(100, (loan.paidAmount / loan.totalPayableAmount) * 100)}%`,
                  backgroundColor: isDarkMode ? '#238636' : '#2DA44E'
                }
              ]}
            />
          </View>
          <Text
            style={[
              styles.progressText,
              { color: isDarkMode ? '#8B949E' : '#6E7781' }
            ]}
          >
            {Math.round((loan.paidAmount / loan.totalPayableAmount) * 100)}% {t('common.completed')}
          </Text>
        </View>
        
        <View style={styles.breakdownContainer}>
          <View style={styles.breakdownItem}>
            <Text style={[styles.breakdownLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('loans.principalBreakdown')}
            </Text>
            <Text style={[styles.breakdownValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {formatCurrency(loan.amount)}
            </Text>
          </View>
          <View style={styles.breakdownItem}>
            <Text style={[styles.breakdownLabel, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('loans.interestBreakdown')}
            </Text>
            <Text style={[styles.breakdownValue, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {formatCurrency(loan.totalPayableAmount - loan.amount)}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  section: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: '#E1E4E8',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressBar: {
    height: '100%',
  },
  progressText: {
    fontSize: 12,
    textAlign: 'right',
  },
  breakdownContainer: {
    flexDirection: 'row',
  },
  breakdownItem: {
    flex: 1,
  },
  breakdownLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  breakdownValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default LoanSummary;
