import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { LoanPayment } from '../../models/Loan';
import { formatCurrency, formatDate } from '../../utils/formatters';
import EmptyState from '../EmptyState';

interface PaymentListProps {
  payments: LoanPayment[];
  onPaymentPress: (payment: LoanPayment) => void;
  onAddPayment: () => void;
  isDarkMode: boolean;
}

const PaymentList: React.FC<PaymentListProps> = ({
  payments,
  onPaymentPress,
  onAddPayment,
  isDarkMode,
}) => {
  const { t } = useTranslation();

  // Render payment item
  const renderPaymentItem = ({ item }: { item: LoanPayment }) => {
    return (
      <TouchableOpacity
        style={[
          styles.paymentItem,
          { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' },
        ]}
        onPress={() => onPaymentPress(item)}
      >
        <View style={styles.paymentHeader}>
          <Text
            style={[
              styles.paymentDate,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {formatDate(item.paymentDate)}
          </Text>
          <Text
            style={[
              styles.paymentAmount,
              { color: isDarkMode ? '#FFFFFF' : '#000000' },
            ]}
          >
            {formatCurrency(item.amount)}
          </Text>
        </View>

        <View style={styles.paymentDetails}>
          <View style={styles.paymentDetail}>
            <Text
              style={[
                styles.paymentDetailLabel,
                { color: isDarkMode ? '#8B949E' : '#6E7781' },
              ]}
            >
              {t('loans.principalAmount')}
            </Text>
            <Text
              style={[
                styles.paymentDetailValue,
                { color: isDarkMode ? '#FFFFFF' : '#000000' },
              ]}
            >
              {formatCurrency(item.principalAmount)}
            </Text>
          </View>

          <View style={styles.paymentDetail}>
            <Text
              style={[
                styles.paymentDetailLabel,
                { color: isDarkMode ? '#8B949E' : '#6E7781' },
              ]}
            >
              {t('loans.interestAmount')}
            </Text>
            <Text
              style={[
                styles.paymentDetailValue,
                { color: isDarkMode ? '#FFFFFF' : '#000000' },
              ]}
            >
              {formatCurrency(item.interestAmount)}
            </Text>
          </View>
        </View>

        {item.paymentMethod && (
          <View style={styles.paymentMethod}>
            <Text
              style={[
                styles.paymentMethodText,
                { color: isDarkMode ? '#8B949E' : '#6E7781' },
              ]}
            >
              {item.paymentMethod}
            </Text>
          </View>
        )}

        {item.isScheduled && (
          <View
            style={[
              styles.scheduledBadge,
              { backgroundColor: isDarkMode ? '#30363D' : '#E1E4E8' },
            ]}
          >
            <Text
              style={[
                styles.scheduledText,
                { color: isDarkMode ? '#FFFFFF' : '#000000' },
              ]}
            >
              {t('common.scheduled')}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  // Render empty state
  const renderEmptyState = () => {
    return (
      <EmptyState
        icon="cash-outline"
        title={t('loans.noPayments')}
        message={t('loans.noPaymentsMessage')}
        buttonTitle={t('loans.addPayment')}
        onButtonPress={onAddPayment}
        isDarkMode={isDarkMode}
      />
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text
          style={[
            styles.headerTitle,
            { color: isDarkMode ? '#FFFFFF' : '#000000' },
          ]}
        >
          {t('loans.payments')}
        </Text>
        <TouchableOpacity
          style={[
            styles.addButton,
            { backgroundColor: isDarkMode ? '#238636' : '#2DA44E' },
          ]}
          onPress={onAddPayment}
        >
          <Ionicons name="add" size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <FlatList
        data={payments}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderPaymentItem}
        ListEmptyComponent={renderEmptyState}
        contentContainerStyle={
          payments.length === 0 ? styles.emptyListContainer : styles.listContainer
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    paddingBottom: 16,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  paymentItem: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  paymentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  paymentDate: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  paymentAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  paymentDetails: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  paymentDetail: {
    flex: 1,
  },
  paymentDetailLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  paymentDetailValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  paymentMethod: {
    marginTop: 4,
  },
  paymentMethodText: {
    fontSize: 12,
  },
  scheduledBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  scheduledText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
});

export default PaymentList;
