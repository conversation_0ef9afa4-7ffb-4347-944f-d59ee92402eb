import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

interface Family {
  id: number;
  name: string;
}

interface FamilySelectorProps {
  families: Family[];
  selectedFamilyId: number | undefined;
  onFamilyChange: (familyId: number) => void;
}

const FamilySelector: React.FC<FamilySelectorProps> = ({
  families,
  selectedFamilyId,
  onFamilyChange,
}) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();

  return (
    <View style={styles.container}>
      <Text style={[styles.label, { color: isDarkMode ? '#D1D5DB' : '#4B5563' }]}>
        {t('family.selectFamily')}
      </Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.familiesContainer}
      >
        {families.map((family) => (
          <TouchableOpacity
            key={family.id}
            style={[
              styles.familyButton,
              selectedFamilyId === family.id && styles.selectedFamily,
              {
                backgroundColor: isDarkMode
                  ? selectedFamilyId === family.id
                    ? '#0366D6'
                    : '#1F2937'
                  : selectedFamilyId === family.id
                  ? '#0366D6'
                  : '#F3F4F6',
              },
            ]}
            onPress={() => onFamilyChange(family.id)}
          >
            <Ionicons
              name="people"
              size={16}
              color={
                selectedFamilyId === family.id
                  ? '#FFFFFF'
                  : isDarkMode
                  ? '#D1D5DB'
                  : '#4B5563'
              }
              style={styles.familyIcon}
            />
            <Text
              style={[
                styles.familyText,
                {
                  color: selectedFamilyId === family.id
                    ? '#FFFFFF'
                    : isDarkMode
                    ? '#D1D5DB'
                    : '#4B5563',
                },
              ]}
            >
              {family.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
  },
  familiesContainer: {
    paddingVertical: 4,
  },
  familyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginRight: 8,
  },
  selectedFamily: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  familyIcon: {
    marginRight: 6,
  },
  familyText: {
    fontSize: 14,
  },
});

export default FamilySelector;
