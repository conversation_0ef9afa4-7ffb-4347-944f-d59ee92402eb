import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, Text, Switch } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import FormInput from '../ui/FormInput';
import Button from '../ui/Button';
import Dropdown from '../ui/Dropdown';
import Card from '../ui/Card';
import { Ionicons } from '@expo/vector-icons';
import { validateRequired, validatePositiveNumber } from '../../utils/validation';

interface BudgetLimitFormProps {
  initialValues?: {
    id?: number;
    amount: number;
    period: string;
    currency: string;
    categoryId: number;
    startDate?: Date;
    endDate?: Date;
    notificationThreshold?: number;
    rolloverUnused?: boolean;
  };
  onSubmit: (values: any) => void;
  isLoading?: boolean;
  mode?: 'create' | 'edit';
  categoryName?: string;
}

const BudgetLimitForm: React.FC<BudgetLimitFormProps> = ({
  initialValues = {
    amount: 0,
    period: 'Monthly',
    currency: 'NPR',
    categoryId: 0,
    startDate: new Date(),
    endDate: undefined,
    notificationThreshold: 80,
    rolloverUnused: false,
  },
  onSubmit,
  isLoading = false,
  mode = 'create',
  categoryName = '',
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();

  // Form state
  const [amount, setAmount] = useState(initialValues.amount.toString());
  const [period, setPeriod] = useState(initialValues.period);
  const [currency, setCurrency] = useState(initialValues.currency);
  const [startDate, setStartDate] = useState(initialValues.startDate || new Date());
  const [endDate, setEndDate] = useState(initialValues.endDate);
  const [notificationThreshold, setNotificationThreshold] = useState(
    initialValues.notificationThreshold?.toString() || '80'
  );
  const [rolloverUnused, setRolloverUnused] = useState(initialValues.rolloverUnused || false);

  // Form validation
  const [touched, setTouched] = useState({
    amount: false,
    notificationThreshold: false,
  });

  const [errors, setErrors] = useState({
    amount: '',
    notificationThreshold: '',
  });

  // Required field indicator
  const requiredField = (label: string) => (
    <View style={styles.labelContainer}>
      <Text style={[
        styles.label,
        { color: isDarkMode ? '#FFFFFF' : '#24292E' }
      ]}>
        {label}
      </Text>
      <Text style={styles.requiredIndicator}>*</Text>
    </View>
  );

  // Period options
  const periodOptions = [
    { label: t('categories.daily'), value: 'Daily' },
    { label: t('categories.weekly'), value: 'Weekly' },
    { label: t('categories.monthly'), value: 'Monthly' },
    { label: t('categories.yearly'), value: 'Yearly' },
  ];

  // Currency options
  const currencyOptions = [
    { label: 'NPR (₨)', value: 'NPR' },
    { label: 'USD ($)', value: 'USD' },
    { label: 'EUR (€)', value: 'EUR' },
    { label: 'INR (₹)', value: 'INR' },
    { label: 'GBP (£)', value: 'GBP' },
  ];

  // Validate form
  const validateForm = () => {
    const newErrors = {
      amount: '',
      notificationThreshold: '',
    };

    // Validate amount
    if (amount.trim() === '') {
      newErrors.amount = validateRequired(amount, t);
    } else if (isNaN(Number(amount))) {
      newErrors.amount = t('validation.number');
    } else if (Number(amount) <= 0) {
      newErrors.amount = validatePositiveNumber(amount, t);
    }

    // Validate notification threshold
    if (notificationThreshold.trim() === '') {
      newErrors.notificationThreshold = validateRequired(notificationThreshold, t);
    } else if (isNaN(Number(notificationThreshold))) {
      newErrors.notificationThreshold = t('validation.number');
    } else if (Number(notificationThreshold) < 0 || Number(notificationThreshold) > 100) {
      newErrors.notificationThreshold = t('validation.percentageRange');
    }

    setErrors(newErrors);

    return !Object.values(newErrors).some(error => error);
  };

  // Handle form submission
  const handleSubmit = () => {
    // Mark all fields as touched
    setTouched({
      amount: true,
      notificationThreshold: true,
    });

    if (validateForm()) {
      const formData = {
        ...(initialValues.id && { id: initialValues.id }),
        amount: Number(amount),
        period,
        currency,
        categoryId: initialValues.categoryId,
        startDate,
        endDate,
        notificationThreshold: Number(notificationThreshold),
        rolloverUnused,
      };

      onSubmit(formData);
    } else {
      Alert.alert(
        t('common.error'),
        t('validation.fixErrors')
      );
    }
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
    >
      {/* Help card */}
      <Card style={styles.helpCard}>
        <View style={styles.helpHeader}>
          <Ionicons
            name="information-circle-outline"
            size={24}
            color={isDarkMode ? '#58A6FF' : '#0366D6'}
          />
          <Text
            style={[
              styles.helpTitle,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
          >
            {t('categories.budgetLimitHelp')}
          </Text>
        </View>
        <Text
          style={[
            styles.helpText,
            { color: isDarkMode ? '#8B949E' : '#6E7781' },
          ]}
        >
          {t('categories.budgetLimitHelpText')}
        </Text>
      </Card>

      {categoryName && (
        <FormInput
          label={t('categories.category')}
          value={categoryName}
          editable={false}
        />
      )}

      <FormInput
        label={requiredField(t('categories.budgetAmount'))}
        value={amount}
        onChangeText={setAmount}
        onBlur={() => setTouched({ ...touched, amount: true })}
        error={errors.amount}
        touched={touched.amount}
        keyboardType="numeric"
        placeholder="0.00"
      />

      <Dropdown
        label={t('categories.budgetPeriod')}
        options={periodOptions}
        selectedValue={period}
        onValueChange={(value) => setPeriod(value as string)}
      />

      <Dropdown
        label={t('categories.currency')}
        options={currencyOptions}
        selectedValue={currency}
        onValueChange={(value) => setCurrency(value as string)}
      />

      <FormInput
        label={requiredField(t('categories.notificationThreshold'))}
        value={notificationThreshold}
        onChangeText={setNotificationThreshold}
        onBlur={() => setTouched({ ...touched, notificationThreshold: true })}
        error={errors.notificationThreshold}
        touched={touched.notificationThreshold}
        keyboardType="numeric"
        placeholder="80"
        rightComponent={
          <Text style={{ color: isDarkMode ? '#8B949E' : '#6E7781' }}>%</Text>
        }
      />

      <View style={styles.switchContainer}>
        <Text
          style={[
            styles.switchLabel,
            { color: isDarkMode ? '#FFFFFF' : '#24292E' },
          ]}
        >
          {t('categories.rolloverUnused')}
        </Text>
        <Switch
          value={rolloverUnused}
          onValueChange={setRolloverUnused}
          trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
          thumbColor={rolloverUnused ? '#FFFFFF' : '#f4f3f4'}
        />
      </View>

      <Text
        style={[
          styles.helperText,
          { color: isDarkMode ? '#8B949E' : '#6E7781' },
        ]}
      >
        {t('categories.rolloverUnusedHelp')}
      </Text>

      <View style={styles.buttonContainer}>
        <Button
          title={
            mode === 'create'
              ? t('categories.addBudgetLimit')
              : t('categories.updateBudgetLimit')
          }
          onPress={handleSubmit}
          isLoading={isLoading}
          fullWidth
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  helpCard: {
    marginBottom: 16,
  },
  helpHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  helpTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  helpText: {
    fontSize: 14,
    lineHeight: 20,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
  },
  requiredIndicator: {
    color: '#F85149',
    marginLeft: 4,
    fontSize: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 16,
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  helperText: {
    fontSize: 14,
    marginBottom: 16,
    fontStyle: 'italic',
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 40,
  },
});

export default BudgetLimitForm;
