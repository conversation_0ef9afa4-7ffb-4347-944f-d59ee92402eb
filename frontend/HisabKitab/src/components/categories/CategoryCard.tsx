import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { Ionicons } from '@expo/vector-icons';
import Card from '../ui/Card';

interface CategoryCardProps {
  id: number;
  name: string;
  type: string;
  icon: string;
  color: string;
  isSystem?: boolean;
  parentCategoryName?: string;
  onPress: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
}

const CategoryCard: React.FC<CategoryCardProps> = ({
  id,
  name,
  type,
  icon,
  color,
  isSystem = false,
  parentCategoryName,
  onPress,
  onEdit,
  onDelete,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();

  return (
    <Card
      onPress={onPress}
      style={styles.card}
    >
      <View style={styles.header}>
        <View
          style={[
            styles.iconContainer,
            { backgroundColor: color || '#808080' },
          ]}
        >
          <Ionicons
            name={(icon as any) || 'list-outline'}
            size={24}
            color="#FFFFFF"
          />
        </View>

        <View style={styles.titleContainer}>
          <Text
            style={[
              styles.name,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
            numberOfLines={1}
          >
            {name}
          </Text>

          <View style={styles.tagsContainer}>
            <Text
              style={[
                styles.typeText,
                { color: isDarkMode ? '#8B949E' : '#6E7781' },
              ]}
            >
              {t(`categories.${typeof type === 'number'
                ? (type === 0 ? 'income' : 'expense')
                : (type ? String(type).toLowerCase() : '')}`)}
            </Text>

            {isSystem && (
              <View
                style={[
                  styles.systemTag,
                  {
                    backgroundColor: isDarkMode ? '#30363D' : '#F6F8FA',
                    marginLeft: 8,
                  },
                ]}
              >
                <Text
                  style={[
                    styles.systemTagText,
                    { color: isDarkMode ? '#8B949E' : '#6E7781' },
                  ]}
                >
                  {t('categories.system')}
                </Text>
              </View>
            )}
          </View>
        </View>

        <View
          style={[
            styles.typeTag,
            {
              backgroundColor:
                (typeof type === 'number' ? type === 0 : type === 'Income')
                  ? isDarkMode
                    ? '#238636'
                    : '#2EA043'
                  : isDarkMode
                  ? '#F85149'
                  : '#DA3633',
            },
          ]}
        >
          <Text style={styles.typeTagText}>
            {t(`categories.${typeof type === 'number'
                ? (type === 0 ? 'income' : 'expense')
                : (type ? String(type).toLowerCase() : '')}`)}
          </Text>
        </View>
      </View>

      {parentCategoryName && (
        <View style={styles.parentContainer}>
          <Text
            style={[
              styles.parentLabel,
              { color: isDarkMode ? '#8B949E' : '#6E7781' },
            ]}
          >
            {t('categories.parentCategory')}
          </Text>
          <Text
            style={[
              styles.parentName,
              { color: isDarkMode ? '#FFFFFF' : '#24292E' },
            ]}
          >
            {parentCategoryName}
          </Text>
        </View>
      )}

      {(onEdit || onDelete) && !isSystem && (
        <View style={styles.actions}>
          {onEdit && (
            <TouchableOpacity
              style={[
                styles.actionButton,
                { backgroundColor: isDarkMode ? '#30363D' : '#F6F8FA' },
              ]}
              onPress={onEdit}
            >
              <Ionicons
                name="pencil-outline"
                size={16}
                color={isDarkMode ? '#58A6FF' : '#0366D6'}
              />
              <Text
                style={[
                  styles.actionText,
                  { color: isDarkMode ? '#58A6FF' : '#0366D6' },
                ]}
              >
                {t('common.edit')}
              </Text>
            </TouchableOpacity>
          )}

          {onDelete && (
            <TouchableOpacity
              style={[
                styles.actionButton,
                { backgroundColor: isDarkMode ? '#30363D' : '#F6F8FA' },
              ]}
              onPress={onDelete}
            >
              <Ionicons
                name="trash-outline"
                size={16}
                color={isDarkMode ? '#F85149' : '#DA3633'}
              />
              <Text
                style={[
                  styles.actionText,
                  { color: isDarkMode ? '#F85149' : '#DA3633' },
                ]}
              >
                {t('common.delete')}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 12,
    padding: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  titleContainer: {
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  tagsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeText: {
    fontSize: 14,
  },
  typeTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginLeft: 8,
  },
  typeTagText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  systemTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  systemTagText: {
    fontSize: 12,
    fontWeight: '600',
  },
  parentContainer: {
    padding: 16,
    paddingTop: 0,
  },
  parentLabel: {
    fontSize: 14,
    marginRight: 4,
    color: '#8B949E',
  },
  parentName: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 4,
  },
  actions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#30363D',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  actionText: {
    marginLeft: 8,
    fontWeight: '600',
  },
});

export default CategoryCard;
