import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, Text } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import FormInput from '../ui/FormInput';
import Button from '../ui/Button';
import Dropdown from '../ui/Dropdown';
import ColorPicker from '../ui/ColorPicker';
import IconPicker from '../ui/IconPicker';
import { useAuth } from '../../contexts/AuthContext';
import {
  validateRequired,
  validateMinLength,
  validateMaxLength
} from '../../utils/validation';
import { getCategoryTypeString, getCategoryTypeEnum } from '../../utils/typeConversion';

interface CategoryFormProps {
  initialValues?: {
    id?: number;
    name: string;
    type: string;
    icon: string;
    color: string;
    parentCategoryId?: number | null;
    familyId?: number | null;
  };
  onSubmit: (values: any) => void;
  isLoading?: boolean;
  mode?: 'create' | 'edit';
  families?: Array<{ id: number; name: string }>;
  parentCategories?: Array<{ id: number; name: string; type: string }>;
}

const CategoryForm: React.FC<CategoryFormProps> = ({
  initialValues = {
    name: '',
    type: 'Expense',
    icon: 'list-outline',
    color: '#4CAF50',
    parentCategoryId: null,
    familyId: null,
  },
  onSubmit,
  isLoading = false,
  mode = 'create',
  families = [],
  parentCategories = [],
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const { isFamilyAdmin } = useAuth();

  // Form state
  const [name, setName] = useState(initialValues.name);
  const [type, setType] = useState(initialValues.type);
  const [icon, setIcon] = useState(initialValues.icon);
  const [color, setColor] = useState(initialValues.color);
  const [parentCategoryId, setParentCategoryId] = useState<number | null>(
    initialValues.parentCategoryId ?? null
  );
  const [familyId, setFamilyId] = useState<number | null>(
    initialValues.familyId ?? null
  );

  // Form validation
  const [touched, setTouched] = useState({
    name: false,
  });

  const [errors, setErrors] = useState({
    name: '',
  });

  // Required field indicator
  const requiredField = (label: string) => (
    <View style={styles.labelContainer}>
      <Text style={[
        styles.label,
        { color: isDarkMode ? '#FFFFFF' : '#24292E' }
      ]}>
        {label}
      </Text>
      <Text style={styles.requiredIndicator}>*</Text>
    </View>
  );

  // Category type options
  const typeOptions = [
    { label: t('categories.expense'), value: 'Expense' },
    { label: t('categories.income'), value: 'Income' },
  ];

  // Filter parent categories based on selected type
  const filteredParentCategories = parentCategories.filter(
    (category) => getCategoryTypeString(category.type) === type
  );

  // Parent category options
  const parentCategoryOptions = [
    { label: t('categories.noParent'), value: 'null' },
    ...filteredParentCategories.map((category) => ({
      label: category.name,
      value: category.id,
    })),
  ];

  // Family options
  const familyOptions = [
    { label: t('categories.personal'), value: 'null' },
    ...families.map((family) => ({
      label: family.name,
      value: family.id,
    })),
  ];

  // Validate form
  const validateForm = () => {
    const newErrors = {
      name: '',
    };

    // Validate name
    if (name.trim() === '') {
      newErrors.name = validateRequired(name, t);
    } else if (name.length < 2) {
      newErrors.name = validateMinLength(name, 2, t);
    } else if (name.length > 50) {
      newErrors.name = validateMaxLength(name, 50, t);
    }

    setErrors(newErrors);

    return !Object.values(newErrors).some((error) => error);
  };

  // Handle form submission
  const handleSubmit = () => {
    // Mark all fields as touched
    setTouched({
      name: true,
    });

    if (validateForm()) {
      const formData = {
        ...(initialValues.id && { id: initialValues.id }),
        name,
        // Convert string type to enum value for backend
        type: getCategoryTypeEnum(type),
        icon,
        color,
        parentCategoryId,
        familyId,
      };

      onSubmit(formData);
    } else {
      Alert.alert(t('common.error'), t('validation.fixErrors'));
    }
  };

  // Handle type change
  const handleTypeChange = (value: string | number | boolean | null) => {
    setType(value as string);
    setParentCategoryId(null); // Reset parent category when type changes
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
    >
      <FormInput
        label={requiredField(t('categories.categoryName'))}
        value={name}
        onChangeText={setName}
        onBlur={() => setTouched({ ...touched, name: true })}
        error={errors.name}
        touched={touched.name}
        placeholder={t('categories.categoryNamePlaceholder')}
        maxLength={50}
      />

      <Dropdown
        label={t('categories.categoryType')}
        options={typeOptions}
        selectedValue={type}
        onValueChange={handleTypeChange}
      />

      <IconPicker
        label={t('categories.icon')}
        selectedIcon={icon}
        onIconSelected={setIcon}
      />

      <ColorPicker
        label={t('categories.color')}
        selectedColor={color}
        onColorSelected={setColor}
      />

      {filteredParentCategories.length > 0 && (
        <Dropdown
          label={t('categories.parentCategory')}
          options={parentCategoryOptions}
          selectedValue={parentCategoryId !== null ? parentCategoryId : 'null'}
          onValueChange={(value) => setParentCategoryId(value === 'null' ? null : Number(value))}
        />
      )}

      {isFamilyAdmin && families.length > 0 && (
        <Dropdown
          label={t('categories.categoryOwner')}
          options={familyOptions}
          selectedValue={familyId !== null ? familyId : 'null'}
          onValueChange={(value) => setFamilyId(value === 'null' ? null : Number(value))}
        />
      )}

      <View style={styles.buttonContainer}>
        <Button
          title={
            mode === 'create'
              ? t('categories.createCategory')
              : t('categories.updateCategory')
          }
          onPress={handleSubmit}
          isLoading={isLoading}
          fullWidth
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 40,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
  },
  requiredIndicator: {
    color: '#F85149',
    marginLeft: 4,
    fontSize: 16,
  },
});

export default CategoryForm;
