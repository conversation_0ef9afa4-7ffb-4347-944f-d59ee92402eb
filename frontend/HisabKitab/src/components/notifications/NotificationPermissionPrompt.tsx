import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Image,
  Platform,
  Linking,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';

interface NotificationPermissionPromptProps {
  visible: boolean;
  onClose: () => void;
  onPermissionGranted: () => void;
}

const NotificationPermissionPrompt: React.FC<NotificationPermissionPromptProps> = ({
  visible,
  onClose,
  onPermissionGranted,
}) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  
  const [permissionStatus, setPermissionStatus] = useState<string | null>(null);
  
  // Check permission status on mount and when visible changes
  useEffect(() => {
    if (visible) {
      checkPermissionStatus();
    }
  }, [visible]);
  
  // Check notification permission status
  const checkPermissionStatus = async () => {
    if (!Device.isDevice) {
      setPermissionStatus('unavailable');
      return;
    }
    
    const { status } = await Notifications.getPermissionsAsync();
    setPermissionStatus(status);
  };
  
  // Request notification permissions
  const requestPermissions = async () => {
    if (!Device.isDevice) {
      Alert.alert(
        t('notifications.error'),
        t('notifications.deviceRequired')
      );
      return;
    }
    
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      setPermissionStatus(status);
      
      if (status === 'granted') {
        // Configure Android notification channel
        if (Platform.OS === 'android') {
          await Notifications.setNotificationChannelAsync('default', {
            name: 'default',
            importance: Notifications.AndroidImportance.MAX,
            vibrationPattern: [0, 250, 250, 250],
            lightColor: '#FF231F7C',
          });
        }
        
        onPermissionGranted();
        onClose();
      }
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      Alert.alert(
        t('notifications.error'),
        t('notifications.permissionError')
      );
    }
  };
  
  // Open app settings
  const openAppSettings = () => {
    if (Platform.OS === 'ios') {
      Linking.openURL('app-settings:');
    } else {
      Linking.openSettings();
    }
  };
  
  // Render permission denied view
  const renderPermissionDenied = () => (
    <View style={styles.contentContainer}>
      <Ionicons
        name="notifications-off-outline"
        size={64}
        color={isDarkMode ? '#F85149' : '#CF222E'}
        style={styles.icon}
      />
      
      <Text
        style={[
          styles.title,
          { color: isDarkMode ? '#FFFFFF' : '#000000' },
        ]}
      >
        {t('notifications.permissionDenied')}
      </Text>
      
      <Text
        style={[
          styles.description,
          { color: isDarkMode ? '#8B949E' : '#6E7781' },
        ]}
      >
        {t('notifications.permissionDeniedDescription')}
      </Text>
      
      <TouchableOpacity
        style={[
          styles.primaryButton,
          { backgroundColor: isDarkMode ? '#1F6FEB' : '#0366D6' },
        ]}
        onPress={openAppSettings}
      >
        <Text style={styles.primaryButtonText}>
          {t('notifications.openSettings')}
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={styles.secondaryButton}
        onPress={onClose}
      >
        <Text
          style={[
            styles.secondaryButtonText,
            { color: isDarkMode ? '#8B949E' : '#6E7781' },
          ]}
        >
          {t('common.notNow')}
        </Text>
      </TouchableOpacity>
    </View>
  );
  
  // Render permission request view
  const renderPermissionRequest = () => (
    <View style={styles.contentContainer}>
      <Image
        source={require('../../../assets/notification-illustration.png')}
        style={styles.illustration}
        resizeMode="contain"
      />
      
      <Text
        style={[
          styles.title,
          { color: isDarkMode ? '#FFFFFF' : '#000000' },
        ]}
      >
        {t('notifications.stayUpdated')}
      </Text>
      
      <Text
        style={[
          styles.description,
          { color: isDarkMode ? '#8B949E' : '#6E7781' },
        ]}
      >
        {t('notifications.permissionDescription')}
      </Text>
      
      <View style={styles.benefitsContainer}>
        <View style={styles.benefitItem}>
          <Ionicons
            name="checkmark-circle"
            size={20}
            color={isDarkMode ? '#3FB950' : '#2EA043'}
            style={styles.benefitIcon}
          />
          <Text
            style={[
              styles.benefitText,
              { color: isDarkMode ? '#C9D1D9' : '#24292E' },
            ]}
          >
            {t('notifications.benefit1')}
          </Text>
        </View>
        
        <View style={styles.benefitItem}>
          <Ionicons
            name="checkmark-circle"
            size={20}
            color={isDarkMode ? '#3FB950' : '#2EA043'}
            style={styles.benefitIcon}
          />
          <Text
            style={[
              styles.benefitText,
              { color: isDarkMode ? '#C9D1D9' : '#24292E' },
            ]}
          >
            {t('notifications.benefit2')}
          </Text>
        </View>
        
        <View style={styles.benefitItem}>
          <Ionicons
            name="checkmark-circle"
            size={20}
            color={isDarkMode ? '#3FB950' : '#2EA043'}
            style={styles.benefitIcon}
          />
          <Text
            style={[
              styles.benefitText,
              { color: isDarkMode ? '#C9D1D9' : '#24292E' },
            ]}
          >
            {t('notifications.benefit3')}
          </Text>
        </View>
      </View>
      
      <TouchableOpacity
        style={[
          styles.primaryButton,
          { backgroundColor: isDarkMode ? '#1F6FEB' : '#0366D6' },
        ]}
        onPress={requestPermissions}
      >
        <Text style={styles.primaryButtonText}>
          {t('notifications.allowNotifications')}
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={styles.secondaryButton}
        onPress={onClose}
      >
        <Text
          style={[
            styles.secondaryButtonText,
            { color: isDarkMode ? '#8B949E' : '#6E7781' },
          ]}
        >
          {t('common.notNow')}
        </Text>
      </TouchableOpacity>
    </View>
  );
  
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View
        style={[
          styles.container,
          { backgroundColor: isDarkMode ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.5)' },
        ]}
      >
        <View
          style={[
            styles.modalContent,
            { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' },
          ]}
        >
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
          >
            <Ionicons
              name="close"
              size={24}
              color={isDarkMode ? '#8B949E' : '#6E7781'}
            />
          </TouchableOpacity>
          
          {permissionStatus === 'denied' ? renderPermissionDenied() : renderPermissionRequest()}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 1,
  },
  contentContainer: {
    width: '100%',
    alignItems: 'center',
  },
  illustration: {
    width: 200,
    height: 200,
    marginBottom: 24,
  },
  icon: {
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 12,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  benefitsContainer: {
    width: '100%',
    marginBottom: 24,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  benefitIcon: {
    marginRight: 12,
  },
  benefitText: {
    fontSize: 14,
  },
  primaryButton: {
    width: '100%',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    paddingVertical: 12,
  },
  secondaryButtonText: {
    fontSize: 14,
  },
});

export default NotificationPermissionPrompt;
