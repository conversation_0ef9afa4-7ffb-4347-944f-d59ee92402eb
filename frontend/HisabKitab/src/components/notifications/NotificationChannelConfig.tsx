import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
// import { useNotifications } from '../../contexts/NotificationContext';
import { useFeatureFlag } from '../../contexts/FeatureFlagContext';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';

interface NotificationChannelConfigProps {
  onSave?: () => void;
}

const NotificationChannelConfig: React.FC<NotificationChannelConfigProps> = ({ onSave }) => {
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();
  // Mock settings and updateSettings
  const [settings, setSettings] = useState({
    pushEnabled: false,
    emailEnabled: false,
    inAppEnabled: true,
  });

  const updateSettings = async (newSettings: any) => {
    setSettings(newSettings);
    return true;
  };
  const { isFeatureEnabled } = useFeatureFlag();

  // Notification channel settings
  const [pushEnabled, setPushEnabled] = useState(settings?.pushEnabled || false);
  const [emailEnabled, setEmailEnabled] = useState(settings?.emailEnabled || false);
  const [inAppEnabled, setInAppEnabled] = useState<boolean>(settings?.inAppEnabled || true);

  // Permission state
  const [permissionsGranted, setPermissionsGranted] = useState(false);

  // Check notification permissions on mount
  useEffect(() => {
    checkNotificationPermissions();
  }, []);

  // Check notification permissions
  const checkNotificationPermissions = async () => {
    if (Device.isDevice) {
      const { status } = await Notifications.getPermissionsAsync();
      setPermissionsGranted(status === 'granted');
    }
  };

  // Request notification permissions
  const requestNotificationPermissions = async () => {
    if (Device.isDevice) {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        Alert.alert(
          t('notifications.permissionsDenied'),
          t('notifications.permissionsDeniedMessage')
        );
        return false;
      }

      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }

      setPermissionsGranted(true);
      return true;
    } else {
      Alert.alert(
        t('common.error'),
        t('notifications.deviceRequired')
      );
      return false;
    }
  };

  // Handle toggle push notifications
  const handleTogglePush = async (value: boolean) => {
    if (value && !permissionsGranted) {
      const granted = await requestNotificationPermissions();
      if (!granted) {
        return;
      }
    }

    setPushEnabled(value);
    await updateSettings({ ...settings, pushEnabled: value });
  };

  // Handle toggle email notifications
  const handleToggleEmail = async (value: boolean) => {
    setEmailEnabled(value);
    await updateSettings({ ...settings, emailEnabled: value });
  };

  // Handle toggle in-app notifications
  const handleToggleInApp = async (value: boolean) => {
    setInAppEnabled(value);
    await updateSettings({ ...settings, inAppEnabled: value });
  };

  // Open app settings
  const openAppSettings = () => {
    if (Platform.OS === 'ios') {
      Notifications.scheduleNotificationAsync({
        content: {
        title: t('notifications.openSettings'),
        body: t('notifications.openSettingsMessage'),
          data: {},
        },
        trigger: null,
      });
    } else {
      // For Android, we can try to open app settings directly
      // This is not available in Expo Go, but works in standalone apps
      Alert.alert(
        t('notifications.openSettings'),
        t('notifications.openSettingsMessage'),
        [
          {
            text: t('common.cancel'),
            style: 'cancel',
          },
          {
            text: t('common.openSettings'),
            onPress: () => {
              // This would open app settings in a standalone app
              // Notifications.getPermissionsAsync().then(() => {});
            },
          },
        ]
      );
    }
  };

  return (
    <View style={styles.container}>
      {/* Push notifications */}
      <View style={[styles.channelItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
        <View style={styles.channelInfo}>
          <Ionicons
            name="phone-portrait-outline"
            size={24}
            color={isDarkMode ? '#FFFFFF' : '#000000'}
            style={styles.channelIcon}
          />
          <View style={styles.channelTextContainer}>
            <Text style={[styles.channelTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {t('notifications.pushNotifications')}
            </Text>
            <Text style={[styles.channelDescription, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('notifications.pushDescription')}
            </Text>
          </View>
        </View>
        <Switch
          value={pushEnabled}
          onValueChange={handleTogglePush}
          trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
          thumbColor={pushEnabled ? '#58A6FF' : '#f4f3f4'}
        />
      </View>

      {/* Permission status */}
      {pushEnabled && !permissionsGranted && (
        <TouchableOpacity
          style={[
            styles.permissionWarning,
            { backgroundColor: isDarkMode ? '#30363D' : '#F6F8FA' },
          ]}
          onPress={openAppSettings}
        >
          <Ionicons
            name="warning-outline"
            size={20}
            color={isDarkMode ? '#F78166' : '#F85149'}
          />
          <Text
            style={[
              styles.permissionWarningText,
              { color: isDarkMode ? '#F78166' : '#F85149' },
            ]}
          >
            {t('notifications.permissionsRequired')}
          </Text>
          <Ionicons
            name="chevron-forward"
            size={16}
            color={isDarkMode ? '#8B949E' : '#6E7781'}
          />
        </TouchableOpacity>
      )}

      {/* Email notifications */}
      {isFeatureEnabled('EmailNotifications') && (
        <View style={[styles.channelItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
          <View style={styles.channelInfo}>
            <Ionicons
              name="mail-outline"
              size={24}
              color={isDarkMode ? '#FFFFFF' : '#000000'}
              style={styles.channelIcon}
            />
            <View style={styles.channelTextContainer}>
              <Text style={[styles.channelTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
                {t('notifications.emailNotifications')}
              </Text>
              <Text style={[styles.channelDescription, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
                {t('notifications.emailDescription')}
              </Text>
            </View>
          </View>
          <Switch
            value={emailEnabled}
            onValueChange={handleToggleEmail}
            trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
            thumbColor={emailEnabled ? '#58A6FF' : '#f4f3f4'}
          />
        </View>
      )}

      {/* In-app notifications */}
      <View style={[styles.channelItem, { borderBottomColor: isDarkMode ? '#30363D' : '#D0D7DE' }]}>
        <View style={styles.channelInfo}>
          <Ionicons
            name="apps-outline"
            size={24}
            color={isDarkMode ? '#FFFFFF' : '#000000'}
            style={styles.channelIcon}
          />
          <View style={styles.channelTextContainer}>
            <Text style={[styles.channelTitle, { color: isDarkMode ? '#FFFFFF' : '#000000' }]}>
              {t('notifications.inAppNotifications')}
            </Text>
            <Text style={[styles.channelDescription, { color: isDarkMode ? '#8B949E' : '#6E7781' }]}>
              {t('notifications.inAppDescription')}
            </Text>
          </View>
        </View>
        <Switch
          value={inAppEnabled}
          onValueChange={handleToggleInApp}
          trackColor={{ false: '#767577', true: isDarkMode ? '#0D419D' : '#0366D6' }}
          thumbColor={inAppEnabled ? '#58A6FF' : '#f4f3f4'}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  channelItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  channelInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  channelIcon: {
    marginRight: 12,
  },
  channelTextContainer: {
    flex: 1,
  },
  channelTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  channelDescription: {
    fontSize: 14,
    marginTop: 2,
  },
  permissionWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    marginTop: 8,
    marginBottom: 16,
  },
  permissionWarningText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
});

export default NotificationChannelConfig;
