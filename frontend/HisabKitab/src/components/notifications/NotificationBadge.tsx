import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { NotificationBadgeProps } from '../../models/Notification';

const NotificationBadge: React.FC<NotificationBadgeProps> = ({
  count,
  size = 20,
  color = '#F85149',
  textColor = '#FFFFFF',
}) => {
  if (count <= 0) {
    return null;
  }

  // Format count for display (e.g., 99+ for large numbers)
  const displayCount = count > 99 ? '99+' : count.toString();

  return (
    <View
      style={[
        styles.badge,
        {
          backgroundColor: color,
          width: displayCount.length > 1 ? size + 8 : size,
          height: size,
          borderRadius: size / 2,
        },
      ]}
    >
      <Text
        style={[
          styles.text,
          {
            color: textColor,
            fontSize: size * 0.6,
          },
        ]}
      >
        {displayCount}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: -5,
    right: -5,
    zIndex: 10,
  },
  text: {
    fontWeight: 'bold',
  },
});

export default NotificationBadge;
