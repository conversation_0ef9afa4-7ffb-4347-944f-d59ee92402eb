import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  LayoutAnimation,
  Platform,
  UIManager,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { Notification } from '../../models/Notification';
import NotificationItem from './NotificationItem';
import { formatDate } from '../../utils/formatters';

// Enable LayoutAnimation for Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

interface NotificationGroupProps {
  title: string;
  notifications: Notification[];
  onPressNotification: (notification: Notification) => void;
  onDeleteNotification: (id: number) => Promise<void>;
  isDarkMode: boolean;
  icon?: string;
  showDate?: boolean;
}

const NotificationGroup: React.FC<NotificationGroupProps> = ({
  title,
  notifications,
  onPressNotification,
  onDeleteNotification,
  isDarkMode,
  icon = 'notifications-outline',
  showDate = true,
}) => {
  const { t } = useTranslation();
  const [expanded, setExpanded] = useState(true);
  const [rotateAnimation] = useState(new Animated.Value(expanded ? 1 : 0));

  // Toggle expanded state
  const toggleExpanded = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpanded(!expanded);

    Animated.timing(rotateAnimation, {
      toValue: expanded ? 0 : 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  // Get rotation interpolation
  const rotate = rotateAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '90deg'],
  });

  // Get group date
  const getGroupDate = () => {
    if (!showDate || notifications.length === 0) return '';

    // Get the most recent notification date
    const mostRecentDate = new Date(
      Math.max(...notifications.map(n => new Date(n.createdAt).getTime()))
    );

    return formatDate(mostRecentDate);
  };

  // Get unread count
  const getUnreadCount = () => {
    return notifications.filter(n => !n.isRead).length;
  };

  // Render notification items
  const renderNotificationItems = () => {
    if (!expanded) return null;

    return notifications.map(notification => (
      <NotificationItem
        key={notification.id}
        notification={notification}
        onPress={onPressNotification}
        onMarkAsRead={async (id) => {
          // In a real implementation, this would mark the notification as read
          console.log('Mark as read:', id);
          return Promise.resolve();
        }}
        onDelete={onDeleteNotification}
        isDarkMode={isDarkMode}
      />
    ));
  };

  return (
    <View style={styles.container}>
      {/* Group header */}
      <TouchableOpacity
        style={[
          styles.header,
          {
            backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
            borderLeftColor: getUnreadCount() > 0
              ? isDarkMode ? '#58A6FF' : '#0366D6'
              : isDarkMode ? '#30363D' : '#D0D7DE',
          },
        ]}
        onPress={toggleExpanded}
      >
        <View style={styles.headerLeft}>
          <Ionicons
            name={icon as any}
            size={24}
            color={
              getUnreadCount() > 0
                ? isDarkMode ? '#58A6FF' : '#0366D6'
                : isDarkMode ? '#8B949E' : '#6E7781'
            }
            style={styles.headerIcon}
          />
          <View style={styles.headerTextContainer}>
            <Text
              style={[
                styles.headerTitle,
                { color: isDarkMode ? '#FFFFFF' : '#000000' },
              ]}
            >
              {title}
            </Text>
            {showDate && (
              <Text
                style={[
                  styles.headerDate,
                  { color: isDarkMode ? '#8B949E' : '#6E7781' },
                ]}
              >
                {getGroupDate()}
              </Text>
            )}
          </View>
        </View>
        <View style={styles.headerRight}>
          {getUnreadCount() > 0 && (
            <View
              style={[
                styles.badge,
                {
                  backgroundColor: isDarkMode ? '#58A6FF' : '#0366D6',
                },
              ]}
            >
              <Text style={styles.badgeText}>
                {getUnreadCount()}
              </Text>
            </View>
          )}
          <Animated.View style={{ transform: [{ rotate }] }}>
            <Ionicons
              name="chevron-forward"
              size={20}
              color={isDarkMode ? '#8B949E' : '#6E7781'}
            />
          </Animated.View>
        </View>
      </TouchableOpacity>

      {/* Notification items */}
      {renderNotificationItems()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerIcon: {
    marginRight: 12,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  headerDate: {
    fontSize: 12,
    marginTop: 2,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
});

export default NotificationGroup;
