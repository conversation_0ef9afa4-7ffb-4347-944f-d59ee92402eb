import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { Notification } from '../../models/Notification';
import { formatRelativeTime } from '../../utils/formatters';

interface NotificationItemProps {
  notification: Notification;
  onPress: (notification: Notification) => void;
  onMarkAsRead: (id: number) => Promise<void>;
  onDelete: (id: number) => Promise<void>;
  isDarkMode: boolean;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onPress,
  onMarkAsRead,
  onDelete,
  isDarkMode,
}) => {
  const { t } = useTranslation();

  // Get icon based on notification type
  const getIcon = () => {
    switch (notification.type) {
      case 'Loan':
        return 'cash-outline';
      case 'Transaction':
        return 'wallet-outline';
      case 'Family':
        return 'people-outline';
      case 'Account':
        return 'card-outline';
      default:
        return 'notifications-outline';
    }
  };

  // Handle mark as read
  const handleMarkAsRead = async (e: any) => {
    e.stopPropagation();
    await onMarkAsRead(notification.id);
  };

  // Handle delete
  const handleDelete = async (e: any) => {
    e.stopPropagation();
    await onDelete(notification.id);
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA',
          borderLeftColor: notification.isRead
            ? 'transparent'
            : isDarkMode
            ? '#58A6FF'
            : '#0366D6',
        },
      ]}
      onPress={() => onPress(notification)}
    >
      <View style={styles.iconContainer}>
        <Ionicons
          name={getIcon()}
          size={24}
          color={isDarkMode ? '#8B949E' : '#6E7781'}
        />
      </View>
      <View style={styles.content}>
        <Text
          style={[
            styles.title,
            {
              color: isDarkMode ? '#FFFFFF' : '#000000',
              fontWeight: notification.isRead ? 'normal' : 'bold',
            },
          ]}
        >
          {notification.title}
        </Text>
        <Text
          style={[
            styles.message,
            { color: isDarkMode ? '#C9D1D9' : '#24292E' },
          ]}
        >
          {notification.message}
        </Text>
        <Text
          style={[
            styles.time,
            { color: isDarkMode ? '#8B949E' : '#6E7781' },
          ]}
        >
          {formatRelativeTime(notification.createdAt)}
        </Text>
      </View>
      <View style={styles.actions}>
        {!notification.isRead && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleMarkAsRead}
          >
            <Ionicons
              name="checkmark-circle-outline"
              size={20}
              color={isDarkMode ? '#58A6FF' : '#0366D6'}
            />
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleDelete}
        >
          <Ionicons
            name="trash-outline"
            size={20}
            color={isDarkMode ? '#F85149' : '#CF222E'}
          />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 4,
  },
  iconContainer: {
    marginRight: 12,
    justifyContent: 'center',
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    marginBottom: 8,
  },
  time: {
    fontSize: 12,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
  },
});

export default NotificationItem;
