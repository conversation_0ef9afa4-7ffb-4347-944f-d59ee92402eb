import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import SyncIndicator from '../sync/SyncIndicator';
import NotificationBadge from '../notifications/NotificationBadge';
import { useNotifications } from '../../contexts/NotificationContext';

interface AppHeaderProps {
  title: string;
  showBack?: boolean;
  showSync?: boolean;
  showNotifications?: boolean;
  rightComponent?: React.ReactNode;
}

/**
 * Custom header component for the app
 */
const AppHeader: React.FC<AppHeaderProps> = ({
  title,
  showBack = false,
  showSync = true,
  showNotifications = true,
  rightComponent,
}) => {
  const navigation = useNavigation<any>();
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const { unreadCount } = useNotifications();
  
  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#0D1117' : '#FFFFFF' }
      ]}
    >
      <View style={styles.leftSection}>
        {showBack && (
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons
              name="arrow-back"
              size={24}
              color={isDarkMode ? '#FFFFFF' : '#000000'}
            />
          </TouchableOpacity>
        )}
        
        <Text
          style={[
            styles.title,
            { color: isDarkMode ? '#FFFFFF' : '#000000' }
          ]}
          numberOfLines={1}
        >
          {title}
        </Text>
      </View>
      
      <View style={styles.rightSection}>
        {showSync && <SyncIndicator />}
        
        {showNotifications && (
          <TouchableOpacity
            style={styles.notificationButton}
            onPress={() => navigation.navigate('notifications')}
          >
            <Ionicons
              name="notifications-outline"
              size={24}
              color={isDarkMode ? '#FFFFFF' : '#000000'}
            />
            <NotificationBadge count={unreadCount} size={16} />
          </TouchableOpacity>
        )}
        
        {rightComponent}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 56,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#30363D',
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  notificationButton: {
    marginLeft: 16,
    position: 'relative',
  },
});

export default AppHeader;
