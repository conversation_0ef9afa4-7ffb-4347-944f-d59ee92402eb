import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import { useSync, SyncStatus } from '../../contexts/SyncContext';
import { formatDistanceToNow } from 'date-fns';

interface SyncStatusBadgeProps {
  onPress?: () => void;
  showLastSync?: boolean;
  compact?: boolean;
}

const SyncStatusBadge: React.FC<SyncStatusBadgeProps> = ({
  onPress,
  showLastSync = true,
  compact = false,
}) => {
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const { syncStatus, lastSyncTime, pendingChanges, isOnline, syncNow } = useSync();
  
  // Get status icon and color
  const getStatusIcon = (): { name: string; color: string } => {
    switch (syncStatus) {
      case 'syncing':
        return { name: 'sync', color: '#F0883E' }; // Orange
      case 'success':
        return { name: 'checkmark-circle', color: '#3FB950' }; // Green
      case 'error':
        return { name: 'alert-circle', color: '#F85149' }; // Red
      case 'offline':
        return { name: 'cloud-offline', color: '#8B949E' }; // Gray
      default:
        return { name: 'cloud-done', color: '#58A6FF' }; // Blue
    }
  };
  
  // Get status text
  const getStatusText = (): string => {
    switch (syncStatus) {
      case 'syncing':
        return t('sync.syncing');
      case 'success':
        return t('sync.synced');
      case 'error':
        return t('sync.syncError');
      case 'offline':
        return t('sync.offline');
      default:
        return pendingChanges > 0 ? t('sync.pendingChanges', { count: pendingChanges }) : t('sync.upToDate');
    }
  };
  
  // Format last sync time
  const getLastSyncText = (): string => {
    if (!lastSyncTime) return t('sync.neverSynced');
    
    return t('sync.lastSynced', {
      time: formatDistanceToNow(lastSyncTime, { addSuffix: true }),
    });
  };
  
  // Handle press
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else if (syncStatus !== 'syncing' && isOnline) {
      syncNow();
    }
  };
  
  const { name, color } = getStatusIcon();
  
  if (compact) {
    return (
      <TouchableOpacity
        style={[
          styles.compactContainer,
          { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
        ]}
        onPress={handlePress}
        disabled={syncStatus === 'syncing'}
      >
        {syncStatus === 'syncing' ? (
          <ActivityIndicator size="small" color={color} />
        ) : (
          <Ionicons name={name as any} size={16} color={color} />
        )}
        
        {pendingChanges > 0 && (
          <View style={[styles.badge, { backgroundColor: '#F85149' }]}>
            <Text style={styles.badgeText}>
              {pendingChanges > 99 ? '99+' : pendingChanges}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  }
  
  return (
    <TouchableOpacity
      style={[
        styles.container,
        { backgroundColor: isDarkMode ? '#161B22' : '#F6F8FA' }
      ]}
      onPress={handlePress}
      disabled={syncStatus === 'syncing'}
    >
      <View style={styles.iconContainer}>
        {syncStatus === 'syncing' ? (
          <ActivityIndicator size="small" color={color} />
        ) : (
          <Ionicons name={name as any} size={20} color={color} />
        )}
      </View>
      
      <View style={styles.textContainer}>
        <Text style={[
          styles.statusText,
          { color: isDarkMode ? '#FFFFFF' : '#24292E' }
        ]}>
          {getStatusText()}
        </Text>
        
        {showLastSync && (
          <Text style={[
            styles.lastSyncText,
            { color: isDarkMode ? '#8B949E' : '#6E7781' }
          ]}>
            {getLastSyncText()}
          </Text>
        )}
      </View>
      
      {pendingChanges > 0 && (
        <View style={[styles.badge, { backgroundColor: '#F85149' }]}>
          <Text style={styles.badgeText}>
            {pendingChanges > 99 ? '99+' : pendingChanges}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    marginVertical: 4,
  },
  compactContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  iconContainer: {
    marginRight: 8,
  },
  textContainer: {
    flex: 1,
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  lastSyncText: {
    fontSize: 12,
  },
  badge: {
    position: 'absolute',
    top: -4,
    right: -4,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
});

export default SyncStatusBadge;
