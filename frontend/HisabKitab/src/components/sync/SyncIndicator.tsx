import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSync } from '../../contexts/SyncContext';
import { useTheme } from '../../contexts/ThemeContext';
import SyncStatusBadge from './SyncStatusBadge';

interface SyncIndicatorProps {
  showLastSync?: boolean;
}

/**
 * A compact sync indicator for the header
 */
const SyncIndicator: React.FC<SyncIndicatorProps> = ({ showLastSync = false }) => {
  const navigation = useNavigation<any>();
  const { isDarkMode } = useTheme();
  
  // Navigate to sync status screen
  const handlePress = () => {
    navigation.navigate('sync');
  };
  
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={handlePress}
      accessibilityLabel="Sync status"
      accessibilityHint="Shows sync status and navigates to sync screen"
    >
      <SyncStatusBadge compact showLastSync={showLastSync} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginRight: 8,
  },
});

export default SyncIndicator;
