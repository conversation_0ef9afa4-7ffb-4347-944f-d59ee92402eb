// Theme configuration for <PERSON>ab-Kitab app
// Dual-tone futuristic + cultural blend

export const colors = {
  // Primary Colors
  primary: '#3AAFA9', // Teal Jade - financially calming, modern and fresh
  primaryLight: '#4FBFB9', // Lighter shade of primary
  primaryDark: '#2A9D97', // Darker shade of primary

  // Background Colors
  background: {
    light: '#F5F5F5', // Light background
    dark: '#0B0C10', // Deep Charcoal Blue - near-black but warm
  },

  // Accent Colors - Cultural Pop
  accent: {
    start: '#FF6B6B', // Royal Pink - start of gradient
    end: '#FFC3A0', // Salmon - end of gradient
  },

  // Neutral Text/Base
  text: {
    light: '#333333', // Dark text for light mode
    dark: '#E0E0E0', // Ash Grey - for dark mode
    white: '#F5F5F5', // Cloud White - for high contrast
  },

  // Status Colors
  success: '#4ADE80', // Spring Green - for success/positive events
  warning: '#FACC15', // Amber Yellow - for warnings
  error: '#EF4444', // Rosy Red - for errors
  info: '#60A5FA', // Blue - for information

  // Card and UI Elements
  card: {
    light: '#FFFFFF', // White cards for light mode
    dark: '#1F2833', // Dark cards for dark mode
    border: {
      light: '#E5E5E5', // Light border
      dark: '#2D3748', // Dark border
    },
  },

  // Gradients
  gradients: {
    primary: ['#3AAFA9', '#2A9D97'], // Teal gradient
    accent: ['#FF6B6B', '#FFC3A0'], // Cultural accent gradient
    success: ['#4ADE80', '#22C55E'], // Success gradient
    warning: ['#FACC15', '#EAB308'], // Warning gradient
    error: ['#EF4444', '#DC2626'], // Error gradient
  },

  // Shadows
  shadow: {
    light: 'rgba(0, 0, 0, 0.1)',
    dark: 'rgba(0, 0, 0, 0.3)',
  },

  // Overlay
  overlay: 'rgba(0, 0, 0, 0.5)', // For modals and overlays
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const borderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  round: 9999, // For circular elements
};

export const typography = {
  fontFamily: {
    regular: 'Manrope-Regular',
    medium: 'Manrope-Medium',
    semiBold: 'Manrope-SemiBold',
    bold: 'Manrope-Bold',
  },
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  lineHeight: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 28,
    xl: 32,
    xxl: 36,
    xxxl: 40,
  },
};

export const elevation = {
  none: 0,
  xs: 1,
  sm: 2,
  md: 4,
  lg: 8,
  xl: 16,
};

// Animation durations
export const animation = {
  fast: 150,
  normal: 300,
  slow: 500,
};

// Screen-specific styles
export const screenStyles = {
  container: {
    padding: spacing.md,
  },
  section: {
    marginBottom: spacing.lg,
  },
  card: {
    borderRadius: borderRadius.md,
    padding: spacing.md,
    marginBottom: spacing.md,
  },
};

// Export theme object
const theme = {
  colors,
  spacing,
  borderRadius,
  typography,
  elevation,
  animation,
  screenStyles,
};

export default theme;
