import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import Constants from 'expo-constants';
import { useEffect, useRef, useState } from 'react';
import apiService from './api';
import dbService from './db';
import { useFeatureFlag } from '../contexts/FeatureFlagContext';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

// Interface for push token
export interface PushToken {
  token: string;
  platform: 'ios' | 'android' | 'web';
  deviceId: string;
}

// Interface for notification data
export interface NotificationData {
  id: string;
  title: string;
  body: string;
  data: any;
  date: Date;
  isRead: boolean;
  createdAt: Date;
}

// Interface for notification trigger options
export interface NotificationTriggerOptions {
  date?: Date;
  repeats?: boolean;
  seconds?: number;
  minutes?: number;
  hours?: number;
  day?: number;
  weekday?: number;
  month?: number;
  year?: number;
}

/**
 * Register for push notifications
 * @returns Push token or null if registration failed
 */
export async function registerForPushNotificationsAsync(): Promise<PushToken | null> {
  let token;

  if (Platform.OS === 'android') {
    // Set notification channel for Android
    await Notifications.setNotificationChannelAsync('default', {
      name: 'Default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  if (Device.isDevice) {
    // Check if we have permission
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    // If we don't have permission, ask for it
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    // If we still don't have permission, return null
    if (finalStatus !== 'granted') {
      console.log('Failed to get push token for push notification!');
      return null;
    }

    // Get the token
    token = await Notifications.getExpoPushTokenAsync({
      projectId: Constants.expoConfig?.extra?.eas?.projectId,
    });

    // Return the token with platform and device info
    return {
      token: token.data,
      platform: Platform.OS as 'ios' | 'android' | 'web',
      deviceId: Device.deviceName || 'Unknown Device',
    };
  } else {
    console.log('Must use physical device for Push Notifications');
    return null;
  }
}

/**
 * Register push token with the server
 * @param token Push token
 * @returns True if successful, false otherwise
 */
export async function registerPushTokenWithServer(token: PushToken): Promise<boolean> {
  try {
    // Call the API to register the token
    const response = await apiService.user.registerPushToken(token);
    return response.status >= 200 && response.status < 300;
  } catch (error) {
    console.error('Error registering push token with server:', error);
    return false;
  }
}

/**
 * Unregister push token with the server
 * @param token Push token
 * @returns True if successful, false otherwise
 */
export async function unregisterPushTokenWithServer(token: string): Promise<boolean> {
  try {
    // Call the API to unregister the token
    const response = await apiService.user.unregisterPushToken(token);
    return response.status >= 200 && response.status < 300;
  } catch (error) {
    console.error('Error unregistering push token with server:', error);
    return false;
  }
}

/**
 * Schedule a local notification
 * @param title Notification title
 * @param body Notification body
 * @param data Additional data to include with the notification
 * @param triggerOptions Options for when to show the notification
 * @returns Notification ID
 */
export async function scheduleLocalNotification(
  title: string,
  body: string,
  data: Record<string, any> = {},
  triggerOptions: NotificationTriggerOptions = {}
): Promise<string> {
  try {
    // Create trigger based on options
    let trigger: any = null;

    if (triggerOptions.date) {
      trigger = {
        date: triggerOptions.date,
      };
    } else if (
      triggerOptions.seconds ||
      triggerOptions.minutes ||
      triggerOptions.hours ||
      triggerOptions.day ||
      triggerOptions.weekday ||
      triggerOptions.month ||
      triggerOptions.year
    ) {
      trigger = {
        repeats: triggerOptions.repeats || false,
        seconds: triggerOptions.seconds,
        minutes: triggerOptions.minutes,
        hours: triggerOptions.hours,
        day: triggerOptions.day,
        weekday: triggerOptions.weekday,
        month: triggerOptions.month,
        year: triggerOptions.year,
      };
    }

    // Schedule notification
    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
        sound: true,
        priority: Notifications.AndroidNotificationPriority.HIGH,
      },
      trigger,
    });

    // Save notification to local database
    await saveNotificationToLocalDB(notificationId, title, body, data, triggerOptions.date || new Date());

    return notificationId;
  } catch (error) {
    console.error('Error scheduling notification:', error);
    throw error;
  }
}

/**
 * Save notification to local database
 * @param id Notification ID
 * @param title Notification title
 * @param body Notification body
 * @param data Additional data
 * @param date Notification date
 */
async function saveNotificationToLocalDB(
  id: string,
  title: string,
  body: string,
  data: any,
  date: Date
): Promise<void> {
  try {
    const notification: NotificationData = {
      id,
      title,
      body,
      data,
      date,
      isRead: false,
      createdAt: new Date(),
    };

    // TODO: Implement saveNotification in dbService
    // await dbService.saveNotification(notification);
    console.log('Notification saved to local DB:', notification);
  } catch (error) {
    console.error('Error saving notification to local DB:', error);
  }
}

/**
 * Cancel a scheduled notification
 * @param notificationId Notification ID
 */
export async function cancelScheduledNotification(notificationId: string): Promise<void> {
  try {
    await Notifications.cancelScheduledNotificationAsync(notificationId);
    // TODO: Implement deleteNotification in dbService
    // await dbService.deleteNotification(notificationId);
    console.log('Notification deleted from local DB:', notificationId);
  } catch (error) {
    console.error('Error canceling notification:', error);
    throw error;
  }
}

/**
 * Cancel all scheduled notifications
 */
export async function cancelAllScheduledNotifications(): Promise<void> {
  await Notifications.cancelAllScheduledNotificationsAsync();
}

/**
 * Get all scheduled notifications
 * @returns Array of scheduled notifications
 */
export async function getAllScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
  return await Notifications.getAllScheduledNotificationsAsync();
}

/**
 * Set notification badge count
 * @param count Badge count
 */
export async function setBadgeCount(count: number): Promise<void> {
  await Notifications.setBadgeCountAsync(count);
}

/**
 * Get notification badge count
 * @returns Badge count
 */
export async function getBadgeCount(): Promise<number> {
  return await Notifications.getBadgeCountAsync();
}

/**
 * Get all delivered notifications
 * @returns Array of delivered notifications
 */
export async function getAllDeliveredNotifications(): Promise<Notifications.Notification[]> {
  try {
    return await Notifications.getPresentedNotificationsAsync();
  } catch (error) {
    console.error('Error getting delivered notifications:', error);
    throw error;
  }
}

/**
 * Dismiss all delivered notifications
 */
export async function dismissAllNotifications(): Promise<void> {
  try {
    await Notifications.dismissAllNotificationsAsync();
  } catch (error) {
    console.error('Error dismissing all notifications:', error);
    throw error;
  }
}

/**
 * Hook for handling notifications
 * @returns Object containing notification and expoPushToken
 */
export function useNotifications() {
  const [notification, setNotification] = useState<Notifications.Notification | null>(null);
  const [expoPushToken, setExpoPushToken] = useState<PushToken | null>(null);
  const notificationListener = useRef<any>(null);
  const responseListener = useRef<any>(null);
  const { isFeatureEnabled } = useFeatureFlag();

  useEffect(() => {
    // Register for push notifications
    registerForPushNotificationsAsync().then((token) => {
      if (token) {
        setExpoPushToken(token);
        // Save token to backend if feature is enabled
        if (isFeatureEnabled('PushNotifications')) {
          registerPushTokenWithServer(token);
        }
      }
    });

    // Add notification received listener
    notificationListener.current = Notifications.addNotificationReceivedListener((notification) => {
      setNotification(notification);
    });

    // Add notification response listener
    responseListener.current = Notifications.addNotificationResponseReceivedListener((response) => {
      const { notification } = response;
      // Handle notification response (e.g., navigate to a screen)
      handleNotificationResponse(notification);
    });

    // Clean up listeners
    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, []);

  // Handle notification response
  const handleNotificationResponse = (notification: Notifications.Notification) => {
    // Mark notification as read
    if (notification.request.identifier) {
      // TODO: Implement markNotificationAsRead in dbService
      // dbService.markNotificationAsRead(notification.request.identifier);
      console.log('Notification marked as read:', notification.request.identifier);
    }

    // Handle navigation or other actions based on notification data
    // const data = notification.request.content.data;

    // Example: Navigate to a screen based on notification data
    // if (data.screen) {
    //   navigation.navigate(data.screen, data.params);
    // }
  };

  return {
    notification,
    expoPushToken,
  };
}

export default {
  registerForPushNotificationsAsync,
  registerPushTokenWithServer,
  unregisterPushTokenWithServer,
  scheduleLocalNotification,
  cancelScheduledNotification,
  cancelAllScheduledNotifications,
  getAllScheduledNotifications,
  getAllDeliveredNotifications,
  dismissAllNotifications,
  setBadgeCount,
  getBadgeCount,
  useNotifications,
};
