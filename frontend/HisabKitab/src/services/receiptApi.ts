import { fetchWithAuth, ApiResponse } from './api';
import { ReceiptData, ProcessReceiptRequest, SaveReceiptRequest, ReceiptFilterOptions, ReceiptHistoryItem } from '../models/Receipt';

/**
 * Receipt API service interface
 */
export interface ReceiptService {
  processReceipt(request: ProcessReceiptRequest): Promise<ApiResponse<ReceiptData>>;
  getReceipt(id: number): Promise<ApiResponse<ReceiptData>>;
  getReceiptsByTransaction(transactionId: number): Promise<ApiResponse<ReceiptData[]>>;
  getReceiptHistory(filter?: ReceiptFilterOptions): Promise<ApiResponse<ReceiptHistoryItem[]>>;
  saveReceipt(request: SaveReceiptRequest): Promise<ApiResponse<ReceiptData>>;
  updateReceipt(id: number, request: Partial<SaveReceiptRequest>): Promise<ApiResponse<ReceiptData>>;
  deleteReceipt(id: number): Promise<ApiResponse<void>>;
}

/**
 * Receipt API implementation
 */
const receiptService: ReceiptService = {
  async processReceipt(request: ProcessReceiptRequest): Promise<ApiResponse<ReceiptData>> {
    return await fetchWithAuth<ReceiptData>('receipts/process', 'POST', request);
  },

  async getReceipt(id: number): Promise<ApiResponse<ReceiptData>> {
    return await fetchWithAuth<ReceiptData>(`receipts/${id}`);
  },

  async getReceiptsByTransaction(transactionId: number): Promise<ApiResponse<ReceiptData[]>> {
    return await fetchWithAuth<ReceiptData[]>(`receipts/transaction/${transactionId}`);
  },

  async getReceiptHistory(filter?: ReceiptFilterOptions): Promise<ApiResponse<ReceiptHistoryItem[]>> {
    let queryParams = '';
    
    if (filter) {
      const params = new URLSearchParams();
      if (filter.startDate) params.append('startDate', filter.startDate);
      if (filter.endDate) params.append('endDate', filter.endDate);
      if (filter.minAmount) params.append('minAmount', filter.minAmount.toString());
      if (filter.maxAmount) params.append('maxAmount', filter.maxAmount.toString());
      if (filter.merchantName) params.append('merchantName', filter.merchantName);
      if (filter.isProcessed !== undefined) params.append('isProcessed', filter.isProcessed.toString());
      
      queryParams = `?${params.toString()}`;
    }
    
    return await fetchWithAuth<ReceiptHistoryItem[]>(`receipts/history${queryParams}`);
  },

  async saveReceipt(request: SaveReceiptRequest): Promise<ApiResponse<ReceiptData>> {
    return await fetchWithAuth<ReceiptData>('receipts', 'POST', request);
  },

  async updateReceipt(id: number, request: Partial<SaveReceiptRequest>): Promise<ApiResponse<ReceiptData>> {
    return await fetchWithAuth<ReceiptData>(`receipts/${id}`, 'PUT', request);
  },

  async deleteReceipt(id: number): Promise<ApiResponse<void>> {
    return await fetchWithAuth<void>(`receipts/${id}`, 'DELETE');
  }
};

export default receiptService;
