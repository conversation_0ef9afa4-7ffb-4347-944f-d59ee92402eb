import { fetchWithAuth, ApiResponse } from './api';

// Budget Control API endpoints
export interface BudgetControlService {
  // Budget Limits
  getBudgetLimits(): Promise<ApiResponse<any>>;
  getBudgetLimit(id: number): Promise<ApiResponse<any>>;
  getFamilyBudgetLimits(familyId: number): Promise<ApiResponse<any>>;
  createBudgetLimit(budgetLimitData: any): Promise<ApiResponse<any>>;
  updateBudgetLimit(id: number, budgetLimitData: any): Promise<ApiResponse<any>>;
  deleteBudgetLimit(id: number): Promise<ApiResponse<any>>;
  getBudgetRemaining(id: number): Promise<ApiResponse<any>>;
  
  // Spending Limits
  getSpendingLimits(familyId: number): Promise<ApiResponse<any>>;
  getSpendingLimit(id: number): Promise<ApiResponse<any>>;
  createSpendingLimit(familyId: number, spendingLimitData: any): Promise<ApiResponse<any>>;
  updateSpendingLimit(id: number, spendingLimitData: any): Promise<ApiResponse<any>>;
  deleteSpendingLimit(id: number): Promise<ApiResponse<any>>;
  getSpendingLimitRemaining(id: number): Promise<ApiResponse<any>>;
}

// Budget Control API implementation
const budgetControlService: BudgetControlService = {
  // Budget Limits
  async getBudgetLimits(): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>('budgetcontrol/budget-limits');
  },

  async getBudgetLimit(id: number): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`budgetcontrol/budget-limits/${id}`);
  },

  async getFamilyBudgetLimits(familyId: number): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`budgetcontrol/family/${familyId}/budget-limits`);
  },

  async createBudgetLimit(budgetLimitData: any): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>('budgetcontrol/budget-limits', 'POST', budgetLimitData);
  },

  async updateBudgetLimit(id: number, budgetLimitData: any): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`budgetcontrol/budget-limits/${id}`, 'PUT', budgetLimitData);
  },

  async deleteBudgetLimit(id: number): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`budgetcontrol/budget-limits/${id}`, 'DELETE');
  },

  async getBudgetRemaining(id: number): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`budgetcontrol/budget-limits/${id}/remaining`);
  },
  
  // Spending Limits
  async getSpendingLimits(familyId: number): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`budgetcontrol/family/${familyId}/spending-limits`);
  },

  async getSpendingLimit(id: number): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`budgetcontrol/spending-limits/${id}`);
  },

  async createSpendingLimit(familyId: number, spendingLimitData: any): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`budgetcontrol/family/${familyId}/spending-limits`, 'POST', spendingLimitData);
  },

  async updateSpendingLimit(id: number, spendingLimitData: any): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`budgetcontrol/spending-limits/${id}`, 'PUT', spendingLimitData);
  },

  async deleteSpendingLimit(id: number): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`budgetcontrol/spending-limits/${id}`, 'DELETE');
  },

  async getSpendingLimitRemaining(id: number): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`budgetcontrol/spending-limits/${id}/remaining`);
  }
};

export default budgetControlService;
