import { fetchWithAuth, ApiResponse } from './api';

// Analytics API endpoints
export interface AnalyticsService {
  getMetrics(startDate: string, endDate: string, period?: string, familyId?: number, categoryId?: number, accountId?: number): Promise<ApiResponse<any>>;
  getCategoryBreakdown(startDate: string, endDate: string, familyId?: number): Promise<ApiResponse<any>>;
  getTrendData(metricType: string, startDate: string, endDate: string, period?: string, familyId?: number, categoryId?: number): Promise<ApiResponse<any>>;
  getNetWorth(asOfDate?: string, familyId?: number): Promise<ApiResponse<any>>;
  getNetWorthTrend(startDate: string, endDate: string, period?: string, familyId?: number): Promise<ApiResponse<any>>;
  getCashflowAnalysis(startDate: string, endDate: string, familyId?: number): Promise<ApiResponse<any>>;
  getAnomalies(startDate: string, endDate: string, familyId?: number): Promise<ApiResponse<any>>;
  getAnomaly(id: number): Promise<ApiResponse<any>>;
  updateAnomalyStatus(id: number, statusData: any): Promise<ApiResponse<any>>;
  exportData(format: string, startDate: string, endDate: string, type: string, familyId?: number): Promise<ApiResponse<any>>;
}

// Analytics API implementation
const analyticsService: AnalyticsService = {
  async getMetrics(startDate: string, endDate: string, period: string = 'Monthly', familyId?: number, categoryId?: number, accountId?: number): Promise<ApiResponse<any>> {
    let queryParams = `startDate=${startDate}&endDate=${endDate}&period=${period}`;
    if (familyId) queryParams += `&familyId=${familyId}`;
    if (categoryId) queryParams += `&categoryId=${categoryId}`;
    if (accountId) queryParams += `&accountId=${accountId}`;
    
    return await fetchWithAuth<any>(`analytics/metrics?${queryParams}`);
  },

  async getCategoryBreakdown(startDate: string, endDate: string, familyId?: number): Promise<ApiResponse<any>> {
    let queryParams = `startDate=${startDate}&endDate=${endDate}`;
    if (familyId) queryParams += `&familyId=${familyId}`;
    
    return await fetchWithAuth<any>(`analytics/category-breakdown?${queryParams}`);
  },

  async getTrendData(metricType: string, startDate: string, endDate: string, period: string = 'Monthly', familyId?: number, categoryId?: number): Promise<ApiResponse<any>> {
    let queryParams = `metricType=${metricType}&startDate=${startDate}&endDate=${endDate}&period=${period}`;
    if (familyId) queryParams += `&familyId=${familyId}`;
    if (categoryId) queryParams += `&categoryId=${categoryId}`;
    
    return await fetchWithAuth<any>(`analytics/trend?${queryParams}`);
  },

  async getNetWorth(asOfDate?: string, familyId?: number): Promise<ApiResponse<any>> {
    let queryParams = '';
    if (asOfDate) queryParams += `asOfDate=${asOfDate}`;
    if (familyId) queryParams += `${queryParams ? '&' : ''}familyId=${familyId}`;
    
    return await fetchWithAuth<any>(`analytics/net-worth${queryParams ? '?' + queryParams : ''}`);
  },

  async getNetWorthTrend(startDate: string, endDate: string, period: string = 'Monthly', familyId?: number): Promise<ApiResponse<any>> {
    let queryParams = `startDate=${startDate}&endDate=${endDate}&period=${period}`;
    if (familyId) queryParams += `&familyId=${familyId}`;
    
    return await fetchWithAuth<any>(`analytics/net-worth/trend?${queryParams}`);
  },

  async getCashflowAnalysis(startDate: string, endDate: string, familyId?: number): Promise<ApiResponse<any>> {
    let queryParams = `startDate=${startDate}&endDate=${endDate}`;
    if (familyId) queryParams += `&familyId=${familyId}`;
    
    return await fetchWithAuth<any>(`analytics/cashflow?${queryParams}`);
  },

  async getAnomalies(startDate: string, endDate: string, familyId?: number): Promise<ApiResponse<any>> {
    let queryParams = `startDate=${startDate}&endDate=${endDate}`;
    if (familyId) queryParams += `&familyId=${familyId}`;
    
    return await fetchWithAuth<any>(`analytics/anomalies?${queryParams}`);
  },

  async getAnomaly(id: number): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`analytics/anomalies/${id}`);
  },

  async updateAnomalyStatus(id: number, statusData: any): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`analytics/anomalies/${id}/status`, 'PUT', statusData);
  },

  async exportData(format: string, startDate: string, endDate: string, type: string, familyId?: number): Promise<ApiResponse<any>> {
    let queryParams = `format=${format}&startDate=${startDate}&endDate=${endDate}&type=${type}`;
    if (familyId) queryParams += `&familyId=${familyId}`;
    
    return await fetchWithAuth<any>(`analytics/export?${queryParams}`);
  }
};

export default analyticsService;
