import axios, { AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

// API URL - same as in config.ts
const API_URL = 'https://66d3-202-51-86-227.ngrok-free.app/api';

// Define API response interface
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  statusCode?: number;
}

// Define API request interface
export interface ApiRequest {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  data?: any;
  params?: any;
  headers?: any;
}

// Create axios instance
const axiosInstance = axios.create({
  baseURL: API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add request interceptor to add auth token
axiosInstance.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    // Get auth token from storage
    const token = await AsyncStorage.getItem('@hisabkitab/authToken');

    // Add token to headers if it exists
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error: any) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle errors
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: any) => {
    // Handle 401 Unauthorized error
    if (error.response && error.response.status === 401) {
      // Try to refresh token
      const refreshToken = await AsyncStorage.getItem('@hisabkitab/refreshToken');

      if (refreshToken) {
        try {
          // Call refresh token endpoint
          const response = await axios.post(`${API_URL}/api/auth/refresh`, {
            refreshToken,
          });

          // Save new tokens
          await AsyncStorage.setItem('@hisabkitab/authToken', response.data.token);
          await AsyncStorage.setItem('@hisabkitab/refreshToken', response.data.refreshToken);

          // Retry original request
          const originalRequest = error.config;
          if (originalRequest && originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${response.data.token}`;
            return axiosInstance(originalRequest);
          }
        } catch (refreshError) {
          // If refresh token is invalid, clear tokens and redirect to login
          await AsyncStorage.removeItem('@hisabkitab/authToken');
          await AsyncStorage.removeItem('@hisabkitab/refreshToken');

          // Redirect to login (this would be handled by the auth context)
          // window.location.href = '/login';
        }
      }
    }

    return Promise.reject(error);
  }
);

// Generic API request function
export const apiRequest = async <T>(request: ApiRequest): Promise<ApiResponse<T>> => {
  try {
    const config: AxiosRequestConfig = {
      method: request.method,
      url: request.url,
      data: request.data,
      params: request.params,
      headers: request.headers,
    };

    const response: AxiosResponse = await axiosInstance(config);

    return {
      success: true,
      data: response.data,
      statusCode: response.status,
    };
  } catch (error: any) {
    // Handle error response
    if (error.response) {
      // Server responded with an error status code
      return {
        success: false,
        error: error.response.data.message || 'An error occurred',
        statusCode: error.response.status,
      };
    } else if (error.request) {
      // Request was made but no response was received
      return {
        success: false,
        error: 'No response from server',
        statusCode: 0,
      };
    } else {
      // Something else happened while setting up the request
      return {
        success: false,
        error: error.message || 'An error occurred',
        statusCode: 0,
      };
    }
  }
};
