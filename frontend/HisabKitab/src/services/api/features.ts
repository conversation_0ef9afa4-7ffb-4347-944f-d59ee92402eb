import { FeatureFlagCreateRequest } from '../../models/FeatureFlag';
// Import the API client from the main API service
import apiService from '../api';

/**
 * Get all feature flags
 * @returns Response with feature flags or error
 */
export const getFeatures = async () => {
  return await apiService.features.getFeatures();
};

/**
 * Get a feature flag by ID
 * @param id Feature flag ID
 * @returns Response with feature flag or error
 */
export const getFeature = async (id: number) => {
  return await apiService.features.getFeature(id);
};

/**
 * Get a feature flag by name
 * @param name Feature flag name
 * @returns Response with feature flag or error
 */
export const getFeatureByName = async (name: string) => {
  return await apiService.features.getFeatureByName(name);
};

/**
 * Create a new feature flag
 * @param feature Feature flag data
 * @returns Response with created feature flag or error
 */
export const createFeature = async (feature: FeatureFlagCreateRequest) => {
  return await apiService.features.createFeature(feature);
};

/**
 * Update a feature flag
 * @param id Feature flag ID
 * @param isEnabled Whether the feature is enabled
 * @param enabledFor Who the feature is enabled for (optional)
 * @returns Response with updated feature flag or error
 */
export const updateFeature = async (
  id: number,
  isEnabled: boolean,
  enabledFor?: string
) => {
  return await apiService.features.updateFeature(id, isEnabled, enabledFor);
};

/**
 * Delete a feature flag
 * @param id Feature flag ID
 * @returns Response with success or error
 */
export const deleteFeature = async (id: number) => {
  return await apiService.features.deleteFeature(id);
};

/**
 * Check if a feature is enabled
 * @param name Feature flag name
 * @returns Response with boolean indicating if feature is enabled
 */
export const isFeatureEnabled = async (name: string) => {
  return await apiService.features.isFeatureEnabled(name);
};

export default {
  getFeatures,
  getFeature,
  getFeatureByName,
  createFeature,
  updateFeature,
  deleteFeature,
  isFeatureEnabled,
};
