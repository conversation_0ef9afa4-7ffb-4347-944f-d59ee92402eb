import dbService, { SyncQueue } from './db';
import apiService from './api';
import NetInfo from '@react-native-community/netinfo';

// Maximum number of retry attempts
const MAX_RETRY_COUNT = 3;

// Interface for sync status
export interface SyncStatus {
  isSyncing: boolean;
  lastSyncedAt: Date | null;
  pendingItems: number;
  error: string | null;
}

// Initial sync status
const initialSyncStatus: SyncStatus = {
  isSyncing: false,
  lastSyncedAt: null,
  pendingItems: 0,
  error: null,
};

// Current sync status
let syncStatus: SyncStatus = { ...initialSyncStatus };

// Listeners for sync status changes
const listeners: ((status: SyncStatus) => void)[] = [];

// Function to subscribe to sync status changes
export function subscribeSyncStatus(listener: (status: SyncStatus) => void): () => void {
  listeners.push(listener);

  // Return unsubscribe function
  return () => {
    const index = listeners.indexOf(listener);
    if (index !== -1) {
      listeners.splice(index, 1);
    }
  };
}

// Function to notify listeners of sync status changes
function notifySyncStatusChange() {
  listeners.forEach(listener => listener({ ...syncStatus }));
}

// Function to update sync status
function updateSyncStatus(update: Partial<SyncStatus>) {
  syncStatus = { ...syncStatus, ...update };
  notifySyncStatusChange();
}

// Function to check if device is online
async function isOnline(): Promise<boolean> {
  const netInfo = await NetInfo.fetch();
  return netInfo.isConnected === true;
}

// Function to process a single sync queue item
async function processSyncItem(item: SyncQueue): Promise<boolean> {
  try {
    let response;

    switch (item.entityType) {
      case 'Transaction':
        const transactionData = JSON.parse(item.entityData);

        if (item.action === 'Create') {
          // Check if we can batch this with other pending transactions
          const pendingItems = await dbService.getPendingSyncItems();
          const pendingCreateTransactions = pendingItems.filter(
            i => i.entityType === 'Transaction' && i.action === 'Create' && i.id !== item.id
          );

          if (pendingCreateTransactions.length > 0) {
            // Batch create transactions
            const batchData = {
              transactions: [
                transactionData,
                ...pendingCreateTransactions.map(i => JSON.parse(i.entityData))
              ]
            };

            response = await apiService.transactions.batchCreateTransactions(batchData as any);

            // Mark all batched items as synced
            if (response && response.status >= 200 && response.status < 300) {
              for (const pendingItem of pendingCreateTransactions) {
                await dbService.updateSyncItemStatus(pendingItem.id!, 'Synced');

                // Update transaction sync status in database
                const transaction = await dbService.getTransaction(pendingItem.entityId);
                if (transaction) {
                  transaction.isSynced = true;
                  transaction.syncStatus = 'Synced';
                  await dbService.saveTransaction(transaction as any);
                }
              }
            }
          } else {
            // Single transaction create
            response = await apiService.transactions.createTransaction(transactionData);
          }
        } else if (item.action === 'Update') {
          // Check if we can batch this with other pending transactions
          const pendingItems = await dbService.getPendingSyncItems();
          const pendingUpdateTransactions = pendingItems.filter(
            i => i.entityType === 'Transaction' && i.action === 'Update' && i.id !== item.id
          );

          if (pendingUpdateTransactions.length > 0) {
            // Batch update transactions
            const batchData = {
              transactions: [
                { ...transactionData, id: item.entityId },
                ...pendingUpdateTransactions.map(i => {
                  const data = JSON.parse(i.entityData);
                  return { ...data, id: i.entityId };
                })
              ]
            };

            response = await apiService.transactions.batchUpdateTransactions(batchData as any);

            // Mark all batched items as synced
            if (response && response.status >= 200 && response.status < 300) {
              for (const pendingItem of pendingUpdateTransactions) {
                await dbService.updateSyncItemStatus(pendingItem.id!, 'Synced');

                // Update transaction sync status in database
                const transaction = await dbService.getTransaction(pendingItem.entityId);
                if (transaction) {
                  transaction.isSynced = true;
                  transaction.syncStatus = 'Synced';
                  await dbService.saveTransaction(transaction as any);
                }
              }
            }
          } else {
            // Single transaction update
            response = await apiService.transactions.updateTransaction(item.entityId, transactionData);
          }
        } else if (item.action === 'Delete') {
          // Check if we can batch this with other pending transactions
          const pendingItems = await dbService.getPendingSyncItems();
          const pendingDeleteTransactions = pendingItems.filter(
            i => i.entityType === 'Transaction' && i.action === 'Delete' && i.id !== item.id
          );

          if (pendingDeleteTransactions.length > 0) {
            // Batch delete transactions
            const transactionIds = [
              item.entityId,
              ...pendingDeleteTransactions.map(i => i.entityId)
            ];

            response = await apiService.transactions.batchDeleteTransactions(transactionIds);

            // Mark all batched items as synced
            if (response && response.status >= 200 && response.status < 300) {
              for (const pendingItem of pendingDeleteTransactions) {
                await dbService.updateSyncItemStatus(pendingItem.id!, 'Synced');
              }
            }
          } else {
            // Single transaction delete
            response = await apiService.transactions.deleteTransaction(item.entityId);
          }
        }
        break;

      case 'Account':
        const accountData = JSON.parse(item.entityData);

        if (item.action === 'Create') {
          response = await apiService.accounts.createAccount(accountData);
        } else if (item.action === 'Update') {
          response = await apiService.accounts.updateAccount(item.entityId, accountData);
        } else if (item.action === 'Delete') {
          response = await apiService.accounts.deleteAccount(item.entityId);
        }
        break;

      case 'Category':
        const categoryData = JSON.parse(item.entityData);

        if (item.action === 'Create') {
          response = await apiService.categories.createCategory(categoryData);
        } else if (item.action === 'Update') {
          response = await apiService.categories.updateCategory(item.entityId, categoryData);
        } else if (item.action === 'Delete') {
          response = await apiService.categories.deleteCategory(item.entityId);
        }
        break;
    }

    // If response is successful, mark item as synced
    if (response && response.status >= 200 && response.status < 300) {
      await dbService.updateSyncItemStatus(item.id!, 'Synced');

      // If it's a transaction, update the transaction in the database
      if (item.entityType === 'Transaction' && item.action !== 'Delete') {
        const transaction = await dbService.getTransaction(item.entityId);
        if (transaction) {
          transaction.isSynced = true;
          transaction.syncStatus = 'Synced';
          await dbService.saveTransaction(transaction as any);
        }
      }

      return true;
    } else {
      // If response is not successful, mark item as failed
      const errorMessage = response ? response.error : 'Unknown error';
      await dbService.updateSyncItemStatus(item.id!, 'Failed', errorMessage);
      return false;
    }
  } catch (error) {
    // If an error occurs, mark item as failed
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    await dbService.updateSyncItemStatus(item.id!, 'Failed', errorMessage);
    return false;
  }
}

// Function to sync data with the server
export async function syncData(): Promise<boolean> {
  // Check if already syncing
  if (syncStatus.isSyncing) {
    return false;
  }

  // Check if device is online
  if (!(await isOnline())) {
    updateSyncStatus({
      error: 'Device is offline',
    });
    return false;
  }

  try {
    // Update sync status
    updateSyncStatus({
      isSyncing: true,
      error: null,
    });

    // Get pending sync items
    const pendingItems = await dbService.getPendingSyncItems();
    updateSyncStatus({
      pendingItems: pendingItems.length,
    });

    // If no pending items, update last synced time and return
    if (pendingItems.length === 0) {
      updateSyncStatus({
        isSyncing: false,
        lastSyncedAt: new Date(),
        pendingItems: 0,
      });
      return true;
    }

    // Process each pending item
    let success = true;
    for (const item of pendingItems) {
      // Skip items that have been retried too many times
      if (item.retryCount >= MAX_RETRY_COUNT) {
        continue;
      }

      // Process item
      const result = await processSyncItem(item);
      if (!result) {
        success = false;
      }

      // Update pending items count
      updateSyncStatus({
        pendingItems: await dbService.getPendingSyncItems().then(items => items.length),
      });
    }

    // Update sync status
    updateSyncStatus({
      isSyncing: false,
      lastSyncedAt: new Date(),
      error: success ? null : 'Some items failed to sync',
    });

    return success;
  } catch (error) {
    // Update sync status
    updateSyncStatus({
      isSyncing: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return false;
  }
}

// Function to get current sync status
export function getSyncStatus(): SyncStatus {
  return { ...syncStatus };
}

// Export sync service
const syncService = {
  syncData,
  getSyncStatus,
  subscribeSyncStatus,
};

export default syncService;
