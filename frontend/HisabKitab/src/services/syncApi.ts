import { fetchWithAuth, ApiResponse } from './api';
import { SyncQueue } from './db';

/**
 * Sync API service interface
 */
export interface SyncService {
  syncBatch(batch: SyncQueue[]): Promise<ApiResponse<SyncBatchResponse>>;
  getLastSyncTime(): Promise<ApiResponse<{ lastSyncTime: string }>>;
  getChanges(since: string): Promise<ApiResponse<SyncChangesResponse>>;
}

/**
 * Sync batch response interface
 */
export interface SyncBatchResponse {
  processed: Array<{ id: number; entityType: string; entityId: number }>;
  conflicts: Array<{
    entityType: string;
    entityId: number;
    localData: any;
    remoteData: any;
  }>;
}

/**
 * Sync changes response interface
 */
export interface SyncChangesResponse {
  changes: Array<{
    entityType: string;
    entityId: number;
    action: string;
    entityData: any;
    timestamp: string;
  }>;
  hasMore: boolean;
  nextCursor?: string;
}

/**
 * Sync API implementation
 */
const syncService: SyncService = {
  async syncBatch(batch: SyncQueue[]): Promise<ApiResponse<SyncBatchResponse>> {
    return await fetchWithAuth<SyncBatchResponse>('sync/batch', 'POST', batch);
  },

  async getLastSyncTime(): Promise<ApiResponse<{ lastSyncTime: string }>> {
    return await fetchWithAuth<{ lastSyncTime: string }>('sync/lastSyncTime');
  },

  async getChanges(since: string): Promise<ApiResponse<SyncChangesResponse>> {
    return await fetchWithAuth<SyncChangesResponse>(`sync/changes?since=${encodeURIComponent(since)}`);
  }
};

export default syncService;
