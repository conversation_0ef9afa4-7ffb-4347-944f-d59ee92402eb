import { fetchWithAuth, ApiResponse } from './api';
import {
  SavingsGoal,
  SavingsContribution,
  CreateSavingsGoalRequest,
  UpdateSavingsGoalRequest,
  CreateContributionRequest,
  UpdateContributionRequest,
  SavingsGoalForecast
} from '../models/SavingsGoal';
import {
  WishlistItem,
  CreateWishlistItemRequest,
  UpdateWishlistItemRequest
} from '../models/WishlistItem';

// Define the savings service interface
export interface SavingsService {
  // Savings Goals
  getAllSavingsGoals(): Promise<ApiResponse<SavingsGoal[]>>;
  getSavingsGoal(id: number): Promise<ApiResponse<SavingsGoal>>;
  createSavingsGoal(goalData: CreateSavingsGoalRequest): Promise<ApiResponse<SavingsGoal>>;
  updateSavingsGoal(id: number, goalData: UpdateSavingsGoalRequest): Promise<ApiResponse<SavingsGoal>>;
  deleteSavingsGoal(id: number): Promise<ApiResponse<any>>;

  // Contributions
  getContributions(goalId: number): Promise<ApiResponse<SavingsContribution[]>>;
  getContribution(id: number): Promise<ApiResponse<SavingsContribution>>;
  addContribution(contributionData: CreateContributionRequest): Promise<ApiResponse<SavingsContribution>>;
  updateContribution(id: number, contributionData: UpdateContributionRequest): Promise<ApiResponse<SavingsContribution>>;
  deleteContribution(id: number): Promise<ApiResponse<any>>;

  // Forecasting
  getSavingsGoalForecast(goalId: number): Promise<ApiResponse<SavingsGoalForecast>>;

  // Wishlist Items
  getAllWishlistItems(): Promise<ApiResponse<WishlistItem[]>>;
  getWishlistItem(id: number): Promise<ApiResponse<WishlistItem>>;
  createWishlistItem(itemData: CreateWishlistItemRequest): Promise<ApiResponse<WishlistItem>>;
  updateWishlistItem(id: number, itemData: UpdateWishlistItemRequest): Promise<ApiResponse<WishlistItem>>;
  deleteWishlistItem(id: number): Promise<ApiResponse<any>>;

  // Link/Unlink
  linkWishlistItemToGoal(wishlistItemId: number, goalId: number): Promise<ApiResponse<any>>;
  unlinkWishlistItemFromGoal(wishlistItemId: number): Promise<ApiResponse<any>>;
}

// Implement the savings service
const savingsService: SavingsService = {
  // Savings Goals
  async getAllSavingsGoals(): Promise<ApiResponse<SavingsGoal[]>> {
    return await fetchWithAuth<SavingsGoal[]>('savings-goals');
  },

  async getSavingsGoal(id: number): Promise<ApiResponse<SavingsGoal>> {
    return await fetchWithAuth<SavingsGoal>(`savings-goals/${id}`);
  },

  async createSavingsGoal(goalData: CreateSavingsGoalRequest): Promise<ApiResponse<SavingsGoal>> {
    return await fetchWithAuth<SavingsGoal>('savings-goals', 'POST', goalData);
  },

  async updateSavingsGoal(id: number, goalData: UpdateSavingsGoalRequest): Promise<ApiResponse<SavingsGoal>> {
    return await fetchWithAuth<SavingsGoal>(`savings-goals/${id}`, 'PUT', goalData);
  },

  async deleteSavingsGoal(id: number): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`savings-goals/${id}`, 'DELETE');
  },

  // Contributions
  async getContributions(goalId: number): Promise<ApiResponse<SavingsContribution[]>> {
    return await fetchWithAuth<SavingsContribution[]>(`savings-goals/${goalId}/contributions`);
  },

  async getContribution(id: number): Promise<ApiResponse<SavingsContribution>> {
    return await fetchWithAuth<SavingsContribution>(`savings-contributions/${id}`);
  },

  async addContribution(contributionData: CreateContributionRequest): Promise<ApiResponse<SavingsContribution>> {
    return await fetchWithAuth<SavingsContribution>('savings-contributions', 'POST', contributionData);
  },

  async updateContribution(id: number, contributionData: UpdateContributionRequest): Promise<ApiResponse<SavingsContribution>> {
    return await fetchWithAuth<SavingsContribution>(`savings-contributions/${id}`, 'PUT', contributionData);
  },

  async deleteContribution(id: number): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`savings-contributions/${id}`, 'DELETE');
  },

  // Forecasting
  async getSavingsGoalForecast(goalId: number): Promise<ApiResponse<SavingsGoalForecast>> {
    return await fetchWithAuth<SavingsGoalForecast>(`savings-goals/${goalId}/forecast`);
  },

  // Wishlist Items
  async getAllWishlistItems(): Promise<ApiResponse<WishlistItem[]>> {
    return await fetchWithAuth<WishlistItem[]>('wishlist-items');
  },

  async getWishlistItem(id: number): Promise<ApiResponse<WishlistItem>> {
    return await fetchWithAuth<WishlistItem>(`wishlist-items/${id}`);
  },

  async createWishlistItem(itemData: CreateWishlistItemRequest): Promise<ApiResponse<WishlistItem>> {
    return await fetchWithAuth<WishlistItem>('wishlist-items', 'POST', itemData);
  },

  async updateWishlistItem(id: number, itemData: UpdateWishlistItemRequest): Promise<ApiResponse<WishlistItem>> {
    return await fetchWithAuth<WishlistItem>(`wishlist-items/${id}`, 'PUT', itemData);
  },

  async deleteWishlistItem(id: number): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`wishlist-items/${id}`, 'DELETE');
  },

  // Link/Unlink
  async linkWishlistItemToGoal(wishlistItemId: number, goalId: number): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`wishlist-items/${wishlistItemId}/link/${goalId}`, 'POST');
  },

  async unlinkWishlistItemFromGoal(wishlistItemId: number): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`wishlist-items/${wishlistItemId}/unlink`, 'POST');
  },
};

export default savingsService;
