import AsyncStorage from '@react-native-async-storage/async-storage';

// Define interfaces for database tables
export interface FamilyMembership {
  familyId: number;
  familyName?: string;
  role: string;
  joinedAt?: Date;
}

export interface User {
  id?: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  language: string;
  roles?: string[];
  isPlatformAdmin?: boolean;
  isFamilyAdmin?: boolean;
  familyMemberships?: FamilyMembership[];
  lastSyncedAt?: Date;
}

export interface Family {
  id: number;
  name: string;
  description?: string;
  inviteCode?: string;
  settings?: string;
  createdByUserId: number;
  createdByUsername?: string;
  createdAt: Date;
  updatedAt: Date;
  members?: FamilyMember[];
  isSynced?: boolean;
}

export interface FamilyMember {
  userId: number;
  username?: string;
  firstName?: string;
  lastName?: string;
  role: string;
  permissions?: string;
  joinedAt: Date;
  isActive: boolean;
}

export interface Account {
  id?: number;
  name: string;
  accountType: string;
  balance: number;
  initialBalance: number;
  currency: string;
  isActive: boolean;
  excludeFromStats: boolean;
  lastUpdatedAt: Date;
  isSynced: boolean;
  familyId?: number;
}

export interface Category {
  id?: number;
  name: string;
  type: string;
  icon?: string;
  color?: string;
  parentCategoryId?: number;
  isSystem: boolean;
  isSynced: boolean;
}

export interface TransactionItem {
  id?: number;
  transactionId: number;
  name: string;
  amount: number;
  quantity: number;
  categoryId?: number;
  notes?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface Transaction {
  id?: number;
  amount: number;
  description?: string;
  date: Date;
  type: string;
  accountId: number;
  toAccountId?: number;
  categoryId: number;
  priorityId?: number;
  userId?: number;
  status: string;
  approvedByUserId?: number;
  receiptImage?: string;
  tags?: string;
  location?: string;
  exchangeRate?: number;
  recurringTransactionId?: number;
  isSynced: boolean;
  syncStatus: string;
  lastUpdatedAt: Date;
  createdAt?: Date;
  updatedAt?: Date;
  items?: TransactionItem[];
}

export interface SyncQueue {
  id?: number;
  entityType: string;
  entityId: number;
  action: string;
  entityData: string;
  status: string;
  retryCount: number;
  errorMessage?: string;
  createdAt: Date;
}

export interface AuthToken {
  id?: number;
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
}

export interface Receipt {
  id: number;
  transactionId?: number;
  merchantName?: string;
  date?: string;
  totalAmount: number;
  items: any[];
  rawText: string;
  imageUri?: string;
  language?: string;
  isProcessed: boolean;
  confidence?: number;
  createdAt?: string;
  updatedAt?: string;
  isDeleted?: boolean;
}

export interface SyncConflict {
  id: number;
  entityType: string;
  entityId: number;
  localData: any;
  remoteData: any;
  resolved: boolean;
  resolution?: 'local' | 'remote' | 'merge';
  createdAt: Date;
}

// Storage keys
const STORAGE_KEYS = {
  USERS: '@hisabkitab/users',
  FAMILIES: '@hisabkitab/families',
  FAMILY_MEMBERS: '@hisabkitab/familyMembers',
  ACCOUNTS: '@hisabkitab/accounts',
  CATEGORIES: '@hisabkitab/categories',
  TRANSACTIONS: '@hisabkitab/transactions',
  RECEIPTS: '@hisabkitab/receipts',
  SYNC_QUEUE: '@hisabkitab/syncQueue',
  AUTH_TOKENS: '@hisabkitab/authTokens',
  COUNTERS: '@hisabkitab/counters',
  SYNC_STATUS: '@hisabkitab/syncStatus',
  CONFLICT_LOGS: '@hisabkitab/conflictLogs',
  FEATURE_FLAGS: '@hisabkitab/featureFlags',
  NOTIFICATIONS: '@hisabkitab/notifications',
  NOTIFICATION_SETTINGS: '@hisabkitab/notificationSettings',
};

// Helper functions for database operations
export const dbService = {
  // ID generation
  async getNextId(entityType: string): Promise<number> {
    try {
      const countersStr = await AsyncStorage.getItem(STORAGE_KEYS.COUNTERS);
      const counters = countersStr ? JSON.parse(countersStr) : {};

      const currentId = counters[entityType] || 0;
      const nextId = currentId + 1;

      counters[entityType] = nextId;
      await AsyncStorage.setItem(STORAGE_KEYS.COUNTERS, JSON.stringify(counters));

      return nextId;
    } catch (error) {
      console.error(`Error generating ID for ${entityType}:`, error);
      return Date.now(); // Fallback to timestamp
    }
  },

  // User operations
  async getCurrentUser(): Promise<User | undefined> {
    try {
      const usersStr = await AsyncStorage.getItem(STORAGE_KEYS.USERS);
      const users = usersStr ? JSON.parse(usersStr) : [];
      return users.length > 0 ? users[0] : undefined;
    } catch (error) {
      console.error('Error getting current user:', error);
      return undefined;
    }
  },

  async saveUser(user: User): Promise<number> {
    try {
      if (!user.id) {
        user.id = await this.getNextId('user');
      }

      const usersStr = await AsyncStorage.getItem(STORAGE_KEYS.USERS);
      const users = usersStr ? JSON.parse(usersStr) : [];

      const existingIndex = users.findIndex((u: User) => u.id === user.id);
      if (existingIndex >= 0) {
        users[existingIndex] = user;
      } else {
        users.push(user);
      }

      await AsyncStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users));
      return user.id;
    } catch (error) {
      console.error('Error saving user:', error);
      return -1;
    }
  },

  // Family operations
  async getFamilies(): Promise<Family[]> {
    try {
      const familiesStr = await AsyncStorage.getItem(STORAGE_KEYS.FAMILIES);
      return familiesStr ? JSON.parse(familiesStr) : [];
    } catch (error) {
      console.error('Error getting families:', error);
      return [];
    }
  },

  async getFamily(id: number): Promise<Family | undefined> {
    try {
      const familiesStr = await AsyncStorage.getItem(STORAGE_KEYS.FAMILIES);
      const families = familiesStr ? JSON.parse(familiesStr) : [];
      return families.find((f: Family) => f.id === id);
    } catch (error) {
      console.error(`Error getting family ${id}:`, error);
      return undefined;
    }
  },

  async saveFamily(family: Family): Promise<number> {
    try {
      const familiesStr = await AsyncStorage.getItem(STORAGE_KEYS.FAMILIES);
      const families = familiesStr ? JSON.parse(familiesStr) : [];

      const existingIndex = families.findIndex((f: Family) => f.id === family.id);
      if (existingIndex >= 0) {
        families[existingIndex] = family;
      } else {
        families.push(family);
      }

      await AsyncStorage.setItem(STORAGE_KEYS.FAMILIES, JSON.stringify(families));
      return family.id;
    } catch (error) {
      console.error('Error saving family:', error);
      return -1;
    }
  },

  async deleteFamily(id: number): Promise<boolean> {
    try {
      const familiesStr = await AsyncStorage.getItem(STORAGE_KEYS.FAMILIES);
      const families = familiesStr ? JSON.parse(familiesStr) : [];

      const updatedFamilies = families.filter((f: Family) => f.id !== id);
      await AsyncStorage.setItem(STORAGE_KEYS.FAMILIES, JSON.stringify(updatedFamilies));

      // Also delete family members
      const familyMembersStr = await AsyncStorage.getItem(STORAGE_KEYS.FAMILY_MEMBERS);
      const familyMembers = familyMembersStr ? JSON.parse(familyMembersStr) : [];
      const updatedFamilyMembers = familyMembers.filter((fm: any) => fm.familyId !== id);
      await AsyncStorage.setItem(STORAGE_KEYS.FAMILY_MEMBERS, JSON.stringify(updatedFamilyMembers));

      return true;
    } catch (error) {
      console.error(`Error deleting family ${id}:`, error);
      return false;
    }
  },

  async getFamilyMembers(familyId: number): Promise<FamilyMember[]> {
    try {
      const familyMembersStr = await AsyncStorage.getItem(STORAGE_KEYS.FAMILY_MEMBERS);
      const familyMembers = familyMembersStr ? JSON.parse(familyMembersStr) : [];
      return familyMembers.filter((fm: any) => fm.familyId === familyId);
    } catch (error) {
      console.error(`Error getting family members for family ${familyId}:`, error);
      return [];
    }
  },

  async saveFamilyMember(familyId: number, member: FamilyMember): Promise<boolean> {
    try {
      const familyMembersStr = await AsyncStorage.getItem(STORAGE_KEYS.FAMILY_MEMBERS);
      const familyMembers = familyMembersStr ? JSON.parse(familyMembersStr) : [];

      const existingIndex = familyMembers.findIndex(
        (fm: any) => fm.familyId === familyId && fm.userId === member.userId
      );

      const memberToSave = { ...member, familyId };

      if (existingIndex >= 0) {
        familyMembers[existingIndex] = memberToSave;
      } else {
        familyMembers.push(memberToSave);
      }

      await AsyncStorage.setItem(STORAGE_KEYS.FAMILY_MEMBERS, JSON.stringify(familyMembers));
      return true;
    } catch (error) {
      console.error('Error saving family member:', error);
      return false;
    }
  },

  async removeFamilyMember(familyId: number, userId: number): Promise<boolean> {
    try {
      const familyMembersStr = await AsyncStorage.getItem(STORAGE_KEYS.FAMILY_MEMBERS);
      const familyMembers = familyMembersStr ? JSON.parse(familyMembersStr) : [];

      const updatedFamilyMembers = familyMembers.filter(
        (fm: any) => !(fm.familyId === familyId && fm.userId === userId)
      );

      await AsyncStorage.setItem(STORAGE_KEYS.FAMILY_MEMBERS, JSON.stringify(updatedFamilyMembers));
      return true;
    } catch (error) {
      console.error(`Error removing family member (family: ${familyId}, user: ${userId}):`, error);
      return false;
    }
  },

  // Account operations
  async getAccounts(): Promise<Account[]> {
    try {
      const accountsStr = await AsyncStorage.getItem(STORAGE_KEYS.ACCOUNTS);
      return accountsStr ? JSON.parse(accountsStr) : [];
    } catch (error) {
      console.error('Error getting accounts:', error);
      return [];
    }
  },

  async getAccount(id: number): Promise<Account | undefined> {
    try {
      const accountsStr = await AsyncStorage.getItem(STORAGE_KEYS.ACCOUNTS);
      const accounts = accountsStr ? JSON.parse(accountsStr) : [];
      return accounts.find((a: Account) => a.id === id);
    } catch (error) {
      console.error(`Error getting account ${id}:`, error);
      return undefined;
    }
  },

  async saveAccount(account: Account): Promise<number> {
    try {
      if (!account.id) {
        account.id = await this.getNextId('account');
      }

      const accountsStr = await AsyncStorage.getItem(STORAGE_KEYS.ACCOUNTS);
      const accounts = accountsStr ? JSON.parse(accountsStr) : [];

      const existingIndex = accounts.findIndex((a: Account) => a.id === account.id);
      if (existingIndex >= 0) {
        accounts[existingIndex] = account;
      } else {
        accounts.push(account);
      }

      await AsyncStorage.setItem(STORAGE_KEYS.ACCOUNTS, JSON.stringify(accounts));
      return account.id;
    } catch (error) {
      console.error('Error saving account:', error);
      return -1;
    }
  },

  async deleteAccount(id: number): Promise<void> {
    try {
      const accountsStr = await AsyncStorage.getItem(STORAGE_KEYS.ACCOUNTS);
      const accounts = accountsStr ? JSON.parse(accountsStr) : [];

      const filteredAccounts = accounts.filter((a: Account) => a.id !== id);
      await AsyncStorage.setItem(STORAGE_KEYS.ACCOUNTS, JSON.stringify(filteredAccounts));
    } catch (error) {
      console.error(`Error deleting account ${id}:`, error);
    }
  },

  // Category operations
  async getCategories(): Promise<Category[]> {
    try {
      const categoriesStr = await AsyncStorage.getItem(STORAGE_KEYS.CATEGORIES);
      return categoriesStr ? JSON.parse(categoriesStr) : [];
    } catch (error) {
      console.error('Error getting categories:', error);
      return [];
    }
  },

  async getCategory(id: number): Promise<Category | undefined> {
    try {
      const categoriesStr = await AsyncStorage.getItem(STORAGE_KEYS.CATEGORIES);
      const categories = categoriesStr ? JSON.parse(categoriesStr) : [];
      return categories.find((c: Category) => c.id === id);
    } catch (error) {
      console.error(`Error getting category ${id}:`, error);
      return undefined;
    }
  },

  async saveCategory(category: Category): Promise<number> {
    try {
      if (!category.id) {
        category.id = await this.getNextId('category');
      }

      const categoriesStr = await AsyncStorage.getItem(STORAGE_KEYS.CATEGORIES);
      const categories = categoriesStr ? JSON.parse(categoriesStr) : [];

      const existingIndex = categories.findIndex((c: Category) => c.id === category.id);
      if (existingIndex >= 0) {
        categories[existingIndex] = category;
      } else {
        categories.push(category);
      }

      await AsyncStorage.setItem(STORAGE_KEYS.CATEGORIES, JSON.stringify(categories));
      return category.id;
    } catch (error) {
      console.error('Error saving category:', error);
      return -1;
    }
  },

  async deleteCategory(id: number): Promise<void> {
    try {
      const categoriesStr = await AsyncStorage.getItem(STORAGE_KEYS.CATEGORIES);
      const categories = categoriesStr ? JSON.parse(categoriesStr) : [];

      const filteredCategories = categories.filter((c: Category) => c.id !== id);
      await AsyncStorage.setItem(STORAGE_KEYS.CATEGORIES, JSON.stringify(filteredCategories));
    } catch (error) {
      console.error(`Error deleting category ${id}:`, error);
    }
  },

  // Transaction operations
  async getTransactions(filters?: any): Promise<Transaction[]> {
    try {
      const transactionsStr = await AsyncStorage.getItem(STORAGE_KEYS.TRANSACTIONS);
      let transactions = transactionsStr ? JSON.parse(transactionsStr) : [];

      // Apply filters if provided
      if (filters) {
        if (filters.startDate) {
          const startDate = new Date(filters.startDate);
          transactions = transactions.filter((t: any) => new Date(t.date) >= startDate);
        }

        if (filters.endDate) {
          const endDate = new Date(filters.endDate);
          transactions = transactions.filter((t: any) => new Date(t.date) <= endDate);
        }

        if (filters.type !== undefined) {
          transactions = transactions.filter((t: any) => t.type === filters.type);
        }

        if (filters.accountId) {
          transactions = transactions.filter((t: any) =>
            t.accountId === filters.accountId || t.toAccountId === filters.accountId
          );
        }

        if (filters.categoryId) {
          transactions = transactions.filter((t: any) =>
            t.categoryId === filters.categoryId ||
            (t.items && t.items.some((item: any) => item.categoryId === filters.categoryId))
          );
        }

        if (filters.priorityId) {
          transactions = transactions.filter((t: any) => t.priorityId === filters.priorityId);
        }

        if (filters.status) {
          transactions = transactions.filter((t: any) => t.status === filters.status);
        }

        if (filters.searchTerm) {
          const searchTerm = filters.searchTerm.toLowerCase();
          transactions = transactions.filter((t: any) =>
            (t.description && t.description.toLowerCase().includes(searchTerm)) ||
            (t.tags && t.tags.toLowerCase().includes(searchTerm)) ||
            (t.location && t.location.toLowerCase().includes(searchTerm)) ||
            (t.items && t.items.some((item: any) =>
              (item.name && item.name.toLowerCase().includes(searchTerm)) ||
              (item.notes && item.notes.toLowerCase().includes(searchTerm))
            ))
          );
        }

        // Apply sorting
        if (filters.sortBy) {
          const sortField = filters.sortBy;
          const sortDirection = filters.ascending ? 1 : -1;

          transactions.sort((a: any, b: any) => {
            let aValue = a[sortField];
            let bValue = b[sortField];

            // Handle date fields
            if (sortField === 'date' || sortField === 'createdAt' || sortField === 'updatedAt' || sortField === 'lastUpdatedAt') {
              aValue = aValue ? new Date(aValue).getTime() : 0;
              bValue = bValue ? new Date(bValue).getTime() : 0;
            }

            if (aValue < bValue) return -1 * sortDirection;
            if (aValue > bValue) return 1 * sortDirection;
            return 0;
          });
        } else {
          // Default sort by date descending
          transactions.sort((a: any, b: any) => {
            const aDate = new Date(a.date).getTime();
            const bDate = new Date(b.date).getTime();
            return bDate - aDate;
          });
        }

        // Apply pagination
        if (filters.skip && filters.take) {
          transactions = transactions.slice(filters.skip, filters.skip + filters.take);
        } else if (filters.take) {
          transactions = transactions.slice(0, filters.take);
        }
      }

      // Convert date strings back to Date objects
      return transactions.map((t: any) => ({
        ...t,
        date: new Date(t.date),
        lastUpdatedAt: new Date(t.lastUpdatedAt),
        createdAt: t.createdAt ? new Date(t.createdAt) : undefined,
        updatedAt: t.updatedAt ? new Date(t.updatedAt) : undefined,
        items: t.items ? t.items.map((item: any) => ({
          ...item,
          createdAt: item.createdAt ? new Date(item.createdAt) : undefined,
          updatedAt: item.updatedAt ? new Date(item.updatedAt) : undefined
        })) : []
      }));
    } catch (error) {
      console.error('Error getting transactions:', error);
      return [];
    }
  },

  async getTransaction(id: number): Promise<Transaction | undefined> {
    try {
      const transactionsStr = await AsyncStorage.getItem(STORAGE_KEYS.TRANSACTIONS);
      const transactions = transactionsStr ? JSON.parse(transactionsStr) : [];
      const transaction = transactions.find((t: Transaction) => t.id === id);

      if (transaction) {
        // Convert date strings back to Date objects
        return {
          ...transaction,
          date: new Date(transaction.date),
          lastUpdatedAt: new Date(transaction.lastUpdatedAt),
          createdAt: transaction.createdAt ? new Date(transaction.createdAt) : undefined,
          updatedAt: transaction.updatedAt ? new Date(transaction.updatedAt) : undefined,
          items: transaction.items ? transaction.items.map((item: any) => ({
            ...item,
            createdAt: item.createdAt ? new Date(item.createdAt) : undefined,
            updatedAt: item.updatedAt ? new Date(item.updatedAt) : undefined
          })) : []
        };
      }

      return undefined;
    } catch (error) {
      console.error(`Error getting transaction ${id}:`, error);
      return undefined;
    }
  },

  async saveTransaction(transaction: Transaction): Promise<number> {
    try {
      if (!transaction.id) {
        transaction.id = await this.getNextId('transaction');
        transaction.createdAt = new Date();
      }

      // Ensure lastUpdatedAt is set
      transaction.lastUpdatedAt = new Date();
      transaction.updatedAt = new Date();

      // Process transaction items
      if (transaction.items && transaction.items.length > 0) {
        transaction.items = await Promise.all(transaction.items.map(async (item) => {
          if (!item.id) {
            item.id = await this.getNextId('transactionItem');
            item.createdAt = new Date();
          }
          item.transactionId = transaction.id!;
          item.updatedAt = new Date();
          return item;
        }));
      } else {
        transaction.items = [];
      }

      const transactionsStr = await AsyncStorage.getItem(STORAGE_KEYS.TRANSACTIONS);
      const transactions = transactionsStr ? JSON.parse(transactionsStr) : [];

      const existingIndex = transactions.findIndex((t: Transaction) => t.id === transaction.id);
      if (existingIndex >= 0) {
        transactions[existingIndex] = transaction;
      } else {
        transactions.push(transaction);
      }

      await AsyncStorage.setItem(STORAGE_KEYS.TRANSACTIONS, JSON.stringify(transactions));

      // If not synced, add to sync queue
      if (!transaction.isSynced) {
        await this.addToSyncQueue({
          entityType: 'Transaction',
          entityId: transaction.id,
          action: existingIndex >= 0 ? 'Update' : 'Create',
          entityData: JSON.stringify(transaction),
          status: 'Pending',
          retryCount: 0,
          createdAt: new Date()
        });
      }

      return transaction.id;
    } catch (error) {
      console.error('Error saving transaction:', error);
      return -1;
    }
  },

  async deleteTransaction(id: number): Promise<void> {
    try {
      const transactionsStr = await AsyncStorage.getItem(STORAGE_KEYS.TRANSACTIONS);
      const transactions = transactionsStr ? JSON.parse(transactionsStr) : [];

      const filteredTransactions = transactions.filter((t: Transaction) => t.id !== id);
      await AsyncStorage.setItem(STORAGE_KEYS.TRANSACTIONS, JSON.stringify(filteredTransactions));

      // Add to sync queue
      await this.addToSyncQueue({
        entityType: 'Transaction',
        entityId: id,
        action: 'Delete',
        entityData: JSON.stringify({ id }),
        status: 'Pending',
        retryCount: 0,
        createdAt: new Date()
      });
    } catch (error) {
      console.error(`Error deleting transaction ${id}:`, error);
    }
  },

  async getTransactionsByAccount(accountId: number): Promise<Transaction[]> {
    try {
      const transactions = await this.getTransactions();
      return transactions.filter(t => t.accountId === accountId || t.toAccountId === accountId);
    } catch (error) {
      console.error(`Error getting transactions for account ${accountId}:`, error);
      return [];
    }
  },

  async batchSaveTransactions(transactions: Transaction[]): Promise<number[]> {
    try {
      const savedIds: number[] = [];

      for (const transaction of transactions) {
        const id = await this.saveTransaction(transaction);
        if (id > 0) {
          savedIds.push(id);
        }
      }

      return savedIds;
    } catch (error) {
      console.error('Error batch saving transactions:', error);
      return [];
    }
  },

  async batchDeleteTransactions(ids: number[]): Promise<void> {
    try {
      for (const id of ids) {
        await this.deleteTransaction(id);
      }
    } catch (error) {
      console.error('Error batch deleting transactions:', error);
    }
  },

  // Receipt operations
  async getReceipts(filter?: any): Promise<Receipt[]> {
    try {
      const receiptsStr = await AsyncStorage.getItem(STORAGE_KEYS.RECEIPTS);
      let receipts = receiptsStr ? JSON.parse(receiptsStr) : [];

      // Filter out deleted receipts
      receipts = receipts.filter((r: Receipt) => !r.isDeleted);

      // Apply filters if provided
      if (filter) {
        if (filter.startDate) {
          receipts = receipts.filter((r: Receipt) =>
            r.date && new Date(r.date) >= new Date(filter.startDate)
          );
        }

        if (filter.endDate) {
          receipts = receipts.filter((r: Receipt) =>
            r.date && new Date(r.date) <= new Date(filter.endDate)
          );
        }

        if (filter.minAmount !== undefined) {
          receipts = receipts.filter((r: Receipt) =>
            r.totalAmount >= filter.minAmount
          );
        }

        if (filter.maxAmount !== undefined) {
          receipts = receipts.filter((r: Receipt) =>
            r.totalAmount <= filter.maxAmount
          );
        }

        if (filter.merchantName) {
          const searchTerm = filter.merchantName.toLowerCase();
          receipts = receipts.filter((r: Receipt) =>
            r.merchantName && r.merchantName.toLowerCase().includes(searchTerm)
          );
        }

        if (filter.isProcessed !== undefined) {
          receipts = receipts.filter((r: Receipt) =>
            r.isProcessed === filter.isProcessed
          );
        }

        if (filter.transactionId !== undefined) {
          receipts = receipts.filter((r: Receipt) =>
            r.transactionId === filter.transactionId
          );
        }
      }

      return receipts;
    } catch (error) {
      console.error('Error getting receipts:', error);
      return [];
    }
  },

  async getReceipt(id: number): Promise<Receipt | undefined> {
    try {
      const receiptsStr = await AsyncStorage.getItem(STORAGE_KEYS.RECEIPTS);
      const receipts = receiptsStr ? JSON.parse(receiptsStr) : [];
      return receipts.find((r: Receipt) => r.id === id && !r.isDeleted);
    } catch (error) {
      console.error(`Error getting receipt ${id}:`, error);
      return undefined;
    }
  },

  async saveReceipt(receipt: Receipt): Promise<number> {
    try {
      if (!receipt.id) {
        receipt.id = await this.getNextId('receipt');
      }

      const receiptsStr = await AsyncStorage.getItem(STORAGE_KEYS.RECEIPTS);
      const receipts = receiptsStr ? JSON.parse(receiptsStr) : [];

      const existingIndex = receipts.findIndex((r: Receipt) => r.id === receipt.id);
      if (existingIndex >= 0) {
        receipts[existingIndex] = receipt;
      } else {
        receipts.push(receipt);
      }

      await AsyncStorage.setItem(STORAGE_KEYS.RECEIPTS, JSON.stringify(receipts));

      // If not synced, add to sync queue
      if (!receipt.isDeleted) {
        await this.addToSyncQueue({
          entityType: 'Receipt',
          entityId: receipt.id,
          action: existingIndex >= 0 ? 'Update' : 'Create',
          entityData: JSON.stringify(receipt),
          status: 'Pending',
          retryCount: 0,
          createdAt: new Date()
        });
      }

      return receipt.id;
    } catch (error) {
      console.error('Error saving receipt:', error);
      return -1;
    }
  },

  async deleteReceipt(id: number): Promise<boolean> {
    try {
      const receiptsStr = await AsyncStorage.getItem(STORAGE_KEYS.RECEIPTS);
      const receipts = receiptsStr ? JSON.parse(receiptsStr) : [];

      const existingIndex = receipts.findIndex((r: Receipt) => r.id === id);
      if (existingIndex >= 0) {
        // Mark as deleted instead of removing
        receipts[existingIndex].isDeleted = true;

        await AsyncStorage.setItem(STORAGE_KEYS.RECEIPTS, JSON.stringify(receipts));

        // Add to sync queue
        await this.addToSyncQueue({
          entityType: 'Receipt',
          entityId: id,
          action: 'Delete',
          entityData: JSON.stringify({ id }),
          status: 'Pending',
          retryCount: 0,
          createdAt: new Date()
        });

        return true;
      }

      return false;
    } catch (error) {
      console.error(`Error deleting receipt ${id}:`, error);
      return false;
    }
  },

  async markReceiptForDeletion(id: number): Promise<boolean> {
    try {
      const receiptsStr = await AsyncStorage.getItem(STORAGE_KEYS.RECEIPTS);
      const receipts = receiptsStr ? JSON.parse(receiptsStr) : [];

      const existingIndex = receipts.findIndex((r: Receipt) => r.id === id);
      if (existingIndex >= 0) {
        // Mark as deleted
        receipts[existingIndex].isDeleted = true;

        await AsyncStorage.setItem(STORAGE_KEYS.RECEIPTS, JSON.stringify(receipts));

        // Add to sync queue
        await this.addToSyncQueue({
          entityType: 'Receipt',
          entityId: id,
          action: 'Delete',
          entityData: JSON.stringify({ id }),
          status: 'Pending',
          retryCount: 0,
          createdAt: new Date()
        });

        return true;
      }

      return false;
    } catch (error) {
      console.error(`Error marking receipt ${id} for deletion:`, error);
      return false;
    }
  },

  async batchSaveReceipts(receipts: Receipt[]): Promise<number[]> {
    try {
      const savedIds: number[] = [];

      for (const receipt of receipts) {
        const id = await this.saveReceipt(receipt);
        if (id > 0) {
          savedIds.push(id);
        }
      }

      return savedIds;
    } catch (error) {
      console.error('Error batch saving receipts:', error);
      return [];
    }
  },

  // Auth token operations
  async saveAuthToken(token: AuthToken): Promise<number> {
    try {
      // Clear existing tokens first
      await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKENS, JSON.stringify([token]));
      return 1;
    } catch (error) {
      console.error('Error saving auth token:', error);
      return -1;
    }
  },

  async getAuthToken(): Promise<AuthToken | undefined> {
    try {
      const tokensStr = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKENS);
      const tokens = tokensStr ? JSON.parse(tokensStr) : [];

      if (tokens.length > 0) {
        const token = tokens[0];
        return {
          ...token,
          expiresAt: new Date(token.expiresAt)
        };
      }

      return undefined;
    } catch (error) {
      console.error('Error getting auth token:', error);
      return undefined;
    }
  },

  async clearAuthToken(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.AUTH_TOKENS);
    } catch (error) {
      console.error('Error clearing auth token:', error);
    }
  },

  // Sync queue operations
  async addToSyncQueue(item: SyncQueue): Promise<number> {
    try {
      if (!item.id) {
        item.id = await this.getNextId('syncQueue');
      }

      const queueStr = await AsyncStorage.getItem(STORAGE_KEYS.SYNC_QUEUE);
      const queue = queueStr ? JSON.parse(queueStr) : [];

      queue.push(item);
      await AsyncStorage.setItem(STORAGE_KEYS.SYNC_QUEUE, JSON.stringify(queue));

      return item.id;
    } catch (error) {
      console.error('Error adding to sync queue:', error);
      return -1;
    }
  },

  async getPendingSyncItems(): Promise<SyncQueue[]> {
    try {
      const queueStr = await AsyncStorage.getItem(STORAGE_KEYS.SYNC_QUEUE);
      const queue = queueStr ? JSON.parse(queueStr) : [];

      return queue
        .filter((item: SyncQueue) => item.status === 'Pending')
        .map((item: any) => ({
          ...item,
          createdAt: new Date(item.createdAt)
        }));
    } catch (error) {
      console.error('Error getting pending sync items:', error);
      return [];
    }
  },

  async updateSyncItemStatus(id: number, status: string, errorMessage?: string): Promise<void> {
    try {
      const queueStr = await AsyncStorage.getItem(STORAGE_KEYS.SYNC_QUEUE);
      const queue = queueStr ? JSON.parse(queueStr) : [];

      const itemIndex = queue.findIndex((item: SyncQueue) => item.id === id);
      if (itemIndex >= 0) {
        queue[itemIndex].status = status;
        if (errorMessage) {
          queue[itemIndex].errorMessage = errorMessage;
          queue[itemIndex].retryCount = (queue[itemIndex].retryCount || 0) + 1;
        }

        await AsyncStorage.setItem(STORAGE_KEYS.SYNC_QUEUE, JSON.stringify(queue));
      }
    } catch (error) {
      console.error(`Error updating sync item ${id} status:`, error);
    }
  },

  async getSyncQueue(): Promise<SyncQueue[]> {
    try {
      const queueStr = await AsyncStorage.getItem(STORAGE_KEYS.SYNC_QUEUE);
      return queueStr ? JSON.parse(queueStr) : [];
    } catch (error) {
      console.error('Error getting sync queue:', error);
      return [];
    }
  },

  async removeSyncQueueItem(id: number): Promise<boolean> {
    try {
      const queueStr = await AsyncStorage.getItem(STORAGE_KEYS.SYNC_QUEUE);
      const queue = queueStr ? JSON.parse(queueStr) : [];

      const newQueue = queue.filter((item: SyncQueue) => item.id !== id);

      await AsyncStorage.setItem(STORAGE_KEYS.SYNC_QUEUE, JSON.stringify(newQueue));

      return true;
    } catch (error) {
      console.error(`Error removing sync queue item ${id}:`, error);
      return false;
    }
  },

  async updateSyncQueueItem(id: number, updates: Partial<SyncQueue>): Promise<boolean> {
    try {
      const queueStr = await AsyncStorage.getItem(STORAGE_KEYS.SYNC_QUEUE);
      const queue = queueStr ? JSON.parse(queueStr) : [];

      const index = queue.findIndex((item: SyncQueue) => item.id === id);

      if (index >= 0) {
        queue[index] = { ...queue[index], ...updates };
        await AsyncStorage.setItem(STORAGE_KEYS.SYNC_QUEUE, JSON.stringify(queue));
        return true;
      }

      return false;
    } catch (error) {
      console.error(`Error updating sync queue item ${id}:`, error);
      return false;
    }
  },

  async clearSyncQueue(): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.SYNC_QUEUE, JSON.stringify([]));
    } catch (error) {
      console.error('Error clearing sync queue:', error);
    }
  },

  async getLastSyncTime(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.SYNC_STATUS);
    } catch (error) {
      console.error('Error getting last sync time:', error);
      return null;
    }
  },

  async setLastSyncTime(time: Date): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.SYNC_STATUS, time.toISOString());
    } catch (error) {
      console.error('Error setting last sync time:', error);
    }
  },

  async saveConflict(conflict: Omit<SyncConflict, 'id'>): Promise<number> {
    try {
      const conflictsStr = await AsyncStorage.getItem(STORAGE_KEYS.CONFLICT_LOGS);
      const conflicts = conflictsStr ? JSON.parse(conflictsStr) : [];

      const newConflict = {
        ...conflict,
        id: await this.getNextId('conflict'),
      };

      conflicts.push(newConflict);

      await AsyncStorage.setItem(STORAGE_KEYS.CONFLICT_LOGS, JSON.stringify(conflicts));

      return newConflict.id;
    } catch (error) {
      console.error('Error saving conflict:', error);
      return -1;
    }
  },

  async getConflicts(): Promise<SyncConflict[]> {
    try {
      const conflictsStr = await AsyncStorage.getItem(STORAGE_KEYS.CONFLICT_LOGS);
      return conflictsStr ? JSON.parse(conflictsStr) : [];
    } catch (error) {
      console.error('Error getting conflicts:', error);
      return [];
    }
  },

  async resolveConflict(id: number, resolution: 'local' | 'remote' | 'merge'): Promise<boolean> {
    try {
      const conflictsStr = await AsyncStorage.getItem(STORAGE_KEYS.CONFLICT_LOGS);
      const conflicts = conflictsStr ? JSON.parse(conflictsStr) : [];

      const index = conflicts.findIndex((conflict: SyncConflict) => conflict.id === id);

      if (index >= 0) {
        conflicts[index] = {
          ...conflicts[index],
          resolved: true,
          resolution
        };

        await AsyncStorage.setItem(STORAGE_KEYS.CONFLICT_LOGS, JSON.stringify(conflicts));
        return true;
      }

      return false;
    } catch (error) {
      console.error(`Error resolving conflict ${id}:`, error);
      return false;
    }
  },

  // Database utilities
  async clearDatabase(): Promise<void> {
    try {
      await AsyncStorage.clear();
      alert('Database cleared successfully');
    } catch (error) {
      console.error('Error clearing database:', error);
    }
  },

  // Feature flag methods
  async getFeatureFlags(): Promise<any[]> {
    try {
      const featureFlagsStr = await AsyncStorage.getItem(STORAGE_KEYS.FEATURE_FLAGS);
      return featureFlagsStr ? JSON.parse(featureFlagsStr) : [];
    } catch (error) {
      console.error('Error getting feature flags:', error);
      return [];
    }
  },

  async saveFeatureFlags(featureFlags: any[]): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.FEATURE_FLAGS, JSON.stringify(featureFlags));
    } catch (error) {
      console.error('Error saving feature flags:', error);
      throw error;
    }
  },

  async updateFeatureFlag(id: number, isEnabled: boolean, enabledFor?: string): Promise<void> {
    try {
      const featureFlagsStr = await AsyncStorage.getItem(STORAGE_KEYS.FEATURE_FLAGS);
      const featureFlags = featureFlagsStr ? JSON.parse(featureFlagsStr) : [];

      const index = featureFlags.findIndex((f: any) => f.id === id);

      if (index !== -1) {
        featureFlags[index].isEnabled = isEnabled;
        if (enabledFor !== undefined) {
          featureFlags[index].enabledFor = enabledFor;
        }
        featureFlags[index].lastUpdatedAt = new Date();

        await AsyncStorage.setItem(STORAGE_KEYS.FEATURE_FLAGS, JSON.stringify(featureFlags));
      }
    } catch (error) {
      console.error('Error updating feature flag:', error);
      throw error;
    }
  },

  // Notification methods
  async getNotifications(): Promise<any[]> {
    try {
      const notificationsStr = await AsyncStorage.getItem(STORAGE_KEYS.NOTIFICATIONS);
      return notificationsStr ? JSON.parse(notificationsStr) : [];
    } catch (error) {
      console.error('Error getting notifications:', error);
      return [];
    }
  },

  async saveNotification(notification: any): Promise<void> {
    try {
      const notificationsStr = await AsyncStorage.getItem(STORAGE_KEYS.NOTIFICATIONS);
      const notifications = notificationsStr ? JSON.parse(notificationsStr) : [];
      notifications.push(notification);
      await AsyncStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications));
    } catch (error) {
      console.error('Error saving notification:', error);
      throw error;
    }
  },

  async deleteNotification(id: string): Promise<void> {
    try {
      const notificationsStr = await AsyncStorage.getItem(STORAGE_KEYS.NOTIFICATIONS);
      const notifications = notificationsStr ? JSON.parse(notificationsStr) : [];
      const filteredNotifications = notifications.filter((n: any) => n.id !== id);
      await AsyncStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(filteredNotifications));
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  },

  async markNotificationAsRead(id: string): Promise<void> {
    try {
      const notificationsStr = await AsyncStorage.getItem(STORAGE_KEYS.NOTIFICATIONS);
      const notifications = notificationsStr ? JSON.parse(notificationsStr) : [];
      const index = notifications.findIndex((n: any) => n.id === id);

      if (index !== -1) {
        notifications[index].isRead = true;
        await AsyncStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications));
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  },

  async getNotificationSettings(): Promise<any> {
    try {
      const settingsStr = await AsyncStorage.getItem(STORAGE_KEYS.NOTIFICATION_SETTINGS);
      return settingsStr ? JSON.parse(settingsStr) : {
        pushEnabled: false,
        emailEnabled: false,
        inAppEnabled: true,
        transactionNotifications: true,
        loanNotifications: true,
        familyNotifications: true,
        accountNotifications: true,
        budgetNotifications: true,
        savingsNotifications: true,
        reminderNotifications: true,
        systemNotifications: true,
        summaryEnabled: false,
        summaryFrequency: 'weekly',
      };
    } catch (error) {
      console.error('Error getting notification settings:', error);
      return {
        pushEnabled: false,
        emailEnabled: false,
        inAppEnabled: true,
      };
    }
  },

  async saveNotificationSettings(settings: any): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.NOTIFICATION_SETTINGS, JSON.stringify(settings));
    } catch (error) {
      console.error('Error saving notification settings:', error);
      throw error;
    }
  }
};

export default dbService;
