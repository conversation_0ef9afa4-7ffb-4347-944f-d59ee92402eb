import Dexie from 'dexie';
import { Transaction } from '../models/Transaction';
import { SyncConflict } from '../contexts/SyncContext';

// Import Receipt from db.ts to avoid circular dependency
import { Receipt } from './db';

// Define Account and Category interfaces here to avoid import issues
interface Account {
  id?: number;
  name: string;
  accountType: string;
  balance: number;
  initialBalance: number;
  currency: string;
  isActive: boolean;
  excludeFromStats: boolean;
  lastUpdatedAt: Date;
  isSynced: boolean;
  isDeleted?: boolean;
}

interface Category {
  id?: number;
  name: string;
  type: string;
  icon?: string;
  color?: string;
  parentCategoryId?: number;
  isSystem: boolean;
  isSynced: boolean;
  isDeleted?: boolean;
}

/**
 * Define the sync queue item interface
 */
export interface SyncQueue {
  id?: number;
  entityType: string;
  entityId: number;
  action: string;
  entityData: string;
  status: string;
  retryCount: number;
  errorMessage?: string;
  createdAt: Date;
  priority: number; // Higher number = higher priority
}

/**
 * Define the auth token interface
 */
export interface AuthToken {
  id?: number;
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
}

/**
 * Define the HisabKitab database class
 */
export class HisabKitabDatabase extends Dexie {
  // Define tables
  transactions: Dexie.Table<Transaction, number>;
  accounts: Dexie.Table<Account, number>;
  categories: Dexie.Table<Category, number>;
  receipts: Dexie.Table<Receipt, number>;
  syncQueue: Dexie.Table<SyncQueue, number>;
  conflicts: Dexie.Table<SyncConflict, number>;
  authTokens: Dexie.Table<AuthToken, number>;
  settings: Dexie.Table<any, string>;

  constructor() {
    super('HisabKitabDB');

    // Define schema
    this.version(1).stores({
      transactions: '++id, accountId, categoryId, date, type, isDeleted',
      accounts: '++id, name, type, isDeleted',
      categories: '++id, name, type, isDeleted',
      receipts: '++id, transactionId, merchantName, date, isProcessed, isDeleted',
      syncQueue: '++id, entityType, entityId, action, status, priority',
      conflicts: '++id, entityType, entityId, resolved',
      authTokens: '++id',
      settings: 'key'
    });

    // Define table mappings
    this.transactions = this.table('transactions');
    this.accounts = this.table('accounts');
    this.categories = this.table('categories');
    this.receipts = this.table('receipts');
    this.syncQueue = this.table('syncQueue');
    this.conflicts = this.table('conflicts');
    this.authTokens = this.table('authTokens');
    this.settings = this.table('settings');
  }
}

// Create and export database instance
export const db = new HisabKitabDatabase();

/**
 * Database service for Hisab-Kitab
 */
const dbService = {
  /**
   * Initialize the database
   */
  async init(): Promise<void> {
    try {
      // Check if database is already initialized
      const initialized = await this.getSetting('initialized');

      if (!initialized) {
        // Set initialized flag
        await this.setSetting('initialized', true);

        // Set last sync time to null
        await this.setSetting('lastSyncTime', null);
      }
    } catch (error) {
      console.error('Error initializing database:', error);
    }
  },

  /**
   * Get a setting value
   */
  async getSetting(key: string): Promise<any> {
    try {
      const setting = await db.settings.get(key);
      return setting ? setting.value : null;
    } catch (error) {
      console.error(`Error getting setting ${key}:`, error);
      return null;
    }
  },

  /**
   * Set a setting value
   */
  async setSetting(key: string, value: any): Promise<void> {
    try {
      await db.settings.put({ key, value });
    } catch (error) {
      console.error(`Error setting setting ${key}:`, error);
    }
  },

  /**
   * Get the last sync time
   */
  async getLastSyncTime(): Promise<string | null> {
    return await this.getSetting('lastSyncTime');
  },

  /**
   * Set the last sync time
   */
  async setLastSyncTime(time: Date): Promise<void> {
    await this.setSetting('lastSyncTime', time.toISOString());
  },

  /**
   * Get all receipts with optional filtering
   */
  async getReceipts(filter?: any): Promise<Receipt[]> {
    try {
      let query = db.receipts.where('isDeleted').equals(0);

      // Apply filters if provided
      if (filter) {
        // Filter by date range
        if (filter.startDate && filter.endDate) {
          query = query.and(item => {
            const itemDate = new Date(item.date || '');
            return itemDate >= new Date(filter.startDate) &&
                   itemDate <= new Date(filter.endDate);
          });
        } else if (filter.startDate) {
          query = query.and(item => {
            const itemDate = new Date(item.date || '');
            return itemDate >= new Date(filter.startDate);
          });
        } else if (filter.endDate) {
          query = query.and(item => {
            const itemDate = new Date(item.date || '');
            return itemDate <= new Date(filter.endDate);
          });
        }

        // Filter by amount range
        if (filter.minAmount !== undefined && filter.maxAmount !== undefined) {
          query = query.and(item =>
            item.totalAmount >= filter.minAmount &&
            item.totalAmount <= filter.maxAmount
          );
        } else if (filter.minAmount !== undefined) {
          query = query.and(item => item.totalAmount >= filter.minAmount);
        } else if (filter.maxAmount !== undefined) {
          query = query.and(item => item.totalAmount <= filter.maxAmount);
        }

        // Filter by merchant name
        if (filter.merchantName) {
          const searchTerm = filter.merchantName.toLowerCase();
          query = query.and(item =>
            typeof item.merchantName === 'string' &&
            item.merchantName.toLowerCase().includes(searchTerm)
          );
        }

        // Filter by processing status
        if (filter.isProcessed !== undefined) {
          query = query.and(item => item.isProcessed === filter.isProcessed);
        }

        // Filter by transaction ID
        if (filter.transactionId !== undefined) {
          query = query.and(item => item.transactionId === filter.transactionId);
        }
      }

      return await query.toArray();
    } catch (error) {
      console.error('Error getting receipts:', error);
      return [];
    }
  },

  /**
   * Get a receipt by ID
   */
  async getReceipt(id: number): Promise<Receipt | undefined> {
    try {
      return await db.receipts.get(id);
    } catch (error) {
      console.error(`Error getting receipt ${id}:`, error);
      return undefined;
    }
  },

  /**
   * Save a receipt
   */
  async saveReceipt(receipt: Receipt): Promise<number> {
    try {
      // Ensure receipt has an ID
      if (!receipt.id) {
        receipt.id = Date.now();
      }

      // Save receipt
      const id = await db.receipts.put(receipt);

      // Add to sync queue if not deleted
      if (!receipt.isDeleted) {
        const existingReceipt = await db.receipts.get(receipt.id);
        const action = existingReceipt ? 'Update' : 'Create';

        await this.addToSyncQueue({
          entityType: 'Receipt',
          entityId: receipt.id,
          action,
          entityData: JSON.stringify(receipt),
          status: 'Pending',
          retryCount: 0,
          createdAt: new Date(),
          priority: 2 // Medium priority
        });
      }

      return id;
    } catch (error) {
      console.error('Error saving receipt:', error);
      return -1;
    }
  },

  /**
   * Delete a receipt
   */
  async deleteReceipt(id: number): Promise<boolean> {
    try {
      const receipt = await db.receipts.get(id);

      if (receipt) {
        // Mark as deleted
        receipt.isDeleted = true;
        await db.receipts.put(receipt);

        // Add to sync queue
        await this.addToSyncQueue({
          entityType: 'Receipt',
          entityId: id,
          action: 'Delete',
          entityData: JSON.stringify({ id }),
          status: 'Pending',
          retryCount: 0,
          createdAt: new Date(),
          priority: 2 // Medium priority
        });

        return true;
      }

      return false;
    } catch (error) {
      console.error(`Error deleting receipt ${id}:`, error);
      return false;
    }
  },

  /**
   * Add an item to the sync queue
   */
  async addToSyncQueue(item: Omit<SyncQueue, 'id'>): Promise<number> {
    try {
      return await db.syncQueue.add(item as SyncQueue);
    } catch (error) {
      console.error('Error adding to sync queue:', error);
      return -1;
    }
  },

  /**
   * Get all items in the sync queue
   */
  async getSyncQueue(): Promise<SyncQueue[]> {
    try {
      return await db.syncQueue.toArray();
    } catch (error) {
      console.error('Error getting sync queue:', error);
      return [];
    }
  },

  /**
   * Remove an item from the sync queue
   */
  async removeSyncQueueItem(id: number): Promise<boolean> {
    try {
      await db.syncQueue.delete(id);
      return true;
    } catch (error) {
      console.error(`Error removing sync queue item ${id}:`, error);
      return false;
    }
  },

  /**
   * Update a sync queue item
   */
  async updateSyncQueueItem(id: number, updates: Partial<SyncQueue>): Promise<boolean> {
    try {
      const item = await db.syncQueue.get(id);

      if (item) {
        await db.syncQueue.update(id, updates);
        return true;
      }

      return false;
    } catch (error) {
      console.error(`Error updating sync queue item ${id}:`, error);
      return false;
    }
  },

  /**
   * Clear the sync queue
   */
  async clearSyncQueue(): Promise<void> {
    try {
      await db.syncQueue.clear();
    } catch (error) {
      console.error('Error clearing sync queue:', error);
    }
  },

  /**
   * Save a conflict
   */
  async saveConflict(conflict: Omit<SyncConflict, 'id'>): Promise<number> {
    try {
      return await db.conflicts.add(conflict as SyncConflict);
    } catch (error) {
      console.error('Error saving conflict:', error);
      return -1;
    }
  },

  /**
   * Get all conflicts
   */
  async getConflicts(): Promise<SyncConflict[]> {
    try {
      return await db.conflicts.toArray();
    } catch (error) {
      console.error('Error getting conflicts:', error);
      return [];
    }
  },

  /**
   * Resolve a conflict
   */
  async resolveConflict(id: number, resolution: 'local' | 'remote' | 'merge'): Promise<boolean> {
    try {
      const conflict = await db.conflicts.get(id);

      if (conflict) {
        conflict.resolved = true;
        conflict.resolution = resolution;
        await db.conflicts.put(conflict);
        return true;
      }

      return false;
    } catch (error) {
      console.error(`Error resolving conflict ${id}:`, error);
      return false;
    }
  }
};

export default dbService;
