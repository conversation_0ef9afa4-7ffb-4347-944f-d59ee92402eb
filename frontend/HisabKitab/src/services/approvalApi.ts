import { fetchWithAuth, ApiResponse } from './api';

// Expense Approval API endpoints
export interface ApprovalService {
  getApprovalRequests(asRequester?: boolean, status?: string): Promise<ApiResponse<any>>;
  getApprovalRequest(id: number): Promise<ApiResponse<any>>;
  getFamilyApprovalRequests(familyId: number, status?: string): Promise<ApiResponse<any>>;
  createApprovalRequest(approvalRequestData: any): Promise<ApiResponse<any>>;
  approveRequest(id: number, approveData: any): Promise<ApiResponse<any>>;
  rejectRequest(id: number, rejectData: any): Promise<ApiResponse<any>>;
  checkApprovalRequired(familyId: number, amount: number, categoryId: number): Promise<ApiResponse<any>>;
}

// Expense Approval API implementation
const approvalService: ApprovalService = {
  async getApprovalRequests(asRequester: boolean = true, status?: string): Promise<ApiResponse<any>> {
    let queryParams = `asRequester=${asRequester}`;
    if (status) queryParams += `&status=${status}`;
    
    return await fetchWithAuth<any>(`expenseapproval?${queryParams}`);
  },

  async getApprovalRequest(id: number): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`expenseapproval/${id}`);
  },

  async getFamilyApprovalRequests(familyId: number, status?: string): Promise<ApiResponse<any>> {
    let queryParams = '';
    if (status) queryParams = `?status=${status}`;
    
    return await fetchWithAuth<any>(`expenseapproval/family/${familyId}${queryParams}`);
  },

  async createApprovalRequest(approvalRequestData: any): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>('expenseapproval', 'POST', approvalRequestData);
  },

  async approveRequest(id: number, approveData: any): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`expenseapproval/${id}/approve`, 'POST', approveData);
  },

  async rejectRequest(id: number, rejectData: any): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`expenseapproval/${id}/reject`, 'POST', rejectData);
  },

  async checkApprovalRequired(familyId: number, amount: number, categoryId: number): Promise<ApiResponse<any>> {
    return await fetchWithAuth<any>(`expenseapproval/check-required?familyId=${familyId}&amount=${amount}&categoryId=${categoryId}`);
  }
};

export default approvalService;
