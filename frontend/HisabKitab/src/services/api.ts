import dbService from './db';

// Default API URL using ngrok
// When you run: ngrok http 5046
// It gives you a URL like: https://2cd8-202-63-244-120.ngrok-free.app
// This URL forwards to your local server at http://localhost:5046
// IMPORTANT: Update this URL with your current ngrok URL
const API_URL = 'https://a3a6-202-63-244-120.ngrok-free.app/api';

// Alternative: Use direct localhost URL if running on the same device
// const API_URL = 'http://localhost:5046/api';

// For debugging - log the URL being used
console.log('Using API URL:', API_URL);

// Interface for API response
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  status: number;
}

// Interface for login request
interface LoginRequest {
  username: string;
  password: string;
}

// Interface for login response
interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  userId: number;
  username: string;
  email: string;
  roles: string[];
  expiresAt: string;
  isPlatformAdmin: boolean;
  isFamilyAdmin: boolean;
}

// Interface for register request
interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  language?: string;
}

// Generic fetch function with authentication and error handling
export async function fetchWithAuth<T>(
  endpoint: string,
  method: string = 'GET',
  body?: any,
  requiresAuth: boolean = true
): Promise<ApiResponse<T>> {
  try {
    // Get auth token from database
    const authToken = requiresAuth ? await dbService.getAuthToken() : null;

    // Prepare headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Add auth token if required
    if (requiresAuth && authToken) {
      headers['Authorization'] = `Bearer ${authToken.accessToken}`;
    }

    // Prepare request options
    const options: RequestInit = {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined,
      // Enable redirect following
      redirect: 'follow',
    };

    // Log the request for debugging
    console.log(`Making ${method} request to ${API_URL}/${endpoint}`);

    // Make the request
    const response = await fetch(`${API_URL}/${endpoint}`, options);

    // Log the response status for debugging
    console.log(`Response status: ${response.status}`);

    // We've disabled HTTPS redirection in the backend, so we shouldn't need this anymore
    // But we'll keep a simple log in case it happens
    if (response.status === 307 || response.status === 308) {
      const redirectUrl = response.headers.get('Location');
      console.log(`Redirect detected to: ${redirectUrl}`);
    }

    // Parse response
    let data;
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
      console.log('Response data:', data);
    } else {
      data = await response.text();
      console.log('Response text:', data);
    }

    // Handle unauthorized error (token expired)
    if (response.status === 401 && requiresAuth && authToken) {
      const refreshed = await refreshToken(authToken.refreshToken);
      if (refreshed) {
        // Retry the request with new token
        return fetchWithAuth<T>(endpoint, method, body, requiresAuth);
      } else {
        // Refresh failed, logout
        await dbService.clearAuthToken();
        return {
          error: 'Session expired. Please login again.',
          status: 401,
        };
      }
    }

    // Return response
    if (response.ok) {
      return {
        data: data,
        status: response.status,
      };
    } else {
      // Extract error message from response
      let errorMessage = 'An error occurred';

      if (data) {
        if (typeof data === 'string') {
          errorMessage = data;
        } else if (data.message) {
          errorMessage = data.message;
        } else if (data.title) {
          errorMessage = data.title;
        } else if (data.error) {
          errorMessage = data.error;
        } else if (data.errors) {
          // Handle validation errors
          const errors = data.errors;
          if (typeof errors === 'object') {
            const errorMessages = Object.values(errors).flat();
            if (errorMessages.length > 0) {
              errorMessage = errorMessages.join('. ');
            }
          }
        }
      }

      return {
        error: errorMessage,
        status: response.status,
      };
    }
  } catch (error) {
    // Handle network errors
    console.error('Network error:', error);

    // Provide a more user-friendly error message
    let errorMessage = 'Network error. Please check your internet connection.';

    if (error instanceof Error) {
      if (error.message.includes('Network request failed')) {
        errorMessage = 'Network request failed. Please check your internet connection.';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Request timed out. Please try again later.';
      } else {
        errorMessage = error.message;
      }
    }

    return {
      error: errorMessage,
      status: 0,
    };
  }
}

// Function to refresh token
async function refreshToken(refreshToken: string): Promise<boolean> {
  try {
    const response = await fetch(`${API_URL}/auth/refresh-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refreshToken }),
    });

    if (response.ok) {
      const data = await response.json();
      await dbService.saveAuthToken({
        accessToken: data.accessToken,
        refreshToken: data.refreshToken,
        expiresAt: new Date(data.expiresAt),
      });
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error refreshing token:', error);
    return false;
  }
}



// Define interfaces for each API service section
interface AuthService {
  login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>>;
  register(userData: RegisterRequest): Promise<ApiResponse<any>>;
  logout(): Promise<ApiResponse<any>>;
}

// Import push notification types
import { PushToken } from './pushNotifications';
import {
  transformApiTransaction,
  transformTransactionForApi,
  transformApiCategory,
  transformCategoryForApi,
  transformApiAccount,
  transformAccountForApi
} from '../utils/apiTransformers';
import { Transaction } from '../models/Transaction';
import { Category } from '../models/Category';
import { Account } from '../models/Account';

interface UserService {
  getProfile(): Promise<ApiResponse<any>>;
  updateProfile(userData: any): Promise<ApiResponse<any>>;
  changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse<any>>;
  changeEmail(newEmail: string, password: string): Promise<ApiResponse<any>>;
  registerPushToken(token: PushToken): Promise<ApiResponse<any>>;
  unregisterPushToken(token: string): Promise<ApiResponse<any>>;
}

interface FamilyService {
  getFamilies(): Promise<ApiResponse<any>>;
  getFamily(id: number): Promise<ApiResponse<any>>;
  createFamily(familyData: any): Promise<ApiResponse<any>>;
  updateFamily(id: number, familyData: any): Promise<ApiResponse<any>>;
  deleteFamily(id: number): Promise<ApiResponse<any>>;
  generateInviteCode(id: number): Promise<ApiResponse<any>>;
  joinFamily(inviteCode: string): Promise<ApiResponse<any>>;
  leaveFamily(id: number): Promise<ApiResponse<any>>;
  getFamilyMembers(id: number): Promise<ApiResponse<any>>;
  addMember(id: number, userData: any): Promise<ApiResponse<any>>;
  removeMember(familyId: number, userId: number): Promise<ApiResponse<any>>;
  updateMemberRole(familyId: number, userId: number, roleData: any): Promise<ApiResponse<any>>;
}

interface AccountService {
  getAccounts(): Promise<ApiResponse<Account[]>>;
  getAccount(id: number): Promise<ApiResponse<Account>>;
  getFamilyAccounts(familyId: number): Promise<ApiResponse<Account[]>>;
  createAccount(accountData: Account): Promise<ApiResponse<Account>>;
  updateAccount(id: number, accountData: Account): Promise<ApiResponse<Account>>;
  deleteAccount(id: number): Promise<ApiResponse<any>>;
  updateBalance(id: number, balanceData: { balance: number }): Promise<ApiResponse<Account>>;
  shareAccount(id: number, shareData: any): Promise<ApiResponse<any>>;
  removeAccountShare(shareId: number): Promise<ApiResponse<any>>;
}

interface TransactionService {
  getTransactions(filters?: any): Promise<ApiResponse<Transaction[]>>;
  getTransaction(id: number): Promise<ApiResponse<Transaction>>;
  getTransactionsByAccount(accountId: number, startDate?: string, endDate?: string, type?: number): Promise<ApiResponse<Transaction[]>>;
  createTransaction(transactionData: Transaction): Promise<ApiResponse<Transaction>>;
  updateTransaction(id: number, transactionData: Transaction): Promise<ApiResponse<Transaction>>;
  deleteTransaction(id: number): Promise<ApiResponse<any>>;
  batchCreateTransactions(transactionsData: Transaction[]): Promise<ApiResponse<Transaction[]>>;
  batchUpdateTransactions(transactionsData: Transaction[]): Promise<ApiResponse<Transaction[]>>;
  batchDeleteTransactions(transactionIds: number[]): Promise<ApiResponse<any>>;
  getTransactionStatistics(startDate: string, endDate: string, accountId?: number): Promise<ApiResponse<any>>;
  getCategorySummary(startDate: string, endDate: string, type: number): Promise<ApiResponse<any>>;
}

interface CategoryService {
  getCategories(): Promise<ApiResponse<Category[]>>;
  getCategory(id: number): Promise<ApiResponse<Category>>;
  getCategoriesByType(type: string): Promise<ApiResponse<Category[]>>;
  getSystemCategories(): Promise<ApiResponse<Category[]>>;
  getFamilyCategories(familyId: number): Promise<ApiResponse<Category[]>>;
  createCategory(categoryData: Category): Promise<ApiResponse<Category>>;
  updateCategory(id: number, categoryData: Category): Promise<ApiResponse<Category>>;
  deleteCategory(id: number): Promise<ApiResponse<any>>;
  setBudgetLimit(budgetData: any): Promise<ApiResponse<any>>;
  getBudgetLimit(id: number): Promise<ApiResponse<any>>;
  getBudgetLimitsByCategory(categoryId: number): Promise<ApiResponse<any>>;
  getAllBudgetLimits(): Promise<ApiResponse<any>>;
  updateBudgetLimit(id: number, budgetData: any): Promise<ApiResponse<any>>;
  deleteBudgetLimit(id: number): Promise<ApiResponse<any>>;
}

interface PriorityService {
  getAllPriorities(): Promise<ApiResponse<any>>;
  getPriority(id: number): Promise<ApiResponse<any>>;
  createPriority(priorityData: any): Promise<ApiResponse<any>>;
  updatePriority(id: number, priorityData: any): Promise<ApiResponse<any>>;
  deletePriority(id: number): Promise<ApiResponse<any>>;
}

// Import loan models
import {
  Loan,
  LoanPayment,
  LoanReminder,
  LoanTimeline,
  CreateLoanRequest,
  UpdateLoanRequest,
  CreateLoanPaymentRequest,
  UpdateLoanPaymentRequest,
  CreateLoanReminderRequest,
  UpdateLoanReminderRequest,
  LoanEMICalculationRequest,
  LoanEMICalculationResponse
} from '../models/Loan';

// Import notification models
import { Notification } from '../models/Notification';

// Import savings models
import { SavingsService } from './savingsApi';
import savingsService from './savingsApi';

// Define loan service interface
interface LoanService {
  getLoans(asLender?: boolean, asBorrower?: boolean, status?: string): Promise<ApiResponse<Loan[]>>;
  getLoan(id: number): Promise<ApiResponse<Loan>>;
  createLoan(loanData: CreateLoanRequest): Promise<ApiResponse<Loan>>;
  updateLoan(id: number, loanData: UpdateLoanRequest): Promise<ApiResponse<Loan>>;
  deleteLoan(id: number): Promise<ApiResponse<any>>;
  getLoanTimeline(id: number): Promise<ApiResponse<LoanTimeline>>;
  calculateEMI(calculationData: LoanEMICalculationRequest): Promise<ApiResponse<LoanEMICalculationResponse>>;

  // Payment operations
  getPayments(loanId: number): Promise<ApiResponse<LoanPayment[]>>;
  getPayment(id: number): Promise<ApiResponse<LoanPayment>>;
  createPayment(paymentData: CreateLoanPaymentRequest): Promise<ApiResponse<LoanPayment>>;
  updatePayment(id: number, paymentData: UpdateLoanPaymentRequest): Promise<ApiResponse<LoanPayment>>;
  deletePayment(id: number): Promise<ApiResponse<any>>;

  // Reminder operations
  getReminders(loanId: number): Promise<ApiResponse<LoanReminder[]>>;
  getActiveReminders(): Promise<ApiResponse<LoanReminder[]>>;
  getReminder(id: number): Promise<ApiResponse<LoanReminder>>;
  createReminder(reminderData: CreateLoanReminderRequest): Promise<ApiResponse<LoanReminder>>;
  updateReminder(id: number, reminderData: UpdateLoanReminderRequest): Promise<ApiResponse<LoanReminder>>;
  deleteReminder(id: number): Promise<ApiResponse<any>>;
  markReminderAsSent(id: number): Promise<ApiResponse<any>>;
}

// Define notification service interface
interface NotificationService {
  // Basic notifications
  getNotifications(includeRead?: boolean): Promise<ApiResponse<Notification[]>>;
  getNotification(id: number): Promise<ApiResponse<Notification>>;
  markAsRead(id: number): Promise<ApiResponse<any>>;
  markAllAsRead(): Promise<ApiResponse<any>>;
  deleteNotification(id: number): Promise<ApiResponse<any>>;

  // Scheduled notifications
  getScheduledNotifications(includeSent?: boolean): Promise<ApiResponse<any>>;
  getScheduledNotification(id: number): Promise<ApiResponse<any>>;
  scheduleNotification(notificationData: any): Promise<ApiResponse<any>>;
  updateScheduledNotification(id: number, notificationData: any): Promise<ApiResponse<any>>;
  deleteScheduledNotification(id: number): Promise<ApiResponse<any>>;

  // Recurring notifications
  getRecurringNotifications(includeInactive?: boolean): Promise<ApiResponse<any>>;
  getRecurringNotification(id: number): Promise<ApiResponse<any>>;
  createRecurringNotification(notificationData: any): Promise<ApiResponse<any>>;
  updateRecurringNotification(id: number, notificationData: any): Promise<ApiResponse<any>>;
  deleteRecurringNotification(id: number): Promise<ApiResponse<any>>;

  // Device management
  registerDevice(deviceData: any): Promise<ApiResponse<any>>;
  updateDeviceToken(deviceId: string, tokenData: any): Promise<ApiResponse<any>>;
  unregisterDevice(deviceId: string): Promise<ApiResponse<any>>;

  // Monthly summary
  getMonthlySummary(month: number, year: number): Promise<ApiResponse<any>>;
}

// Import our new services
import analyticsService, { AnalyticsService } from './analyticsApi';
import budgetControlService, { BudgetControlService } from './budgetControlApi';
import approvalService, { ApprovalService } from './approvalApi';
import receiptService, { ReceiptService } from './receiptApi';
import syncService, { SyncService } from './syncApi';

// Define feature service interface
interface FeatureService {
  getFeatures(): Promise<ApiResponse<any>>;
  getFeature(id: number): Promise<ApiResponse<any>>;
  getFeatureByName(name: string): Promise<ApiResponse<any>>;
  createFeature(featureData: any): Promise<ApiResponse<any>>;
  updateFeature(id: number, isEnabled: boolean, enabledFor?: string): Promise<ApiResponse<any>>;
  deleteFeature(id: number): Promise<ApiResponse<any>>;
  isFeatureEnabled(name: string): Promise<ApiResponse<boolean>>;
}

// Define the main API service interface
interface ApiService {
  auth: AuthService;
  user: UserService;
  family: FamilyService;
  accounts: AccountService;
  transactions: TransactionService;
  categories: CategoryService;
  priorities: PriorityService;
  loans: LoanService;
  notifications: NotificationService;
  savings: SavingsService;
  analytics: AnalyticsService;
  budgetControl: BudgetControlService;
  approval: ApprovalService;
  receipts: ReceiptService;
  sync: SyncService;
  features: FeatureService;
  https?: any;
}

// API service
const apiService: ApiService = {
  // Auth endpoints
  auth: {
    async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
      const response = await fetchWithAuth<LoginResponse>('auth/login', 'POST', credentials, false);

      if (response.data) {
        // Save auth token to database
        await dbService.saveAuthToken({
          accessToken: response.data.accessToken,
          refreshToken: response.data.refreshToken,
          expiresAt: new Date(response.data.expiresAt),
        });

        // Save user to database
        await dbService.saveUser({
          id: response.data.userId,
          username: response.data.username,
          email: response.data.email,
          firstName: '', // These fields are not returned from login
          lastName: '',  // These fields are not returned from login
          language: 'en',
          roles: response.data.roles,
          isPlatformAdmin: response.data.isPlatformAdmin,
          isFamilyAdmin: response.data.isFamilyAdmin,
          lastSyncedAt: new Date(),
        });
      }

      return response;
    },

    async register(userData: RegisterRequest): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('auth/register', 'POST', userData, false);
    },

    async logout(): Promise<ApiResponse<any>> {
      const response = await fetchWithAuth<any>('auth/logout', 'POST');
      await dbService.clearAuthToken();
      return response;
    },
  },

  // User endpoints
  user: {
    async getProfile(): Promise<ApiResponse<any>> {
      const response = await fetchWithAuth<any>('user/profile');

      if (response.data) {
        // Update user in database with profile data
        const currentUser = await dbService.getCurrentUser();
        if (currentUser) {
          // Check if user has PlatformAdmin or FamilyAdmin roles
          const isPlatformAdmin = response.data.roles?.includes('PlatformAdmin') || false;
          const isFamilyAdmin = response.data.roles?.includes('FamilyAdmin') || false;

          console.log('User profile roles:', {
            roles: response.data.roles,
            isPlatformAdmin,
            isFamilyAdmin
          });

          await dbService.saveUser({
            ...currentUser,
            firstName: response.data.firstName,
            lastName: response.data.lastName,
            phoneNumber: response.data.phoneNumber,
            language: response.data.language,
            roles: response.data.roles,
            isPlatformAdmin: isPlatformAdmin,
            isFamilyAdmin: isFamilyAdmin,
            lastSyncedAt: new Date(),
          });
        }
      }

      return response;
    },

    async updateProfile(userData: any): Promise<ApiResponse<any>> {
      const response = await fetchWithAuth<any>('user/profile', 'PUT', userData);

      if (response.status === 200) {
        // Update local user data
        await this.getProfile();
      }

      return response;
    },

    async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('user/change-password', 'POST', {
        currentPassword,
        newPassword,
        confirmNewPassword: newPassword
      });
    },

    async changeEmail(newEmail: string, password: string): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('user/change-email', 'POST', {
        newEmail,
        password
      });
    },

    async registerPushToken(token: PushToken): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('user/push-token', 'POST', token);
    },

    async unregisterPushToken(token: string): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('user/push-token', 'DELETE', { token });
    },
  },

  // Family endpoints
  family: {
    async getFamilies(): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('family');
    },

    async getFamily(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`family/${id}`);
    },

    async createFamily(familyData: any): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('family', 'POST', familyData);
    },

    async updateFamily(id: number, familyData: any): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`family/${id}`, 'PUT', familyData);
    },

    async deleteFamily(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`family/${id}`, 'DELETE');
    },

    async generateInviteCode(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`family/${id}/invite`, 'POST');
    },

    async joinFamily(inviteCode: string): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('family/join', 'POST', { inviteCode });
    },

    async leaveFamily(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`family/${id}/leave`, 'POST');
    },

    async getFamilyMembers(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`family/${id}/members`);
    },

    async addMember(id: number, userData: any): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`family/${id}/members`, 'POST', userData);
    },

    async removeMember(familyId: number, userId: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`family/${familyId}/members/${userId}`, 'DELETE');
    },

    async updateMemberRole(familyId: number, userId: number, roleData: any): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`family/${familyId}/members/${userId}/role`, 'PUT', roleData);
    },
  },

  // Account endpoints
  accounts: {
    async getAccounts(): Promise<ApiResponse<Account[]>> {
      const response = await fetchWithAuth<any>('accounts');

      // Transform API response to frontend format
      if (response.data && Array.isArray(response.data)) {
        response.data = response.data.map(account => transformApiAccount(account));
      }

      return response as ApiResponse<Account[]>;
    },

    async getAccount(id: number): Promise<ApiResponse<Account>> {
      const response = await fetchWithAuth<any>(`accounts/${id}`);

      // Transform API response to frontend format
      if (response.data) {
        response.data = transformApiAccount(response.data);
      }

      return response as ApiResponse<Account>;
    },

    async getFamilyAccounts(familyId: number): Promise<ApiResponse<Account[]>> {
      const response = await fetchWithAuth<any>(`accounts/family/${familyId}`);

      // Transform API response to frontend format
      if (response.data && Array.isArray(response.data)) {
        response.data = response.data.map(account => transformApiAccount(account));
      }

      return response as ApiResponse<Account[]>;
    },

    async createAccount(accountData: Account): Promise<ApiResponse<Account>> {
      // Transform frontend data to API format
      const apiData = transformAccountForApi(accountData);

      const response = await fetchWithAuth<any>('accounts', 'POST', apiData);

      // Transform API response back to frontend format
      if (response.data) {
        response.data = transformApiAccount(response.data);
      }

      return response as ApiResponse<Account>;
    },

    async updateAccount(id: number, accountData: Account): Promise<ApiResponse<Account>> {
      // Transform frontend data to API format
      const apiData = transformAccountForApi(accountData);

      const response = await fetchWithAuth<any>(`accounts/${id}`, 'PUT', apiData);

      // Transform API response back to frontend format
      if (response.data) {
        response.data = transformApiAccount(response.data);
      }

      return response as ApiResponse<Account>;
    },

    async deleteAccount(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`accounts/${id}`, 'DELETE');
    },

    async updateBalance(id: number, balanceData: { balance: number }): Promise<ApiResponse<Account>> {
      const response = await fetchWithAuth<any>(`accounts/${id}/balance`, 'PUT', balanceData);

      // Transform API response back to frontend format
      if (response.data) {
        response.data = transformApiAccount(response.data);
      }

      return response as ApiResponse<Account>;
    },

    async shareAccount(id: number, shareData: any): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`accounts/${id}/share`, 'POST', shareData);
    },

    async removeAccountShare(shareId: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`accounts/share/${shareId}`, 'DELETE');
    },
  },

  // Transaction endpoints
  transactions: {
    async getTransactions(filters?: any): Promise<ApiResponse<Transaction[]>> {
      // Build query string from filters
      let queryString = '';
      if (filters) {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            params.append(key, String(value));
          }
        });
        queryString = params.toString();
        if (queryString) {
          queryString = `?${queryString}`;
        }
      }

      const response = await fetchWithAuth<any>(`transactions${queryString}`);

      // Transform API response to frontend format
      if (response.data && Array.isArray(response.data)) {
        response.data = response.data.map(transaction => transformApiTransaction(transaction));
      }

      return response as ApiResponse<Transaction[]>;
    },

    async getTransaction(id: number): Promise<ApiResponse<Transaction>> {
      const response = await fetchWithAuth<any>(`transactions/${id}`);

      // Transform API response to frontend format
      if (response.data) {
        response.data = transformApiTransaction(response.data);
      }

      return response as ApiResponse<Transaction>;
    },

    async getTransactionsByAccount(accountId: number, startDate?: string, endDate?: string, type?: number): Promise<ApiResponse<Transaction[]>> {
      let queryString = '';
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);
      if (type !== undefined) params.append('type', String(type));

      queryString = params.toString();
      if (queryString) {
        queryString = `?${queryString}`;
      }

      const response = await fetchWithAuth<any>(`transactions/account/${accountId}${queryString}`);

      // Transform API response to frontend format
      if (response.data && Array.isArray(response.data)) {
        response.data = response.data.map(transaction => transformApiTransaction(transaction));
      }

      return response as ApiResponse<Transaction[]>;
    },

    async createTransaction(transactionData: Transaction): Promise<ApiResponse<Transaction>> {
      // Transform frontend data to API format
      const apiData = transformTransactionForApi(transactionData);

      const response = await fetchWithAuth<any>('transactions', 'POST', apiData);

      // Transform API response back to frontend format
      if (response.data) {
        response.data = transformApiTransaction(response.data);
      }

      return response as ApiResponse<Transaction>;
    },

    async updateTransaction(id: number, transactionData: Transaction): Promise<ApiResponse<Transaction>> {
      // Transform frontend data to API format
      const apiData = transformTransactionForApi(transactionData);

      const response = await fetchWithAuth<any>(`transactions/${id}`, 'PUT', apiData);

      // Transform API response back to frontend format
      if (response.data) {
        response.data = transformApiTransaction(response.data);
      }

      return response as ApiResponse<Transaction>;
    },

    async deleteTransaction(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`transactions/${id}`, 'DELETE');
    },

    async batchCreateTransactions(transactionsData: Transaction[]): Promise<ApiResponse<Transaction[]>> {
      // Transform frontend data to API format
      const apiData = transactionsData.map(transaction => transformTransactionForApi(transaction));

      const response = await fetchWithAuth<any>('transactions/batch', 'POST', apiData);

      // Transform API response back to frontend format
      if (response.data && Array.isArray(response.data)) {
        response.data = response.data.map(transaction => transformApiTransaction(transaction));
      }

      return response as ApiResponse<Transaction[]>;
    },

    async batchUpdateTransactions(transactionsData: Transaction[]): Promise<ApiResponse<Transaction[]>> {
      // Transform frontend data to API format
      const apiData = transactionsData.map(transaction => transformTransactionForApi(transaction));

      const response = await fetchWithAuth<any>('transactions/batch', 'PUT', apiData);

      // Transform API response back to frontend format
      if (response.data && Array.isArray(response.data)) {
        response.data = response.data.map(transaction => transformApiTransaction(transaction));
      }

      return response as ApiResponse<Transaction[]>;
    },

    async batchDeleteTransactions(transactionIds: number[]): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('transactions/batch', 'DELETE', { transactionIds });
    },

    async getTransactionStatistics(startDate: string, endDate: string, accountId?: number): Promise<ApiResponse<any>> {
      let queryString = `?startDate=${startDate}&endDate=${endDate}`;
      if (accountId) {
        queryString += `&accountId=${accountId}`;
      }
      return await fetchWithAuth<any>(`transactions/statistics${queryString}`);
    },

    async getCategorySummary(startDate: string, endDate: string, type: number): Promise<ApiResponse<any>> {
      let queryString = `?startDate=${startDate}&endDate=${endDate}&type=${type}`;
      return await fetchWithAuth<any>(`transactions/category-summary${queryString}`);
    },
  },

  // Category endpoints
  categories: {
    async getCategories(): Promise<ApiResponse<Category[]>> {
      const response = await fetchWithAuth<any>('categories');

      // Transform API response to frontend format
      if (response.data && Array.isArray(response.data)) {
        response.data = response.data.map(category => transformApiCategory(category));
      }

      return response as ApiResponse<Category[]>;
    },

    async getCategory(id: number): Promise<ApiResponse<Category>> {
      const response = await fetchWithAuth<any>(`categories/${id}`);

      // Transform API response to frontend format
      if (response.data) {
        response.data = transformApiCategory(response.data);
      }

      return response as ApiResponse<Category>;
    },

    async getCategoriesByType(type: string): Promise<ApiResponse<Category[]>> {
      const response = await fetchWithAuth<any>(`categories/type/${type}`);

      // Transform API response to frontend format
      if (response.data && Array.isArray(response.data)) {
        response.data = response.data.map(category => transformApiCategory(category));
      }

      return response as ApiResponse<Category[]>;
    },

    async getSystemCategories(): Promise<ApiResponse<Category[]>> {
      const response = await fetchWithAuth<any>('categories/system');

      // Transform API response to frontend format
      if (response.data && Array.isArray(response.data)) {
        response.data = response.data.map(category => transformApiCategory(category));
      }

      return response as ApiResponse<Category[]>;
    },

    async getFamilyCategories(familyId: number): Promise<ApiResponse<Category[]>> {
      const response = await fetchWithAuth<any>(`categories/family/${familyId}`);

      // Transform API response to frontend format
      if (response.data && Array.isArray(response.data)) {
        response.data = response.data.map(category => transformApiCategory(category));
      }

      return response as ApiResponse<Category[]>;
    },

    async createCategory(categoryData: Category): Promise<ApiResponse<Category>> {
      // Transform frontend data to API format
      const apiData = transformCategoryForApi(categoryData);

      const response = await fetchWithAuth<any>('categories', 'POST', apiData);

      // Transform API response back to frontend format
      if (response.data) {
        response.data = transformApiCategory(response.data);
      }

      return response as ApiResponse<Category>;
    },

    async updateCategory(id: number, categoryData: Category): Promise<ApiResponse<Category>> {
      // Transform frontend data to API format
      const apiData = transformCategoryForApi(categoryData);

      const response = await fetchWithAuth<any>(`categories/${id}`, 'PUT', apiData);

      // Transform API response back to frontend format
      if (response.data) {
        response.data = transformApiCategory(response.data);
      }

      return response as ApiResponse<Category>;
    },

    async deleteCategory(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`categories/${id}`, 'DELETE');
    },

    // Budget endpoints
    async setBudgetLimit(budgetData: any): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('categories/budget', 'POST', budgetData);
    },

    async getBudgetLimit(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`categories/budget/${id}`);
    },

    async getBudgetLimitsByCategory(categoryId: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`categories/budget/category/${categoryId}`);
    },

    async getAllBudgetLimits(): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('categories/budget');
    },

    async updateBudgetLimit(id: number, budgetData: any): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`categories/budget/${id}`, 'PUT', budgetData);
    },

    async deleteBudgetLimit(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`categories/budget/${id}`, 'DELETE');
    },
  },

  // Priority endpoints
  priorities: {
    async getAllPriorities(): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('priorities');
    },

    async getPriority(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`priorities/${id}`);
    },

    async createPriority(priorityData: any): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('priorities', 'POST', priorityData);
    },

    async updatePriority(id: number, priorityData: any): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`priorities/${id}`, 'PUT', priorityData);
    },

    async deletePriority(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`priorities/${id}`, 'DELETE');
    },
  },

  // Notifications endpoints
  notifications: {
    // Basic notifications
    async getNotifications(includeRead?: boolean): Promise<ApiResponse<Notification[]>> {
      const queryParams = includeRead !== undefined ? `?includeRead=${includeRead}` : '';
      return await fetchWithAuth<Notification[]>(`notification${queryParams}`);
    },

    async getNotification(id: number): Promise<ApiResponse<Notification>> {
      return await fetchWithAuth<Notification>(`notification/${id}`);
    },

    async markAsRead(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`notification/${id}/mark-read`, 'POST');
    },

    async markAllAsRead(): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('notification/mark-all-read', 'POST');
    },

    async deleteNotification(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`notification/${id}`, 'DELETE');
    },

    // Scheduled notifications
    async getScheduledNotifications(includeSent?: boolean): Promise<ApiResponse<any>> {
      const queryParams = includeSent !== undefined ? `?includeSent=${includeSent}` : '';
      return await fetchWithAuth<any>(`notification/scheduled${queryParams}`);
    },

    async getScheduledNotification(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`notification/scheduled/${id}`);
    },

    async scheduleNotification(notificationData: any): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('notification/schedule', 'POST', notificationData);
    },

    async updateScheduledNotification(id: number, notificationData: any): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`notification/scheduled/${id}`, 'PUT', notificationData);
    },

    async deleteScheduledNotification(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`notification/scheduled/${id}`, 'DELETE');
    },

    // Recurring notifications
    async getRecurringNotifications(includeInactive?: boolean): Promise<ApiResponse<any>> {
      const queryParams = includeInactive !== undefined ? `?includeInactive=${includeInactive}` : '';
      return await fetchWithAuth<any>(`notification/recurring${queryParams}`);
    },

    async getRecurringNotification(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`notification/recurring/${id}`);
    },

    async createRecurringNotification(notificationData: any): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('notification/recurring', 'POST', notificationData);
    },

    async updateRecurringNotification(id: number, notificationData: any): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`notification/recurring/${id}`, 'PUT', notificationData);
    },

    async deleteRecurringNotification(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`notification/recurring/${id}`, 'DELETE');
    },

    // Device management
    async registerDevice(deviceData: any): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('notification/devices', 'POST', deviceData);
    },

    async updateDeviceToken(deviceId: string, tokenData: any): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`notification/devices/${deviceId}`, 'PUT', tokenData);
    },

    async unregisterDevice(deviceId: string): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`notification/devices/${deviceId}`, 'DELETE');
    },

    // Monthly summary
    async getMonthlySummary(month: number, year: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`notification/monthly-summary/${month}/${year}`);
    },
  },

  // Loans endpoints
  loans: {
    async getLoans(asLender?: boolean, asBorrower?: boolean, status?: string): Promise<ApiResponse<Loan[]>> {
      let queryParams = '';
      if (asLender !== undefined) queryParams += `asLender=${asLender}&`;
      if (asBorrower !== undefined) queryParams += `asBorrower=${asBorrower}&`;
      if (status) queryParams += `status=${status}&`;

      const endpoint = queryParams ? `loans?${queryParams.slice(0, -1)}` : 'loans';
      return await fetchWithAuth<Loan[]>(endpoint);
    },

    async getLoan(id: number): Promise<ApiResponse<Loan>> {
      return await fetchWithAuth<Loan>(`loans/${id}`);
    },

    async createLoan(loanData: CreateLoanRequest): Promise<ApiResponse<Loan>> {
      return await fetchWithAuth<Loan>('loans', 'POST', loanData);
    },

    async updateLoan(id: number, loanData: UpdateLoanRequest): Promise<ApiResponse<Loan>> {
      return await fetchWithAuth<Loan>(`loans/${id}`, 'PUT', loanData);
    },

    async deleteLoan(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`loans/${id}`, 'DELETE');
    },

    async getLoanTimeline(id: number): Promise<ApiResponse<LoanTimeline>> {
      return await fetchWithAuth<LoanTimeline>(`loans/${id}/timeline`);
    },

    async calculateEMI(calculationData: LoanEMICalculationRequest): Promise<ApiResponse<LoanEMICalculationResponse>> {
      return await fetchWithAuth<LoanEMICalculationResponse>('loans/calculate-emi', 'POST', calculationData);
    },

    // Payment operations
    async getPayments(loanId: number): Promise<ApiResponse<LoanPayment[]>> {
      return await fetchWithAuth<LoanPayment[]>(`loan-payments/by-loan/${loanId}`);
    },

    async getPayment(id: number): Promise<ApiResponse<LoanPayment>> {
      return await fetchWithAuth<LoanPayment>(`loan-payments/${id}`);
    },

    async createPayment(paymentData: CreateLoanPaymentRequest): Promise<ApiResponse<LoanPayment>> {
      return await fetchWithAuth<LoanPayment>('loan-payments', 'POST', paymentData);
    },

    async updatePayment(id: number, paymentData: UpdateLoanPaymentRequest): Promise<ApiResponse<LoanPayment>> {
      return await fetchWithAuth<LoanPayment>(`loan-payments/${id}`, 'PUT', paymentData);
    },

    async deletePayment(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`loan-payments/${id}`, 'DELETE');
    },

    // Reminder operations
    async getReminders(loanId: number): Promise<ApiResponse<LoanReminder[]>> {
      return await fetchWithAuth<LoanReminder[]>(`loan-reminders/by-loan/${loanId}`);
    },

    async getActiveReminders(): Promise<ApiResponse<LoanReminder[]>> {
      return await fetchWithAuth<LoanReminder[]>('loan-reminders/active');
    },

    async getReminder(id: number): Promise<ApiResponse<LoanReminder>> {
      return await fetchWithAuth<LoanReminder>(`loan-reminders/${id}`);
    },

    async createReminder(reminderData: CreateLoanReminderRequest): Promise<ApiResponse<LoanReminder>> {
      return await fetchWithAuth<LoanReminder>('loan-reminders', 'POST', reminderData);
    },

    async updateReminder(id: number, reminderData: UpdateLoanReminderRequest): Promise<ApiResponse<LoanReminder>> {
      return await fetchWithAuth<LoanReminder>(`loan-reminders/${id}`, 'PUT', reminderData);
    },

    async deleteReminder(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`loan-reminders/${id}`, 'DELETE');
    },

    async markReminderAsSent(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`loan-reminders/${id}/mark-sent`, 'POST');
    },
  },

  // Savings and Wishlist endpoints
  savings: savingsService,

  // Analytics endpoints
  analytics: analyticsService,

  // Budget Control endpoints
  budgetControl: budgetControlService,

  // Approval endpoints
  approval: approvalService,

  // Receipt endpoints
  receipts: receiptService,

  // Sync endpoints
  sync: syncService,

  // Feature endpoints
  features: {
    async getFeatures(): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('features');
    },

    async getFeature(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`features/${id}`);
    },

    async getFeatureByName(name: string): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`features/name/${name}`);
    },

    async createFeature(featureData: any): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>('features', 'POST', featureData);
    },

    async updateFeature(id: number, isEnabled: boolean, enabledFor?: string): Promise<ApiResponse<any>> {
      const updateData: any = { isEnabled };
      if (enabledFor !== undefined) {
        updateData.enabledFor = enabledFor;
      }
      return await fetchWithAuth<any>(`features/${id}`, 'PUT', updateData);
    },

    async deleteFeature(id: number): Promise<ApiResponse<any>> {
      return await fetchWithAuth<any>(`features/${id}`, 'DELETE');
    },

    async isFeatureEnabled(name: string): Promise<ApiResponse<boolean>> {
      return await fetchWithAuth<boolean>(`features/enabled/${name}`);
    },
  },
};

export default apiService;
