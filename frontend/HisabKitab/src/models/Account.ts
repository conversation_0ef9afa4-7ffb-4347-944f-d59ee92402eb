/**
 * Account models for the frontend
 */

export interface Account {
  id: number;
  name: string;
  accountType: string;
  balance: number;
  initialBalance: number;
  currency: string;
  isActive: boolean;
  excludeFromStats: boolean;
  lastUpdatedAt: Date;
  isSynced: boolean;
  isDeleted?: boolean;
  userId?: number;
  createdAt?: string;
  updatedAt?: string;
  familyId?: number;
  sharedWith?: AccountShare[];
}

export interface AccountCreateRequest {
  name: string;
  accountType: string;
  initialBalance: number;
  currency: string;
  excludeFromStats?: boolean;
}

export interface AccountUpdateRequest {
  id: number;
  name?: string;
  accountType?: string;
  balance?: number;
  initialBalance?: number;
  currency?: string;
  isActive?: boolean;
  excludeFromStats?: boolean;
}

export interface AccountSummary {
  id: number;
  name: string;
  accountType: string;
  balance: number;
  currency: string;
}

export interface AccountBalance {
  accountId: number;
  accountName: string;
  balance: number;
  currency: string;
}

export interface AccountFilterOptions {
  accountType?: string;
  isActive?: boolean;
  excludeFromStats?: boolean;
}

export interface AccountShare {
  id: number;
  familyMemberId: number;
  permissions: string;
  familyMemberName: string;
}

// Account types
export const ACCOUNT_TYPES = {
  CASH: 'Cash',
  BANK: 'Bank',
  CREDIT_CARD: 'CreditCard',
  INVESTMENT: 'Investment',
  LOAN: 'Loan',
  OTHER: 'Other',
};

// Helper functions
export const getAccountTypeLabel = (type: string): string => {
  switch (type) {
    case ACCOUNT_TYPES.CASH:
      return 'Cash';
    case ACCOUNT_TYPES.BANK:
      return 'Bank Account';
    case ACCOUNT_TYPES.CREDIT_CARD:
      return 'Credit Card';
    case ACCOUNT_TYPES.INVESTMENT:
      return 'Investment';
    case ACCOUNT_TYPES.LOAN:
      return 'Loan';
    case ACCOUNT_TYPES.OTHER:
      return 'Other';
    default:
      return type;
  }
};
