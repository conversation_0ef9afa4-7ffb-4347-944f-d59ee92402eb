/**
 * Receipt models for the frontend
 */

export interface ReceiptItem {
  name: string;
  amount: number;
  quantity?: number;
  categoryId?: number;
  categoryName?: string;
}

export interface ReceiptData {
  id?: number;
  transactionId?: number;
  merchantName?: string;
  date?: string;
  totalAmount: number;
  items: ReceiptItem[];
  rawText: string;
  imageUri?: string;
  language?: string;
  isProcessed?: boolean;
  confidence?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface ProcessReceiptRequest {
  imageBase64: string;
  language?: string;
  transactionId?: number;
}

export interface SaveReceiptRequest {
  transactionId: number;
  merchantName?: string;
  date?: string;
  totalAmount: number;
  items: ReceiptItem[];
  rawText: string;
  imageUri: string;
  language?: string;
}

export interface ReceiptHistoryItem {
  id: number;
  transactionId: number;
  transactionDescription?: string;
  merchantName?: string;
  date: string;
  totalAmount: number;
  imageUri?: string;
  isProcessed: boolean;
  createdAt: string;
}

export interface ReceiptFilterOptions {
  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
  merchantName?: string;
  isProcessed?: boolean;
}

// Helper functions for receipt data
export const calculateReceiptTotal = (items: ReceiptItem[]): number => {
  return items.reduce((total, item) => {
    const quantity = item.quantity || 1;
    return total + (item.amount * quantity);
  }, 0);
};

export const formatReceiptDate = (dateStr?: string): string => {
  if (!dateStr) return '';
  
  // Try to parse the date string
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) {
      // If invalid date, return the original string
      return dateStr;
    }
    
    // Format as YYYY-MM-DD
    return date.toISOString().split('T')[0];
  } catch (error) {
    console.error('Error formatting receipt date:', error);
    return dateStr;
  }
};

export const getReceiptStatusColor = (isProcessed: boolean): string => {
  return isProcessed ? '#3FB950' : '#F0883E'; // Green if processed, Orange if pending
};

export const getReceiptStatusText = (isProcessed: boolean): string => {
  return isProcessed ? 'Processed' : 'Pending';
};
