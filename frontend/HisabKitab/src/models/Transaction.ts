/**
 * Transaction models for the frontend
 */

export enum TransactionType {
  Income = 0,
  Expense = 1,
  Transfer = 2
}

export enum SyncStatus {
  Pending = 0,
  Synced = 1,
  Failed = 2
}

export enum FrequencyType {
  Daily = 0,
  Weekly = 1,
  Monthly = 2,
  Quarterly = 3,
  Yearly = 4
}

export interface TransactionItem {
  id: number;
  transactionId: number;
  name: string;
  amount: number;
  quantity: number;
  categoryId?: number;
  categoryName?: string;
  notes?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface Transaction {
  id: number;
  amount: number;
  description: string;
  date: string;
  type: TransactionType;
  accountId: number;
  accountName: string;
  toAccountId?: number;
  toAccountName?: string;
  categoryId: number;
  categoryName: string;
  priorityId?: number;
  priorityName?: string;
  userId: number;
  username: string;
  status: string;
  approvedByUserId?: number;
  approvedByUsername?: string;
  receiptImage?: string;
  tags?: string;
  location?: string;
  exchangeRate?: number;
  recurringTransactionId?: number;
  isSynced: boolean;
  syncStatus: SyncStatus;
  lastUpdatedAt: string;
  createdAt: string;
  updatedAt?: string;
  items: TransactionItem[];
}

export interface CreateTransactionDto {
  amount: number;
  description?: string;
  date: string;
  type: TransactionType;
  accountId: number;
  toAccountId?: number;
  categoryId: number;
  priorityId?: number;
  status?: string;
  receiptImage?: string;
  tags?: string;
  location?: string;
  exchangeRate?: number;
  recurringTransactionId?: number;
  items?: CreateTransactionItemDto[];
}

export interface CreateTransactionItemDto {
  name: string;
  amount: number;
  quantity?: number;
  categoryId?: number;
  notes?: string;
}

export interface UpdateTransactionDto {
  amount?: number;
  description?: string;
  date?: string;
  type?: TransactionType;
  accountId?: number;
  toAccountId?: number;
  categoryId?: number;
  priorityId?: number;
  status?: string;
  receiptImage?: string;
  tags?: string;
  location?: string;
  exchangeRate?: number;
  recurringTransactionId?: number;
  items?: UpdateTransactionItemDto[];
}

export interface UpdateTransactionItemDto {
  id?: number;
  name?: string;
  amount?: number;
  quantity?: number;
  categoryId?: number;
  notes?: string;
}

export interface TransactionFilterDto {
  startDate?: string;
  endDate?: string;
  type?: TransactionType;
  accountId?: number;
  categoryId?: number;
  priorityId?: number;
  status?: string;
  searchTerm?: string;
  sortBy?: string;
  ascending?: boolean;
  skip?: number;
  take?: number;
}

export interface BatchTransactionDto {
  transactions: CreateTransactionDto[];
}

export interface BatchUpdateTransactionDto {
  transactions: (UpdateTransactionDto & { id: number })[];
}

export interface BatchDeleteTransactionDto {
  transactionIds: number[];
}

export interface TransactionStatistics {
  startDate: string;
  endDate: string;
  accountId?: number;
  income: number;
  expense: number;
  balance: number;
}

// Helper functions for transactions
export const getTransactionTypeLabel = (type: TransactionType): string => {
  switch (type) {
    case TransactionType.Income:
      return 'Income';
    case TransactionType.Expense:
      return 'Expense';
    case TransactionType.Transfer:
      return 'Transfer';
    default:
      return 'Unknown';
  }
};

export const getTransactionTypeFromString = (type: string): TransactionType => {
  switch (type.toLowerCase()) {
    case 'income':
      return TransactionType.Income;
    case 'expense':
      return TransactionType.Expense;
    case 'transfer':
      return TransactionType.Transfer;
    default:
      return TransactionType.Expense; // Default to expense
  }
};

export const getTransactionStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'completed':
      return '#3FB950'; // Green
    case 'pending':
      return '#F0883E'; // Orange
    case 'rejected':
      return '#F85149'; // Red
    default:
      return '#8B949E'; // Gray
  }
};

export const getTransactionTypeColor = (type: TransactionType): string => {
  switch (type) {
    case TransactionType.Income:
      return '#3FB950'; // Green
    case TransactionType.Expense:
      return '#F85149'; // Red
    case TransactionType.Transfer:
      return '#58A6FF'; // Blue
    default:
      return '#8B949E'; // Gray
  }
};

export const getTransactionTypeIcon = (type: TransactionType): string => {
  switch (type) {
    case TransactionType.Income:
      return 'arrow-down-outline';
    case TransactionType.Expense:
      return 'arrow-up-outline';
    case TransactionType.Transfer:
      return 'swap-horizontal-outline';
    default:
      return 'help-circle-outline';
  }
};
