// Define interfaces locally to avoid import issues
interface Priority {
  id: number;
  name: string;
  description?: string;
  color?: string;
  order: number;
  isSystem: boolean;
  createdAt: string;
  updatedAt?: string;
}

interface SavingsGoal {
  id: number;
  title: string;
  description?: string;
  targetAmount: number;
  currentAmount: number;
  startDate: string;
  targetDate: string;
  userId?: number;
  familyId?: number;
  isShared: boolean;
  priorityId?: number;
  priority?: Priority;
  autoContribute: boolean;
  autoContributeAmount?: number;
  autoContributeFrequency?: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  progress: number;
  remainingAmount: number;
  daysRemaining: number;
  isOnTrack: boolean;
}

export type WishlistItemStatus = 'Pending' | 'Purchased' | 'Abandoned';

export interface WishlistItem {
  id: number;
  title: string;
  description?: string;
  estimatedPrice: number;
  productUrl?: string;
  imageUrl?: string;
  addedDate: string;
  targetPurchaseDate?: string;
  userId?: number;
  familyId?: number;
  isShared: boolean;
  priorityId?: number;
  priority?: Priority;
  linkedSavingsGoalId?: number;
  linkedSavingsGoal?: SavingsGoal;
  status: WishlistItemStatus;
  createdAt: string;
  updatedAt: string;
}

export interface CreateWishlistItemRequest {
  title: string;
  description?: string;
  estimatedPrice: number;
  productUrl?: string;
  imageUrl?: string;
  targetPurchaseDate?: string;
  familyId?: number;
  isShared: boolean;
  priorityId?: number;
  linkedSavingsGoalId?: number;
}

export interface UpdateWishlistItemRequest {
  id: number;
  title: string;
  description?: string;
  estimatedPrice: number;
  productUrl?: string;
  imageUrl?: string;
  targetPurchaseDate?: string;
  familyId?: number;
  isShared: boolean;
  priorityId?: number;
  linkedSavingsGoalId?: number;
  status: WishlistItemStatus;
}
