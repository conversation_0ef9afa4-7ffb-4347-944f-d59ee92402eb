/**
 * Feature flag related models
 */

export interface FeatureFlag {
  id: number;
  name: string;
  description: string;
  isEnabled: boolean;
  enabledFor: string; // JSON string: "all", "beta", or array of user IDs
  lastUpdatedAt: string | Date;
}

export interface FeatureFlagCreateRequest {
  name: string;
  description: string;
  isEnabled: boolean;
  enabledFor: string;
}

export interface FeatureFlagUpdateRequest {
  id: number;
  name?: string;
  description?: string;
  isEnabled?: boolean;
  enabledFor?: string;
}
