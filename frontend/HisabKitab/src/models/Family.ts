/**
 * Family model for the frontend
 */

export interface Family {
  id: number;
  name: string;
  description?: string;
  createdByUserId: number;
  createdByUsername?: string;
  createdAt: string;
  updatedAt?: string;
  memberCount: number;
  isUserAdmin: boolean;
}

export interface FamilyMember {
  id: number;
  familyId: number;
  userId: number;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  joinedAt: string;
}

export interface CreateFamilyRequest {
  name: string;
  description?: string;
}

export interface UpdateFamilyRequest {
  id: number;
  name: string;
  description?: string;
}

export interface InviteCodeResponse {
  familyId: number;
  inviteCode: string;
  expiresAt: string;
}

export interface JoinFamilyRequest {
  inviteCode: string;
}

export interface AddFamilyMemberRequest {
  userId: number;
  role: string;
}

export interface UpdateFamilyMemberRoleRequest {
  role: string;
}
