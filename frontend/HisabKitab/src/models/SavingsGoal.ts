import { FrequencyType } from './Transaction';

// Define interfaces locally to avoid import issues
interface Priority {
  id: number;
  name: string;
  description?: string;
  color?: string;
  order: number;
  isSystem: boolean;
  createdAt: string;
  updatedAt?: string;
}

interface WishlistItem {
  id: number;
  title: string;
  description?: string;
  estimatedPrice: number;
  productUrl?: string;
  imageUrl?: string;
  addedDate: string;
  targetPurchaseDate?: string;
  userId?: number;
  familyId?: number;
  isShared: boolean;
  priorityId?: number;
  priority?: Priority;
  linkedSavingsGoalId?: number;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export type SavingsGoalStatus = 'Active' | 'Achieved' | 'Abandoned';

export interface SavingsContribution {
  id: number;
  savingsGoalId: number;
  amount: number;
  date: string;
  notes?: string;
  userId: number;
  userName?: string;
}

export interface SavingsGoal {
  id: number;
  title: string;
  description?: string;
  targetAmount: number;
  currentAmount: number;
  startDate: string;
  targetDate: string;
  userId?: number;
  familyId?: number;
  isShared: boolean;
  priorityId?: number;
  priority?: Priority;
  autoContribute: boolean;
  autoContributeAmount?: number;
  autoContributeFrequency?: FrequencyType;
  status: SavingsGoalStatus;
  contributions?: SavingsContribution[];
  linkedWishlistItems?: WishlistItem[];
  createdAt: string;
  updatedAt: string;
  progress: number; // Calculated field (currentAmount / targetAmount * 100)
  remainingAmount: number; // Calculated field (targetAmount - currentAmount)
  daysRemaining: number; // Calculated field
  isOnTrack: boolean; // Calculated field
}

export interface CreateSavingsGoalRequest {
  title: string;
  description?: string;
  targetAmount: number;
  startDate: string;
  targetDate: string;
  familyId?: number;
  isShared: boolean;
  priorityId?: number;
  autoContribute: boolean;
  autoContributeAmount?: number;
  autoContributeFrequency?: FrequencyType;
}

export interface UpdateSavingsGoalRequest {
  id: number;
  title: string;
  description?: string;
  targetAmount: number;
  startDate: string;
  targetDate: string;
  familyId?: number;
  isShared: boolean;
  priorityId?: number;
  autoContribute: boolean;
  autoContributeAmount?: number;
  autoContributeFrequency?: FrequencyType;
  status: SavingsGoalStatus;
}

export interface CreateContributionRequest {
  savingsGoalId: number;
  amount: number;
  date: string;
  notes?: string;
}

export interface UpdateContributionRequest {
  id: number;
  savingsGoalId: number;
  amount: number;
  date: string;
  notes?: string;
}

export interface SavingsGoalForecast {
  goalId: number;
  projectedCompletionDate: string;
  monthlyContributionNeeded: number;
  weeklyContributionNeeded: number;
  isAchievable: boolean;
  timelineData: { date: string; amount: number }[];
}
