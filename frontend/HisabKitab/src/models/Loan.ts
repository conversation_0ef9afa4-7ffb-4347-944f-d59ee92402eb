/**
 * Loan related models
 */

export enum InterestType {
  Flat = 0,
  ReducingBalance = 1,
  Compound = 2
}

export enum FrequencyType {
  Daily = 0,
  Weekly = 1,
  Monthly = 2,
  Quarterly = 3,
  Yearly = 4
}

export interface Loan {
  id: number;
  title: string;
  amount: number;
  interestRate: number;
  interestType: InterestType;
  interestTypeName: string;
  paymentFrequency: FrequencyType;
  paymentFrequencyName: string;
  totalPayableAmount: number;
  remainingAmount: number;
  paidAmount: number;
  startDate: string | Date;
  endDate?: string | Date;
  lenderUserId?: number;
  lenderUsername?: string;
  borrowerUserId?: number;
  borrowerUsername?: string;
  isExternalEntity: boolean;
  externalEntityName?: string;
  status: string;
  notes?: string;
  lastUpdatedAt: string | Date;
  createdAt: string | Date;
  updatedAt?: string | Date;
  payments?: LoanPayment[];
  reminders?: LoanReminder[];
  monthlyEMI: number;
  predictedCompletionDate?: string | Date;
}

export interface LoanPayment {
  id: number;
  loanId: number;
  amount: number;
  principalAmount: number;
  interestAmount: number;
  paymentDate: string | Date;
  paymentMethod?: string;
  isScheduled: boolean;
  transactionId?: number;
  notes?: string;
  createdAt: string | Date;
  updatedAt?: string | Date;
}

export interface LoanReminder {
  id: number;
  loanId: number;
  reminderDate: string | Date;
  message: string;
  isActive: boolean;
  isSent: boolean;
  sentAt?: string | Date;
  createdAt: string | Date;
  updatedAt?: string | Date;
}

export interface LoanTimeline {
  loanId: number;
  loanTitle: string;
  timeline: Record<string, number>;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  predictedCompletionDate?: string | Date;
}

// Request/Response interfaces

export interface CreateLoanRequest {
  title: string;
  amount: number;
  interestRate: number;
  interestType: InterestType;
  paymentFrequency: FrequencyType;
  startDate: string;
  endDate?: string;
  lenderUserId?: number;
  borrowerUserId?: number;
  isExternalEntity: boolean;
  externalEntityName?: string;
  status?: string;
  notes?: string;
}

export interface UpdateLoanRequest {
  title?: string;
  amount?: number;
  interestRate?: number;
  interestType?: InterestType;
  paymentFrequency?: FrequencyType;
  startDate?: string;
  endDate?: string;
  lenderUserId?: number;
  borrowerUserId?: number;
  isExternalEntity?: boolean;
  externalEntityName?: string;
  status?: string;
  notes?: string;
}

export interface CreateLoanPaymentRequest {
  loanId: number;
  amount: number;
  principalAmount: number;
  interestAmount: number;
  paymentDate: string;
  paymentMethod?: string;
  isScheduled?: boolean;
  transactionId?: number;
  notes?: string;
}

export interface UpdateLoanPaymentRequest {
  amount?: number;
  principalAmount?: number;
  interestAmount?: number;
  paymentDate?: string;
  paymentMethod?: string;
  isScheduled?: boolean;
  transactionId?: number;
  notes?: string;
}

export interface CreateLoanReminderRequest {
  loanId: number;
  reminderDate: string;
  message: string;
  isActive?: boolean;
}

export interface UpdateLoanReminderRequest {
  reminderDate?: string;
  message?: string;
  isActive?: boolean;
  isSent?: boolean;
  sentAt?: string;
}

export interface LoanEMICalculationRequest {
  principal: number;
  interestRate: number;
  interestType: InterestType;
  paymentFrequency: FrequencyType;
  startDate: string;
  endDate?: string;
}

export interface LoanEMICalculationResponse {
  emi: number;
}
