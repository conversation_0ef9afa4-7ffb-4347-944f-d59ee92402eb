/**
 * Category models for the frontend
 */

export interface Category {
  id: number;
  name: string;
  type: string;
  icon: string;
  color: string;
  parentCategoryId?: number | null;
  isSystem: boolean;
  isSynced?: boolean;
  isDeleted?: boolean;
  familyId?: number | null;
  userId?: number | null;
  createdAt: string;
}

export interface CategoryCreateRequest {
  name: string;
  type: string;
  icon?: string;
  color?: string;
  parentCategoryId?: number;
}

export interface CategoryUpdateRequest {
  id: number;
  name?: string;
  type?: string;
  icon?: string;
  color?: string;
  parentCategoryId?: number;
}

export interface CategorySummary {
  id: number;
  name: string;
  type: string;
  icon?: string;
  color?: string;
}

export interface CategoryFilterOptions {
  type?: string;
  parentCategoryId?: number;
  isSystem?: boolean;
}

// Category types
export const CATEGORY_TYPES = {
  INCOME: 'Income',
  EXPENSE: 'Expense',
};

// Helper functions
export const getCategoryTypeLabel = (type: string): string => {
  switch (type) {
    case CATEGORY_TYPES.INCOME:
      return 'Income';
    case CATEGORY_TYPES.EXPENSE:
      return 'Expense';
    default:
      return type;
  }
};
