/**
 * Priority model for the frontend
 */

export interface Priority {
  id: number;
  name: string;
  description?: string;
  color?: string;
  order: number;
  isSystem: boolean;
  createdAt: string;
  updatedAt?: string;
}

export interface CreatePriorityRequest {
  name: string;
  description?: string;
  color?: string;
  order?: number;
}

export interface UpdatePriorityRequest {
  id: number;
  name: string;
  description?: string;
  color?: string;
  order?: number;
}

// Default priority levels
export enum PriorityLevel {
  Urgent = 1,
  High = 2,
  Normal = 3,
  Low = 4
}

// Helper functions for priorities
export const getPriorityColor = (priorityName?: string): string => {
  if (!priorityName) return '#8B949E'; // Default gray
  
  switch (priorityName.toLowerCase()) {
    case 'urgent':
      return '#F85149'; // Red
    case 'high':
      return '#F0883E'; // Orange
    case 'normal':
      return '#3FB950'; // Green
    case 'low':
      return '#58A6FF'; // Blue
    default:
      return '#8B949E'; // Gray
  }
};

export const getPriorityIcon = (priorityName?: string): string => {
  if (!priorityName) return 'help-circle-outline';
  
  switch (priorityName.toLowerCase()) {
    case 'urgent':
      return 'alert-circle-outline';
    case 'high':
      return 'arrow-up-outline';
    case 'normal':
      return 'checkmark-circle-outline';
    case 'low':
      return 'arrow-down-outline';
    default:
      return 'help-circle-outline';
  }
};
