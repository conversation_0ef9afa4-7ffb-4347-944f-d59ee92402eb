# OCR & Sync Functionality Setup Guide

This guide provides instructions for setting up the OCR (Optical Character Recognition) and Sync functionality in the Hisab-Kitab application.

## Prerequisites

- .NET 8 SDK
- PostgreSQL database
- Tesseract OCR language files

## OCR Setup

### 1. Install Tesseract Language Files

The OCR functionality requires Tesseract language data files. Follow these steps to set them up:

1. Download the required language files from the [Tesseract GitHub repository](https://github.com/tesseract-ocr/tessdata):
   - [eng.traineddata](https://github.com/tesseract-ocr/tessdata/raw/main/eng.traineddata) (English)
   - [nep.traineddata](https://github.com/tesseract-ocr/tessdata/raw/main/nep.traineddata) (Nepali)

2. Create a `tessdata` directory in your application's output directory:
   ```
   mkdir -p backend/HisabKitab.API/bin/Debug/net8.0/tessdata
   ```

3. Copy the downloaded language files to the `tessdata` directory:
   ```
   cp eng.traineddata nep.traineddata backend/HisabKitab.API/bin/Debug/net8.0/tessdata/
   ```

### 2. Configure Storage Directory

The application needs a directory to store uploaded receipt images:

1. Create a `Storage` directory in your application's root directory:
   ```
   mkdir -p backend/HisabKitab.API/Storage/receipts
   ```

2. Ensure the application has write permissions to this directory.

## Database Migration

After adding the new entities for OCR and Sync functionality, you need to update the database schema:

1. Create a new migration:
   ```
   cd backend
   dotnet ef migrations add AddOcrAndSyncEntities -p HisabKitab.Infrastructure -s HisabKitab.API
   ```

2. Apply the migration to update the database:
   ```
   dotnet ef database update -p HisabKitab.Infrastructure -s HisabKitab.API
   ```

## Configuration

The application uses the following configuration settings for OCR and Sync functionality:

### OCR Configuration

In `appsettings.json`:

```json
"OCR": {
  "TessdataPath": "tessdata",
  "DefaultLanguage": "eng",
  "SupportedLanguages": ["eng", "nep"],
  "MaxImageSizeMB": 5
}
```

### File Storage Configuration

In `appsettings.json`:

```json
"FileStorage": {
  "BaseUrl": "/storage",
  "MaxFileSizeMB": 10,
  "AllowedExtensions": [".jpg", ".jpeg", ".png", ".pdf"],
  "ImageCompressionQuality": 85
}
```

### Sync Configuration

In `appsettings.json`:

```json
"Sync": {
  "BatchSize": 100,
  "MaxRetries": 3,
  "DefaultSyncInterval": 15
}
```

## API Endpoints

### Receipt Endpoints

- `POST /api/receipt/process` - Process a receipt image with OCR
- `GET /api/receipt/{id}` - Get a receipt by ID
- `GET /api/receipt/transaction/{transactionId}` - Get receipts by transaction ID
- `GET /api/receipt/history` - Get receipt history with optional filtering
- `POST /api/receipt` - Save a receipt
- `PUT /api/receipt/{id}` - Update a receipt
- `DELETE /api/receipt/{id}` - Delete a receipt

### Sync Endpoints

- `POST /api/sync/batch` - Process a batch of sync queue items
- `GET /api/sync/lastSyncTime` - Get the last sync time for the current user
- `GET /api/sync/changes` - Get changes since a specific time
- `GET /api/sync/conflicts` - Get conflicts for the current user
- `POST /api/sync/conflicts/{id}/resolve` - Resolve a conflict

## Troubleshooting

### OCR Issues

- **Missing language files**: If you see errors about missing language files, ensure you've downloaded and placed the correct files in the `tessdata` directory.
- **Poor OCR quality**: Try improving the image quality or adjusting the image before processing.

### Sync Issues

- **Conflicts**: Conflicts occur when the same entity is modified in different ways on the client and server. Use the conflict resolution endpoints to resolve these issues.
- **Failed sync items**: Check the error messages in the sync queue items to diagnose issues.

## Testing

You can test the OCR functionality using Postman:

1. Create a new POST request to `/api/receipt/process`
2. Set the Content-Type to `application/json`
3. In the request body, include:
   ```json
   {
     "imageBase64": "base64_encoded_image_data",
     "language": "eng"
   }
   ```
4. Send the request and check the response for the extracted data

## Security Considerations

- The application validates base64 image data before processing
- File size limits are enforced to prevent denial of service attacks
- User authorization is checked for all operations
