using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Interfaces
{
    public interface IBudgetControlService
    {
        // Budget limit operations
        Task<BudgetLimit> CreateBudgetLimitAsync(
            int categoryId,
            decimal amount,
            FrequencyType period,
            DateTime startDate,
            DateTime? endDate,
            int? userId,
            int? familyId,
            int? familyMemberUserId,
            decimal notificationThreshold = 80,
            bool rolloverUnused = false);
            
        Task<BudgetLimit> GetBudgetLimitByIdAsync(int budgetLimitId);
        
        Task<IEnumerable<BudgetLimit>> GetBudgetLimitsByUserIdAsync(int userId);
        
        Task<IEnumerable<BudgetLimit>> GetBudgetLimitsByFamilyIdAsync(int familyId);
        
        Task<IEnumerable<BudgetLimit>> GetBudgetLimitsByFamilyMemberAsync(int familyId, int userId);
        
        Task<BudgetLimit> UpdateBudgetLimitAsync(
            int budgetLimitId,
            decimal amount,
            FrequencyType period,
            DateTime startDate,
            DateTime? endDate,
            decimal notificationThreshold,
            bool rolloverUnused);
            
        Task<bool> DeleteBudgetLimitAsync(int budgetLimitId);
        
        // Spending limit operations
        Task<SpendingLimit> CreateSpendingLimitAsync(
            int familyId,
            int familyMemberUserId,
            decimal amount,
            FrequencyType period,
            DateTime startDate,
            DateTime? endDate,
            int? categoryId,
            decimal notificationThreshold = 80,
            bool requireApprovalOverLimit = true);
            
        Task<SpendingLimit> GetSpendingLimitByIdAsync(int spendingLimitId);
        
        Task<IEnumerable<SpendingLimit>> GetSpendingLimitsByFamilyIdAsync(int familyId);
        
        Task<IEnumerable<SpendingLimit>> GetSpendingLimitsByFamilyMemberAsync(int familyId, int userId);
        
        Task<SpendingLimit> UpdateSpendingLimitAsync(
            int spendingLimitId,
            decimal amount,
            FrequencyType period,
            DateTime startDate,
            DateTime? endDate,
            int? categoryId,
            decimal notificationThreshold,
            bool requireApprovalOverLimit,
            bool isActive);
            
        Task<bool> DeleteSpendingLimitAsync(int spendingLimitId);
        
        // Budget tracking
        Task<decimal> GetCurrentSpendingAsync(
            int categoryId,
            int? userId,
            int? familyId,
            int? familyMemberUserId,
            DateTime? startDate,
            DateTime? endDate);
            
        Task<decimal> GetBudgetRemainingAsync(
            int budgetLimitId,
            DateTime? asOfDate = null);
            
        Task<decimal> GetSpendingLimitRemainingAsync(
            int spendingLimitId,
            DateTime? asOfDate = null);
            
        Task<bool> IsOverBudgetAsync(
            int categoryId,
            decimal amount,
            int? userId,
            int? familyId,
            int? familyMemberUserId);
            
        Task<bool> IsOverSpendingLimitAsync(
            int familyId,
            int familyMemberUserId,
            decimal amount,
            int? categoryId = null);
    }
}
