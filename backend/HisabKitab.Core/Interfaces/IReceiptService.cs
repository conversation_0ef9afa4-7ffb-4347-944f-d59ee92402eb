using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;

namespace HisabKitab.Core.Interfaces
{
    public interface IReceiptService
    {
        /// <summary>
        /// Create a new receipt
        /// </summary>
        Task<ReceiptData> CreateReceiptAsync(
            int transactionId,
            string imagePath,
            string ocrText,
            string parsedData,
            DateTime scanDate,
            string language = "eng",
            bool isProcessed = false);

        /// <summary>
        /// Get a receipt by ID
        /// </summary>
        Task<ReceiptData> GetReceiptByIdAsync(int receiptId);

        /// <summary>
        /// Get receipts by transaction ID
        /// </summary>
        Task<IEnumerable<ReceiptData>> GetReceiptsByTransactionIdAsync(int transactionId);

        /// <summary>
        /// Get receipt history with optional filtering
        /// </summary>
        Task<IEnumerable<ReceiptData>> GetReceiptHistoryAsync(
            int userId,
            DateTime? startDate = null,
            DateTime? endDate = null,
            decimal? minAmount = null,
            decimal? maxAmount = null,
            string? merchantName = null,
            bool? isProcessed = null);

        /// <summary>
        /// Update an existing receipt
        /// </summary>
        Task<ReceiptData> UpdateReceiptAsync(
            int receiptId,
            string? ocrText = null,
            string? parsedData = null,
            bool? isProcessed = null);

        /// <summary>
        /// Delete a receipt
        /// </summary>
        Task<bool> DeleteReceiptAsync(int receiptId);

        /// <summary>
        /// Process a receipt image and save the results
        /// </summary>
        Task<ReceiptData> ProcessAndSaveReceiptAsync(
            string base64Image,
            int? transactionId = null,
            string language = "eng");

        /// <summary>
        /// Check if a user can access a receipt
        /// </summary>
        Task<bool> UserCanAccessReceiptAsync(int receiptId, int userId);

        /// <summary>
        /// Batch operations for syncing
        /// </summary>
        Task<IEnumerable<ReceiptData>> BatchCreateReceiptsAsync(IEnumerable<ReceiptData> receipts);
        Task<IEnumerable<ReceiptData>> BatchUpdateReceiptsAsync(IEnumerable<ReceiptData> receipts);
        Task<bool> BatchDeleteReceiptsAsync(IEnumerable<int> receiptIds);
    }
}
