using System.Collections.Generic;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;

namespace HisabKitab.Core.Interfaces
{
    public interface IFamilyService
    {
        // Family CRUD operations
        Task<Family> CreateFamilyAsync(string name, string description, int createdByUserId, string? settings = null);
        Task<Family> GetFamilyByIdAsync(int familyId);
        Task<IEnumerable<Family>> GetFamiliesByUserIdAsync(int userId);
        Task<Family> UpdateFamilyAsync(int familyId, string name, string description, string? settings = null);
        Task<bool> DeleteFamilyAsync(int familyId);

        // Family member management
        Task<string> GenerateInviteCodeAsync(int familyId);
        Task<FamilyMember> AddMemberAsync(int familyId, int userId, string role = "Member");
        Task<FamilyMember> JoinFamilyWithInviteCodeAsync(string inviteCode, int userId);
        Task<bool> RemoveMemberAsync(int familyId, int userId);
        Task<bool> LeaveFamilyAsync(int familyId, int userId);
        Task<IEnumerable<FamilyMember>> GetFamilyMembersAsync(int familyId);

        // Family role management
        Task<bool> UpdateMemberRoleAsync(int familyId, int userId, string newRole);
        Task<bool> IsFamilyAdminAsync(int familyId, int userId);
        Task<bool> IsFamilyMemberAsync(int familyId, int userId);

        // Family validation
        Task<bool> FamilyExistsAsync(int familyId);
        Task<bool> IsValidInviteCodeAsync(string inviteCode);
    }
}
