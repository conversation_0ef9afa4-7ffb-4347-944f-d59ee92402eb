using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Interfaces
{
    public interface IApprovalService
    {
        // Approval request operations
        Task<ExpenseApprovalRequest> CreateApprovalRequestAsync(
            int requestedByUserId,
            int familyId,
            decimal amount,
            string description,
            int categoryId);
            
        Task<ExpenseApprovalRequest> GetApprovalRequestByIdAsync(int requestId);
        
        Task<IEnumerable<ExpenseApprovalRequest>> GetApprovalRequestsByFamilyIdAsync(
            int familyId,
            ApprovalStatus? status = null);
            
        Task<IEnumerable<ExpenseApprovalRequest>> GetApprovalRequestsByUserIdAsync(
            int userId,
            bool asRequester = true,
            ApprovalStatus? status = null);
            
        Task<ExpenseApprovalRequest> ApproveRequestAsync(
            int requestId,
            int approvedByUserId,
            int? accountId = null);
            
        Task<ExpenseApprovalRequest> RejectRequestAsync(
            int requestId,
            int rejectedByUserId,
            string rejectionReason);
            
        // Approval workflow
        Task<bool> RequiresApprovalAsync(
            int familyId,
            int userId,
            decimal amount,
            int categoryId);
            
        Task<Transaction> CreateTransactionFromApprovalAsync(
            int requestId,
            int accountId);
    }
}
