using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;

namespace HisabKitab.Core.Interfaces
{
    public interface IJwtService
    {
        Task<(string accessToken, string refreshToken)> GenerateTokensAsync(User user);
        ClaimsPrincipal GetPrincipalFromExpiredToken(string token);
        Task<bool> ValidateRefreshTokenAsync(string refreshToken, int userId);
        Task RevokeRefreshTokenAsync(string refreshToken);
    }
}
