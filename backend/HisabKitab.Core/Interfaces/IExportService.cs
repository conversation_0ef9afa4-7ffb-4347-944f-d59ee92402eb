using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;

namespace HisabKitab.Core.Interfaces
{
    public interface IExportService
    {
        // Export operations
        Task<ExportHistory> ExportToCsvAsync(
            string contentType,
            string parameters,
            int userId,
            int? reportId = null);
            
        Task<ExportHistory> ExportToPdfAsync(
            string contentType,
            string parameters,
            int userId,
            int? reportId = null);
            
        // Export history
        Task<ExportHistory> GetExportByIdAsync(int exportId);
        
        Task<IEnumerable<ExportHistory>> GetExportHistoryByUserIdAsync(
            int userId,
            int limit = 10,
            int offset = 0);
            
        Task<bool> DeleteExportAsync(int exportId);
        
        // File operations
        Task<string> GetExportFilePathAsync(int exportId);
    }
}
