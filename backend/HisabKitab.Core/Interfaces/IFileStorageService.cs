using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace HisabKitab.Core.Interfaces
{
    public interface IFileStorageService
    {
        /// <summary>
        /// Save a file from an IFormFile
        /// </summary>
        Task<string> SaveFileAsync(IFormFile file, string containerName, string? fileName = null);

        /// <summary>
        /// Save a file from a base64 string
        /// </summary>
        Task<string> SaveBase64FileAsync(string base64String, string containerName, string? fileName = null, string contentType = "image/jpeg");

        /// <summary>
        /// Save a file from a stream
        /// </summary>
        Task<string> SaveStreamAsync(Stream stream, string containerName, string fileName, string? contentType = null);

        /// <summary>
        /// Get a file as a stream
        /// </summary>
        Task<Stream> GetFileStreamAsync(string filePath);

        /// <summary>
        /// Get a file as a byte array
        /// </summary>
        Task<byte[]> GetFileAsync(string filePath);

        /// <summary>
        /// Delete a file
        /// </summary>
        Task<bool> DeleteFileAsync(string filePath);

        /// <summary>
        /// Check if a file exists
        /// </summary>
        Task<bool> FileExistsAsync(string filePath);

        /// <summary>
        /// Get a file's URL
        /// </summary>
        string GetFileUrl(string filePath);

        /// <summary>
        /// Optimize an image for storage (compress, resize, etc.)
        /// </summary>
        Task<byte[]> OptimizeImageAsync(byte[] imageData, int maxWidth = 1920, int maxHeight = 1080, int quality = 85);

        /// <summary>
        /// Optimize an image from a base64 string
        /// </summary>
        Task<string> OptimizeBase64ImageAsync(string base64Image, int maxWidth = 1920, int maxHeight = 1080, int quality = 85);
    }
}
