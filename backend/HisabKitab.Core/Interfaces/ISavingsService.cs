using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Interfaces
{
    public interface ISavingsService
    {
        // Savings Goal CRUD operations
        Task<SavingsGoal> CreateSavingsGoalAsync(
            string title,
            string description,
            decimal targetAmount,
            DateTime startDate,
            DateTime targetDate,
            int? userId,
            int? familyId,
            bool isShared,
            int? priorityId,
            bool autoContribute,
            decimal? autoContributeAmount,
            FrequencyType? autoContributeFrequency);

        Task<SavingsGoal> GetSavingsGoalByIdAsync(int goalId);

        Task<IEnumerable<SavingsGoal>> GetSavingsGoalsByUserIdAsync(
            int userId,
            string? status = null,
            DateTime? startDate = null,
            DateTime? endDate = null);

        Task<IEnumerable<SavingsGoal>> GetSavingsGoalsByFamilyIdAsync(
            int familyId,
            string? status = null,
            DateTime? startDate = null,
            DateTime? endDate = null);

        Task<SavingsGoal> UpdateSavingsGoalAsync(
            int goalId,
            string? title = null,
            string? description = null,
            decimal? targetAmount = null,
            DateTime? startDate = null,
            DateTime? targetDate = null,
            int? priorityId = null,
            bool? autoContribute = null,
            decimal? autoContributeAmount = null,
            FrequencyType? autoContributeFrequency = null,
            string? status = null);

        Task<bool> DeleteSavingsGoalAsync(int goalId);

        // Contribution operations
        Task<SavingsContribution> AddContributionAsync(
            int savingsGoalId,
            decimal amount,
            DateTime contributionDate,
            int? userId,
            int? transactionId,
            string notes);

        Task<SavingsContribution> GetContributionByIdAsync(int contributionId);

        Task<IEnumerable<SavingsContribution>> GetContributionsBySavingsGoalIdAsync(int savingsGoalId);

        Task<SavingsContribution> UpdateContributionAsync(
            int contributionId,
            decimal? amount = null,
            DateTime? contributionDate = null,
            string? notes = null);

        Task<bool> DeleteContributionAsync(int contributionId);

        // Forecasting operations
        Task<(DateTime projectedCompletionDate, decimal monthlyContributionNeeded, decimal weeklyContributionNeeded, bool isAchievable, IEnumerable<(DateTime date, decimal amount)> timelineData)>
            GenerateSavingsGoalForecastAsync(int goalId);

        // Wishlist Item CRUD operations
        Task<WishlistItem> CreateWishlistItemAsync(
            string title,
            string description,
            decimal estimatedPrice,
            string productUrl,
            string imageUrl,
            DateTime? targetPurchaseDate,
            int? userId,
            int? familyId,
            bool isShared,
            int? priorityId,
            int? linkedSavingsGoalId);

        Task<WishlistItem> GetWishlistItemByIdAsync(int itemId);

        Task<IEnumerable<WishlistItem>> GetWishlistItemsByUserIdAsync(
            int userId,
            string? status = null);

        Task<IEnumerable<WishlistItem>> GetWishlistItemsByFamilyIdAsync(
            int familyId,
            string? status = null);

        Task<WishlistItem> UpdateWishlistItemAsync(
            int itemId,
            string? title = null,
            string? description = null,
            decimal? estimatedPrice = null,
            string? productUrl = null,
            string? imageUrl = null,
            DateTime? targetPurchaseDate = null,
            int? priorityId = null,
            string? status = null);

        Task<bool> DeleteWishlistItemAsync(int itemId);

        // Link/Unlink operations
        Task<bool> LinkWishlistItemToSavingsGoalAsync(int wishlistItemId, int savingsGoalId);

        Task<bool> UnlinkWishlistItemFromSavingsGoalAsync(int wishlistItemId);

        // Validation methods
        Task<bool> SavingsGoalExistsAsync(int goalId);

        Task<bool> WishlistItemExistsAsync(int itemId);

        Task<bool> UserCanAccessSavingsGoalAsync(int goalId, int userId);

        Task<bool> UserCanAccessWishlistItemAsync(int itemId, int userId);

        Task<bool> UserCanModifySavingsGoalAsync(int goalId, int userId);

        Task<bool> UserCanModifyWishlistItemAsync(int itemId, int userId);
    }
}
