using System.Collections.Generic;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Interfaces
{
    public interface IPriorityService
    {
        // Priority CRUD operations
        Task<Priority> CreatePriorityAsync(string name, string description, PriorityLevel level, string color);
        Task<Priority> GetPriorityByIdAsync(int priorityId);
        Task<Priority> GetPriorityByLevelAsync(PriorityLevel level);
        Task<IEnumerable<Priority>> GetAllPrioritiesAsync();
        Task<Priority> UpdatePriorityAsync(int priorityId, string name, string description, PriorityLevel level, string color);
        Task<bool> DeletePriorityAsync(int priorityId);
        
        // Seed default priorities
        Task SeedDefaultPrioritiesAsync();
    }
}
