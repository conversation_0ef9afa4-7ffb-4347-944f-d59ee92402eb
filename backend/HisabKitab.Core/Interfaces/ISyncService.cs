using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;

namespace HisabKitab.Core.Interfaces
{
    public interface ISyncService
    {
        /// <summary>
        /// Process a batch of sync queue items
        /// </summary>
        Task<(IEnumerable<SyncQueue> Processed, IEnumerable<SyncConflict> Conflicts)> ProcessSyncBatchAsync(
            IEnumerable<SyncQueue> batch,
            int userId);

        /// <summary>
        /// Get the last sync time for a user
        /// </summary>
        Task<DateTime> GetLastSyncTimeAsync(int userId);

        /// <summary>
        /// Set the last sync time for a user
        /// </summary>
        Task SetLastSyncTimeAsync(int userId, DateTime syncTime);

        /// <summary>
        /// Get changes since a specific time
        /// </summary>
        Task<IEnumerable<SyncQueue>> GetChangesSinceAsync(
            int userId,
            DateTime since,
            int limit = 100,
            string? cursor = null);

        /// <summary>
        /// Create a sync conflict
        /// </summary>
        Task<SyncConflict> CreateConflictAsync(
            string entityType,
            int entityId,
            string localData,
            string remoteData,
            int userId);

        /// <summary>
        /// Get conflicts for a user
        /// </summary>
        Task<IEnumerable<SyncConflict>> GetConflictsByUserIdAsync(
            int userId,
            bool? resolved = null);

        /// <summary>
        /// Resolve a conflict
        /// </summary>
        Task<SyncConflict> ResolveConflictAsync(
            int conflictId,
            string resolution,
            string resolvedData);

        /// <summary>
        /// Add an item to the sync queue
        /// </summary>
        Task<SyncQueue> AddToSyncQueueAsync(
            string entityType,
            int entityId,
            string action,
            string entityData,
            int userId);

        /// <summary>
        /// Update the status of a sync queue item
        /// </summary>
        Task<SyncQueue> UpdateSyncQueueItemStatusAsync(
            int syncQueueItemId,
            string status,
            string? errorMessage = null);

        /// <summary>
        /// Check if an entity has conflicts
        /// </summary>
        Task<bool> HasConflictsAsync(string entityType, int entityId);

        /// <summary>
        /// Detect conflicts between local and remote data
        /// </summary>
        Task<bool> DetectConflictAsync(
            string entityType,
            int entityId,
            string localData,
            string remoteData);
    }
}
