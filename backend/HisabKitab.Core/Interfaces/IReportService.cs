using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;

namespace HisabKitab.Core.Interfaces
{
    public interface IReportService
    {
        // Report CRUD operations
        Task<Report> CreateReportAsync(
            string name,
            string type,
            string parameters,
            int userId,
            int? familyId = null,
            bool isShared = false,
            string? layout = null);

        Task<Report> GetReportByIdAsync(int reportId);

        Task<IEnumerable<Report>> GetReportsByUserIdAsync(int userId, bool includeShared = true);

        Task<IEnumerable<Report>> GetReportsByFamilyIdAsync(int familyId);

        Task<Report> UpdateReportAsync(
            int reportId,
            string name,
            string type,
            string parameters,
            bool isShared,
            string layout);

        Task<bool> DeleteReportAsync(int reportId);

        // Report generation
        Task<ReportData> GenerateReportAsync(int reportId);

        Task<ReportData> GenerateReportAsync(
            string type,
            string parameters,
            int userId,
            int? familyId = null);

        Task<ReportData> GetReportDataAsync(int reportId, bool forceRefresh = false);

        // Report sharing
        Task<bool> ShareReportAsync(int reportId, bool isShared);
    }
}
