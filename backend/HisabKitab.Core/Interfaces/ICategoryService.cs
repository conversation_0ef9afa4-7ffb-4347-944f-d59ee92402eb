using System.Collections.Generic;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Interfaces
{
    public interface ICategoryService
    {
        // Category CRUD operations
        Task<Category> CreateCategoryAsync(string name, CategoryType type, string icon, string color, int? parentCategoryId, int? userId, int? familyId, bool isSystem = false);
        Task<Category> GetCategoryByIdAsync(int categoryId);
        Task<IEnumerable<Category>> GetCategoriesByUserIdAsync(int userId);
        Task<IEnumerable<Category>> GetCategoriesByFamilyIdAsync(int familyId);
        Task<IEnumerable<Category>> GetSystemCategoriesAsync();
        Task<IEnumerable<Category>> GetCategoriesByTypeAsync(CategoryType type);
        Task<Category> UpdateCategoryAsync(int categoryId, string name, CategoryType type, string icon, string color, int? parentCategoryId);
        Task<bool> DeleteCategoryAsync(int categoryId);
        
        // Budget operations
        Task<BudgetLimit> SetBudgetLimitAsync(int categoryId, decimal amount, FrequencyType period, int? userId, int? familyId, int? familyMemberId, decimal notificationThreshold = 80, bool rolloverUnused = false);
        Task<BudgetLimit> GetBudgetLimitAsync(int budgetLimitId);
        Task<IEnumerable<BudgetLimit>> GetBudgetLimitsByCategoryAsync(int categoryId);
        Task<IEnumerable<BudgetLimit>> GetBudgetLimitsByUserAsync(int userId);
        Task<IEnumerable<BudgetLimit>> GetBudgetLimitsByFamilyAsync(int familyId);
        Task<BudgetLimit> UpdateBudgetLimitAsync(int budgetLimitId, decimal amount, FrequencyType period, decimal notificationThreshold, bool rolloverUnused);
        Task<bool> DeleteBudgetLimitAsync(int budgetLimitId);
        
        // Category validation
        Task<bool> CategoryExistsAsync(int categoryId);
        Task<bool> UserCanAccessCategoryAsync(int categoryId, int userId);
        Task<bool> UserCanModifyCategoryAsync(int categoryId, int userId);
        
        // Seed default categories
        Task SeedDefaultCategoriesAsync();
    }
}
