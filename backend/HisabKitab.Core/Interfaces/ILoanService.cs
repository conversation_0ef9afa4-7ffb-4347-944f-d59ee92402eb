using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Interfaces
{
    public interface ILoanService
    {
        // Loan CRUD operations
        Task<Loan> CreateLoanAsync(
            string title,
            decimal amount,
            decimal interestRate,
            InterestType interestType,
            FrequencyType paymentFrequency,
            DateTime startDate,
            DateTime? endDate,
            int? lenderUserId,
            int? borrowerUserId,
            bool isExternalEntity,
            string externalEntityName,
            string status,
            string notes);

        Task<Loan> GetLoanByIdAsync(int loanId);

        Task<IEnumerable<Loan>> GetLoansByUserIdAsync(
            int userId,
            bool asLender = false,
            bool asBorrower = false,
            string? status = null,
            DateTime? startDate = null,
            DateTime? endDate = null);

        Task<Loan> UpdateLoanAsync(
            int loanId,
            string? title = null,
            decimal? amount = null,
            decimal? interestRate = null,
            InterestType? interestType = null,
            FrequencyType? paymentFrequency = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int? lenderUserId = null,
            int? borrowerUserId = null,
            bool? isExternalEntity = null,
            string? externalEntityName = null,
            string? status = null,
            string? notes = null);

        Task<bool> DeleteLoanAsync(int loanId);

        // EMI and payment operations
        Task<decimal> CalculateEMIAsync(int loanId);

        Task<decimal> CalculateEMIAsync(
            decimal principal,
            decimal interestRate,
            InterestType interestType,
            FrequencyType paymentFrequency,
            DateTime startDate,
            DateTime? endDate);

        Task<IEnumerable<LoanPayment>> GeneratePaymentScheduleAsync(int loanId);

        Task<LoanPayment> RecordPaymentAsync(
            int loanId,
            decimal amount,
            decimal principalAmount,
            decimal interestAmount,
            DateTime paymentDate,
            string paymentMethod,
            bool isScheduled,
            int? transactionId,
            string notes);

        Task<LoanPayment> GetPaymentByIdAsync(int paymentId);

        Task<IEnumerable<LoanPayment>> GetPaymentsByLoanIdAsync(int loanId);

        Task<LoanPayment> UpdatePaymentAsync(
            int paymentId,
            decimal? amount = null,
            decimal? principalAmount = null,
            decimal? interestAmount = null,
            DateTime? paymentDate = null,
            string? paymentMethod = null,
            bool? isScheduled = null,
            int? transactionId = null,
            string? notes = null);

        Task<bool> DeletePaymentAsync(int paymentId);

        // Loan timeline and prediction
        Task<Dictionary<DateTime, decimal>> GenerateLoanTimelineAsync(int loanId);

        Task<DateTime> PredictCompletionDateAsync(int loanId);

        Task<decimal> CalculateRemainingBalanceAsync(int loanId, DateTime? asOfDate = null);

        // Reminder operations
        Task<LoanReminder> CreateReminderAsync(
            int loanId,
            DateTime reminderDate,
            string message,
            bool isActive = true);

        Task<LoanReminder> GetReminderByIdAsync(int reminderId);

        Task<IEnumerable<LoanReminder>> GetRemindersByLoanIdAsync(int loanId);

        Task<IEnumerable<LoanReminder>> GetActiveRemindersAsync(DateTime? beforeDate = null);

        Task<LoanReminder> UpdateReminderAsync(
            int reminderId,
            DateTime? reminderDate = null,
            string? message = null,
            bool? isActive = null,
            bool? isSent = null,
            DateTime? sentAt = null);

        Task<bool> DeleteReminderAsync(int reminderId);

        Task<bool> MarkReminderAsSentAsync(int reminderId);

        // Validation methods
        Task<bool> LoanExistsAsync(int loanId);

        Task<bool> UserCanAccessLoanAsync(int loanId, int userId);

        Task<bool> UserCanModifyLoanAsync(int loanId, int userId);
    }
}
