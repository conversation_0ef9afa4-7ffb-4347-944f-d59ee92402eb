using System.Collections.Generic;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;

namespace HisabKitab.Core.Interfaces
{
    public interface IAccountService
    {
        // Account CRUD operations
        Task<Account> CreateAccountAsync(string name, decimal initialBalance, int accountTypeId, string currency, int? userId, int? familyId, bool excludeFromStats = false);
        Task<Account> GetAccountByIdAsync(int accountId);
        Task<IEnumerable<Account>> GetAccountsByUserIdAsync(int userId);
        Task<IEnumerable<Account>> GetAccountsByFamilyIdAsync(int familyId);
        Task<Account> UpdateAccountAsync(int accountId, string name, int accountTypeId, decimal initialBalance, string currency, bool isActive, bool excludeFromStats);
        Task<bool> DeleteAccountAsync(int accountId);

        // Account balance operations
        Task<Account> UpdateAccountBalanceAsync(int accountId, decimal newBalance);

        // Account sharing operations
        Task<AccountShare> ShareAccountWithFamilyMemberAsync(int accountId, int familyMemberId, string permissions);
        Task<bool> RemoveAccountShareAsync(int accountShareId);
        Task<AccountShare> GetAccountShareByIdAsync(int accountShareId);
        Task<IEnumerable<AccountShare>> GetAccountSharesAsync(int accountId);
        Task<IEnumerable<Account>> GetSharedAccountsForUserAsync(int userId);

        // Account validation
        Task<bool> AccountExistsAsync(int accountId);
        Task<bool> UserCanAccessAccountAsync(int accountId, int userId);
        Task<bool> UserCanModifyAccountAsync(int accountId, int userId);
    }
}
