using System;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;

namespace HisabKitab.Core.Interfaces
{
    public interface IOCRService
    {
        /// <summary>
        /// Process an image and extract text using OCR
        /// </summary>
        /// <param name="imagePath">Path to the image file</param>
        /// <param name="language">Language code (e.g., "eng", "nep")</param>
        /// <returns>Extracted text from the image</returns>
        Task<string> ExtractTextFromImageAsync(string imagePath, string language = "eng");
        
        /// <summary>
        /// Process an image from base64 string and extract text using OCR
        /// </summary>
        /// <param name="base64Image">Base64 encoded image data</param>
        /// <param name="language">Language code (e.g., "eng", "nep")</param>
        /// <returns>Extracted text from the image</returns>
        Task<string> ExtractTextFromBase64Async(string base64Image, string language = "eng");
        
        /// <summary>
        /// Parse receipt text to extract structured data
        /// </summary>
        /// <param name="text">OCR extracted text</param>
        /// <param name="language">Language code (e.g., "eng", "nep")</param>
        /// <returns>Parsed receipt data as JSON string</returns>
        Task<string> ParseReceiptTextAsync(string text, string language = "eng");
        
        /// <summary>
        /// Process a receipt image and extract structured data
        /// </summary>
        /// <param name="imagePath">Path to the image file</param>
        /// <param name="language">Language code (e.g., "eng", "nep")</param>
        /// <returns>Receipt data entity with extracted information</returns>
        Task<ReceiptData> ProcessReceiptImageAsync(string imagePath, string language = "eng");
        
        /// <summary>
        /// Process a receipt image from base64 string and extract structured data
        /// </summary>
        /// <param name="base64Image">Base64 encoded image data</param>
        /// <param name="language">Language code (e.g., "eng", "nep")</param>
        /// <returns>Receipt data entity with extracted information</returns>
        Task<ReceiptData> ProcessReceiptBase64Async(string base64Image, int? transactionId = null, string language = "eng");
    }
}
