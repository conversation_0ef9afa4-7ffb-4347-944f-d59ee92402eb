using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Interfaces
{
    public interface INotificationService
    {
        #region Immediate Notifications

        /// <summary>
        /// Creates a new notification for a user
        /// </summary>
        /// <param name="userId">The ID of the user to notify</param>
        /// <param name="title">The notification title</param>
        /// <param name="message">The notification message</param>
        /// <param name="type">The notification type (e.g., "Loan", "Transaction", etc.)</param>
        /// <param name="referenceId">Optional reference ID (e.g., loan ID, transaction ID)</param>
        /// <returns>The created notification</returns>
        Task<Notification> CreateNotificationAsync(
            int userId,
            string title,
            string message,
            string type,
            int? referenceId = null);

        /// <summary>
        /// Gets all notifications for a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="includeRead">Whether to include read notifications</param>
        /// <returns>List of notifications</returns>
        Task<IEnumerable<Notification>> GetNotificationsByUserIdAsync(
            int userId,
            bool includeRead = false);

        /// <summary>
        /// Gets a notification by ID
        /// </summary>
        /// <param name="notificationId">The notification ID</param>
        /// <returns>The notification</returns>
        Task<Notification> GetNotificationByIdAsync(int notificationId);

        /// <summary>
        /// Marks a notification as read
        /// </summary>
        /// <param name="notificationId">The notification ID</param>
        /// <returns>True if successful</returns>
        Task<bool> MarkNotificationAsReadAsync(int notificationId);

        /// <summary>
        /// Marks all notifications for a user as read
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>True if successful</returns>
        Task<bool> MarkAllNotificationsAsReadAsync(int userId);

        /// <summary>
        /// Deletes a notification
        /// </summary>
        /// <param name="notificationId">The notification ID</param>
        /// <returns>True if successful</returns>
        Task<bool> DeleteNotificationAsync(int notificationId);

        #endregion

        #region Scheduled Notifications

        /// <summary>
        /// Creates a new scheduled notification
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="title">The notification title</param>
        /// <param name="message">The notification message</param>
        /// <param name="scheduledFor">When to send the notification</param>
        /// <param name="type">The notification type</param>
        /// <param name="relatedEntityType">The related entity type</param>
        /// <param name="relatedEntityId">The related entity ID</param>
        /// <param name="actionUrl">The action URL</param>
        /// <returns>The created notification schedule</returns>
        Task<NotificationSchedule> ScheduleNotificationAsync(
            int userId,
            string title,
            string message,
            DateTime scheduledFor,
            NotificationType type = NotificationType.Info,
            string? relatedEntityType = null,
            int? relatedEntityId = null,
            string? actionUrl = null);

        /// <summary>
        /// Gets all scheduled notifications for a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="includeSent">Whether to include sent notifications</param>
        /// <returns>List of scheduled notifications</returns>
        Task<IEnumerable<NotificationSchedule>> GetScheduledNotificationsByUserIdAsync(
            int userId,
            bool includeSent = false);

        /// <summary>
        /// Gets a scheduled notification by ID
        /// </summary>
        /// <param name="scheduleId">The schedule ID</param>
        /// <returns>The scheduled notification</returns>
        Task<NotificationSchedule> GetScheduledNotificationByIdAsync(int scheduleId);

        /// <summary>
        /// Updates a scheduled notification
        /// </summary>
        /// <param name="scheduleId">The schedule ID</param>
        /// <param name="title">The notification title</param>
        /// <param name="message">The notification message</param>
        /// <param name="scheduledFor">When to send the notification</param>
        /// <param name="isActive">Whether the schedule is active</param>
        /// <returns>The updated notification schedule</returns>
        Task<NotificationSchedule> UpdateScheduledNotificationAsync(
            int scheduleId,
            string? title = null,
            string? message = null,
            DateTime? scheduledFor = null,
            bool? isActive = null);

        /// <summary>
        /// Deletes a scheduled notification
        /// </summary>
        /// <param name="scheduleId">The schedule ID</param>
        /// <returns>True if successful</returns>
        Task<bool> DeleteScheduledNotificationAsync(int scheduleId);

        /// <summary>
        /// Gets all due scheduled notifications
        /// </summary>
        /// <param name="beforeDate">Get notifications scheduled before this date</param>
        /// <returns>List of due scheduled notifications</returns>
        Task<IEnumerable<NotificationSchedule>> GetDueScheduledNotificationsAsync(DateTime? beforeDate = null);

        /// <summary>
        /// Marks a scheduled notification as sent
        /// </summary>
        /// <param name="scheduleId">The schedule ID</param>
        /// <returns>True if successful</returns>
        Task<bool> MarkScheduledNotificationAsSentAsync(int scheduleId);

        #endregion

        #region Recurring Notifications

        /// <summary>
        /// Creates a new recurring notification
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="title">The notification title</param>
        /// <param name="message">The notification message</param>
        /// <param name="recurrenceType">The recurrence type</param>
        /// <param name="recurrenceInterval">The recurrence interval</param>
        /// <param name="startDate">When to start sending notifications</param>
        /// <param name="endDate">When to stop sending notifications</param>
        /// <param name="type">The notification type</param>
        /// <param name="relatedEntityType">The related entity type</param>
        /// <param name="relatedEntityId">The related entity ID</param>
        /// <param name="actionUrl">The action URL</param>
        /// <param name="recurrenceData">Additional recurrence data</param>
        /// <returns>The created recurring notification</returns>
        Task<RecurringNotification> CreateRecurringNotificationAsync(
            int userId,
            string title,
            string message,
            RecurrenceType recurrenceType,
            int recurrenceInterval,
            DateTime startDate,
            DateTime? endDate = null,
            NotificationType type = NotificationType.Info,
            string? relatedEntityType = null,
            int? relatedEntityId = null,
            string? actionUrl = null,
            string? recurrenceData = null);

        /// <summary>
        /// Gets all recurring notifications for a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="includeInactive">Whether to include inactive notifications</param>
        /// <returns>List of recurring notifications</returns>
        Task<IEnumerable<RecurringNotification>> GetRecurringNotificationsByUserIdAsync(
            int userId,
            bool includeInactive = false);

        /// <summary>
        /// Gets a recurring notification by ID
        /// </summary>
        /// <param name="recurringId">The recurring notification ID</param>
        /// <returns>The recurring notification</returns>
        Task<RecurringNotification> GetRecurringNotificationByIdAsync(int recurringId);

        /// <summary>
        /// Updates a recurring notification
        /// </summary>
        /// <param name="recurringId">The recurring notification ID</param>
        /// <param name="title">The notification title</param>
        /// <param name="message">The notification message</param>
        /// <param name="recurrenceType">The recurrence type</param>
        /// <param name="recurrenceInterval">The recurrence interval</param>
        /// <param name="startDate">When to start sending notifications</param>
        /// <param name="endDate">When to stop sending notifications</param>
        /// <param name="isActive">Whether the notification is active</param>
        /// <param name="recurrenceData">Additional recurrence data</param>
        /// <returns>The updated recurring notification</returns>
        Task<RecurringNotification> UpdateRecurringNotificationAsync(
            int recurringId,
            string? title = null,
            string? message = null,
            RecurrenceType? recurrenceType = null,
            int? recurrenceInterval = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            bool? isActive = null,
            string? recurrenceData = null);

        /// <summary>
        /// Deletes a recurring notification
        /// </summary>
        /// <param name="recurringId">The recurring notification ID</param>
        /// <returns>True if successful</returns>
        Task<bool> DeleteRecurringNotificationAsync(int recurringId);

        /// <summary>
        /// Gets all due recurring notifications
        /// </summary>
        /// <param name="beforeDate">Get notifications due before this date</param>
        /// <returns>List of due recurring notifications</returns>
        Task<IEnumerable<RecurringNotification>> GetDueRecurringNotificationsAsync(DateTime? beforeDate = null);

        /// <summary>
        /// Updates the next run time for a recurring notification
        /// </summary>
        /// <param name="recurringId">The recurring notification ID</param>
        /// <returns>True if successful</returns>
        Task<bool> UpdateRecurringNotificationNextRunTimeAsync(int recurringId);

        #endregion

        #region Push Notifications

        /// <summary>
        /// Sends a push notification to a user's devices
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="title">The notification title</param>
        /// <param name="message">The notification message</param>
        /// <param name="data">Additional data to send</param>
        /// <returns>True if successful</returns>
        Task<bool> SendPushNotificationAsync(
            int userId,
            string title,
            string message,
            Dictionary<string, string>? data = null);

        /// <summary>
        /// Registers a device for push notifications
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="deviceId">The device ID</param>
        /// <param name="pushToken">The push notification token</param>
        /// <param name="deviceName">The device name</param>
        /// <param name="deviceType">The device type</param>
        /// <returns>The registered device</returns>
        Task<UserDevice> RegisterDeviceAsync(
            int userId,
            string deviceId,
            string pushToken,
            string? deviceName = null,
            string? deviceType = null);

        /// <summary>
        /// Updates a device's push notification token
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="deviceId">The device ID</param>
        /// <param name="pushToken">The new push notification token</param>
        /// <returns>The updated device</returns>
        Task<UserDevice> UpdateDeviceTokenAsync(
            int userId,
            string deviceId,
            string pushToken);

        /// <summary>
        /// Unregisters a device from push notifications
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="deviceId">The device ID</param>
        /// <returns>True if successful</returns>
        Task<bool> UnregisterDeviceAsync(
            int userId,
            string deviceId);

        #endregion

        #region Monthly Summary

        /// <summary>
        /// Generates a monthly summary for a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="month">The month to generate the summary for</param>
        /// <param name="year">The year to generate the summary for</param>
        /// <returns>True if successful</returns>
        Task<bool> GenerateMonthlyUserSummaryAsync(
            int userId,
            int month,
            int year);

        /// <summary>
        /// Generates monthly summaries for all users
        /// </summary>
        /// <param name="month">The month to generate summaries for</param>
        /// <param name="year">The year to generate summaries for</param>
        /// <returns>True if successful</returns>
        Task<bool> GenerateAllMonthlySummariesAsync(
            int month,
            int year);

        #endregion
    }
}
