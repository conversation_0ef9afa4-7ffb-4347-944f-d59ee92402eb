using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;

namespace HisabKitab.Core.Interfaces
{
    public interface IAnalyticsService
    {
        // Metrics and trends
        Task<IEnumerable<AnalyticsMetric>> GetMetricsAsync(
            int userId,
            DateTime startDate,
            DateTime endDate,
            string period = "Monthly",
            int? familyId = null,
            int? categoryId = null,
            int? accountId = null);

        Task<Dictionary<string, decimal>> GetCategoryBreakdownAsync(
            int userId,
            DateTime startDate,
            DateTime endDate,
            int? familyId = null);

        Task<Dictionary<DateTime, decimal>> GetTrendDataAsync(
            int userId,
            string metricType,
            DateTime startDate,
            DateTime endDate,
            string period = "Monthly",
            int? familyId = null,
            int? categoryId = null);

        // Net worth tracking
        Task<decimal> CalculateNetWorthAsync(
            int userId,
            DateTime? asOfDate = null,
            int? familyId = null);

        Task<Dictionary<DateTime, decimal>> GetNetWorthTrendAsync(
            int userId,
            DateTime startDate,
            DateTime endDate,
            string period = "Monthly",
            int? familyId = null);

        // Cashflow analysis
        Task<Dictionary<string, decimal>> AnalyzeCashflowAsync(
            int userId,
            DateTime startDate,
            DateTime endDate,
            int? familyId = null);

        // Anomaly detection
        Task<IEnumerable<AnomalyDetection>> DetectAnomaliesAsync(
            int userId,
            DateTime startDate,
            DateTime endDate,
            int? familyId = null);

        Task<AnomalyDetection> GetAnomalyByIdAsync(int anomalyId);

        Task<bool> UpdateAnomalyStatusAsync(
            int anomalyId,
            HisabKitab.Core.Enums.AnomalyStatus status,
            string? resolutionNotes = null);
    }
}
