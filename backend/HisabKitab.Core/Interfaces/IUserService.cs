using System.Threading.Tasks;
using HisabKitab.Core.Entities;

namespace HisabKitab.Core.Interfaces
{
    public interface IUserService
    {
        Task<User> RegisterAsync(string username, string email, string password, string firstName, string lastName, string? phoneNumber = null, string language = "en");
        Task<User?> AuthenticateAsync(string username, string password);
        Task<User?> GetByIdAsync(int id);
        Task<User?> GetByUsernameAsync(string username);
        Task<User?> GetByEmailAsync(string email);
        Task<bool> UsernameExistsAsync(string username);
        Task<bool> EmailExistsAsync(string email);
        Task<User> UpdateProfileAsync(int userId, string firstName, string lastName, string phoneNumber, string language);
        Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);
        Task<bool> ChangeEmailAsync(int userId, string newEmail, string password);
        Task<bool> UpdateProfilePictureAsync(int userId, string profilePicture);
    }
}
