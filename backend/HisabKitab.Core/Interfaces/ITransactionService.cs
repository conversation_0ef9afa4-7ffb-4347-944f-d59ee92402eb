using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Interfaces
{
    public interface ITransactionService
    {
        // Transaction CRUD operations
        Task<Transaction> CreateTransactionAsync(
            decimal amount,
            string description,
            DateTime date,
            TransactionType type,
            int accountId,
            int? toAccountId,
            int categoryId,
            int? priorityId,
            int userId,
            string status = "Completed",
            int? approvedByUserId = null,
            string? receiptImage = null,
            string? tags = null,
            string? location = null,
            decimal? exchangeRate = null,
            int? recurringTransactionId = null,
            bool isSynced = true,
            SyncStatus syncStatus = SyncStatus.Synced,
            IEnumerable<TransactionItem>? items = null);

        Task<Transaction> GetTransactionByIdAsync(int transactionId);

        Task<IEnumerable<Transaction>> GetTransactionsByUserIdAsync(
            int userId,
            DateTime? startDate = null,
            DateTime? endDate = null,
            TransactionType? type = null,
            int? accountId = null,
            int? categoryId = null,
            int? priorityId = null,
            string? status = null,
            string? searchTerm = null,
            string? sortBy = null,
            bool ascending = true,
            int? skip = null,
            int? take = null);

        Task<IEnumerable<Transaction>> GetTransactionsByAccountIdAsync(
            int accountId,
            DateTime? startDate = null,
            DateTime? endDate = null,
            TransactionType? type = null);

        Task<IEnumerable<Transaction>> GetTransactionsByFamilyIdAsync(
            int familyId,
            DateTime? startDate = null,
            DateTime? endDate = null,
            TransactionType? type = null,
            int? accountId = null,
            int? categoryId = null,
            int? priorityId = null,
            string? status = null,
            string? searchTerm = null,
            string? sortBy = null,
            bool ascending = true,
            int? skip = null,
            int? take = null);

        Task<Transaction> UpdateTransactionAsync(
            int transactionId,
            decimal? amount = null,
            string? description = null,
            DateTime? date = null,
            TransactionType? type = null,
            int? accountId = null,
            int? toAccountId = null,
            int? categoryId = null,
            int? priorityId = null,
            string? status = null,
            int? approvedByUserId = null,
            string? receiptImage = null,
            string? tags = null,
            string? location = null,
            decimal? exchangeRate = null,
            int? recurringTransactionId = null,
            bool? isSynced = null,
            SyncStatus? syncStatus = null);

        Task<bool> DeleteTransactionAsync(int transactionId);

        // Transaction item operations
        Task<TransactionItem> AddTransactionItemAsync(
            int transactionId,
            string name,
            decimal amount,
            int quantity = 1,
            int? categoryId = null,
            string? notes = null);

        Task<TransactionItem> UpdateTransactionItemAsync(
            int transactionItemId,
            string? name = null,
            decimal? amount = null,
            int? quantity = null,
            int? categoryId = null,
            string? notes = null);

        Task<bool> DeleteTransactionItemAsync(int transactionItemId);

        // Batch operations for syncing
        Task<IEnumerable<Transaction>> BatchCreateTransactionsAsync(IEnumerable<Transaction> transactions);
        Task<IEnumerable<Transaction>> BatchUpdateTransactionsAsync(IEnumerable<Transaction> transactions);
        Task<bool> BatchDeleteTransactionsAsync(IEnumerable<int> transactionIds);

        // Transaction validation
        Task<bool> TransactionExistsAsync(int transactionId);
        Task<bool> UserCanAccessTransactionAsync(int transactionId, int userId);
        Task<bool> UserCanModifyTransactionAsync(int transactionId, int userId);

        // Transaction statistics
        Task<decimal> GetTotalIncomeAsync(int userId, DateTime startDate, DateTime endDate, int? accountId = null);
        Task<decimal> GetTotalExpenseAsync(int userId, DateTime startDate, DateTime endDate, int? accountId = null);
        Task<decimal> GetBalanceAsync(int userId, DateTime startDate, DateTime endDate, int? accountId = null);
    }
}
