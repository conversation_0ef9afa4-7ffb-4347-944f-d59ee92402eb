using System;
using System.Reflection;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Helpers
{
    public static class EntityHelper
    {
        /// <summary>
        /// Ensures that all string properties of an entity are initialized with default values if they are null.
        /// This helps prevent not-null constraint violations in the database.
        /// </summary>
        /// <typeparam name="T">The entity type</typeparam>
        /// <param name="entity">The entity to initialize</param>
        /// <returns>The initialized entity</returns>
        public static T EnsureStringPropertiesInitialized<T>(T entity) where T : BaseEntity
        {
            if (entity == null)
                throw new ArgumentNullException(nameof(entity), "Entity cannot be null");

            var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (var property in properties)
            {
                if (property.PropertyType == typeof(string) && property.CanWrite)
                {
                    var value = property.GetValue(entity);
                    if (value == null)
                    {
                        property.SetValue(entity, string.Empty);
                    }
                }
            }

            return entity;
        }
    }
}
