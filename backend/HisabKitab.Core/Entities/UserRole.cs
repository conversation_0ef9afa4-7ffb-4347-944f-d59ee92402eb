using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace HisabKitab.Core.Entities
{
    public class UserRole
    {
        public int UserId { get; set; }

        public int RoleId { get; set; }

        public DateTime AssignedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual User? User { get; set; }
        public virtual Role? Role { get; set; }
    }
}
