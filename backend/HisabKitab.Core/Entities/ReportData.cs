using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class ReportData : BaseEntity
    {
        public ReportData()
        {
            Data = string.Empty;
        }

        [Required]
        public int ReportId { get; set; }

        [Required]
        public string Data { get; set; } // JSON data of the report results

        [Required]
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

        public DateTime? ExpiresAt { get; set; } // Optional expiration date for cached reports

        public bool IsCached { get; set; } = true; // Whether this is a cached result

        // Navigation properties
        public virtual Report? Report { get; set; }
    }
}
