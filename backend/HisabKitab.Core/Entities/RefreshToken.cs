using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class RefreshToken : BaseEntity
    {
        public RefreshToken()
        {
            Token = string.Empty;
            ReplacedByToken = "";
            DeviceInfo = "Mobile";
        }

        [Required]
        public int UserId { get; set; }

        [Required]
        public string Token { get; set; }

        [Required]
        public DateTime ExpiryDate { get; set; }

        public bool IsRevoked { get; set; } = false;

        public string ReplacedByToken { get; set; } = ""; // Default to empty string

        [MaxLength(255)]
        public string DeviceInfo { get; set; } = "Mobile"; // Default to Web

        public DateTime? LastUsedDate { get; set; }

        // Navigation properties
        public virtual User? User { get; set; }
    }
}
