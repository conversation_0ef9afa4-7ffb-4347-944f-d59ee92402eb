using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace HisabKitab.Core.Entities
{
    public class FamilyMember
    {
        public FamilyMember()
        {
            Role = string.Empty;
            Permissions = string.Empty;
            PersonalBudgetLimits = new List<BudgetLimit>();
            SharedAccounts = new List<AccountShare>();
        }

        public int FamilyId { get; set; }

        public int UserId { get; set; }

        [Required]
        [MaxLength(50)]
        public string Role { get; set; } // e.g., <PERSON><PERSON>, Member, Child

        public string Permissions { get; set; } // JSON string of specific permissions

        public DateTime JoinedAt { get; set; } = DateTime.UtcNow;

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual Family? Family { get; set; }
        public virtual User? User { get; set; }
        public virtual ICollection<BudgetLimit> PersonalBudgetLimits { get; set; }
        public virtual ICollection<AccountShare> SharedAccounts { get; set; }
    }
}
