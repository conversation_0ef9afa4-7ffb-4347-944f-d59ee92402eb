using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class ImportHistory : BaseEntity
    {
        public ImportHistory()
        {
            SourceType = string.Empty;
            FileName = string.Empty;
            ErrorDetails = string.Empty;
            ImportedTransactions = new List<Transaction>();
        }

        [Required]
        public int UserId { get; set; }

        [Required]
        [MaxLength(50)]
        public string SourceType { get; set; } // CSV, Bank Statement, etc.

        [Required]
        [MaxLength(255)]
        public string FileName { get; set; }

        [Required]
        public int TotalRecords { get; set; }

        [Required]
        public int SuccessCount { get; set; }

        [Required]
        public int FailureCount { get; set; }

        public string ErrorDetails { get; set; } // JSON of errors

        [Required]
        public DateTime ImportedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual User? User { get; set; }
        public virtual ICollection<Transaction> ImportedTransactions { get; set; }
    }
}
