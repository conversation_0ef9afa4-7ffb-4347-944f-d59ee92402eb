using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class Transaction : BaseEntity
    {
        public Transaction()
        {
            Description = string.Empty;
            Status = "Completed";
            ReceiptImage = string.Empty;
            Tags = string.Empty;
            Location = string.Empty;
            Items = new List<TransactionItem>();
            LoanPayments = new List<LoanPayment>();
            SavingsContributions = new List<SavingsContribution>();
            TransactionTags = new List<TransactionTag>();
        }

        [Required]
        public decimal Amount { get; set; }

        [MaxLength(500)]
        public string Description { get; set; }

        [Required]
        public DateTime Date { get; set; }

        [Required]
        public TransactionType Type { get; set; }

        [Required]
        public int AccountId { get; set; }

        public int? ToAccountId { get; set; } // For transfers

        [Required]
        public int CategoryId { get; set; }

        public int? PriorityId { get; set; }

        [Required]
        public int UserId { get; set; } // User who created the transaction

        [MaxLength(50)]
        public string Status { get; set; } = "Completed"; // Pending, Completed, Rejected (for approval workflow)

        public int? ApprovedByUserId { get; set; } // For approval workflow

        [MaxLength(255)]
        public string ReceiptImage { get; set; } // URL or path to receipt image

        [MaxLength(500)]
        public string Tags { get; set; } // Comma-separated tags

        [MaxLength(255)]
        public string Location { get; set; } // Geographic location

        public decimal? ExchangeRate { get; set; } // For multi-currency transactions

        public int? RecurringTransactionId { get; set; } // Link to recurring transaction if generated

        public bool IsSynced { get; set; } = true; // For offline sync

        public SyncStatus SyncStatus { get; set; } = SyncStatus.Synced; // Pending, Synced, Failed

        public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Account? Account { get; set; }
        public virtual Account? ToAccount { get; set; }
        public virtual Category? Category { get; set; }
        public virtual Priority? Priority { get; set; }
        public virtual User? User { get; set; }
        public virtual User? ApprovedByUser { get; set; }
        public virtual RecurringTransaction? RecurringTransaction { get; set; }
        public virtual ICollection<TransactionItem> Items { get; set; }
        public virtual ICollection<LoanPayment> LoanPayments { get; set; }
        public virtual ICollection<SavingsContribution> SavingsContributions { get; set; }
        public virtual ExpenseApprovalRequest? ApprovalRequest { get; set; }
        public virtual ReceiptData? ReceiptData { get; set; }
        public virtual ICollection<TransactionTag> TransactionTags { get; set; }
    }
}
