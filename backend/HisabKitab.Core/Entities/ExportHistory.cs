using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class ExportHistory : BaseEntity
    {
        public ExportHistory()
        {
            ExportType = string.Empty;
            FileName = string.Empty;
            ContentType = string.Empty;
            Parameters = string.Empty;
            FilePath = string.Empty;
        }

        [Required]
        public int UserId { get; set; }

        [Required]
        [MaxLength(50)]
        public string ExportType { get; set; } // CSV, PDF, etc.

        [Required]
        [MaxLength(255)]
        public string FileName { get; set; }

        [Required]
        [MaxLength(50)]
        public string ContentType { get; set; } // Transactions, Reports, etc.

        public int? ReportId { get; set; } // If exporting a report

        public string Parameters { get; set; } // JSON of export parameters

        [Required]
        public DateTime ExportedAt { get; set; } = DateTime.UtcNow;

        [Required]
        public ExportStatus Status { get; set; } = ExportStatus.Completed;

        [MaxLength(255)]
        public string FilePath { get; set; } // Path to the exported file

        // Navigation properties
        public virtual User? User { get; set; }
        public virtual Report? Report { get; set; }
    }
}
