using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class MonthlySummary : BaseEntity
    {
        [Required]
        public int UserId { get; set; }

        [Required]
        public int Month { get; set; }

        [Required]
        public int Year { get; set; }

        [Required]
        public decimal TotalIncome { get; set; }

        [Required]
        public decimal TotalExpense { get; set; }

        [Required]
        public decimal NetSavings { get; set; }

        [Required]
        public decimal SavingsRate { get; set; } // Percentage

        [Required]
        public int TransactionCount { get; set; }

        public decimal AverageExpense { get; set; }

        public decimal LargestExpense { get; set; }

        [MaxLength(100)]
        public string? TopExpenseCategory { get; set; }

        [MaxLength(100)]
        public string? TopIncomeSource { get; set; }

        public string? ExpensesByCategory { get; set; } // JSON array of category-amount pairs

        public string? DailyTrend { get; set; } // JSON array of date-income-expense triplets

        [Required]
        public bool IsNotified { get; set; } = false;

        public DateTime? NotifiedAt { get; set; }

        // Navigation properties
        public virtual User? User { get; set; }
    }
}
