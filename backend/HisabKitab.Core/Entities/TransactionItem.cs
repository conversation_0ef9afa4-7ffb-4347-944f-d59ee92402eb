using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class TransactionItem : BaseEntity
    {
        public TransactionItem()
        {
            Name = string.Empty;
            Notes = string.Empty;
        }

        [Required]
        public int TransactionId { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [Required]
        public decimal Amount { get; set; }

        public int Quantity { get; set; } = 1;

        public int? CategoryId { get; set; } // Allow different category per item

        [MaxLength(255)]
        public string Notes { get; set; }

        // Navigation properties
        public virtual Transaction? Transaction { get; set; }
        public virtual Category? Category { get; set; }
    }
}
