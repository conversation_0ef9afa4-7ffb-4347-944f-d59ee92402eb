using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class Tag : BaseEntity
    {
        public Tag()
        {
            Name = string.Empty;
            Color = "#CCCCCC";
            TransactionTags = new List<TransactionTag>();
        }

        [Required]
        [MaxLength(50)]
        public string Name { get; set; }

        [MaxLength(20)]
        public string Color { get; set; } = "#CCCCCC"; // Default to light gray

        public int? UserId { get; set; } // For user-defined tags

        public int? FamilyId { get; set; } // For family-defined tags

        // Navigation properties
        public virtual User? User { get; set; }
        public virtual Family? Family { get; set; }
        public virtual ICollection<TransactionTag> TransactionTags { get; set; }
    }
}
