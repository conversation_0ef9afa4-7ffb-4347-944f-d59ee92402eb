using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class Priority : BaseEntity
    {
        public Priority()
        {
            Name = string.Empty;
            Description = "";
            Color = "#CCCCCC";
            Transactions = new List<Transaction>();
            SavingsGoals = new List<SavingsGoal>();
            WishlistItems = new List<WishlistItem>();
        }

        [Required]
        [MaxLength(50)]
        public string Name { get; set; }

        [MaxLength(255)]
        public string Description { get; set; } = ""; // Default to empty string

        [Required]
        public PriorityLevel Level { get; set; }

        [MaxLength(20)]
        public string Color { get; set; } = "#CCCCCC"; // Default to light gray

        // Navigation properties
        public virtual ICollection<Transaction> Transactions { get; set; }
        public virtual ICollection<SavingsGoal> SavingsGoals { get; set; }
        public virtual ICollection<WishlistItem> WishlistItems { get; set; }
    }
}
