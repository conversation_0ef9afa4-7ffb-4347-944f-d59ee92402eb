using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class Account : BaseEntity
    {
        public Account()
        {
            Name = string.Empty;
            Transactions = new List<Transaction>();
            TransfersTo = new List<Transaction>();
            SharedWith = new List<AccountShare>();
            RecurringTransactions = new List<RecurringTransaction>();
        }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [Required]
        public AccountType AccountType { get; set; }

        [Required]
        public decimal Balance { get; set; }

        [Required]
        public decimal InitialBalance { get; set; }

        [Required]
        [MaxLength(3)]
        public string Currency { get; set; } = "NPR"; // Default to Nepali Rupee

        public int? UserId { get; set; } // Null if it's a shared account

        public int? FamilyId { get; set; } // Null if it's a personal account

        public bool IsActive { get; set; } = true;

        public bool ExcludeFromStats { get; set; } = false;

        public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual User? User { get; set; }
        public virtual Family? Family { get; set; }
        public virtual ICollection<Transaction> Transactions { get; set; }
        public virtual ICollection<Transaction> TransfersTo { get; set; }
        public virtual ICollection<AccountShare> SharedWith { get; set; }
        public virtual ICollection<RecurringTransaction> RecurringTransactions { get; set; }
    }
}
