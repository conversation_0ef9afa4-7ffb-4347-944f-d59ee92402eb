using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class Family : BaseEntity
    {
        public Family()
        {
            Name = string.Empty;
            Description = string.Empty;
            InviteCode = string.Empty;
            Settings = string.Empty;
            Members = new List<FamilyMember>();
            SharedAccounts = new List<Account>();
            BudgetLimits = new List<BudgetLimit>();
            SharedSavingsGoals = new List<SavingsGoal>();
            SharedWishlistItems = new List<WishlistItem>();
            ApprovalRequests = new List<ExpenseApprovalRequest>();
            SharedReports = new List<Report>();
            Categories = new List<Category>();
            Tags = new List<Tag>();
        }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [MaxLength(500)]
        public string Description { get; set; }

        [MaxLength(20)]
        public string InviteCode { get; set; } // Code for joining the family

        public string Settings { get; set; } // JSON string of family settings

        public int CreatedByUserId { get; set; }

        // Navigation properties
        public virtual User? CreatedByUser { get; set; }
        public virtual ICollection<FamilyMember> Members { get; set; }
        public virtual ICollection<Account> SharedAccounts { get; set; }
        public virtual ICollection<BudgetLimit> BudgetLimits { get; set; }
        public virtual ICollection<SavingsGoal> SharedSavingsGoals { get; set; }
        public virtual ICollection<WishlistItem> SharedWishlistItems { get; set; }
        public virtual ICollection<ExpenseApprovalRequest> ApprovalRequests { get; set; }
        public virtual ICollection<Report> SharedReports { get; set; }
        public virtual ICollection<Category> Categories { get; set; }
        public virtual ICollection<Tag> Tags { get; set; }
    }
}
