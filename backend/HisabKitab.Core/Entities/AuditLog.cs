using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class AuditLog : BaseEntity
    {
        public AuditLog()
        {
            Action = string.Empty;
            EntityName = string.Empty;
            EntityId = string.Empty;
            OldValues = string.Empty;
            NewValues = string.Empty;
            IpAddress = string.Empty;
        }

        public int? UserId { get; set; }

        [Required]
        [MaxLength(50)]
        public string Action { get; set; }

        [Required]
        [MaxLength(50)]
        public string EntityName { get; set; }

        [Required]
        [MaxLength(50)]
        public string EntityId { get; set; }

        public string OldValues { get; set; }

        public string NewValues { get; set; }

        [MaxLength(50)]
        public string IpAddress { get; set; }

        [Required]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual User? User { get; set; }
    }
}
