using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class AccountShare : BaseEntity
    {
        public AccountShare()
        {
            Permissions = "View"; // Default permission
        }

        public int AccountId { get; set; }

        public int FamilyMemberId { get; set; }

        [Required]
        [MaxLength(50)]
        public string Permissions { get; set; } // View, Edit, Delete

        public DateTime SharedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Account? Account { get; set; }
        public virtual FamilyMember? FamilyMember { get; set; }
    }
}
