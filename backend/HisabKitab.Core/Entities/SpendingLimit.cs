using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class SpendingLimit : BaseEntity
    {
        [Required]
        public int FamilyId { get; set; }

        [Required]
        public int FamilyMemberUserId { get; set; }

        [Required]
        public decimal Amount { get; set; }

        [Required]
        public FrequencyType Period { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public int? CategoryId { get; set; } // If limit is for specific category

        [Range(0, 100)]
        public decimal NotificationThreshold { get; set; } = 80; // Percentage (0-100) for alerts

        public bool RequireApprovalOverLimit { get; set; } = true; // Require approval for expenses over limit

        [Required]
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual Family? Family { get; set; }
        public virtual User? FamilyMemberUser { get; set; }
        public virtual Category? Category { get; set; }
    }
}
