using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class AnomalyDetection : BaseEntity
    {
        public AnomalyDetection()
        {
            AnomalyType = string.Empty;
            Description = string.Empty;
            ResolutionNotes = string.Empty;
        }

        [Required]
        public int UserId { get; set; }

        public int? FamilyId { get; set; }

        [Required]
        [MaxLength(50)]
        public string AnomalyType { get; set; } // SpendingSpike, UnusualCategory, etc.

        [Required]
        public decimal Severity { get; set; } // 0-100 scale

        [Required]
        [MaxLength(255)]
        public string Description { get; set; }

        public int? TransactionId { get; set; }

        public int? CategoryId { get; set; }

        public int? AccountId { get; set; }

        [Required]
        public DateTime DetectedAt { get; set; } = DateTime.UtcNow;

        [Required]
        public AnomalyStatus Status { get; set; } = AnomalyStatus.Detected;

        public DateTime? ResolvedAt { get; set; }

        [MaxLength(500)]
        public string ResolutionNotes { get; set; }

        // Navigation properties
        public virtual User? User { get; set; }
        public virtual Family? Family { get; set; }
        public virtual Transaction? Transaction { get; set; }
        public virtual Category? Category { get; set; }
        public virtual Account? Account { get; set; }
    }
}
