using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class ExchangeRate : BaseEntity
    {
        [Required]
        public int FromCurrencyId { get; set; }

        [Required]
        public int ToCurrencyId { get; set; }

        [Required]
        public decimal Rate { get; set; }

        [Required]
        public DateTime EffectiveDate { get; set; }

        public DateTime? ExpiryDate { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual Currency? FromCurrency { get; set; }
        public virtual Currency? ToCurrency { get; set; }
    }
}
