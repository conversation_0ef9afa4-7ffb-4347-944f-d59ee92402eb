using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class User : BaseEntity
    {
        public User()
        {
            Username = string.Empty;
            Email = string.Empty;
            PasswordHash = string.Empty;
            FirstName = string.Empty;
            LastName = string.Empty;
            PhoneNumber = string.Empty;
            ProfilePicture = "";
            Language = "en";
            NotificationPreferences = "{}";

            // Initialize collections
            UserRoles = new List<UserRole>();
            FamilyMemberships = new List<FamilyMember>();
            Accounts = new List<Account>();
            Transactions = new List<Transaction>();
            LoansGiven = new List<Loan>();
            LoansReceived = new List<Loan>();
            SavingsGoals = new List<SavingsGoal>();
            WishlistItems = new List<WishlistItem>();
            Notifications = new List<Notification>();
            RefreshTokens = new List<RefreshToken>();
            RecurringTransactions = new List<RecurringTransaction>();
            RequestedApprovals = new List<ExpenseApprovalRequest>();
            ApprovedApprovals = new List<ExpenseApprovalRequest>();
            SyncQueue = new List<SyncQueue>();
            Reports = new List<Report>();
            Devices = new List<UserDevice>();
            Categories = new List<Category>();
            Tags = new List<Tag>();
            ImportHistory = new List<ImportHistory>();
            ExportHistory = new List<ExportHistory>();
            AuditLogs = new List<AuditLog>();
        }

        [Required]
        [MaxLength(50)]
        public string Username { get; set; }

        [Required]
        [EmailAddress]
        [MaxLength(100)]
        public string Email { get; set; }

        [Required]
        public string PasswordHash { get; set; }

        [Required]
        [MaxLength(50)]
        public string FirstName { get; set; }

        [Required]
        [MaxLength(50)]
        public string LastName { get; set; }

        [MaxLength(20)]
        public string PhoneNumber { get; set; }

        [MaxLength(255)]
        public string ProfilePicture { get; set; } = ""; // Default to empty string

        [MaxLength(10)]
        public string Language { get; set; } = "en"; // Default to English

        public string NotificationPreferences { get; set; } = "{}"; // Default to empty JSON object

        public DateTime? LastLoginAt { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<UserRole> UserRoles { get; set; }
        public virtual ICollection<FamilyMember> FamilyMemberships { get; set; }
        public virtual ICollection<Account> Accounts { get; set; }
        public virtual ICollection<Transaction> Transactions { get; set; }
        public virtual ICollection<Loan> LoansGiven { get; set; }
        public virtual ICollection<Loan> LoansReceived { get; set; }
        public virtual ICollection<SavingsGoal> SavingsGoals { get; set; }
        public virtual ICollection<WishlistItem> WishlistItems { get; set; }
        public virtual ICollection<Notification> Notifications { get; set; }
        public virtual ICollection<RefreshToken> RefreshTokens { get; set; }
        public virtual ICollection<RecurringTransaction> RecurringTransactions { get; set; }
        public virtual ICollection<ExpenseApprovalRequest> RequestedApprovals { get; set; }
        public virtual ICollection<ExpenseApprovalRequest> ApprovedApprovals { get; set; }
        public virtual ICollection<SyncQueue> SyncQueue { get; set; }
        public virtual ICollection<Report> Reports { get; set; }
        public virtual ICollection<UserDevice> Devices { get; set; }
        public virtual ICollection<Category> Categories { get; set; }
        public virtual ICollection<Tag> Tags { get; set; }
        public virtual ICollection<ImportHistory> ImportHistory { get; set; }
        public virtual ICollection<ExportHistory> ExportHistory { get; set; }
        public virtual ICollection<AuditLog> AuditLogs { get; set; }
    }
}
