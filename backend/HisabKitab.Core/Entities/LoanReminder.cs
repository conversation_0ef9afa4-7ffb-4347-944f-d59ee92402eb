using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class LoanReminder : BaseEntity
    {
        [Required]
        public int LoanId { get; set; }

        [Required]
        public DateTime ReminderDate { get; set; }

        [Required]
        [MaxLength(255)]
        public string Message { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public bool IsSent { get; set; } = false;

        public DateTime? SentAt { get; set; }

        // Navigation properties
        public virtual Loan? Loan { get; set; }
    }
}
