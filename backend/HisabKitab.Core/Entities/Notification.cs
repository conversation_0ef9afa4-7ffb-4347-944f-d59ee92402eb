using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class Notification : BaseEntity
    {
        public Notification()
        {
            Title = string.Empty;
            Message = string.Empty;
            ActionUrl = string.Empty;
            RelatedEntityType = string.Empty;
        }

        [Required]
        public int UserId { get; set; }

        [Required]
        [MaxLength(100)]
        public string Title { get; set; }

        [Required]
        [MaxLength(500)]
        public string Message { get; set; }

        [Required]
        public NotificationType Type { get; set; }

        public bool IsRead { get; set; } = false;

        [MaxLength(255)]
        public string ActionUrl { get; set; } // Deep link to relevant screen

        [MaxLength(50)]
        public string RelatedEntityType { get; set; } // Transaction, Loan, etc.

        public int? RelatedEntityId { get; set; } // ID of the related entity

        public DateTime? ReadAt { get; set; }

        // Navigation properties
        public virtual User? User { get; set; }
    }
}
