using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class SavingsGoal : BaseEntity
    {
        public SavingsGoal()
        {
            Title = string.Empty;
            Description = string.Empty;
            Status = "Active";
            Contributions = new List<SavingsContribution>();
            LinkedWishlistItems = new List<WishlistItem>();
        }

        [Required]
        [MaxLength(100)]
        public string Title { get; set; }

        [MaxLength(500)]
        public string Description { get; set; }

        [Required]
        public decimal TargetAmount { get; set; }

        [Required]
        public decimal CurrentAmount { get; set; } = 0;

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime TargetDate { get; set; }

        public int? UserId { get; set; } // Null if it's a shared goal

        public int? FamilyId { get; set; } // Null if it's a personal goal

        public bool IsShared { get; set; } = false;

        public int? PriorityId { get; set; }

        public bool AutoContribute { get; set; } = false;

        public decimal? AutoContributeAmount { get; set; }

        public FrequencyType? AutoContributeFrequency { get; set; }

        [Required]
        [MaxLength(50)]
        public string Status { get; set; } = "Active"; // Active, Achieved, Abandoned

        // Navigation properties
        public virtual User? User { get; set; }
        public virtual Family? Family { get; set; }
        public virtual Priority? Priority { get; set; }
        public virtual ICollection<SavingsContribution> Contributions { get; set; }
        public virtual ICollection<WishlistItem> LinkedWishlistItems { get; set; }
    }
}
