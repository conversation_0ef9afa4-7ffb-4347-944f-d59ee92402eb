using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class RecurringTransaction : BaseEntity
    {
        public RecurringTransaction()
        {
            Title = string.Empty;
            Description = string.Empty;
            GeneratedTransactions = new List<Transaction>();
        }

        [Required]
        [MaxLength(100)]
        public string Title { get; set; }

        [Required]
        public decimal Amount { get; set; }

        [MaxLength(500)]
        public string Description { get; set; }

        [Required]
        public int AccountId { get; set; }

        [Required]
        public int CategoryId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        public FrequencyType Frequency { get; set; }

        public int? DayOfMonth { get; set; } // For monthly frequency

        public int? DayOfWeek { get; set; } // For weekly frequency

        [Required]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public DateTime LastGeneratedAt { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual Account? Account { get; set; }
        public virtual Category? Category { get; set; }
        public virtual User? User { get; set; }
        public virtual ICollection<Transaction> GeneratedTransactions { get; set; }
    }
}
