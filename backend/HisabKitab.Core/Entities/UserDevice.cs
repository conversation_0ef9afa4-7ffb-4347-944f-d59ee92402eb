using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class UserDevice : BaseEntity
    {
        public UserDevice()
        {
            DeviceId = string.Empty;
            DeviceName = string.Empty;
            DeviceType = string.Empty;
            PushToken = string.Empty;
        }

        [Required]
        public int UserId { get; set; }

        [Required]
        [MaxLength(100)]
        public string DeviceId { get; set; }

        [MaxLength(100)]
        public string DeviceName { get; set; }

        [MaxLength(20)]
        public string DeviceType { get; set; } // iOS, Android, Web

        [MaxLength(255)]
        public string PushToken { get; set; } // For push notifications

        [Required]
        public DateTime LastActiveAt { get; set; } = DateTime.UtcNow;

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual User? User { get; set; }
    }
}
