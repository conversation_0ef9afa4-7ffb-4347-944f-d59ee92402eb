using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class WishlistItem : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Title { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        [Required]
        public decimal EstimatedPrice { get; set; }

        [MaxLength(255)]
        public string? ProductUrl { get; set; }

        [MaxLength(255)]
        public string? ImageUrl { get; set; }

        [Required]
        public DateTime AddedDate { get; set; } = DateTime.UtcNow;

        public DateTime? TargetPurchaseDate { get; set; }

        public int? UserId { get; set; } // Null if it's a shared item

        public int? FamilyId { get; set; } // Null if it's a personal item

        public bool IsShared { get; set; } = false;

        public int? PriorityId { get; set; }

        public int? LinkedSavingsGoalId { get; set; } // Link to a savings goal

        [Required]
        [MaxLength(50)]
        public string Status { get; set; } = "Pending"; // Pending, Purchased, Abandoned

        // Navigation properties
        public virtual User? User { get; set; }
        public virtual Family? Family { get; set; }
        public virtual Priority? Priority { get; set; }
        public virtual SavingsGoal? LinkedSavingsGoal { get; set; }
    }
}
