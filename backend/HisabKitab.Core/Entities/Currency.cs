using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class Currency : BaseEntity
    {
        public Currency()
        {
            Code = "NPR";
            Name = "Nepali Rupee";
            Symbol = "₨";
            FromExchangeRates = new List<ExchangeRate>();
            ToExchangeRates = new List<ExchangeRate>();
        }

        [Required]
        [MaxLength(3)]
        public string Code { get; set; } // NPR, USD, etc.

        [Required]
        [MaxLength(50)]
        public string Name { get; set; } // Nepali Rupee, US Dollar, etc.

        [Required]
        [MaxLength(10)]
        public string Symbol { get; set; } // ₨, $, etc.

        public bool IsBaseCurrency { get; set; } = false;

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<ExchangeRate> FromExchangeRates { get; set; }
        public virtual ICollection<ExchangeRate> ToExchangeRates { get; set; }
    }
}
