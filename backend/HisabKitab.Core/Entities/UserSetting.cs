using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class UserSetting : BaseEntity
    {
        public UserSetting()
        {
            Key = string.Empty;
            Value = string.Empty;
        }

        [Required]
        public int UserId { get; set; }

        [Required]
        [MaxLength(100)]
        public string Key { get; set; }

        [Required]
        public string Value { get; set; }

        public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual User? User { get; set; }
    }
}
