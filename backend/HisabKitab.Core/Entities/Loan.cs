using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class Loan : BaseEntity
    {
        public Loan()
        {
            Title = string.Empty;
            ExternalEntityName = string.Empty;
            Notes = string.Empty;
            Status = "Active";
            Payments = new List<LoanPayment>();
            Reminders = new List<LoanReminder>();
        }

        [Required]
        [MaxLength(100)]
        public string Title { get; set; }

        [Required]
        public decimal Amount { get; set; }

        [Required]
        public decimal InterestRate { get; set; }

        [Required]
        public InterestType InterestType { get; set; }

        [Required]
        public FrequencyType PaymentFrequency { get; set; }

        [Required]
        public decimal TotalPayableAmount { get; set; } // Calculated total with interest

        [Required]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public int? LenderUserId { get; set; } // Who gave the loan (null if external)

        public int? BorrowerUserId { get; set; } // Who received the loan (null if external)

        public bool IsExternalEntity { get; set; } = false;

        [MaxLength(100)]
        public string ExternalEntityName { get; set; } // Name of bank/institution if external

        [Required]
        [MaxLength(50)]
        public string Status { get; set; } = "Active"; // Active, Completed, Defaulted

        [MaxLength(500)]
        public string Notes { get; set; }

        public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual User? LenderUser { get; set; }
        public virtual User? BorrowerUser { get; set; }
        public virtual ICollection<LoanPayment> Payments { get; set; }
        public virtual ICollection<LoanReminder> Reminders { get; set; }
    }
}
