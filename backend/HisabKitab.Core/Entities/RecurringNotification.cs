using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class RecurringNotification : BaseEntity
    {
        public RecurringNotification()
        {
            Title = string.Empty;
            Message = string.Empty;
            RecurrenceData = string.Empty;
            ActionUrl = string.Empty;
            RelatedEntityType = string.Empty;
        }

        [Required]
        public int UserId { get; set; }

        [Required]
        [MaxLength(100)]
        public string Title { get; set; }

        [Required]
        [MaxLength(500)]
        public string Message { get; set; }

        [Required]
        public NotificationType Type { get; set; }

        [Required]
        public RecurrenceType RecurrenceType { get; set; } // Daily, Weekly, Monthly, etc.

        [Required]
        public int RecurrenceInterval { get; set; } = 1; // Every X days, weeks, months, etc.

        [Required]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public string RecurrenceData { get; set; } // JSON data for specific recurrence rules

        [MaxLength(255)]
        public string ActionUrl { get; set; } // Deep link to relevant screen

        [MaxLength(50)]
        public string RelatedEntityType { get; set; } // Transaction, Loan, etc.

        public int? RelatedEntityId { get; set; } // ID of the related entity

        [Required]
        public bool IsActive { get; set; } = true;

        public DateTime LastRunAt { get; set; } = DateTime.MinValue;

        public DateTime NextRunAt { get; set; }

        // Navigation properties
        public virtual User? User { get; set; }
    }
}
