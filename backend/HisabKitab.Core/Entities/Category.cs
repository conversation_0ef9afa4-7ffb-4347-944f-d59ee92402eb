using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class Category : BaseEntity
    {
        public Category()
        {
            Name = string.Empty;
            Icon = "";
            Color = "#CCCCCC";
            Subcategories = new List<Category>();
            Transactions = new List<Transaction>();
            TransactionItems = new List<TransactionItem>();
            BudgetLimits = new List<BudgetLimit>();
            RecurringTransactions = new List<RecurringTransaction>();
            ApprovalRequests = new List<ExpenseApprovalRequest>();
        }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [Required]
        public CategoryType Type { get; set; }

        [MaxLength(50)]
        public string Icon { get; set; } = ""; // Default to empty string

        [MaxLength(20)]
        public string Color { get; set; } = "#CCCCCC"; // Default to light gray

        public int? ParentCategoryId { get; set; } // For subcategories

        public int? UserId { get; set; } // For user-defined categories

        public int? FamilyId { get; set; } // For family-defined categories

        public bool IsSystem { get; set; } = false; // True for system-defined categories

        // Navigation properties
        public virtual Category? ParentCategory { get; set; }
        public virtual ICollection<Category> Subcategories { get; set; }
        public virtual User? User { get; set; }
        public virtual Family? Family { get; set; }
        public virtual ICollection<Transaction> Transactions { get; set; }
        public virtual ICollection<TransactionItem> TransactionItems { get; set; }
        public virtual ICollection<BudgetLimit> BudgetLimits { get; set; }
        public virtual ICollection<RecurringTransaction> RecurringTransactions { get; set; }
        public virtual ICollection<ExpenseApprovalRequest> ApprovalRequests { get; set; }
    }
}
