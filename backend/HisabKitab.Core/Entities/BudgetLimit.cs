using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class BudgetLimit : BaseEntity
    {
        public BudgetLimit()
        {
            // Navigation properties will be set by Entity Framework when loading from database
        }

        [Required]
        public int CategoryId { get; set; }

        [Required]
        public decimal Amount { get; set; }

        [Required]
        public FrequencyType Period { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public int? UserId { get; set; } // For personal budget

        public int? FamilyId { get; set; } // For family budget

        public int? FamilyMemberId { get; set; } // For family member budget

        [Range(0, 100)]
        public decimal NotificationThreshold { get; set; } = 80; // Percentage (0-100) for alerts

        public bool RolloverUnused { get; set; } = false; // Allow unused budget to roll over

        // Navigation properties
        public virtual Category? Category { get; set; }
        public virtual User? User { get; set; }
        public virtual Family? Family { get; set; }
        public virtual FamilyMember? FamilyMember { get; set; }
    }
}
