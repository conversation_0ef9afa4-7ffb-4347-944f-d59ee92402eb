using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class FeatureFlag : BaseEntity
    {
        public FeatureFlag()
        {
            Name = string.Empty;
            Description = string.Empty;
            EnabledFor = string.Empty;
        }

        [Required]
        [MaxLength(50)]
        public string Name { get; set; }

        [MaxLength(255)]
        public string Description { get; set; }

        [Required]
        public bool IsEnabled { get; set; } = false;

        public string EnabledFor { get; set; } // All, Beta, Specific Users (JSON array)

        public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
