using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class AnalyticsMetric : BaseEntity
    {
        public AnalyticsMetric()
        {
            Name = string.Empty;
            Type = string.Empty;
            Period = string.Empty;
            Metadata = string.Empty;
        }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [Required]
        [MaxLength(50)]
        public string Type { get; set; } // Income, Expense, Balance, Trend, etc.

        [Required]
        public decimal Value { get; set; }

        [Required]
        public DateTime Date { get; set; }

        [Required]
        public string Period { get; set; } // Daily, Weekly, Monthly, Yearly

        public int? CategoryId { get; set; }

        public int? AccountId { get; set; }

        [Required]
        public int UserId { get; set; }

        public int? FamilyId { get; set; }

        public string Metadata { get; set; } // JSON with additional data

        // Navigation properties
        public virtual User? User { get; set; }
        public virtual Family? Family { get; set; }
        public virtual Category? Category { get; set; }
        public virtual Account? Account { get; set; }
    }
}
