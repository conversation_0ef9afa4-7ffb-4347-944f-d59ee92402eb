using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class ReceiptData : BaseEntity
    {
        public ReceiptData()
        {
            MerchantName = string.Empty;
            ImagePath = string.Empty;
            OcrText = string.Empty;
            ParsedData = string.Empty;
            Language = "eng";
            Items = new List<ReceiptItem>();
        }

        [Required]
        public int TransactionId { get; set; }

        [MaxLength(255)]
        public string MerchantName { get; set; }

        public DateTime? ReceiptDate { get; set; }

        public decimal TotalAmount { get; set; }

        [Required]
        [MaxLength(255)]
        public string ImagePath { get; set; }

        public string OcrText { get; set; } // Full text from OCR

        public string ParsedData { get; set; } // JSON of structured data extracted from receipt

        [Required]
        public DateTime ScanDate { get; set; } = DateTime.UtcNow;

        [MaxLength(10)]
        public string Language { get; set; } = "eng"; // eng, nep

        public bool IsProcessed { get; set; } = false;

        public double? Confidence { get; set; } // OCR confidence score

        public bool IsSynced { get; set; } = true;

        public SyncStatus SyncStatus { get; set; } = SyncStatus.Synced;

        public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Transaction? Transaction { get; set; }

        public virtual ICollection<ReceiptItem> Items { get; set; } = new List<ReceiptItem>();

        [NotMapped]
        public List<ReceiptItem> ItemsList
        {
            get
            {
                if (string.IsNullOrEmpty(ParsedData))
                    return new List<ReceiptItem>();

                try
                {
                    return JsonSerializer.Deserialize<List<ReceiptItem>>(ParsedData) ?? new List<ReceiptItem>();
                }
                catch
                {
                    return new List<ReceiptItem>();
                }
            }
        }
    }

    public class ReceiptItem : BaseEntity
    {
        public ReceiptItem()
        {
            Name = string.Empty;
            Notes = string.Empty;
        }

        [Required]
        public int ReceiptDataId { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [Required]
        public decimal Amount { get; set; }

        public int Quantity { get; set; } = 1;

        public int? CategoryId { get; set; }

        [MaxLength(255)]
        public string Notes { get; set; }

        // Navigation properties
        public virtual ReceiptData? ReceiptData { get; set; }

        public virtual Category? Category { get; set; }
    }
}
