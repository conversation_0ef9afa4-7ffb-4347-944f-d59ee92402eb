using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class SavingsContribution : BaseEntity
    {
        public SavingsContribution()
        {
            Notes = string.Empty;
        }

        [Required]
        public int SavingsGoalId { get; set; }

        public int? UserId { get; set; } // Which family member made the contribution

        [Required]
        public decimal Amount { get; set; }

        [Required]
        public DateTime ContributionDate { get; set; }

        public int? TransactionId { get; set; } // Link to transaction if contribution was made from an account

        [MaxLength(255)]
        public string Notes { get; set; }

        // Navigation properties
        public virtual SavingsGoal? SavingsGoal { get; set; }
        public virtual User? User { get; set; }
        public virtual Transaction? Transaction { get; set; }
    }
}
