using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class LoanPayment : BaseEntity
    {
        [Required]
        public int LoanId { get; set; }

        [Required]
        public decimal Amount { get; set; }

        [Required]
        public decimal PrincipalAmount { get; set; }

        [Required]
        public decimal InterestAmount { get; set; }

        [Required]
        public DateTime PaymentDate { get; set; }

        [MaxLength(50)]
        public string? PaymentMethod { get; set; }

        public bool IsScheduled { get; set; } = false; // True for future scheduled payments

        public int? TransactionId { get; set; } // Link to transaction if payment was made from an account

        [MaxLength(255)]
        public string Notes { get; set; } = string.Empty;

        // Navigation properties
        public virtual Loan? Loan { get; set; }
        public virtual Transaction? Transaction { get; set; }
    }
}
