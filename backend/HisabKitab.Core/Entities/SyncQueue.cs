using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class SyncQueue : BaseEntity
    {
        public SyncQueue()
        {
            EntityType = string.Empty;
            Action = string.Empty;
            EntityData = string.Empty;
            ErrorMessage = string.Empty;
        }

        [Required]
        public int UserId { get; set; }

        [Required]
        [MaxLength(50)]
        public string EntityType { get; set; } // Transaction, Category, etc.

        [Required]
        public int EntityId { get; set; }

        [Required]
        [MaxLength(20)]
        public string Action { get; set; } // Create, Update, Delete

        [Required]
        public string EntityData { get; set; } // JSON data of the entity

        [Required]
        public SyncStatus Status { get; set; } = SyncStatus.Pending;

        public int RetryCount { get; set; } = 0;

        [MaxLength(500)]
        public string ErrorMessage { get; set; }

        public DateTime? SyncedAt { get; set; }

        // Navigation properties
        public virtual User? User { get; set; }
    }
}
