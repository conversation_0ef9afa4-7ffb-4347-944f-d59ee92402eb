using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class NotificationSchedule : BaseEntity
    {
        public NotificationSchedule()
        {
            Title = string.Empty;
            Message = string.Empty;
            ActionUrl = string.Empty;
            RelatedEntityType = string.Empty;
        }

        [Required]
        public int UserId { get; set; }

        [Required]
        [MaxLength(100)]
        public string Title { get; set; }

        [Required]
        [MaxLength(500)]
        public string Message { get; set; }

        [Required]
        public NotificationType Type { get; set; }

        [Required]
        public DateTime ScheduledFor { get; set; }

        [MaxLength(255)]
        public string ActionUrl { get; set; } // Deep link to relevant screen

        [MaxLength(50)]
        public string RelatedEntityType { get; set; } // Transaction, Loan, etc.

        public int? RelatedEntityId { get; set; } // ID of the related entity

        [Required]
        public bool IsActive { get; set; } = true;

        public bool IsSent { get; set; } = false;

        public DateTime? SentAt { get; set; }

        public int? RecurringNotificationId { get; set; } // If this was generated from a recurring notification

        // Navigation properties
        public virtual User? User { get; set; }
        public virtual RecurringNotification? RecurringNotification { get; set; }
    }
}
