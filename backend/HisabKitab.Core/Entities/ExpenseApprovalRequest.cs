using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;
using HisabKitab.Core.Enums;

namespace HisabKitab.Core.Entities
{
    public class ExpenseApprovalRequest : BaseEntity
    {
        public ExpenseApprovalRequest()
        {
            Description = string.Empty;
            RejectionReason = string.Empty;
        }

        [Required]
        public int RequestedByUserId { get; set; }

        public int? ApprovedByUserId { get; set; }

        [Required]
        public int FamilyId { get; set; }

        [Required]
        public decimal Amount { get; set; }

        [MaxLength(500)]
        public string Description { get; set; }

        [Required]
        public int CategoryId { get; set; }

        [Required]
        public ApprovalStatus Status { get; set; } = ApprovalStatus.Pending;

        [MaxLength(255)]
        public string RejectionReason { get; set; }

        [Required]
        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;

        public DateTime? RespondedAt { get; set; }

        public int? ResultingTransactionId { get; set; }

        // Navigation properties
        public virtual User? RequestedByUser { get; set; }
        public virtual User? ApprovedByUser { get; set; }
        public virtual Family? Family { get; set; }
        public virtual Category? Category { get; set; }
        public virtual Transaction? ResultingTransaction { get; set; }
    }
}
