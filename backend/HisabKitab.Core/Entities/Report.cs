using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class Report : BaseEntity
    {
        public Report()
        {
            Name = string.Empty;
            Type = string.Empty;
            Parameters = string.Empty;
            Layout = string.Empty;
        }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [Required]
        [MaxLength(50)]
        public string Type { get; set; } // Expense, Income, Net Worth, etc.

        public string Parameters { get; set; } // JSON of report parameters

        [Required]
        public int UserId { get; set; }

        public int? FamilyId { get; set; }

        public bool IsShared { get; set; } = false;

        public string Layout { get; set; } // JSON describing report layout

        [Required]
        public DateTime LastRunAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual User? User { get; set; }
        public virtual Family? Family { get; set; }
    }
}
