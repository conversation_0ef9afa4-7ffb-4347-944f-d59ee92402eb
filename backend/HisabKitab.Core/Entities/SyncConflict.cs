using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Common;

namespace HisabKitab.Core.Entities
{
    public class SyncConflict : BaseEntity
    {
        public SyncConflict()
        {
            EntityType = string.Empty;
            LocalData = string.Empty;
            RemoteData = string.Empty;
            Resolution = string.Empty;
            ResolvedData = string.Empty;
        }

        [Required]
        public int UserId { get; set; }

        [Required]
        [MaxLength(50)]
        public string EntityType { get; set; } // Transaction, Category, etc.

        [Required]
        public int EntityId { get; set; }

        [Required]
        public string LocalData { get; set; } // JSON data from client

        [Required]
        public string RemoteData { get; set; } // JSON data from server

        public bool Resolved { get; set; } = false;

        [MaxLength(20)]
        public string Resolution { get; set; } // local, remote, merge

        public string ResolvedData { get; set; } // JSON data after resolution

        public DateTime? ResolvedAt { get; set; }

        // Navigation properties
        public virtual User? User { get; set; }
    }
}
