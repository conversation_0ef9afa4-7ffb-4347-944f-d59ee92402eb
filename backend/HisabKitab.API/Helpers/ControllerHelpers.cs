using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace HisabKitab.API.Helpers
{
    public static class ControllerHelpers
    {
        /// <summary>
        /// Safely gets the user ID from claims
        /// </summary>
        /// <param name="controller">The controller instance</param>
        /// <returns>User ID if found, or null if not found</returns>
        public static int? GetUserIdFromClaims(ControllerBase controller)
        {
            var userIdStr = controller.User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userIdStr))
            {
                return null;
            }

            if (int.TryParse(userIdStr, out int userId))
            {
                return userId;
            }

            return null;
        }
    }
}
