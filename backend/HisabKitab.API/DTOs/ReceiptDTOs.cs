using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace HisabKitab.API.DTOs
{
    public class ReceiptDto
    {
        public ReceiptDto()
        {
            MerchantName = string.Empty;
            Items = new List<ReceiptItemDto>();
            RawText = string.Empty;
            ImageUrl = string.Empty;
            Language = string.Empty;
        }

        public int Id { get; set; }
        public int TransactionId { get; set; }
        public string MerchantName { get; set; }
        public DateTime Date { get; set; }
        public decimal TotalAmount { get; set; }
        public List<ReceiptItemDto> Items { get; set; }
        public string RawText { get; set; }
        public string ImageUrl { get; set; }
        public string Language { get; set; }
        public bool IsProcessed { get; set; }
        public double? Confidence { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class ReceiptItemDto
    {
        public ReceiptItemDto()
        {
            Name = string.Empty;
            CategoryName = string.Empty;
        }

        public string Name { get; set; }
        public decimal Amount { get; set; }
        public int Quantity { get; set; } = 1;
        public int? CategoryId { get; set; }
        public string CategoryName { get; set; }
    }

    public class ProcessReceiptRequestDto
    {
        public ProcessReceiptRequestDto()
        {
            ImageBase64 = string.Empty;
            Language = "eng";
        }

        [Required]
        public string ImageBase64 { get; set; }

        public string Language { get; set; } = "eng";

        public int? TransactionId { get; set; }
    }

    public class SaveReceiptRequestDto
    {
        public SaveReceiptRequestDto()
        {
            MerchantName = string.Empty;
            Items = new List<ReceiptItemDto>();
            RawText = string.Empty;
            ImageBase64 = string.Empty;
            Language = "eng";
        }

        [Required]
        public int TransactionId { get; set; }

        public string MerchantName { get; set; }

        public DateTime? Date { get; set; }

        [Required]
        public decimal TotalAmount { get; set; }

        public List<ReceiptItemDto> Items { get; set; } = new List<ReceiptItemDto>();

        [Required]
        public string RawText { get; set; }

        [Required]
        public string ImageBase64 { get; set; }

        public string Language { get; set; } = "eng";
    }

    public class UpdateReceiptRequestDto
    {
        public UpdateReceiptRequestDto()
        {
            MerchantName = string.Empty;
            Items = new List<ReceiptItemDto>();
            RawText = string.Empty;
            ImageBase64 = string.Empty;
        }

        public string MerchantName { get; set; }

        public DateTime? Date { get; set; }

        public decimal? TotalAmount { get; set; }

        public List<ReceiptItemDto> Items { get; set; }

        public string RawText { get; set; }

        public string ImageBase64 { get; set; }

        public bool? IsProcessed { get; set; }
    }

    public class ReceiptHistoryItemDto
    {
        public ReceiptHistoryItemDto()
        {
            TransactionDescription = string.Empty;
            MerchantName = string.Empty;
            ImageUrl = string.Empty;
        }

        public int Id { get; set; }
        public int TransactionId { get; set; }
        public string TransactionDescription { get; set; }
        public string MerchantName { get; set; }
        public DateTime Date { get; set; }
        public decimal TotalAmount { get; set; }
        public string ImageUrl { get; set; }
        public bool IsProcessed { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class ReceiptFilterDto
    {
        public ReceiptFilterDto()
        {
            MerchantName = string.Empty;
        }

        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public string MerchantName { get; set; }
        public bool? IsProcessed { get; set; }
    }
}
