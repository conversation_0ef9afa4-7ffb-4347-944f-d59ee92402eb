using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Enums;

namespace HisabKitab.API.DTOs
{
    public class CategoryDto
    {
        public CategoryDto()
        {
            Name = string.Empty;
            Icon = string.Empty;
            Color = string.Empty;
            ParentCategoryName = string.Empty;
            Subcategories = new List<CategoryDto>();
            BudgetLimits = new List<BudgetLimitDto>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public CategoryType Type { get; set; }
        public string Icon { get; set; }
        public string Color { get; set; }
        public int? ParentCategoryId { get; set; }
        public string ParentCategoryName { get; set; }
        public int? UserId { get; set; }
        public int? FamilyId { get; set; }
        public bool IsSystem { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<CategoryDto> Subcategories { get; set; }
        public List<BudgetLimitDto> BudgetLimits { get; set; }
    }

    public class CreateCategoryDto
    {
        [Required]
        [StringLength(100, MinimumLength = 2)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public CategoryType Type { get; set; }

        [StringLength(50)]
        public string Icon { get; set; } = "";

        [StringLength(20)]
        public string Color { get; set; } = "#CCCCCC";

        public int? ParentCategoryId { get; set; }

        public int? FamilyId { get; set; }
    }

    public class UpdateCategoryDto
    {
        public UpdateCategoryDto()
        {
            Name = string.Empty;
            Icon = string.Empty;
            Color = string.Empty;
        }

        [Required]
        [StringLength(100, MinimumLength = 2)]
        public string Name { get; set; }

        [Required]
        public CategoryType Type { get; set; }

        [StringLength(50)]
        public string Icon { get; set; }

        [StringLength(20)]
        public string Color { get; set; }

        public int? ParentCategoryId { get; set; }
    }

    public class BudgetLimitDto
    {
        public BudgetLimitDto()
        {
            CategoryName = string.Empty;
        }

        public int Id { get; set; }
        public int CategoryId { get; set; }
        public string CategoryName { get; set; }
        public decimal Amount { get; set; }
        public FrequencyType Period { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? UserId { get; set; }
        public int? FamilyId { get; set; }
        public int? FamilyMemberId { get; set; }
        public decimal NotificationThreshold { get; set; }
        public bool RolloverUnused { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CreateBudgetLimitDto
    {
        [Required]
        public int CategoryId { get; set; }

        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal Amount { get; set; }

        [Required]
        public FrequencyType Period { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public int? FamilyId { get; set; }

        public int? FamilyMemberId { get; set; }

        [Range(0, 100)]
        public decimal NotificationThreshold { get; set; } = 80;

        public bool RolloverUnused { get; set; } = false;
    }

    public class UpdateBudgetLimitDto
    {
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal Amount { get; set; }

        [Required]
        public FrequencyType Period { get; set; }

        [Range(0, 100)]
        public decimal NotificationThreshold { get; set; }

        public bool RolloverUnused { get; set; }

        public DateTime? EndDate { get; set; }
    }
}
