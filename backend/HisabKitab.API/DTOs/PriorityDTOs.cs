using System;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Enums;

namespace HisabKitab.API.DTOs
{
    public class PriorityDto
    {
        public PriorityDto()
        {
            Name = string.Empty;
            Description = string.Empty;
            Color = string.Empty;
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public PriorityLevel Level { get; set; }
        public string Color { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CreatePriorityDto
    {
        public CreatePriorityDto()
        {
            Name = string.Empty;
            Description = "";
            Color = "#CCCCCC";
        }

        [Required]
        [StringLength(50, MinimumLength = 2)]
        public string Name { get; set; }

        [StringLength(255)]
        public string Description { get; set; } = "";

        [Required]
        public PriorityLevel Level { get; set; }

        [StringLength(20)]
        public string Color { get; set; } = "#CCCCCC";
    }

    public class UpdatePriorityDto
    {
        public UpdatePriorityDto()
        {
            Name = string.Empty;
            Description = string.Empty;
            Color = string.Empty;
        }

        [Required]
        [StringLength(50, MinimumLength = 2)]
        public string Name { get; set; }

        [StringLength(255)]
        public string Description { get; set; }

        [Required]
        public PriorityLevel Level { get; set; }

        [StringLength(20)]
        public string Color { get; set; }
    }
}
