using System.ComponentModel.DataAnnotations;

namespace HisabKitab.API.DTOs
{
    public class UserProfileDto
    {
        public UserProfileDto()
        {
            Username = string.Empty;
            Email = string.Empty;
            FirstName = string.Empty;
            LastName = string.Empty;
            PhoneNumber = string.Empty;
            ProfilePicture = string.Empty;
            Language = string.Empty;
            Roles = Array.Empty<string>();
        }

        public int Id { get; set; }
        public string Username { get; set; }
        public string Email { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string PhoneNumber { get; set; }
        public string ProfilePicture { get; set; }
        public string Language { get; set; }
        public string[] Roles { get; set; }
    }

    public class UpdateProfileDto
    {
        public UpdateProfileDto()
        {
            FirstName = string.Empty;
            LastName = string.Empty;
            PhoneNumber = string.Empty;
            Language = string.Empty;
        }

        [StringLength(50)]
        public string FirstName { get; set; }

        [StringLength(50)]
        public string LastName { get; set; }

        [StringLength(20)]
        public string PhoneNumber { get; set; }

        [StringLength(10)]
        public string Language { get; set; }
    }

    public class ChangePasswordDto
    {
        public ChangePasswordDto()
        {
            CurrentPassword = string.Empty;
            NewPassword = string.Empty;
            ConfirmNewPassword = string.Empty;
        }

        [Required]
        public string CurrentPassword { get; set; }

        [Required]
        [StringLength(50, MinimumLength = 6)]
        public string NewPassword { get; set; }

        [Required]
        [Compare("NewPassword")]
        public string ConfirmNewPassword { get; set; }
    }

    public class ChangeEmailDto
    {
        public ChangeEmailDto()
        {
            NewEmail = string.Empty;
            Password = string.Empty;
        }

        [Required]
        [EmailAddress]
        public string NewEmail { get; set; }

        [Required]
        public string Password { get; set; }
    }
}
