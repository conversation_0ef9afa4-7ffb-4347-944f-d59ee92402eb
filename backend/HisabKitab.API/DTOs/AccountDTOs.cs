using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Enums;

namespace HisabKitab.API.DTOs
{
    public class AccountDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public AccountType AccountType { get; set; }
        public decimal Balance { get; set; }
        public decimal InitialBalance { get; set; }
        public string Currency { get; set; } = string.Empty;
        public int? UserId { get; set; }
        public int? FamilyId { get; set; }
        public bool IsActive { get; set; }
        public bool ExcludeFromStats { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<AccountShareDto> SharedWith { get; set; } = new List<AccountShareDto>();
    }

    public class CreateAccountDto
    {
        [Required]
        [StringLength(100, MinimumLength = 3)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public AccountType AccountType { get; set; }

        [Required]
        public decimal InitialBalance { get; set; }

        [Required]
        [StringLength(3, MinimumLength = 3)]
        public string Currency { get; set; } = "NPR";

        public int? FamilyId { get; set; }

        public bool ExcludeFromStats { get; set; } = false;
    }

    public class UpdateAccountDto
    {
        [Required]
        [StringLength(100, MinimumLength = 3)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public AccountType AccountType { get; set; }

        [Required]
        public decimal InitialBalance { get; set; }

        [Required]
        [StringLength(3, MinimumLength = 3)]
        public string Currency { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public bool ExcludeFromStats { get; set; } = false;
    }

    public class AccountShareDto
    {
        public int Id { get; set; }
        public int AccountId { get; set; }
        public int FamilyMemberId { get; set; }
        public string Permissions { get; set; } = string.Empty;
        public DateTime SharedAt { get; set; }
        public string MemberUsername { get; set; } = string.Empty;
        public string MemberFullName { get; set; } = string.Empty;
    }

    public class ShareAccountDto
    {
        [Required]
        public int FamilyMemberId { get; set; }

        [Required]
        [StringLength(50)]
        public string Permissions { get; set; } = "View"; // View, Edit, Delete
    }

    public class UpdateBalanceDto
    {
        [Required]
        public decimal NewBalance { get; set; }
    }
}
