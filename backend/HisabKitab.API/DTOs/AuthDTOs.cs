using System.ComponentModel.DataAnnotations;

namespace HisabKitab.API.DTOs
{
    public class RegisterDto
    {
        public RegisterDto()
        {
            Username = string.Empty;
            Email = string.Empty;
            Password = string.Empty;
            FirstName = string.Empty;
            LastName = string.Empty;
            PhoneNumber = string.Empty;
            Language = "en";
        }

        [Required]
        [StringLength(50, MinimumLength = 3)]
        public string Username { get; set; }

        [Required]
        [EmailAddress]
        public string Email { get; set; }

        [Required]
        [StringLength(50, MinimumLength = 6)]
        public string Password { get; set; }

        [Required]
        [StringLength(50)]
        public string FirstName { get; set; }

        [Required]
        [StringLength(50)]
        public string LastName { get; set; }

        [StringLength(20)]
        public string PhoneNumber { get; set; }

        [StringLength(10)]
        public string Language { get; set; } = "en";
    }

    public class LoginDto
    {
        public LoginDto()
        {
            Username = string.Empty;
            Password = string.Empty;
        }

        [Required]
        public string Username { get; set; }

        [Required]
        public string Password { get; set; }
    }

    public class TokenResponseDto
    {
        public TokenResponseDto()
        {
            AccessToken = string.Empty;
            RefreshToken = string.Empty;
            Username = string.Empty;
            Email = string.Empty;
            Roles = new List<string>();
        }

        public string AccessToken { get; set; }
        public string RefreshToken { get; set; }
        public int UserId { get; set; }
        public string Username { get; set; }
        public string Email { get; set; }
        public List<string> Roles { get; set; }
        public DateTime ExpiresAt { get; set; }
        public bool IsPlatformAdmin { get; set; }
        public bool IsFamilyAdmin { get; set; }
    }

    public class RefreshTokenDto
    {
        public RefreshTokenDto()
        {
            RefreshToken = string.Empty;
        }

        [Required]
        public string RefreshToken { get; set; }
    }
}
