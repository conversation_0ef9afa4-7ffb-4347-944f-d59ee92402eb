using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Enums;

namespace HisabKitab.API.DTOs
{
    public class SyncQueueItemDto
    {
        public SyncQueueItemDto()
        {
            EntityType = string.Empty;
            Action = string.Empty;
            EntityData = string.Empty;
            ErrorMessage = string.Empty;
        }

        public int Id { get; set; }
        public string EntityType { get; set; }
        public int EntityId { get; set; }
        public string Action { get; set; }
        public string EntityData { get; set; }
        public SyncStatus Status { get; set; }
        public int RetryCount { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? SyncedAt { get; set; }
    }

    public class SyncBatchRequestDto
    {
        [Required]
        public List<SyncQueueItemDto> Items { get; set; } = new List<SyncQueueItemDto>();
    }

    public class SyncBatchResponseDto
    {
        public List<ProcessedItemDto> Processed { get; set; } = new List<ProcessedItemDto>();
        public List<ConflictDto> Conflicts { get; set; } = new List<ConflictDto>();
    }

    public class ProcessedItemDto
    {
        public ProcessedItemDto()
        {
            EntityType = string.Empty;
        }

        public int Id { get; set; }
        public string EntityType { get; set; }
        public int EntityId { get; set; }
    }

    public class ConflictDto
    {
        public ConflictDto()
        {
            EntityType = string.Empty;
            Resolution = string.Empty;
        }

        public int Id { get; set; }
        public string EntityType { get; set; }
        public int EntityId { get; set; }
        public object? LocalData { get; set; }
        public object? RemoteData { get; set; }
        public bool Resolved { get; set; }
        public string Resolution { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class ResolveConflictRequestDto
    {
        public ResolveConflictRequestDto()
        {
            Resolution = string.Empty;
        }

        [Required]
        public string Resolution { get; set; } // "local", "remote", "merge"

        public object? MergedData { get; set; } // Only required when resolution is "merge"
    }

    public class SyncChangesResponseDto
    {
        public SyncChangesResponseDto()
        {
            Changes = new List<SyncQueueItemDto>();
            NextCursor = string.Empty;
        }

        public List<SyncQueueItemDto> Changes { get; set; }
        public bool HasMore { get; set; }
        public string? NextCursor { get; set; }
    }

    public class LastSyncTimeResponseDto
    {
        public LastSyncTimeResponseDto()
        {
            LastSyncTime = string.Empty;
        }

        public string LastSyncTime { get; set; }
    }
}
