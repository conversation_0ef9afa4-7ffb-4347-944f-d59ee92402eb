using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Enums;

namespace HisabKitab.API.DTOs
{
    // Existing DTOs

    public class NotificationDto
    {
        public int Id { get; set; }
        public string? Title { get; set; }
        public string? Message { get; set; }
        public string? Type { get; set; } // We'll convert NotificationType enum to string for the frontend
        public string? RelatedEntityType { get; set; }
        public int? RelatedEntityId { get; set; }
        public bool IsRead { get; set; }
        public DateTime? ReadAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    // New DTOs for scheduled notifications

    public class ScheduleNotificationDto
    {
        [Required]
        [MaxLength(100)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string Message { get; set; } = string.Empty;

        [Required]
        public DateTime ScheduledFor { get; set; }

        public string Type { get; set; } = "Info";

        public string? RelatedEntityType { get; set; }

        public int? RelatedEntityId { get; set; }

        public string? ActionUrl { get; set; }
    }

    public class ScheduledNotificationDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public DateTime ScheduledFor { get; set; }
        public string? RelatedEntityType { get; set; }
        public int? RelatedEntityId { get; set; }
        public string? ActionUrl { get; set; }
        public bool IsActive { get; set; }
        public bool IsSent { get; set; }
        public DateTime? SentAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class UpdateScheduledNotificationDto
    {
        public string? Title { get; set; }
        public string? Message { get; set; }
        public DateTime? ScheduledFor { get; set; }
        public bool? IsActive { get; set; }
    }

    // DTOs for recurring notifications

    public class CreateRecurringNotificationDto
    {
        [Required]
        [MaxLength(100)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string Message { get; set; } = string.Empty;

        [Required]
        public string RecurrenceType { get; set; } = string.Empty;

        [Required]
        [Range(1, int.MaxValue)]
        public int RecurrenceInterval { get; set; } = 1;

        [Required]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public string Type { get; set; } = "Info";

        public string? RelatedEntityType { get; set; }

        public int? RelatedEntityId { get; set; }

        public string? ActionUrl { get; set; }

        public string? RecurrenceData { get; set; }
    }

    public class RecurringNotificationDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string RecurrenceType { get; set; } = string.Empty;
        public int RecurrenceInterval { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? RecurrenceData { get; set; }
        public string? RelatedEntityType { get; set; }
        public int? RelatedEntityId { get; set; }
        public string? ActionUrl { get; set; }
        public bool IsActive { get; set; }
        public DateTime LastRunAt { get; set; }
        public DateTime NextRunAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class UpdateRecurringNotificationDto
    {
        public string? Title { get; set; }
        public string? Message { get; set; }
        public string? RecurrenceType { get; set; }
        public int? RecurrenceInterval { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool? IsActive { get; set; }
        public string? RecurrenceData { get; set; }
    }

    // DTOs for device management

    public class RegisterDeviceDto
    {
        [Required]
        public string DeviceId { get; set; } = string.Empty;

        [Required]
        public string PushToken { get; set; } = string.Empty;

        public string? DeviceName { get; set; }

        public string? DeviceType { get; set; }
    }

    public class UpdateDeviceTokenDto
    {
        [Required]
        public string PushToken { get; set; } = string.Empty;
    }

    public class UserDeviceDto
    {
        public int Id { get; set; }
        public string DeviceId { get; set; } = string.Empty;
        public string DeviceName { get; set; } = string.Empty;
        public string DeviceType { get; set; } = string.Empty;
        public string PushToken { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime LastActiveAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    // DTOs for monthly summary

    public class CategoryAmount
    {
        public string Category { get; set; } = string.Empty;
        public decimal Amount { get; set; }
    }

    public class DailyTrend
    {
        public string Date { get; set; } = string.Empty;
        public decimal Income { get; set; }
        public decimal Expense { get; set; }
    }

    public class MonthlySummaryDto
    {
        public int Id { get; set; }
        public int Month { get; set; }
        public int Year { get; set; }
        public decimal TotalIncome { get; set; }
        public decimal TotalExpense { get; set; }
        public decimal NetSavings { get; set; }
        public decimal SavingsRate { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageExpense { get; set; }
        public decimal LargestExpense { get; set; }
        public string TopExpenseCategory { get; set; } = string.Empty;
        public string TopIncomeSource { get; set; } = string.Empty;
        public List<CategoryAmount> ExpensesByCategory { get; set; } = new List<CategoryAmount>();
        public List<DailyTrend> DailyTrend { get; set; } = new List<DailyTrend>();
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
