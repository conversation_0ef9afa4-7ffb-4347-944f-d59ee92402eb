using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace HisabKitab.API.DTOs
{
    public class FamilyDto
    {
        public FamilyDto()
        {
            Name = string.Empty;
            Description = string.Empty;
            InviteCode = string.Empty;
            Settings = string.Empty;
            CreatedByUsername = string.Empty;
            Members = new List<FamilyMemberDto>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string InviteCode { get; set; }
        public string Settings { get; set; }
        public int CreatedByUserId { get; set; }
        public string CreatedByUsername { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<FamilyMemberDto> Members { get; set; }
    }

    public class CreateFamilyDto
    {
        [Required]
        [StringLength(100, MinimumLength = 3)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        public string Settings { get; set; } = string.Empty;
    }

    public class UpdateFamilyDto
    {
        [Required]
        [StringLength(100, MinimumLength = 3)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        public string Settings { get; set; } = string.Empty;
    }

    public class FamilyMemberDto
    {
        public FamilyMemberDto()
        {
            Username = string.Empty;
            FirstName = string.Empty;
            LastName = string.Empty;
            Role = string.Empty;
            Permissions = string.Empty;
        }

        public int UserId { get; set; }
        public string Username { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Role { get; set; }
        public string Permissions { get; set; }
        public DateTime JoinedAt { get; set; }
        public bool IsActive { get; set; }
    }

    public class InviteMemberDto
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Role { get; set; } = "Member";
    }

    public class JoinFamilyDto
    {
        [Required]
        [StringLength(20)]
        public string InviteCode { get; set; } = string.Empty;
    }

    public class UpdateMemberRoleDto
    {
        [Required]
        public int UserId { get; set; }

        [Required]
        [StringLength(50)]
        public string Role { get; set; } = string.Empty;
    }

    public class FamilyInviteResponseDto
    {
        public FamilyInviteResponseDto()
        {
            InviteCode = string.Empty;
            FamilyName = string.Empty;
        }

        public string InviteCode { get; set; }
        public int FamilyId { get; set; }
        public string FamilyName { get; set; }
        public DateTime ExpiresAt { get; set; }
    }
}
