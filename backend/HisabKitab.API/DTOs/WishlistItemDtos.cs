using System;
using System.ComponentModel.DataAnnotations;

namespace HisabKitab.API.DTOs
{
    public class WishlistItemDto
    {
        public WishlistItemDto()
        {
            Title = string.Empty;
            Description = string.Empty;
            ProductUrl = string.Empty;
            ImageUrl = string.Empty;
            Priority = new PriorityDto();
            LinkedSavingsGoal = new SavingsGoalDto();
            Status = string.Empty;
        }

        public int Id { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public decimal EstimatedPrice { get; set; }
        public string ProductUrl { get; set; }
        public string ImageUrl { get; set; }
        public DateTime AddedDate { get; set; }
        public DateTime? TargetPurchaseDate { get; set; }
        public int? UserId { get; set; }
        public string? UserName { get; set; }
        public int? FamilyId { get; set; }
        public string? FamilyName { get; set; }
        public bool IsShared { get; set; }
        public int? PriorityId { get; set; }
        public PriorityDto Priority { get; set; }
        public int? LinkedSavingsGoalId { get; set; }
        public SavingsGoalDto LinkedSavingsGoal { get; set; }
        public string Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class CreateWishlistItemDto
    {
        public CreateWishlistItemDto()
        {
            Title = string.Empty;
            Description = string.Empty;
            ProductUrl = string.Empty;
            ImageUrl = string.Empty;
        }

        [Required]
        [StringLength(100)]
        public string Title { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Estimated price must be greater than zero")]
        public decimal EstimatedPrice { get; set; }

        [StringLength(255)]
        [Url(ErrorMessage = "Invalid URL format")]
        public string ProductUrl { get; set; }

        [StringLength(255)]
        [Url(ErrorMessage = "Invalid URL format")]
        public string ImageUrl { get; set; }

        public DateTime? TargetPurchaseDate { get; set; }

        public int? FamilyId { get; set; }

        [Required]
        public bool IsShared { get; set; }

        public int? PriorityId { get; set; }

        public int? LinkedSavingsGoalId { get; set; }
    }

    public class UpdateWishlistItemDto
    {
        public UpdateWishlistItemDto()
        {
            Title = string.Empty;
            Description = string.Empty;
            ProductUrl = string.Empty;
            ImageUrl = string.Empty;
            Status = string.Empty;
        }

        [StringLength(100)]
        public string Title { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [Range(0.01, double.MaxValue, ErrorMessage = "Estimated price must be greater than zero")]
        public decimal? EstimatedPrice { get; set; }

        [StringLength(255)]
        [Url(ErrorMessage = "Invalid URL format")]
        public string ProductUrl { get; set; }

        [StringLength(255)]
        [Url(ErrorMessage = "Invalid URL format")]
        public string ImageUrl { get; set; }

        public DateTime? TargetPurchaseDate { get; set; }

        public int? PriorityId { get; set; }

        [RegularExpression("^(Pending|Purchased|Abandoned)$", ErrorMessage = "Status must be 'Pending', 'Purchased', or 'Abandoned'")]
        public string Status { get; set; }
    }

    public class LinkWishlistItemDto
    {
        [Required]
        public int SavingsGoalId { get; set; }
    }
}
