using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Enums;

namespace HisabKitab.API.DTOs
{
    public class SavingsGoalDto
    {
        public SavingsGoalDto()
        {
            Title = string.Empty;
            Description = string.Empty;
            Priority = new PriorityDto();
            AutoContributeFrequency = string.Empty;
            Status = string.Empty;
            Contributions = new List<SavingsContributionDto>();
            LinkedWishlistItems = new List<WishlistItemDto>();
        }

        public int Id { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public decimal TargetAmount { get; set; }
        public decimal CurrentAmount { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime TargetDate { get; set; }
        public int? UserId { get; set; }
        public string? UserName { get; set; }
        public int? FamilyId { get; set; }
        public string? FamilyName { get; set; }
        public bool IsShared { get; set; }
        public int? PriorityId { get; set; }
        public PriorityDto Priority { get; set; }
        public bool AutoContribute { get; set; }
        public decimal? AutoContributeAmount { get; set; }
        public string AutoContributeFrequency { get; set; }
        public string Status { get; set; }
        public List<SavingsContributionDto> Contributions { get; set; }
        public List<WishlistItemDto> LinkedWishlistItems { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Calculated fields
        public decimal Progress { get; set; }
        public decimal RemainingAmount { get; set; }
        public int DaysRemaining { get; set; }
        public bool IsOnTrack { get; set; }
    }

    public class CreateSavingsGoalDto
    {
        public CreateSavingsGoalDto()
        {
            Title = string.Empty;
            Description = string.Empty;
        }

        [Required]
        [StringLength(100)]
        public string Title { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Target amount must be greater than zero")]
        public decimal TargetAmount { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime TargetDate { get; set; }

        public int? FamilyId { get; set; }

        [Required]
        public bool IsShared { get; set; }

        public int? PriorityId { get; set; }

        [Required]
        public bool AutoContribute { get; set; }

        [Range(0.01, double.MaxValue, ErrorMessage = "Auto-contribute amount must be greater than zero")]
        public decimal? AutoContributeAmount { get; set; }

        public FrequencyType? AutoContributeFrequency { get; set; }
    }

    public class UpdateSavingsGoalDto
    {
        public UpdateSavingsGoalDto()
        {
            Title = string.Empty;
            Description = string.Empty;
            Status = string.Empty;
        }

        [StringLength(100)]
        public string Title { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [Range(0.01, double.MaxValue, ErrorMessage = "Target amount must be greater than zero")]
        public decimal? TargetAmount { get; set; }

        public DateTime? StartDate { get; set; }

        public DateTime? TargetDate { get; set; }

        public int? PriorityId { get; set; }

        public bool? AutoContribute { get; set; }

        [Range(0.01, double.MaxValue, ErrorMessage = "Auto-contribute amount must be greater than zero")]
        public decimal? AutoContributeAmount { get; set; }

        public FrequencyType? AutoContributeFrequency { get; set; }

        [RegularExpression("^(Active|Achieved|Abandoned)$", ErrorMessage = "Status must be 'Active', 'Achieved', or 'Abandoned'")]
        public string Status { get; set; }
    }

    public class SavingsContributionDto
    {
        public SavingsContributionDto()
        {
            Notes = string.Empty;
        }

        public int Id { get; set; }
        public int SavingsGoalId { get; set; }
        public int? UserId { get; set; }
        public string? UserName { get; set; }
        public decimal Amount { get; set; }
        public DateTime ContributionDate { get; set; }
        public int? TransactionId { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class CreateContributionDto
    {
        public CreateContributionDto()
        {
            Notes = string.Empty;
        }

        [Required]
        public int SavingsGoalId { get; set; }

        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than zero")]
        public decimal Amount { get; set; }

        [Required]
        public DateTime ContributionDate { get; set; }

        public int? TransactionId { get; set; }

        [StringLength(255)]
        public string Notes { get; set; }
    }

    public class UpdateContributionDto
    {
        public UpdateContributionDto()
        {
            Notes = string.Empty;
        }

        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than zero")]
        public decimal? Amount { get; set; }

        public DateTime? ContributionDate { get; set; }

        [StringLength(255)]
        public string Notes { get; set; }
    }

    public class SavingsGoalForecastDto
    {
        public SavingsGoalForecastDto()
        {
            TimelineData = new List<TimelineDataPoint>();
        }

        public int GoalId { get; set; }
        public DateTime ProjectedCompletionDate { get; set; }
        public decimal MonthlyContributionNeeded { get; set; }
        public decimal WeeklyContributionNeeded { get; set; }
        public bool IsAchievable { get; set; }
        public List<TimelineDataPoint> TimelineData { get; set; }
    }

    public class TimelineDataPoint
    {
        public DateTime Date { get; set; }
        public decimal Amount { get; set; }
    }
}
