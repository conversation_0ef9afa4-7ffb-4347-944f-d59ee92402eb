using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Enums;

namespace HisabKitab.API.DTOs
{
    public class TransactionDto
    {
        public TransactionDto()
        {
            Description = string.Empty;
            AccountName = string.Empty;
            ToAccountName = string.Empty;
            CategoryName = string.Empty;
            PriorityName = string.Empty;
            Username = string.Empty;
            Status = string.Empty;
            ApprovedByUsername = string.Empty;
            ReceiptImage = string.Empty;
            Tags = string.Empty;
            Location = string.Empty;
            Items = new List<TransactionItemDto>();
        }

        public int Id { get; set; }
        public decimal Amount { get; set; }
        public string Description { get; set; }
        public DateTime Date { get; set; }
        public TransactionType Type { get; set; }
        public int AccountId { get; set; }
        public string AccountName { get; set; }
        public int? ToAccountId { get; set; }
        public string ToAccountName { get; set; }
        public int CategoryId { get; set; }
        public string CategoryName { get; set; }
        public int? PriorityId { get; set; }
        public string PriorityName { get; set; }
        public int UserId { get; set; }
        public string Username { get; set; }
        public string Status { get; set; }
        public int? ApprovedByUserId { get; set; }
        public string ApprovedByUsername { get; set; }
        public string ReceiptImage { get; set; }
        public string Tags { get; set; }
        public string Location { get; set; }
        public decimal? ExchangeRate { get; set; }
        public int? RecurringTransactionId { get; set; }
        public bool IsSynced { get; set; }
        public SyncStatus SyncStatus { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<TransactionItemDto> Items { get; set; }
    }

    public class CreateTransactionDto
    {
        public CreateTransactionDto()
        {
            Description = string.Empty;
            Status = "Completed";
            ReceiptImage = string.Empty;
            Tags = string.Empty;
            Location = string.Empty;
            Items = new List<CreateTransactionItemDto>();
        }

        [Required]
        public decimal Amount { get; set; }

        [MaxLength(500)]
        public string Description { get; set; }

        [Required]
        public DateTime Date { get; set; }

        [Required]
        public TransactionType Type { get; set; }

        [Required]
        public int AccountId { get; set; }

        public int? ToAccountId { get; set; } // For transfers

        [Required]
        public int CategoryId { get; set; }

        public int? PriorityId { get; set; }

        [MaxLength(50)]
        public string Status { get; set; } = "Completed";

        [MaxLength(255)]
        public string ReceiptImage { get; set; }

        [MaxLength(500)]
        public string Tags { get; set; }

        [MaxLength(255)]
        public string Location { get; set; }

        public decimal? ExchangeRate { get; set; }

        public int? RecurringTransactionId { get; set; }

        public List<CreateTransactionItemDto> Items { get; set; }
    }

    public class UpdateTransactionDto
    {
        public UpdateTransactionDto()
        {
            Description = string.Empty;
            Status = string.Empty;
            ReceiptImage = string.Empty;
            Tags = string.Empty;
            Location = string.Empty;
            Items = new List<UpdateTransactionItemDto>();
        }

        public decimal? Amount { get; set; }

        [MaxLength(500)]
        public string Description { get; set; }

        public DateTime? Date { get; set; }

        public TransactionType? Type { get; set; }

        public int? AccountId { get; set; }

        public int? ToAccountId { get; set; }

        public int? CategoryId { get; set; }

        public int? PriorityId { get; set; }

        [MaxLength(50)]
        public string Status { get; set; }

        [MaxLength(255)]
        public string ReceiptImage { get; set; }

        [MaxLength(500)]
        public string Tags { get; set; }

        [MaxLength(255)]
        public string Location { get; set; }

        public decimal? ExchangeRate { get; set; }

        public int? RecurringTransactionId { get; set; }

        public List<UpdateTransactionItemDto> Items { get; set; }
    }

    public class TransactionItemDto
    {
        public TransactionItemDto()
        {
            Name = string.Empty;
            CategoryName = string.Empty;
            Notes = string.Empty;
        }

        public int Id { get; set; }
        public int TransactionId { get; set; }
        public string Name { get; set; }
        public decimal Amount { get; set; }
        public int Quantity { get; set; }
        public int? CategoryId { get; set; }
        public string CategoryName { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CreateTransactionItemDto
    {
        public CreateTransactionItemDto()
        {
            Name = string.Empty;
            Notes = string.Empty;
        }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [Required]
        public decimal Amount { get; set; }

        public int Quantity { get; set; } = 1;

        public int? CategoryId { get; set; }

        [MaxLength(255)]
        public string Notes { get; set; }
    }

    public class UpdateTransactionItemDto
    {
        public UpdateTransactionItemDto()
        {
            Name = string.Empty;
            Notes = string.Empty;
        }

        public int Id { get; set; }

        [MaxLength(100)]
        public string Name { get; set; }

        public decimal? Amount { get; set; }

        public int? Quantity { get; set; }

        public int? CategoryId { get; set; }

        [MaxLength(255)]
        public string Notes { get; set; }
    }

    public class TransactionFilterDto
    {
        public TransactionFilterDto()
        {
            Status = string.Empty;
            SearchTerm = string.Empty;
            SortBy = string.Empty;
        }

        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public TransactionType? Type { get; set; }
        public int? AccountId { get; set; }
        public int? CategoryId { get; set; }
        public int? PriorityId { get; set; }
        public string Status { get; set; }
        public string SearchTerm { get; set; }
        public string SortBy { get; set; }
        public bool Ascending { get; set; } = true;
        public int? Skip { get; set; }
        public int? Take { get; set; }
    }

    public class BatchTransactionDto
    {
        public BatchTransactionDto()
        {
            Transactions = new List<CreateTransactionDto>();
        }

        [Required]
        public List<CreateTransactionDto> Transactions { get; set; }
    }

    public class BatchUpdateTransactionDto
    {
        public BatchUpdateTransactionDto()
        {
            Transactions = new List<UpdateTransactionWithIdDto>();
        }

        [Required]
        public List<UpdateTransactionWithIdDto> Transactions { get; set; }
    }

    public class UpdateTransactionWithIdDto : UpdateTransactionDto
    {
        [Required]
        public int Id { get; set; }
    }

    public class BatchDeleteTransactionDto
    {
        public BatchDeleteTransactionDto()
        {
            TransactionIds = new List<int>();
        }

        [Required]
        public List<int> TransactionIds { get; set; }
    }
}
