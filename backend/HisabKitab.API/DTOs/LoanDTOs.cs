using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HisabKitab.Core.Enums;

namespace HisabKitab.API.DTOs
{
    public class LoanDto
    {
        public LoanDto()
        {
            Title = string.Empty;
            InterestTypeName = string.Empty;
            PaymentFrequencyName = string.Empty;
            LenderUsername = string.Empty;
            BorrowerUsername = string.Empty;
            ExternalEntityName = string.Empty;
            Status = string.Empty;
            Notes = string.Empty;
            Payments = new List<LoanPaymentDto>();
            Reminders = new List<LoanReminderDto>();
        }

        public int Id { get; set; }
        public string Title { get; set; }
        public decimal Amount { get; set; }
        public decimal InterestRate { get; set; }
        public InterestType InterestType { get; set; }
        public string InterestTypeName { get; set; }
        public FrequencyType PaymentFrequency { get; set; }
        public string PaymentFrequencyName { get; set; }
        public decimal TotalPayableAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? LenderUserId { get; set; }
        public string LenderUsername { get; set; }
        public int? BorrowerUserId { get; set; }
        public string BorrowerUsername { get; set; }
        public bool IsExternalEntity { get; set; }
        public string ExternalEntityName { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<LoanPaymentDto> Payments { get; set; }
        public List<LoanReminderDto> Reminders { get; set; }
        public decimal MonthlyEMI { get; set; }
        public DateTime? PredictedCompletionDate { get; set; }
    }

    public class CreateLoanDto
    {
        public CreateLoanDto()
        {
            Title = string.Empty;
            ExternalEntityName = string.Empty;
            Status = "Active";
            Notes = string.Empty;
        }

        [Required]
        [StringLength(100, MinimumLength = 3)]
        public string Title { get; set; }

        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal Amount { get; set; }

        [Required]
        [Range(0, 100)]
        public decimal InterestRate { get; set; }

        [Required]
        public InterestType InterestType { get; set; }

        [Required]
        public FrequencyType PaymentFrequency { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public int? LenderUserId { get; set; }

        public int? BorrowerUserId { get; set; }

        public bool IsExternalEntity { get; set; } = false;

        [StringLength(100)]
        public string ExternalEntityName { get; set; }

        [StringLength(50)]
        public string Status { get; set; } = "Active";

        [StringLength(500)]
        public string Notes { get; set; }
    }

    public class UpdateLoanDto
    {
        public UpdateLoanDto()
        {
            Title = string.Empty;
            ExternalEntityName = string.Empty;
            Status = string.Empty;
            Notes = string.Empty;
        }

        [StringLength(100, MinimumLength = 3)]
        public string Title { get; set; }

        [Range(0.01, double.MaxValue)]
        public decimal? Amount { get; set; }

        [Range(0, 100)]
        public decimal? InterestRate { get; set; }

        public InterestType? InterestType { get; set; }

        public FrequencyType? PaymentFrequency { get; set; }

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public int? LenderUserId { get; set; }

        public int? BorrowerUserId { get; set; }

        public bool? IsExternalEntity { get; set; }

        [StringLength(100)]
        public string ExternalEntityName { get; set; }

        [StringLength(50)]
        public string Status { get; set; }

        [StringLength(500)]
        public string Notes { get; set; }
    }

    public class LoanPaymentDto
    {
        public LoanPaymentDto()
        {
            PaymentMethod = string.Empty;
            Notes = string.Empty;
        }

        public int Id { get; set; }
        public int LoanId { get; set; }
        public decimal Amount { get; set; }
        public decimal PrincipalAmount { get; set; }
        public decimal InterestAmount { get; set; }
        public DateTime PaymentDate { get; set; }
        public string PaymentMethod { get; set; }
        public bool IsScheduled { get; set; }
        public int? TransactionId { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CreateLoanPaymentDto
    {
        public CreateLoanPaymentDto()
        {
            PaymentMethod = string.Empty;
            Notes = string.Empty;
        }

        [Required]
        public int LoanId { get; set; }

        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal Amount { get; set; }

        [Required]
        [Range(0, double.MaxValue)]
        public decimal PrincipalAmount { get; set; }

        [Required]
        [Range(0, double.MaxValue)]
        public decimal InterestAmount { get; set; }

        [Required]
        public DateTime PaymentDate { get; set; }

        [StringLength(50)]
        public string PaymentMethod { get; set; }

        public bool IsScheduled { get; set; } = false;

        public int? TransactionId { get; set; }

        [StringLength(255)]
        public string Notes { get; set; }
    }

    public class UpdateLoanPaymentDto
    {
        public UpdateLoanPaymentDto()
        {
            PaymentMethod = string.Empty;
            Notes = string.Empty;
        }

        [Range(0.01, double.MaxValue)]
        public decimal? Amount { get; set; }

        [Range(0, double.MaxValue)]
        public decimal? PrincipalAmount { get; set; }

        [Range(0, double.MaxValue)]
        public decimal? InterestAmount { get; set; }

        public DateTime? PaymentDate { get; set; }

        [StringLength(50)]
        public string PaymentMethod { get; set; }

        public bool? IsScheduled { get; set; }

        public int? TransactionId { get; set; }

        [StringLength(255)]
        public string Notes { get; set; }
    }

    public class LoanReminderDto
    {
        public LoanReminderDto()
        {
            Message = string.Empty;
        }

        public int Id { get; set; }
        public int LoanId { get; set; }
        public DateTime ReminderDate { get; set; }
        public string Message { get; set; }
        public bool IsActive { get; set; }
        public bool IsSent { get; set; }
        public DateTime? SentAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CreateLoanReminderDto
    {
        public CreateLoanReminderDto()
        {
            Message = string.Empty;
        }

        [Required]
        public int LoanId { get; set; }

        [Required]
        public DateTime ReminderDate { get; set; }

        [Required]
        [StringLength(255)]
        public string Message { get; set; }

        public bool IsActive { get; set; } = true;
    }

    public class UpdateLoanReminderDto
    {
        public UpdateLoanReminderDto()
        {
            Message = string.Empty;
        }

        public DateTime? ReminderDate { get; set; }

        [StringLength(255)]
        public string Message { get; set; }

        public bool? IsActive { get; set; }

        public bool? IsSent { get; set; }

        public DateTime? SentAt { get; set; }
    }

    public class LoanTimelineDto
    {
        public LoanTimelineDto()
        {
            LoanTitle = string.Empty;
            Timeline = new Dictionary<string, decimal>();
        }

        public int LoanId { get; set; }
        public string LoanTitle { get; set; }
        public Dictionary<string, decimal> Timeline { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public DateTime? PredictedCompletionDate { get; set; }
    }

    public class LoanEMICalculationDto
    {
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal Principal { get; set; }

        [Required]
        [Range(0, 100)]
        public decimal InterestRate { get; set; }

        [Required]
        public InterestType InterestType { get; set; }

        [Required]
        public FrequencyType PaymentFrequency { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }
    }
}
