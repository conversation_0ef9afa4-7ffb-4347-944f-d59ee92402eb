using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.API.DTOs;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReceiptController : ControllerBase
    {
        private readonly ILogger<ReceiptController> _logger;
        private readonly IReceiptService _receiptService;
        private readonly IOCRService _ocrService;
        private readonly IFileStorageService _fileStorageService;
        private readonly ApplicationDbContext _dbContext;

        public ReceiptController(
            ILogger<ReceiptController> logger,
            IReceiptService receiptService,
            IOCRService ocrService,
            IFileStorageService fileStorageService,
            ApplicationDbContext dbContext)
        {
            _logger = logger;
            _receiptService = receiptService;
            _ocrService = ocrService;
            _fileStorageService = fileStorageService;
            _dbContext = dbContext;
        }

        [HttpPost("process")]
        public async Task<IActionResult> ProcessReceipt([FromBody] ProcessReceiptRequestDto request)
        {
            try
            {
                var userId = int.Parse(User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "0");

                // Validate request
                if (string.IsNullOrEmpty(request.ImageBase64))
                {
                    return BadRequest(new { message = "Image data is required" });
                }

                // Validate base64 format
                if (!IsValidBase64(request.ImageBase64))
                {
                    return BadRequest(new { message = "Invalid image format. Please provide a valid base64 encoded image." });
                }

                // Validate transaction access if provided
                if (request.TransactionId.HasValue)
                {
                    var transaction = await _dbContext.Transactions
                        .FirstOrDefaultAsync(t => t.Id == request.TransactionId.Value);

                    if (transaction == null)
                    {
                        return NotFound(new { message = $"Transaction with ID {request.TransactionId.Value} not found" });
                    }

                    // Check if user owns the transaction
                    if (transaction.UserId != userId)
                    {
                        // Check if user has access to the account
                        var account = await _dbContext.Accounts
                            .FirstOrDefaultAsync(a => a.Id == transaction.AccountId);

                        if (account == null || (account.UserId != userId &&
                            !await _dbContext.AccountShares.AnyAsync(s =>
                                s.AccountId == account.Id && s.FamilyMemberId == userId)))
                        {
                            return Forbid();
                        }
                    }
                }

                // Process the receipt
                var receiptData = await _ocrService.ProcessReceiptBase64Async(
                    request.ImageBase64,
                    request.TransactionId,
                    request.Language);

                // Save to database
                if (request.TransactionId.HasValue)
                {
                    receiptData.TransactionId = request.TransactionId.Value;
                }

                var savedReceipt = await _receiptService.CreateReceiptAsync(
                    receiptData.TransactionId,
                    receiptData.ImagePath,
                    receiptData.OcrText,
                    receiptData.ParsedData,
                    receiptData.ScanDate,
                    receiptData.Language,
                    receiptData.IsProcessed);

                // Map to DTO
                var receiptDto = MapReceiptToDto(savedReceipt);

                return Ok(receiptDto);
            }
            catch (FormatException ex)
            {
                _logger.LogWarning(ex, "Invalid image format");
                return BadRequest(new { message = "Invalid image format. Please provide a valid base64 encoded image." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing receipt");
                return StatusCode(500, new { message = "An error occurred while processing the receipt" });
            }
        }

        private static bool IsValidBase64(string base64String)
        {
            // Remove data:image/jpeg;base64, prefix if present
            if (base64String.Contains(','))
            {
                base64String = base64String.Split(',')[1];
            }

            try
            {
                var buffer = Convert.FromBase64String(base64String);
                return buffer.Length > 0;
            }
            catch
            {
                return false;
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetReceipt(int id)
        {
            try
            {
                var userId = int.Parse(User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "0");

                // Check if user can access the receipt
                var canAccess = await _receiptService.UserCanAccessReceiptAsync(id, userId);
                if (!canAccess)
                {
                    return Forbid();
                }

                var receipt = await _receiptService.GetReceiptByIdAsync(id);
                var receiptDto = MapReceiptToDto(receipt);

                return Ok(receiptDto);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting receipt {id}");
                return StatusCode(500, new { message = "An error occurred while retrieving the receipt" });
            }
        }

        [HttpGet("transaction/{transactionId}")]
        public async Task<IActionResult> GetReceiptsByTransaction(int transactionId)
        {
            try
            {
                var userId = int.Parse(User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "0");

                // Check if transaction exists
                var transaction = await _dbContext.Transactions
                    .FirstOrDefaultAsync(t => t.Id == transactionId);

                if (transaction == null)
                {
                    return NotFound(new { message = $"Transaction with ID {transactionId} not found" });
                }

                // Check if user owns the transaction
                if (transaction.UserId != userId)
                {
                    // Check if user has access to the account
                    var account = await _dbContext.Accounts
                        .FirstOrDefaultAsync(a => a.Id == transaction.AccountId);

                    if (account == null || (account.UserId != userId &&
                        !await _dbContext.AccountShares.AnyAsync(s =>
                            s.AccountId == account.Id && s.FamilyMemberId == userId)))
                    {
                        return Forbid();
                    }
                }

                var receipts = await _receiptService.GetReceiptsByTransactionIdAsync(transactionId);
                var receiptDtos = receipts.Select(MapReceiptToDto).ToList();

                return Ok(receiptDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting receipts for transaction {transactionId}");
                return StatusCode(500, new { message = "An error occurred while retrieving the receipts" });
            }
        }

        [HttpGet("history")]
        public async Task<IActionResult> GetReceiptHistory([FromQuery] ReceiptFilterDto filter)
        {
            try
            {
                var userId = int.Parse(User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "0");

                var receipts = await _receiptService.GetReceiptHistoryAsync(
                    userId,
                    filter.StartDate,
                    filter.EndDate,
                    filter.MinAmount,
                    filter.MaxAmount,
                    filter.MerchantName,
                    filter.IsProcessed);

                var historyItems = receipts.Select(MapReceiptToHistoryItem).ToList();

                return Ok(historyItems);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting receipt history");
                return StatusCode(500, new { message = "An error occurred while retrieving the receipt history" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SaveReceipt([FromBody] SaveReceiptRequestDto request)
        {
            try
            {
                var userId = int.Parse(User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "0");

                // Validate request
                if (string.IsNullOrEmpty(request.ImageBase64))
                {
                    return BadRequest(new { message = "Image data is required" });
                }

                // Validate base64 format
                if (!IsValidBase64(request.ImageBase64))
                {
                    return BadRequest(new { message = "Invalid image format. Please provide a valid base64 encoded image." });
                }

                // Check if transaction exists
                var transaction = await _dbContext.Transactions
                    .FirstOrDefaultAsync(t => t.Id == request.TransactionId);

                if (transaction == null)
                {
                    return NotFound(new { message = $"Transaction with ID {request.TransactionId} not found" });
                }

                // Check if user owns the transaction
                if (transaction.UserId != userId)
                {
                    // Check if user has access to the account
                    var account = await _dbContext.Accounts
                        .FirstOrDefaultAsync(a => a.Id == transaction.AccountId);

                    if (account == null || (account.UserId != userId &&
                        !await _dbContext.AccountShares.AnyAsync(s =>
                            s.AccountId == account.Id && s.FamilyMemberId == userId)))
                    {
                        return Forbid();
                    }
                }

                // Save the image
                var imagePath = await _fileStorageService.SaveBase64FileAsync(
                    request.ImageBase64,
                    "receipts",
                    $"receipt_{Guid.NewGuid()}.jpg");

                // Create receipt items JSON
                var parsedData = System.Text.Json.JsonSerializer.Serialize(new
                {
                    MerchantName = request.MerchantName,
                    Date = request.Date,
                    TotalAmount = request.TotalAmount,
                    Items = request.Items
                });

                // Create receipt
                var receipt = await _receiptService.CreateReceiptAsync(
                    request.TransactionId,
                    imagePath,
                    request.RawText,
                    parsedData,
                    DateTime.UtcNow,
                    request.Language,
                    true);

                // Update additional fields
                receipt.MerchantName = request.MerchantName;
                receipt.ReceiptDate = request.Date;
                receipt.TotalAmount = request.TotalAmount;

                await _receiptService.UpdateReceiptAsync(
                    receipt.Id,
                    receipt.OcrText,
                    parsedData,
                    true);

                var receiptDto = MapReceiptToDto(receipt);

                return CreatedAtAction(nameof(GetReceipt), new { id = receipt.Id }, receiptDto);
            }
            catch (FormatException ex)
            {
                _logger.LogWarning(ex, "Invalid image format");
                return BadRequest(new { message = "Invalid image format. Please provide a valid base64 encoded image." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving receipt");
                return StatusCode(500, new { message = "An error occurred while saving the receipt" });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateReceipt(int id, [FromBody] UpdateReceiptRequestDto request)
        {
            try
            {
                var userId = int.Parse(User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "0");

                // Check if user can access the receipt
                var canAccess = await _receiptService.UserCanAccessReceiptAsync(id, userId);
                if (!canAccess)
                {
                    return Forbid();
                }

                // Get the receipt
                var receipt = await _receiptService.GetReceiptByIdAsync(id);

                // Update image if provided
                string imagePath = receipt.ImagePath;
                if (!string.IsNullOrEmpty(request.ImageBase64))
                {
                    // Delete old image
                    await _fileStorageService.DeleteFileAsync(receipt.ImagePath);

                    // Save new image
                    imagePath = await _fileStorageService.SaveBase64FileAsync(
                        request.ImageBase64,
                        "receipts",
                        $"receipt_{Guid.NewGuid()}.jpg");
                }

                // Create updated parsed data
                string parsedData = receipt.ParsedData;
                if (request.MerchantName != null || request.Date.HasValue || request.TotalAmount.HasValue || request.Items != null)
                {
                    var parsedJson = System.Text.Json.JsonSerializer.Deserialize<dynamic>(receipt.ParsedData);

                    var updatedData = new
                    {
                        MerchantName = request.MerchantName ?? receipt.MerchantName,
                        Date = request.Date ?? receipt.ReceiptDate,
                        TotalAmount = request.TotalAmount ?? receipt.TotalAmount,
                        Items = request.Items ?? parsedJson?.Items
                    };

                    parsedData = System.Text.Json.JsonSerializer.Serialize(updatedData);
                }

                // Update receipt
                var updatedReceipt = await _receiptService.UpdateReceiptAsync(
                    id,
                    request.RawText,
                    parsedData,
                    request.IsProcessed);

                // Update additional fields
                if (request.MerchantName != null)
                    updatedReceipt.MerchantName = request.MerchantName;

                if (request.Date.HasValue)
                    updatedReceipt.ReceiptDate = request.Date;

                if (request.TotalAmount.HasValue)
                    updatedReceipt.TotalAmount = request.TotalAmount.Value;

                if (imagePath != receipt.ImagePath)
                    updatedReceipt.ImagePath = imagePath;

                await _receiptService.UpdateReceiptAsync(
                    id,
                    updatedReceipt.OcrText,
                    parsedData,
                    updatedReceipt.IsProcessed);

                var receiptDto = MapReceiptToDto(updatedReceipt);

                return Ok(receiptDto);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating receipt {id}");
                return StatusCode(500, new { message = "An error occurred while updating the receipt" });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteReceipt(int id)
        {
            try
            {
                var userId = int.Parse(User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "0");

                // Check if user can access the receipt
                var canAccess = await _receiptService.UserCanAccessReceiptAsync(id, userId);
                if (!canAccess)
                {
                    return Forbid();
                }

                var result = await _receiptService.DeleteReceiptAsync(id);
                if (!result)
                {
                    return NotFound();
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting receipt {id}");
                return StatusCode(500, new { message = "An error occurred while deleting the receipt" });
            }
        }

        #region Helper Methods

        private ReceiptDto MapReceiptToDto(ReceiptData receipt)
        {
            // Parse items from JSON
            var items = new List<ReceiptItemDto>();
            if (!string.IsNullOrEmpty(receipt.ParsedData))
            {
                try
                {
                    var parsedJson = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(receipt.ParsedData);
                    if (parsedJson.ValueKind != System.Text.Json.JsonValueKind.Undefined &&
                        parsedJson.TryGetProperty("Items", out System.Text.Json.JsonElement itemsProperty))
                    {
                        foreach (var item in itemsProperty.EnumerateArray())
                        {
                            items.Add(new ReceiptItemDto
                            {
                                Name = item.GetProperty("Name").GetString() ?? string.Empty,
                                Amount = item.GetProperty("Amount").GetDecimal(),
                                Quantity = item.TryGetProperty("Quantity", out System.Text.Json.JsonElement quantityProp) ? quantityProp.GetInt32() : 1,
                                CategoryId = item.TryGetProperty("CategoryId", out System.Text.Json.JsonElement categoryIdProp) ? categoryIdProp.GetInt32() : null,
                                CategoryName = item.TryGetProperty("CategoryName", out System.Text.Json.JsonElement categoryNameProp) && categoryNameProp.ValueKind != System.Text.Json.JsonValueKind.Null ? categoryNameProp.GetString() ?? string.Empty : string.Empty
                            });
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"Error parsing receipt items for receipt {receipt.Id}");
                }
            }

            return new ReceiptDto
            {
                Id = receipt.Id,
                TransactionId = receipt.TransactionId,
                MerchantName = receipt.MerchantName,
                Date = receipt.ReceiptDate ?? receipt.ScanDate,
                TotalAmount = receipt.TotalAmount,
                Items = items,
                RawText = receipt.OcrText,
                ImageUrl = _fileStorageService.GetFileUrl(receipt.ImagePath),
                Language = receipt.Language,
                IsProcessed = receipt.IsProcessed,
                Confidence = receipt.Confidence,
                CreatedAt = receipt.CreatedAt,
                UpdatedAt = receipt.UpdatedAt
            };
        }

        private ReceiptHistoryItemDto MapReceiptToHistoryItem(ReceiptData receipt)
        {
            return new ReceiptHistoryItemDto
            {
                Id = receipt.Id,
                TransactionId = receipt.TransactionId,
                TransactionDescription = receipt.Transaction?.Description ?? string.Empty,
                MerchantName = receipt.MerchantName,
                Date = receipt.ReceiptDate ?? receipt.ScanDate,
                TotalAmount = receipt.TotalAmount,
                ImageUrl = _fileStorageService.GetFileUrl(receipt.ImagePath),
                IsProcessed = receipt.IsProcessed,
                CreatedAt = receipt.CreatedAt
            };
        }

        #endregion
    }
}
