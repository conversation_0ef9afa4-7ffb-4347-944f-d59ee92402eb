using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.API.DTOs;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/loan-reminders")]
    [Authorize]
    public class LoanReminderController : ControllerBase
    {
        private readonly ILoanService _loanService;
        private readonly ILogger<LoanReminderController> _logger;

        public LoanReminderController(
            ILoanService loanService,
            ILogger<LoanReminderController> logger)
        {
            _loanService = loanService;
            _logger = logger;
        }

        [HttpPost]
        public async Task<IActionResult> CreateReminder([FromBody] CreateLoanReminderDto createReminderDto)
        {
            try
            {
                string? userIdString = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdString))
                {
                    return Unauthorized(new { message = "User ID not found in claims" });
                }
                int userId = int.Parse(userIdString);

                // Check if user can access this loan
                if (!await _loanService.UserCanAccessLoanAsync(createReminderDto.LoanId, userId))
                {
                    return Forbid();
                }

                var reminder = await _loanService.CreateReminderAsync(
                    createReminderDto.LoanId,
                    createReminderDto.ReminderDate,
                    createReminderDto.Message,
                    createReminderDto.IsActive);

                // Map to DTO
                var reminderDto = new LoanReminderDto
                {
                    Id = reminder.Id,
                    LoanId = reminder.LoanId,
                    ReminderDate = reminder.ReminderDate,
                    Message = reminder.Message,
                    IsActive = reminder.IsActive,
                    IsSent = reminder.IsSent,
                    SentAt = reminder.SentAt,
                    CreatedAt = reminder.CreatedAt,
                    UpdatedAt = reminder.UpdatedAt
                };

                _logger.LogInformation($"Reminder created for loan ID {reminder.LoanId}: {reminder.Message}");

                return CreatedAtAction(nameof(GetReminderById), new { id = reminder.Id }, reminderDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning($"Loan not found: {ex.Message}");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating reminder");
                return StatusCode(500, new { message = "An error occurred while creating the reminder" });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetReminderById(int id)
        {
            try
            {
                string? userIdString = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdString))
                {
                    return Unauthorized(new { message = "User ID not found in claims" });
                }
                int userId = int.Parse(userIdString);

                var reminder = await _loanService.GetReminderByIdAsync(id);

                // Check if user can access this loan
                if (!await _loanService.UserCanAccessLoanAsync(reminder.LoanId, userId))
                {
                    return Forbid();
                }

                // Map to DTO
                var reminderDto = new LoanReminderDto
                {
                    Id = reminder.Id,
                    LoanId = reminder.LoanId,
                    ReminderDate = reminder.ReminderDate,
                    Message = reminder.Message,
                    IsActive = reminder.IsActive,
                    IsSent = reminder.IsSent,
                    SentAt = reminder.SentAt,
                    CreatedAt = reminder.CreatedAt,
                    UpdatedAt = reminder.UpdatedAt
                };

                return Ok(reminderDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning($"Reminder not found: {ex.Message}");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving reminder with ID {id}");
                return StatusCode(500, new { message = "An error occurred while retrieving the reminder" });
            }
        }

        [HttpGet("by-loan/{loanId}")]
        public async Task<IActionResult> GetRemindersByLoanId(int loanId)
        {
            try
            {
                string? userIdString = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdString))
                {
                    return Unauthorized(new { message = "User ID not found in claims" });
                }
                int userId = int.Parse(userIdString);

                // Check if user can access this loan
                if (!await _loanService.UserCanAccessLoanAsync(loanId, userId))
                {
                    return Forbid();
                }

                var reminders = await _loanService.GetRemindersByLoanIdAsync(loanId);

                // Map to DTOs
                var reminderDtos = reminders.Select(r => new LoanReminderDto
                {
                    Id = r.Id,
                    LoanId = r.LoanId,
                    ReminderDate = r.ReminderDate,
                    Message = r.Message,
                    IsActive = r.IsActive,
                    IsSent = r.IsSent,
                    SentAt = r.SentAt,
                    CreatedAt = r.CreatedAt,
                    UpdatedAt = r.UpdatedAt
                }).ToList();

                return Ok(reminderDtos);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning($"Loan not found: {ex.Message}");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving reminders for loan ID {loanId}");
                return StatusCode(500, new { message = "An error occurred while retrieving the reminders" });
            }
        }

        [HttpGet("active")]
        public async Task<IActionResult> GetActiveReminders([FromQuery] DateTime? beforeDate = null)
        {
            try
            {
                string? userIdString = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdString))
                {
                    return Unauthorized(new { message = "User ID not found in claims" });
                }
                int userId = int.Parse(userIdString);

                var reminders = await _loanService.GetActiveRemindersAsync(beforeDate);

                // Filter reminders for loans the user can access
                var accessibleReminders = new List<LoanReminderDto>();
                foreach (var reminder in reminders)
                {
                    if (await _loanService.UserCanAccessLoanAsync(reminder.LoanId, userId))
                    {
                        accessibleReminders.Add(new LoanReminderDto
                        {
                            Id = reminder.Id,
                            LoanId = reminder.LoanId,
                            ReminderDate = reminder.ReminderDate,
                            Message = reminder.Message,
                            IsActive = reminder.IsActive,
                            IsSent = reminder.IsSent,
                            SentAt = reminder.SentAt,
                            CreatedAt = reminder.CreatedAt,
                            UpdatedAt = reminder.UpdatedAt
                        });
                    }
                }

                return Ok(accessibleReminders);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active reminders");
                return StatusCode(500, new { message = "An error occurred while retrieving active reminders" });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateReminder(int id, [FromBody] UpdateLoanReminderDto updateReminderDto)
        {
            try
            {
                string? userIdString = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdString))
                {
                    return Unauthorized(new { message = "User ID not found in claims" });
                }
                int userId = int.Parse(userIdString);

                // Get reminder to check loan access
                var existingReminder = await _loanService.GetReminderByIdAsync(id);

                // Check if user can access this loan
                if (!await _loanService.UserCanAccessLoanAsync(existingReminder.LoanId, userId))
                {
                    return Forbid();
                }

                var reminder = await _loanService.UpdateReminderAsync(
                    id,
                    updateReminderDto.ReminderDate,
                    updateReminderDto.Message,
                    updateReminderDto.IsActive,
                    updateReminderDto.IsSent,
                    updateReminderDto.SentAt);

                // Map to DTO
                var reminderDto = new LoanReminderDto
                {
                    Id = reminder.Id,
                    LoanId = reminder.LoanId,
                    ReminderDate = reminder.ReminderDate,
                    Message = reminder.Message,
                    IsActive = reminder.IsActive,
                    IsSent = reminder.IsSent,
                    SentAt = reminder.SentAt,
                    CreatedAt = reminder.CreatedAt,
                    UpdatedAt = reminder.UpdatedAt
                };

                _logger.LogInformation($"Reminder updated: ID {reminder.Id} for loan ID {reminder.LoanId}");

                return Ok(reminderDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning($"Reminder not found: {ex.Message}");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating reminder with ID {id}");
                return StatusCode(500, new { message = "An error occurred while updating the reminder" });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteReminder(int id)
        {
            try
            {
                string? userIdString = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdString))
                {
                    return Unauthorized(new { message = "User ID not found in claims" });
                }
                int userId = int.Parse(userIdString);

                // Get reminder to check loan access
                var existingReminder = await _loanService.GetReminderByIdAsync(id);

                // Check if user can access this loan
                if (!await _loanService.UserCanAccessLoanAsync(existingReminder.LoanId, userId))
                {
                    return Forbid();
                }

                await _loanService.DeleteReminderAsync(id);

                _logger.LogInformation($"Reminder deleted with ID {id}");

                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning($"Reminder not found: {ex.Message}");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting reminder with ID {id}");
                return StatusCode(500, new { message = "An error occurred while deleting the reminder" });
            }
        }

        [HttpPost("{id}/mark-sent")]
        public async Task<IActionResult> MarkReminderAsSent(int id)
        {
            try
            {
                string? userIdString = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdString))
                {
                    return Unauthorized(new { message = "User ID not found in claims" });
                }
                int userId = int.Parse(userIdString);

                // Get reminder to check loan access
                var existingReminder = await _loanService.GetReminderByIdAsync(id);

                // Check if user can access this loan
                if (!await _loanService.UserCanAccessLoanAsync(existingReminder.LoanId, userId))
                {
                    return Forbid();
                }

                await _loanService.MarkReminderAsSentAsync(id);

                _logger.LogInformation($"Reminder marked as sent: ID {id}");

                return Ok(new { message = "Reminder marked as sent" });
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning($"Reminder not found: {ex.Message}");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error marking reminder as sent with ID {id}");
                return StatusCode(500, new { message = "An error occurred while marking the reminder as sent" });
            }
        }
    }
}
