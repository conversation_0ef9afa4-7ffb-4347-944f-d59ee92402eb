using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.API.Middleware;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReportController : ControllerBase
    {
        private readonly IReportService _reportService;
        private readonly IFamilyService _familyService;
        private readonly ILogger<ReportController> _logger;

        public ReportController(
            IReportService reportService,
            IFamilyService familyService,
            ILogger<ReportController> logger)
        {
            _reportService = reportService;
            _familyService = familyService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetReports([FromQuery] bool includeShared = true)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }
                var reports = await _reportService.GetReportsByUserIdAsync(userId, includeShared);

                var reportDtos = reports.Select(r => new
                {
                    r.Id,
                    r.Name,
                    r.Type,
                    r.Parameters,
                    r.UserId,
                    UserName = r.User?.Username,
                    r.FamilyId,
                    FamilyName = r.Family?.Name,
                    r.IsShared,
                    r.Layout,
                    r.LastRunAt,
                    r.CreatedAt,
                    r.UpdatedAt
                }).ToList();

                return Ok(reportDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reports");
                return StatusCode(500, new { message = "An error occurred while retrieving reports" });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetReport(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }
                var report = await _reportService.GetReportByIdAsync(id);

                // Check if user can access the report
                if (report.UserId != userId)
                {
                    // If report is shared with a family, check if user is a member
                    if (report.FamilyId.HasValue && report.IsShared)
                    {
                        var isFamilyMember = await _familyService.IsFamilyMemberAsync(report.FamilyId.Value, userId);
                        if (!isFamilyMember)
                        {
                            return Forbid();
                        }
                    }
                    else
                    {
                        return Forbid();
                    }
                }

                var reportDto = new
                {
                    report.Id,
                    report.Name,
                    report.Type,
                    report.Parameters,
                    report.UserId,
                    UserName = report.User?.Username,
                    report.FamilyId,
                    FamilyName = report.Family?.Name,
                    report.IsShared,
                    report.Layout,
                    report.LastRunAt,
                    report.CreatedAt,
                    report.UpdatedAt
                };

                return Ok(reportDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report");
                return StatusCode(500, new { message = "An error occurred while retrieving the report" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateReport([FromBody] CreateReportDto createReportDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // If family ID is provided, check if user is a member
                if (createReportDto.FamilyId.HasValue)
                {
                    var isFamilyMember = await _familyService.IsFamilyMemberAsync(createReportDto.FamilyId.Value, userId);
                    if (!isFamilyMember)
                    {
                        return Forbid();
                    }
                }

                var report = await _reportService.CreateReportAsync(
                    createReportDto.Name,
                    createReportDto.Type,
                    createReportDto.Parameters,
                    userId,
                    createReportDto.FamilyId,
                    createReportDto.IsShared,
                    createReportDto.Layout);

                var reportDto = new
                {
                    report.Id,
                    report.Name,
                    report.Type,
                    report.Parameters,
                    report.UserId,
                    report.FamilyId,
                    report.IsShared,
                    report.Layout,
                    report.LastRunAt,
                    report.CreatedAt,
                    report.UpdatedAt
                };

                return CreatedAtAction(nameof(GetReport), new { id = report.Id }, reportDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating report");
                return StatusCode(500, new { message = "An error occurred while creating the report" });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateReport(int id, [FromBody] UpdateReportDto updateReportDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }
                var report = await _reportService.GetReportByIdAsync(id);

                // Check if user owns the report
                if (report.UserId != userId)
                {
                    return Forbid();
                }

                var updatedReport = await _reportService.UpdateReportAsync(
                    id,
                    updateReportDto.Name,
                    updateReportDto.Type,
                    updateReportDto.Parameters,
                    updateReportDto.IsShared,
                    updateReportDto.Layout);

                var reportDto = new
                {
                    updatedReport.Id,
                    updatedReport.Name,
                    updatedReport.Type,
                    updatedReport.Parameters,
                    updatedReport.UserId,
                    updatedReport.FamilyId,
                    updatedReport.IsShared,
                    updatedReport.Layout,
                    updatedReport.LastRunAt,
                    updatedReport.CreatedAt,
                    updatedReport.UpdatedAt
                };

                return Ok(reportDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating report");
                return StatusCode(500, new { message = "An error occurred while updating the report" });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteReport(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }
                var report = await _reportService.GetReportByIdAsync(id);

                // Check if user owns the report
                if (report.UserId != userId)
                {
                    return Forbid();
                }

                await _reportService.DeleteReportAsync(id);

                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting report");
                return StatusCode(500, new { message = "An error occurred while deleting the report" });
            }
        }

        [HttpGet("{id}/data")]
        public async Task<IActionResult> GetReportData(int id, [FromQuery] bool forceRefresh = false)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }
                var report = await _reportService.GetReportByIdAsync(id);

                // Check if user can access the report
                if (report.UserId != userId)
                {
                    // If report is shared with a family, check if user is a member
                    if (report.FamilyId.HasValue && report.IsShared)
                    {
                        var isFamilyMember = await _familyService.IsFamilyMemberAsync(report.FamilyId.Value, userId);
                        if (!isFamilyMember)
                        {
                            return Forbid();
                        }
                    }
                    else
                    {
                        return Forbid();
                    }
                }

                var reportData = await _reportService.GetReportDataAsync(id, forceRefresh);

                var reportDataDto = new
                {
                    reportData.Id,
                    reportData.ReportId,
                    reportData.Data,
                    reportData.GeneratedAt,
                    reportData.ExpiresAt,
                    reportData.IsCached
                };

                return Ok(reportDataDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report data");
                return StatusCode(500, new { message = "An error occurred while retrieving the report data" });
            }
        }

        [HttpPost("{id}/share")]
        public async Task<IActionResult> ShareReport(int id, [FromBody] ShareReportDto shareReportDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }
                var report = await _reportService.GetReportByIdAsync(id);

                // Check if user owns the report
                if (report.UserId != userId)
                {
                    return Forbid();
                }

                await _reportService.ShareReportAsync(id, shareReportDto.IsShared);

                return Ok(new { message = $"Report sharing {(shareReportDto.IsShared ? "enabled" : "disabled")}" });
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sharing report");
                return StatusCode(500, new { message = "An error occurred while sharing the report" });
            }
        }
    }

    public class CreateReportDto
    {
        [Required]
        [StringLength(100, MinimumLength = 3)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Type { get; set; } = string.Empty;

        public string Parameters { get; set; } = string.Empty;

        public int? FamilyId { get; set; }

        public bool IsShared { get; set; } = false;

        public string Layout { get; set; } = string.Empty;
    }

    public class UpdateReportDto
    {
        [Required]
        [StringLength(100, MinimumLength = 3)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Type { get; set; } = string.Empty;

        public string Parameters { get; set; } = string.Empty;

        public bool IsShared { get; set; }

        public string Layout { get; set; } = string.Empty;
    }

    public class ShareReportDto
    {
        [Required]
        public bool IsShared { get; set; }
    }
}
