using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using HisabKitab.API.DTOs;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Interfaces;

namespace HisabKitab.API.Controllers
{
    [Route("api/savings-goals")]
    [Authorize]
    public class SavingsGoalController : BaseApiController
    {
        private readonly ISavingsService _savingsService;
        private readonly ILogger<SavingsGoalController> _logger;

        public SavingsGoalController(ISavingsService savingsService, ILogger<SavingsGoalController> logger)
        {
            _savingsService = savingsService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<SavingsGoalDto>>> GetSavingsGoals(
            [FromQuery] string? status = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }
                var savingsGoals = await _savingsService.GetSavingsGoalsByUserIdAsync(userId, status, startDate, endDate);

                var savingsGoalDtos = savingsGoals.Select(MapToSavingsGoalDto).ToList();

                return Ok(savingsGoalDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting savings goals");
                return StatusCode(500, "An error occurred while retrieving savings goals");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<SavingsGoalDto>> GetSavingsGoal(int id)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Check if user can access the savings goal
                if (!await _savingsService.UserCanAccessSavingsGoalAsync(id, userId))
                {
                    return Forbid();
                }

                var savingsGoal = await _savingsService.GetSavingsGoalByIdAsync(id);
                var savingsGoalDto = MapToSavingsGoalDto(savingsGoal);

                return Ok(savingsGoalDto);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting savings goal with ID {id}");
                return StatusCode(500, "An error occurred while retrieving the savings goal");
            }
        }

        [HttpPost]
        public async Task<ActionResult<SavingsGoalDto>> CreateSavingsGoal(CreateSavingsGoalDto createDto)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Validate target date is after start date
                if (createDto.TargetDate <= createDto.StartDate)
                {
                    return BadRequest("Target date must be after start date");
                }

                // Validate auto-contribute fields
                if (createDto.AutoContribute)
                {
                    if (!createDto.AutoContributeAmount.HasValue || createDto.AutoContributeAmount.Value <= 0)
                    {
                        return BadRequest("Auto-contribute amount must be greater than zero");
                    }

                    if (!createDto.AutoContributeFrequency.HasValue)
                    {
                        return BadRequest("Auto-contribute frequency is required");
                    }
                }

                var savingsGoal = await _savingsService.CreateSavingsGoalAsync(
                    createDto.Title,
                    createDto.Description,
                    createDto.TargetAmount,
                    createDto.StartDate,
                    createDto.TargetDate,
                    userId,
                    createDto.IsShared ? createDto.FamilyId : null,
                    createDto.IsShared,
                    createDto.PriorityId,
                    createDto.AutoContribute,
                    createDto.AutoContributeAmount,
                    createDto.AutoContributeFrequency);

                var savingsGoalDto = MapToSavingsGoalDto(savingsGoal);

                return CreatedAtAction(nameof(GetSavingsGoal), new { id = savingsGoalDto.Id }, savingsGoalDto);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating savings goal");
                return StatusCode(500, "An error occurred while creating the savings goal");
            }
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<SavingsGoalDto>> UpdateSavingsGoal(int id, UpdateSavingsGoalDto updateDto)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Check if user can modify the savings goal
                if (!await _savingsService.UserCanModifySavingsGoalAsync(id, userId))
                {
                    return Forbid();
                }

                // Validate target date is after start date if both are provided
                if (updateDto.StartDate.HasValue && updateDto.TargetDate.HasValue && updateDto.TargetDate.Value <= updateDto.StartDate.Value)
                {
                    return BadRequest("Target date must be after start date");
                }

                // Validate auto-contribute fields
                if (updateDto.AutoContribute.HasValue && updateDto.AutoContribute.Value)
                {
                    if (updateDto.AutoContributeAmount.HasValue && updateDto.AutoContributeAmount.Value <= 0)
                    {
                        return BadRequest("Auto-contribute amount must be greater than zero");
                    }
                }

                var savingsGoal = await _savingsService.UpdateSavingsGoalAsync(
                    id,
                    updateDto.Title,
                    updateDto.Description,
                    updateDto.TargetAmount,
                    updateDto.StartDate,
                    updateDto.TargetDate,
                    updateDto.PriorityId,
                    updateDto.AutoContribute,
                    updateDto.AutoContributeAmount,
                    updateDto.AutoContributeFrequency,
                    updateDto.Status);

                var savingsGoalDto = MapToSavingsGoalDto(savingsGoal);

                return Ok(savingsGoalDto);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating savings goal with ID {id}");
                return StatusCode(500, "An error occurred while updating the savings goal");
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteSavingsGoal(int id)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Check if user can modify the savings goal
                if (!await _savingsService.UserCanModifySavingsGoalAsync(id, userId))
                {
                    return Forbid();
                }

                await _savingsService.DeleteSavingsGoalAsync(id);

                return Ok();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting savings goal with ID {id}");
                return StatusCode(500, "An error occurred while deleting the savings goal");
            }
        }

        [HttpGet("{id}/forecast")]
        public async Task<ActionResult<SavingsGoalForecastDto>> GetSavingsGoalForecast(int id)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Check if user can access the savings goal
                if (!await _savingsService.UserCanAccessSavingsGoalAsync(id, userId))
                {
                    return Forbid();
                }

                var forecast = await _savingsService.GenerateSavingsGoalForecastAsync(id);

                var forecastDto = new SavingsGoalForecastDto
                {
                    GoalId = id,
                    ProjectedCompletionDate = forecast.projectedCompletionDate,
                    MonthlyContributionNeeded = forecast.monthlyContributionNeeded,
                    WeeklyContributionNeeded = forecast.weeklyContributionNeeded,
                    IsAchievable = forecast.isAchievable,
                    TimelineData = forecast.timelineData.Select(t => new TimelineDataPoint
                    {
                        Date = t.date,
                        Amount = t.amount
                    }).ToList()
                };

                return Ok(forecastDto);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error generating forecast for savings goal with ID {id}");
                return StatusCode(500, "An error occurred while generating the forecast");
            }
        }

        [HttpGet("{id}/contributions")]
        public async Task<ActionResult<IEnumerable<SavingsContributionDto>>> GetContributions(int id)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Check if user can access the savings goal
                if (!await _savingsService.UserCanAccessSavingsGoalAsync(id, userId))
                {
                    return Forbid();
                }

                var contributions = await _savingsService.GetContributionsBySavingsGoalIdAsync(id);

                var contributionDtos = contributions.Select(MapToContributionDto).ToList();

                return Ok(contributionDtos);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting contributions for savings goal with ID {id}");
                return StatusCode(500, "An error occurred while retrieving contributions");
            }
        }

        [HttpPost("/api/savings-contributions")]
        public async Task<ActionResult<SavingsContributionDto>> AddContribution(CreateContributionDto createDto)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Check if user can modify the savings goal
                if (!await _savingsService.UserCanModifySavingsGoalAsync(createDto.SavingsGoalId, userId))
                {
                    return Forbid();
                }

                var contribution = await _savingsService.AddContributionAsync(
                    createDto.SavingsGoalId,
                    createDto.Amount,
                    createDto.ContributionDate,
                    userId,
                    createDto.TransactionId,
                    createDto.Notes);

                var contributionDto = MapToContributionDto(contribution);

                return CreatedAtAction(nameof(GetContribution), new { id = contributionDto.Id }, contributionDto);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding contribution");
                return StatusCode(500, "An error occurred while adding the contribution");
            }
        }

        [HttpGet("/api/savings-contributions/{id}")]
        public async Task<ActionResult<SavingsContributionDto>> GetContribution(int id)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                var contribution = await _savingsService.GetContributionByIdAsync(id);

                // Check if user can access the savings goal
                if (!await _savingsService.UserCanAccessSavingsGoalAsync(contribution.SavingsGoalId, userId))
                {
                    return Forbid();
                }

                var contributionDto = MapToContributionDto(contribution);

                return Ok(contributionDto);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting contribution with ID {id}");
                return StatusCode(500, "An error occurred while retrieving the contribution");
            }
        }

        [HttpPut("/api/savings-contributions/{id}")]
        public async Task<ActionResult<SavingsContributionDto>> UpdateContribution(int id, UpdateContributionDto updateDto)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Get the contribution to check permissions
                var contribution = await _savingsService.GetContributionByIdAsync(id);

                // Check if user can modify the savings goal
                if (!await _savingsService.UserCanModifySavingsGoalAsync(contribution.SavingsGoalId, userId))
                {
                    return Forbid();
                }

                var updatedContribution = await _savingsService.UpdateContributionAsync(
                    id,
                    updateDto.Amount,
                    updateDto.ContributionDate,
                    updateDto.Notes);

                var contributionDto = MapToContributionDto(updatedContribution);

                return Ok(contributionDto);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating contribution with ID {id}");
                return StatusCode(500, "An error occurred while updating the contribution");
            }
        }

        [HttpDelete("/api/savings-contributions/{id}")]
        public async Task<ActionResult> DeleteContribution(int id)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Get the contribution to check permissions
                var contribution = await _savingsService.GetContributionByIdAsync(id);

                // Check if user can modify the savings goal
                if (!await _savingsService.UserCanModifySavingsGoalAsync(contribution.SavingsGoalId, userId))
                {
                    return Forbid();
                }

                await _savingsService.DeleteContributionAsync(id);

                return Ok();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting contribution with ID {id}");
                return StatusCode(500, "An error occurred while deleting the contribution");
            }
        }

        #region Helper Methods

        private SavingsGoalDto MapToSavingsGoalDto(SavingsGoal savingsGoal)
        {
            if (savingsGoal == null) return new SavingsGoalDto();

            // Calculate progress
            decimal progress = savingsGoal.TargetAmount > 0
                ? (savingsGoal.CurrentAmount / savingsGoal.TargetAmount) * 100
                : 0;

            // Calculate remaining amount
            decimal remainingAmount = Math.Max(0, savingsGoal.TargetAmount - savingsGoal.CurrentAmount);

            // Calculate days remaining
            var today = DateTime.UtcNow.Date;
            int daysRemaining = Math.Max(0, (savingsGoal.TargetDate.Date - today).Days);

            // Determine if on track
            var elapsedDays = Math.Max(0, (today - savingsGoal.StartDate.Date).Days);
            var totalDays = Math.Max(1, (savingsGoal.TargetDate.Date - savingsGoal.StartDate.Date).Days);
            var expectedProgress = (elapsedDays / (decimal)totalDays) * 100;
            bool isOnTrack = progress >= expectedProgress;

            return new SavingsGoalDto
            {
                Id = savingsGoal.Id,
                Title = savingsGoal.Title,
                Description = savingsGoal.Description,
                TargetAmount = savingsGoal.TargetAmount,
                CurrentAmount = savingsGoal.CurrentAmount,
                StartDate = savingsGoal.StartDate,
                TargetDate = savingsGoal.TargetDate,
                UserId = savingsGoal.UserId,
                UserName = savingsGoal.User?.Username,
                FamilyId = savingsGoal.FamilyId,
                FamilyName = savingsGoal.Family?.Name,
                IsShared = savingsGoal.IsShared,
                PriorityId = savingsGoal.PriorityId,
                Priority = savingsGoal.Priority != null ? new PriorityDto
                {
                    Id = savingsGoal.Priority.Id,
                    Name = savingsGoal.Priority.Name,
                    Color = savingsGoal.Priority.Color ?? string.Empty
                } : new PriorityDto(),
                AutoContribute = savingsGoal.AutoContribute,
                AutoContributeAmount = savingsGoal.AutoContributeAmount,
                AutoContributeFrequency = savingsGoal.AutoContributeFrequency?.ToString() ?? string.Empty,
                Status = savingsGoal.Status,
                Contributions = savingsGoal.Contributions?.Select(MapToContributionDto).ToList() ?? new List<SavingsContributionDto>(),
                LinkedWishlistItems = savingsGoal.LinkedWishlistItems?.Select(MapToWishlistItemDto).ToList() ?? new List<WishlistItemDto>(),
                CreatedAt = savingsGoal.CreatedAt,
                UpdatedAt = savingsGoal.UpdatedAt.GetValueOrDefault(),
                Progress = progress,
                RemainingAmount = remainingAmount,
                DaysRemaining = daysRemaining,
                IsOnTrack = isOnTrack
            };
        }

        private SavingsContributionDto MapToContributionDto(SavingsContribution contribution)
        {
            if (contribution == null) return new SavingsContributionDto();

            return new SavingsContributionDto
            {
                Id = contribution.Id,
                SavingsGoalId = contribution.SavingsGoalId,
                UserId = contribution.UserId,
                UserName = contribution.User?.Username,
                Amount = contribution.Amount,
                ContributionDate = contribution.ContributionDate,
                TransactionId = contribution.TransactionId,
                Notes = contribution.Notes,
                CreatedAt = contribution.CreatedAt,
                UpdatedAt = contribution.UpdatedAt.GetValueOrDefault()
            };
        }

        private WishlistItemDto MapToWishlistItemDto(WishlistItem wishlistItem)
        {
            if (wishlistItem == null) return new WishlistItemDto();

            return new WishlistItemDto
            {
                Id = wishlistItem.Id,
                Title = wishlistItem.Title,
                Description = wishlistItem.Description ?? string.Empty,
                EstimatedPrice = wishlistItem.EstimatedPrice,
                ProductUrl = wishlistItem.ProductUrl ?? string.Empty,
                ImageUrl = wishlistItem.ImageUrl ?? string.Empty,
                AddedDate = wishlistItem.AddedDate,
                TargetPurchaseDate = wishlistItem.TargetPurchaseDate,
                UserId = wishlistItem.UserId,
                UserName = wishlistItem.User?.Username,
                FamilyId = wishlistItem.FamilyId,
                FamilyName = wishlistItem.Family?.Name,
                IsShared = wishlistItem.IsShared,
                PriorityId = wishlistItem.PriorityId,
                Priority = wishlistItem.Priority != null ? new PriorityDto
                {
                    Id = wishlistItem.Priority.Id,
                    Name = wishlistItem.Priority.Name,
                    Color = wishlistItem.Priority.Color ?? string.Empty
                } : new PriorityDto(),
                LinkedSavingsGoalId = wishlistItem.LinkedSavingsGoalId,
                Status = wishlistItem.Status,
                CreatedAt = wishlistItem.CreatedAt,
                UpdatedAt = wishlistItem.UpdatedAt.GetValueOrDefault()
            };
        }

        #endregion
    }
}
