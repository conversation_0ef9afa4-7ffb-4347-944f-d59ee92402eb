using System.Security.Claims;
using Microsoft.AspNetCore.Mvc;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public abstract class BaseApiController : ControllerBase
    {
        /// <summary>
        /// Safely tries to get the current user's ID from claims
        /// </summary>
        /// <param name="userId">The parsed user ID if successful</param>
        /// <returns>True if the user ID was successfully parsed, false otherwise</returns>
        protected bool TryGetUserId(out int userId)
        {
            userId = 0;
            var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
            return !string.IsNullOrEmpty(userIdClaim) && int.TryParse(userIdClaim, out userId);
        }

        /// <summary>
        /// Gets the current user's ID from claims or returns an Unauthorized result
        /// </summary>
        /// <returns>The user ID if found, or null if not found (in which case the out parameter contains the ActionResult)</returns>
        protected int? GetUserIdOrUnauthorized(out IActionResult? actionResult)
        {
            actionResult = null;

            if (!TryGetUserId(out int userId))
            {
                actionResult = Unauthorized(new { message = "Invalid user identity" });
                return null;
            }

            return userId;
        }
    }
}
