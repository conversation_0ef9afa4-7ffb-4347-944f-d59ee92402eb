using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.API.DTOs;
using HisabKitab.API.Helpers;
using HisabKitab.API.Middleware;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/categories")]
    [Authorize]
    public class CategoryController : ControllerBase
    {
        private readonly ICategoryService _categoryService;
        private readonly IFamilyService _familyService;
        private readonly ILogger<CategoryController> _logger;

        public CategoryController(
            ICategoryService categoryService,
            IFamilyService familyService,
            ILogger<CategoryController> logger)
        {
            _categoryService = categoryService;
            _familyService = familyService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetUserCategories()
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }
                var categories = await _categoryService.GetCategoriesByUserIdAsync(userId.Value);

                var categoryDtos = categories.Select(MapCategoryToDto).ToList();
                return Ok(categoryDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user categories");
                return StatusCode(500, new { message = "An error occurred while retrieving categories" });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetCategory(int id)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }

                // Check if the user can access this category
                var canAccess = await _categoryService.UserCanAccessCategoryAsync(id, userId.Value);
                if (!canAccess)
                {
                    return Forbid();
                }

                var category = await _categoryService.GetCategoryByIdAsync(id);
                var categoryDto = MapCategoryToDto(category);

                return Ok(categoryDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Category not found");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting category");
                return StatusCode(500, new { message = "An error occurred while retrieving the category" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateCategory([FromBody] CreateCategoryDto createCategoryDto)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }

                // If familyId is provided, check if the user is a member of the family
                if (createCategoryDto.FamilyId.HasValue)
                {
                    var isFamilyAdmin = await _familyService.IsFamilyAdminAsync(createCategoryDto.FamilyId.Value, userId.Value);
                    if (!isFamilyAdmin)
                    {
                        return Forbid();
                    }
                }

                // If parentCategoryId is provided, check if the user can access it
                if (createCategoryDto.ParentCategoryId.HasValue)
                {
                    var canAccessParent = await _categoryService.UserCanAccessCategoryAsync(createCategoryDto.ParentCategoryId.Value, userId.Value);
                    if (!canAccessParent)
                    {
                        return BadRequest(new { message = "You do not have access to the specified parent category" });
                    }
                }

                var category = await _categoryService.CreateCategoryAsync(
                    createCategoryDto.Name,
                    createCategoryDto.Type,
                    createCategoryDto.Icon,
                    createCategoryDto.Color,
                    createCategoryDto.ParentCategoryId,
                    createCategoryDto.FamilyId.HasValue ? null : userId,
                    createCategoryDto.FamilyId,
                    false // Not a system category
                );

                var categoryDto = MapCategoryToDto(category);
                return CreatedAtAction(nameof(GetCategory), new { id = category.Id }, categoryDto);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid category data");
                return BadRequest(new { message = ex.Message });
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Entity not found");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating category");
                return StatusCode(500, new { message = "An error occurred while creating the category" });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateCategory(int id, [FromBody] UpdateCategoryDto updateCategoryDto)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }

                // Check if the user can modify this category
                var canModify = await _categoryService.UserCanModifyCategoryAsync(id, userId.Value);
                if (!canModify)
                {
                    return Forbid();
                }

                // If parentCategoryId is provided, check if the user can access it
                if (updateCategoryDto.ParentCategoryId.HasValue)
                {
                    var canAccessParent = await _categoryService.UserCanAccessCategoryAsync(updateCategoryDto.ParentCategoryId.Value, userId.Value);
                    if (!canAccessParent)
                    {
                        return BadRequest(new { message = "You do not have access to the specified parent category" });
                    }
                }

                var category = await _categoryService.UpdateCategoryAsync(
                    id,
                    updateCategoryDto.Name,
                    updateCategoryDto.Type,
                    updateCategoryDto.Icon,
                    updateCategoryDto.Color,
                    updateCategoryDto.ParentCategoryId
                );

                var categoryDto = MapCategoryToDto(category);
                return Ok(categoryDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Category not found");
                return NotFound(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid category data");
                return BadRequest(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Cannot modify system category");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating category");
                return StatusCode(500, new { message = "An error occurred while updating the category" });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteCategory(int id)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }

                // Check if the user can modify this category
                var canModify = await _categoryService.UserCanModifyCategoryAsync(id, userId.Value);
                if (!canModify)
                {
                    return Forbid();
                }

                await _categoryService.DeleteCategoryAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Category not found");
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Cannot delete category");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting category");
                return StatusCode(500, new { message = "An error occurred while deleting the category" });
            }
        }

        [HttpGet("system")]
        public async Task<IActionResult> GetSystemCategories()
        {
            try
            {
                var categories = await _categoryService.GetSystemCategoriesAsync();
                var categoryDtos = categories.Select(MapCategoryToDto).ToList();
                return Ok(categoryDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting system categories");
                return StatusCode(500, new { message = "An error occurred while retrieving system categories" });
            }
        }

        [HttpGet("type/{type}")]
        public async Task<IActionResult> GetCategoriesByType(CategoryType type)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }
                var allCategories = await _categoryService.GetCategoriesByTypeAsync(type);

                // Filter to only include categories the user can access
                var accessibleCategories = new List<Category>();
                foreach (var category in allCategories)
                {
                    var canAccess = await _categoryService.UserCanAccessCategoryAsync(category.Id, userId.Value);
                    if (canAccess)
                    {
                        accessibleCategories.Add(category);
                    }
                }

                var categoryDtos = accessibleCategories.Select(MapCategoryToDto).ToList();
                return Ok(categoryDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting categories by type");
                return StatusCode(500, new { message = "An error occurred while retrieving categories" });
            }
        }

        [HttpGet("family/{familyId}")]
        public async Task<IActionResult> GetFamilyCategories(int familyId)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }

                // Check if the user is a member of the family
                var isFamilyMember = await _familyService.IsFamilyMemberAsync(familyId, userId.Value);
                if (!isFamilyMember)
                {
                    return Forbid();
                }

                var categories = await _categoryService.GetCategoriesByFamilyIdAsync(familyId);
                var categoryDtos = categories.Select(MapCategoryToDto).ToList();
                return Ok(categoryDtos);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Family not found");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting family categories");
                return StatusCode(500, new { message = "An error occurred while retrieving family categories" });
            }
        }

        [HttpPost("budget")]
        public async Task<IActionResult> SetBudgetLimit([FromBody] CreateBudgetLimitDto createBudgetLimitDto)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }

                // Check if the user can access the category
                var canAccess = await _categoryService.UserCanAccessCategoryAsync(createBudgetLimitDto.CategoryId, userId.Value);
                if (!canAccess)
                {
                    return Forbid();
                }

                // If familyId is provided, check if the user is an admin of the family
                if (createBudgetLimitDto.FamilyId.HasValue)
                {
                    var isFamilyAdmin = await _familyService.IsFamilyAdminAsync(createBudgetLimitDto.FamilyId.Value, userId.Value);
                    if (!isFamilyAdmin)
                    {
                        return Forbid();
                    }
                }

                var budgetLimit = await _categoryService.SetBudgetLimitAsync(
                    createBudgetLimitDto.CategoryId,
                    createBudgetLimitDto.Amount,
                    createBudgetLimitDto.Period,
                    createBudgetLimitDto.FamilyId.HasValue ? null : userId,
                    createBudgetLimitDto.FamilyId,
                    createBudgetLimitDto.FamilyMemberUserId,
                    createBudgetLimitDto.NotificationThreshold,
                    createBudgetLimitDto.RolloverUnused
                );

                var budgetLimitDto = MapBudgetLimitToDto(budgetLimit);
                return Ok(budgetLimitDto);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid budget limit data");
                return BadRequest(new { message = ex.Message });
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Entity not found");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting budget limit");
                return StatusCode(500, new { message = "An error occurred while setting the budget limit" });
            }
        }

        [HttpGet("budget/{id}")]
        public async Task<IActionResult> GetBudgetLimit(int id)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }
                var budgetLimit = await _categoryService.GetBudgetLimitAsync(id);

                // Check if the user can access this budget limit
                var canAccessCategory = await _categoryService.UserCanAccessCategoryAsync(budgetLimit.CategoryId, userId.Value);
                if (!canAccessCategory)
                {
                    return Forbid();
                }

                var budgetLimitDto = MapBudgetLimitToDto(budgetLimit);
                return Ok(budgetLimitDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Budget limit not found");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting budget limit");
                return StatusCode(500, new { message = "An error occurred while retrieving the budget limit" });
            }
        }

        [HttpPut("budget/{id}")]
        public async Task<IActionResult> UpdateBudgetLimit(int id, [FromBody] UpdateBudgetLimitDto updateBudgetLimitDto)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }
                var budgetLimit = await _categoryService.GetBudgetLimitAsync(id);

                // Check if the user can modify this budget limit
                if (budgetLimit.UserId.HasValue && budgetLimit.UserId.Value != userId)
                {
                    return Forbid();
                }

                if (budgetLimit.FamilyId.HasValue)
                {
                    var isFamilyAdmin = await _familyService.IsFamilyAdminAsync(budgetLimit.FamilyId.Value, userId.Value);
                    if (!isFamilyAdmin)
                    {
                        return Forbid();
                    }
                }

                var updatedBudgetLimit = await _categoryService.UpdateBudgetLimitAsync(
                    id,
                    updateBudgetLimitDto.Amount,
                    updateBudgetLimitDto.Period,
                    updateBudgetLimitDto.NotificationThreshold,
                    updateBudgetLimitDto.RolloverUnused
                );

                var budgetLimitDto = MapBudgetLimitToDto(updatedBudgetLimit);
                return Ok(budgetLimitDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Budget limit not found");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating budget limit");
                return StatusCode(500, new { message = "An error occurred while updating the budget limit" });
            }
        }

        [HttpDelete("budget/{id}")]
        public async Task<IActionResult> DeleteBudgetLimit(int id)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }
                var budgetLimit = await _categoryService.GetBudgetLimitAsync(id);

                // Check if the user can modify this budget limit
                if (budgetLimit.UserId.HasValue && budgetLimit.UserId.Value != userId)
                {
                    return Forbid();
                }

                if (budgetLimit.FamilyId.HasValue)
                {
                    var isFamilyAdmin = await _familyService.IsFamilyAdminAsync(budgetLimit.FamilyId.Value, userId.Value);
                    if (!isFamilyAdmin)
                    {
                        return Forbid();
                    }
                }

                await _categoryService.DeleteBudgetLimitAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Budget limit not found");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting budget limit");
                return StatusCode(500, new { message = "An error occurred while deleting the budget limit" });
            }
        }

        [HttpGet("budget/category/{categoryId}")]
        public async Task<IActionResult> GetBudgetLimitsByCategory(int categoryId)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }

                // Check if the user can access the category
                var canAccess = await _categoryService.UserCanAccessCategoryAsync(categoryId, userId.Value);
                if (!canAccess)
                {
                    return Forbid();
                }

                var budgetLimits = await _categoryService.GetBudgetLimitsByCategoryAsync(categoryId);
                var budgetLimitDtos = budgetLimits.Select(MapBudgetLimitToDto).ToList();
                return Ok(budgetLimitDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting budget limits by category");
                return StatusCode(500, new { message = "An error occurred while retrieving budget limits" });
            }
        }

        // Helper methods
        private CategoryDto MapCategoryToDto(Category category)
        {
            return new CategoryDto
            {
                Id = category.Id,
                Name = category.Name,
                Type = category.Type,
                Icon = category.Icon,
                Color = category.Color,
                ParentCategoryId = category.ParentCategoryId,
                ParentCategoryName = category.ParentCategory?.Name ?? string.Empty,
                UserId = category.UserId,
                FamilyId = category.FamilyId,
                IsSystem = category.IsSystem,
                CreatedAt = category.CreatedAt,
                UpdatedAt = category.UpdatedAt,
                Subcategories = category.Subcategories?.Select(MapCategoryToDto).ToList() ?? new List<CategoryDto>(),
                BudgetLimits = category.BudgetLimits?.Select(MapBudgetLimitToDto).ToList() ?? new List<BudgetLimitDto>()
            };
        }

        private BudgetLimitDto MapBudgetLimitToDto(BudgetLimit budgetLimit)
        {
            return new BudgetLimitDto
            {
                Id = budgetLimit.Id,
                CategoryId = budgetLimit.CategoryId,
                CategoryName = budgetLimit.Category?.Name ?? string.Empty,
                Amount = budgetLimit.Amount,
                Period = budgetLimit.Period,
                StartDate = budgetLimit.StartDate,
                EndDate = budgetLimit.EndDate,
                UserId = budgetLimit.UserId,
                FamilyId = budgetLimit.FamilyId,
                FamilyMemberId = budgetLimit.FamilyMemberId,
                NotificationThreshold = budgetLimit.NotificationThreshold,
                RolloverUnused = budgetLimit.RolloverUnused,
                CreatedAt = budgetLimit.CreatedAt,
                UpdatedAt = budgetLimit.UpdatedAt
            };
        }
    }
}
