using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;
using HisabKitab.API.DTOs;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class NotificationController : ControllerBase
    {
        private readonly INotificationService _notificationService;
        private readonly ILogger<NotificationController> _logger;

        public NotificationController(
            INotificationService notificationService,
            ILogger<NotificationController> logger)
        {
            _notificationService = notificationService;
            _logger = logger;
        }

        private int GetUserIdFromClaims()
        {
            var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (userIdClaim == null)
            {
                throw new UnauthorizedAccessException("User ID not found in token");
            }
            return int.Parse(userIdClaim);
        }

        [HttpGet]
        public async Task<IActionResult> GetNotifications([FromQuery] bool includeRead = false)
        {
            try
            {
                int userId;
                try
                {
                    userId = GetUserIdFromClaims();
                }
                catch (UnauthorizedAccessException)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }

                var notifications = await _notificationService.GetNotificationsByUserIdAsync(userId, includeRead);

                // Map to DTOs
                var notificationDtos = notifications.Select(n => new NotificationDto
                {
                    Id = n.Id,
                    Title = n.Title,
                    Message = n.Message,
                    Type = n.Type.ToString(), // Convert enum to string
                    RelatedEntityType = n.RelatedEntityType,
                    RelatedEntityId = n.RelatedEntityId,
                    IsRead = n.IsRead,
                    ReadAt = n.ReadAt,
                    CreatedAt = n.CreatedAt,
                    UpdatedAt = n.UpdatedAt
                }).ToList();

                return Ok(notificationDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving notifications");
                return StatusCode(500, new { message = "An error occurred while retrieving notifications" });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetNotificationById(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userIdClaim == null)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                var notification = await _notificationService.GetNotificationByIdAsync(id);

                // Check if notification belongs to user
                if (notification.UserId != userId)
                {
                    return Forbid();
                }

                // Map to DTO
                var notificationDto = new NotificationDto
                {
                    Id = notification.Id,
                    Title = notification.Title,
                    Message = notification.Message,
                    Type = notification.Type.ToString(), // Convert enum to string
                    RelatedEntityType = notification.RelatedEntityType,
                    RelatedEntityId = notification.RelatedEntityId,
                    IsRead = notification.IsRead,
                    ReadAt = notification.ReadAt,
                    CreatedAt = notification.CreatedAt,
                    UpdatedAt = notification.UpdatedAt
                };

                return Ok(notificationDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Notification not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving notification ID {Id}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the notification" });
            }
        }

        [HttpPost("{id}/mark-read")]
        public async Task<IActionResult> MarkNotificationAsRead(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userIdClaim == null)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                var notification = await _notificationService.GetNotificationByIdAsync(id);

                // Check if notification belongs to user
                if (notification.UserId != userId)
                {
                    return Forbid();
                }

                await _notificationService.MarkNotificationAsReadAsync(id);

                return Ok(new { message = "Notification marked as read" });
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Notification not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking notification ID {Id} as read", id);
                return StatusCode(500, new { message = "An error occurred while marking the notification as read" });
            }
        }

        [HttpPost("mark-all-read")]
        public async Task<IActionResult> MarkAllNotificationsAsRead()
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userIdClaim == null)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                await _notificationService.MarkAllNotificationsAsReadAsync(userId);

                return Ok(new { message = "All notifications marked as read" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking all notifications as read");
                return StatusCode(500, new { message = "An error occurred while marking all notifications as read" });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteNotification(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userIdClaim == null)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                var notification = await _notificationService.GetNotificationByIdAsync(id);

                // Check if notification belongs to user
                if (notification.UserId != userId)
                {
                    return Forbid();
                }

                await _notificationService.DeleteNotificationAsync(id);

                return Ok(new { message = "Notification deleted" });
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Notification not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting notification ID {Id}", id);
                return StatusCode(500, new { message = "An error occurred while deleting the notification" });
            }
        }

        #region Scheduled Notifications

        [HttpPost("schedule")]
        public async Task<IActionResult> ScheduleNotification([FromBody] ScheduleNotificationDto scheduleDto)
        {
            try
            {
                int userId;
                try
                {
                    userId = GetUserIdFromClaims();
                }
                catch (UnauthorizedAccessException)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }

                // Parse notification type
                if (!Enum.TryParse<NotificationType>(scheduleDto.Type, true, out var notificationType))
                {
                    notificationType = NotificationType.Info; // Default to Info
                }

                var schedule = await _notificationService.ScheduleNotificationAsync(
                    userId,
                    scheduleDto.Title,
                    scheduleDto.Message,
                    scheduleDto.ScheduledFor,
                    notificationType,
                    scheduleDto.RelatedEntityType,
                    scheduleDto.RelatedEntityId,
                    scheduleDto.ActionUrl);

                // Map to DTO
                var responseDto = new ScheduledNotificationDto
                {
                    Id = schedule.Id,
                    Title = schedule.Title,
                    Message = schedule.Message,
                    Type = schedule.Type.ToString(),
                    ScheduledFor = schedule.ScheduledFor,
                    RelatedEntityType = schedule.RelatedEntityType,
                    RelatedEntityId = schedule.RelatedEntityId,
                    ActionUrl = schedule.ActionUrl,
                    IsActive = schedule.IsActive,
                    IsSent = schedule.IsSent,
                    SentAt = schedule.SentAt,
                    CreatedAt = schedule.CreatedAt,
                    UpdatedAt = schedule.UpdatedAt
                };

                return CreatedAtAction(nameof(GetScheduledNotification), new { id = schedule.Id }, responseDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("User not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error scheduling notification");
                return StatusCode(500, new { message = "An error occurred while scheduling the notification" });
            }
        }

        [HttpGet("scheduled")]
        public async Task<IActionResult> GetScheduledNotifications([FromQuery] bool includeSent = false)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userIdClaim == null)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                var schedules = await _notificationService.GetScheduledNotificationsByUserIdAsync(userId, includeSent);

                // Map to DTOs
                var scheduleDtos = schedules.Select(s => new ScheduledNotificationDto
                {
                    Id = s.Id,
                    Title = s.Title,
                    Message = s.Message,
                    Type = s.Type.ToString(),
                    ScheduledFor = s.ScheduledFor,
                    RelatedEntityType = s.RelatedEntityType,
                    RelatedEntityId = s.RelatedEntityId,
                    ActionUrl = s.ActionUrl,
                    IsActive = s.IsActive,
                    IsSent = s.IsSent,
                    SentAt = s.SentAt,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.UpdatedAt
                }).ToList();

                return Ok(scheduleDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving scheduled notifications");
                return StatusCode(500, new { message = "An error occurred while retrieving scheduled notifications" });
            }
        }

        [HttpGet("scheduled/{id}")]
        public async Task<IActionResult> GetScheduledNotification(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userIdClaim == null)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                var schedule = await _notificationService.GetScheduledNotificationByIdAsync(id);

                // Check if schedule belongs to user
                if (schedule.UserId != userId)
                {
                    return Forbid();
                }

                // Map to DTO
                var scheduleDto = new ScheduledNotificationDto
                {
                    Id = schedule.Id,
                    Title = schedule.Title,
                    Message = schedule.Message,
                    Type = schedule.Type.ToString(),
                    ScheduledFor = schedule.ScheduledFor,
                    RelatedEntityType = schedule.RelatedEntityType,
                    RelatedEntityId = schedule.RelatedEntityId,
                    ActionUrl = schedule.ActionUrl,
                    IsActive = schedule.IsActive,
                    IsSent = schedule.IsSent,
                    SentAt = schedule.SentAt,
                    CreatedAt = schedule.CreatedAt,
                    UpdatedAt = schedule.UpdatedAt
                };

                return Ok(scheduleDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Scheduled notification not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving scheduled notification");
                return StatusCode(500, new { message = "An error occurred while retrieving the scheduled notification" });
            }
        }

        [HttpPut("scheduled/{id}")]
        public async Task<IActionResult> UpdateScheduledNotification(int id, [FromBody] UpdateScheduledNotificationDto updateDto)
        {
            try
            {
                int userId;
                try
                {
                    userId = GetUserIdFromClaims();
                }
                catch (UnauthorizedAccessException)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }

                // Check if schedule belongs to user
                var existingSchedule = await _notificationService.GetScheduledNotificationByIdAsync(id);
                if (existingSchedule.UserId != userId)
                {
                    return Forbid();
                }

                var schedule = await _notificationService.UpdateScheduledNotificationAsync(
                    id,
                    updateDto.Title,
                    updateDto.Message,
                    updateDto.ScheduledFor,
                    updateDto.IsActive);

                // Map to DTO
                var scheduleDto = new ScheduledNotificationDto
                {
                    Id = schedule.Id,
                    Title = schedule.Title,
                    Message = schedule.Message,
                    Type = schedule.Type.ToString(),
                    ScheduledFor = schedule.ScheduledFor,
                    RelatedEntityType = schedule.RelatedEntityType,
                    RelatedEntityId = schedule.RelatedEntityId,
                    ActionUrl = schedule.ActionUrl,
                    IsActive = schedule.IsActive,
                    IsSent = schedule.IsSent,
                    SentAt = schedule.SentAt,
                    CreatedAt = schedule.CreatedAt,
                    UpdatedAt = schedule.UpdatedAt
                };

                return Ok(scheduleDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Scheduled notification not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating scheduled notification");
                return StatusCode(500, new { message = "An error occurred while updating the scheduled notification" });
            }
        }

        [HttpDelete("scheduled/{id}")]
        public async Task<IActionResult> DeleteScheduledNotification(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userIdClaim == null)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                // Check if schedule belongs to user
                var existingSchedule = await _notificationService.GetScheduledNotificationByIdAsync(id);
                if (existingSchedule.UserId != userId)
                {
                    return Forbid();
                }

                await _notificationService.DeleteScheduledNotificationAsync(id);

                return Ok(new { message = "Scheduled notification deleted" });
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Scheduled notification not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting scheduled notification");
                return StatusCode(500, new { message = "An error occurred while deleting the scheduled notification" });
            }
        }

        #endregion

        #region Recurring Notifications

        [HttpPost("recurring")]
        public async Task<IActionResult> CreateRecurringNotification([FromBody] CreateRecurringNotificationDto createDto)
        {
            try
            {
                int userId;
                try
                {
                    userId = GetUserIdFromClaims();
                }
                catch (UnauthorizedAccessException)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }

                // Parse notification type
                if (!Enum.TryParse<NotificationType>(createDto.Type, true, out var notificationType))
                {
                    notificationType = NotificationType.Info; // Default to Info
                }

                // Parse recurrence type
                if (!Enum.TryParse<RecurrenceType>(createDto.RecurrenceType, true, out var recurrenceType))
                {
                    return BadRequest(new { message = "Invalid recurrence type" });
                }

                var recurring = await _notificationService.CreateRecurringNotificationAsync(
                    userId,
                    createDto.Title,
                    createDto.Message,
                    recurrenceType,
                    createDto.RecurrenceInterval,
                    createDto.StartDate,
                    createDto.EndDate,
                    notificationType,
                    createDto.RelatedEntityType,
                    createDto.RelatedEntityId,
                    createDto.ActionUrl,
                    createDto.RecurrenceData);

                // Map to DTO
                var responseDto = new RecurringNotificationDto
                {
                    Id = recurring.Id,
                    Title = recurring.Title,
                    Message = recurring.Message,
                    Type = recurring.Type.ToString(),
                    RecurrenceType = recurring.RecurrenceType.ToString(),
                    RecurrenceInterval = recurring.RecurrenceInterval,
                    StartDate = recurring.StartDate,
                    EndDate = recurring.EndDate,
                    RecurrenceData = recurring.RecurrenceData,
                    RelatedEntityType = recurring.RelatedEntityType,
                    RelatedEntityId = recurring.RelatedEntityId,
                    ActionUrl = recurring.ActionUrl,
                    IsActive = recurring.IsActive,
                    LastRunAt = recurring.LastRunAt,
                    NextRunAt = recurring.NextRunAt,
                    CreatedAt = recurring.CreatedAt,
                    UpdatedAt = recurring.UpdatedAt
                };

                return CreatedAtAction(nameof(GetRecurringNotification), new { id = recurring.Id }, responseDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("User not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating recurring notification");
                return StatusCode(500, new { message = "An error occurred while creating the recurring notification" });
            }
        }

        [HttpGet("recurring")]
        public async Task<IActionResult> GetRecurringNotifications([FromQuery] bool includeInactive = false)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userIdClaim == null)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                var recurrings = await _notificationService.GetRecurringNotificationsByUserIdAsync(userId, includeInactive);

                // Map to DTOs
                var recurringDtos = recurrings.Select(r => new RecurringNotificationDto
                {
                    Id = r.Id,
                    Title = r.Title,
                    Message = r.Message,
                    Type = r.Type.ToString(),
                    RecurrenceType = r.RecurrenceType.ToString(),
                    RecurrenceInterval = r.RecurrenceInterval,
                    StartDate = r.StartDate,
                    EndDate = r.EndDate,
                    RecurrenceData = r.RecurrenceData,
                    RelatedEntityType = r.RelatedEntityType,
                    RelatedEntityId = r.RelatedEntityId,
                    ActionUrl = r.ActionUrl,
                    IsActive = r.IsActive,
                    LastRunAt = r.LastRunAt,
                    NextRunAt = r.NextRunAt,
                    CreatedAt = r.CreatedAt,
                    UpdatedAt = r.UpdatedAt
                }).ToList();

                return Ok(recurringDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving recurring notifications");
                return StatusCode(500, new { message = "An error occurred while retrieving recurring notifications" });
            }
        }

        [HttpGet("recurring/{id}")]
        public async Task<IActionResult> GetRecurringNotification(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userIdClaim == null)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                var recurring = await _notificationService.GetRecurringNotificationByIdAsync(id);

                // Check if recurring notification belongs to user
                if (recurring.UserId != userId)
                {
                    return Forbid();
                }

                // Map to DTO
                var recurringDto = new RecurringNotificationDto
                {
                    Id = recurring.Id,
                    Title = recurring.Title,
                    Message = recurring.Message,
                    Type = recurring.Type.ToString(),
                    RecurrenceType = recurring.RecurrenceType.ToString(),
                    RecurrenceInterval = recurring.RecurrenceInterval,
                    StartDate = recurring.StartDate,
                    EndDate = recurring.EndDate,
                    RecurrenceData = recurring.RecurrenceData,
                    RelatedEntityType = recurring.RelatedEntityType,
                    RelatedEntityId = recurring.RelatedEntityId,
                    ActionUrl = recurring.ActionUrl,
                    IsActive = recurring.IsActive,
                    LastRunAt = recurring.LastRunAt,
                    NextRunAt = recurring.NextRunAt,
                    CreatedAt = recurring.CreatedAt,
                    UpdatedAt = recurring.UpdatedAt
                };

                return Ok(recurringDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Recurring notification not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving recurring notification");
                return StatusCode(500, new { message = "An error occurred while retrieving the recurring notification" });
            }
        }

        [HttpPut("recurring/{id}")]
        public async Task<IActionResult> UpdateRecurringNotification(int id, [FromBody] UpdateRecurringNotificationDto updateDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userIdClaim == null)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                // Check if recurring notification belongs to user
                var existingRecurring = await _notificationService.GetRecurringNotificationByIdAsync(id);
                if (existingRecurring.UserId != userId)
                {
                    return Forbid();
                }

                // Parse recurrence type if provided
                RecurrenceType? recurrenceType = null;
                if (!string.IsNullOrEmpty(updateDto.RecurrenceType))
                {
                    if (!Enum.TryParse<RecurrenceType>(updateDto.RecurrenceType, true, out var parsedType))
                    {
                        return BadRequest(new { message = "Invalid recurrence type" });
                    }
                    recurrenceType = parsedType;
                }

                var recurring = await _notificationService.UpdateRecurringNotificationAsync(
                    id,
                    updateDto.Title,
                    updateDto.Message,
                    recurrenceType,
                    updateDto.RecurrenceInterval,
                    updateDto.StartDate,
                    updateDto.EndDate,
                    updateDto.IsActive,
                    updateDto.RecurrenceData);

                // Map to DTO
                var recurringDto = new RecurringNotificationDto
                {
                    Id = recurring.Id,
                    Title = recurring.Title,
                    Message = recurring.Message,
                    Type = recurring.Type.ToString(),
                    RecurrenceType = recurring.RecurrenceType.ToString(),
                    RecurrenceInterval = recurring.RecurrenceInterval,
                    StartDate = recurring.StartDate,
                    EndDate = recurring.EndDate,
                    RecurrenceData = recurring.RecurrenceData,
                    RelatedEntityType = recurring.RelatedEntityType,
                    RelatedEntityId = recurring.RelatedEntityId,
                    ActionUrl = recurring.ActionUrl,
                    IsActive = recurring.IsActive,
                    LastRunAt = recurring.LastRunAt,
                    NextRunAt = recurring.NextRunAt,
                    CreatedAt = recurring.CreatedAt,
                    UpdatedAt = recurring.UpdatedAt
                };

                return Ok(recurringDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Recurring notification not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating recurring notification");
                return StatusCode(500, new { message = "An error occurred while updating the recurring notification" });
            }
        }

        [HttpDelete("recurring/{id}")]
        public async Task<IActionResult> DeleteRecurringNotification(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userIdClaim == null)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                // Check if recurring notification belongs to user
                var existingRecurring = await _notificationService.GetRecurringNotificationByIdAsync(id);
                if (existingRecurring.UserId != userId)
                {
                    return Forbid();
                }

                await _notificationService.DeleteRecurringNotificationAsync(id);

                return Ok(new { message = "Recurring notification deleted" });
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Recurring notification not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting recurring notification");
                return StatusCode(500, new { message = "An error occurred while deleting the recurring notification" });
            }
        }

        #endregion

        #region Device Management

        [HttpPost("devices")]
        public async Task<IActionResult> RegisterDevice([FromBody] RegisterDeviceDto registerDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userIdClaim == null)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                var device = await _notificationService.RegisterDeviceAsync(
                    userId,
                    registerDto.DeviceId,
                    registerDto.PushToken,
                    registerDto.DeviceName,
                    registerDto.DeviceType);

                // Map to DTO
                var deviceDto = new UserDeviceDto
                {
                    Id = device.Id,
                    DeviceId = device.DeviceId,
                    DeviceName = device.DeviceName,
                    DeviceType = device.DeviceType,
                    PushToken = device.PushToken,
                    IsActive = device.IsActive,
                    LastActiveAt = device.LastActiveAt,
                    CreatedAt = device.CreatedAt,
                    UpdatedAt = device.UpdatedAt
                };

                return Ok(deviceDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering device");
                return StatusCode(500, new { message = "An error occurred while registering the device" });
            }
        }

        [HttpPut("devices/{deviceId}")]
        public async Task<IActionResult> UpdateDeviceToken(string deviceId, [FromBody] UpdateDeviceTokenDto updateDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userIdClaim == null)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                var device = await _notificationService.UpdateDeviceTokenAsync(
                    userId,
                    deviceId,
                    updateDto.PushToken);

                // Map to DTO
                var deviceDto = new UserDeviceDto
                {
                    Id = device.Id,
                    DeviceId = device.DeviceId,
                    DeviceName = device.DeviceName,
                    DeviceType = device.DeviceType,
                    PushToken = device.PushToken,
                    IsActive = device.IsActive,
                    LastActiveAt = device.LastActiveAt,
                    CreatedAt = device.CreatedAt,
                    UpdatedAt = device.UpdatedAt
                };

                return Ok(deviceDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Device not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating device token");
                return StatusCode(500, new { message = "An error occurred while updating the device token" });
            }
        }

        [HttpDelete("devices/{deviceId}")]
        public async Task<IActionResult> UnregisterDevice(string deviceId)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userIdClaim == null)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                await _notificationService.UnregisterDeviceAsync(userId, deviceId);

                return Ok(new { message = "Device unregistered" });
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Device not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unregistering device");
                return StatusCode(500, new { message = "An error occurred while unregistering the device" });
            }
        }

        #endregion

        #region Monthly Summary

        [HttpGet("monthly-summary/{month}/{year}")]
        public async Task<IActionResult> GetMonthlySummary(int month, int year)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userIdClaim == null)
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                // Check if summary exists
                var dbContext = HttpContext.RequestServices.GetRequiredService<HisabKitab.Infrastructure.Data.ApplicationDbContext>();
                var summary = await dbContext.MonthlySummaries
                    .FirstOrDefaultAsync(s => s.UserId == userId && s.Month == month && s.Year == year);

                if (summary == null)
                {
                    // Generate summary on-demand if it doesn't exist
                    var success = await _notificationService.GenerateMonthlyUserSummaryAsync(userId, month, year);
                    if (!success)
                    {
                        return StatusCode(500, new { message = "Failed to generate monthly summary" });
                    }

                    // Retrieve the newly generated summary
                    summary = await dbContext.MonthlySummaries
                        .FirstOrDefaultAsync(s => s.UserId == userId && s.Month == month && s.Year == year);

                    if (summary == null)
                    {
                        return NotFound(new { message = "Monthly summary not found" });
                    }
                }

                // Parse JSON data
                var expensesByCategory = !string.IsNullOrEmpty(summary.ExpensesByCategory)
                    ? JsonSerializer.Deserialize<List<CategoryAmount>>(summary.ExpensesByCategory)
                    : new List<CategoryAmount>();
                var dailyTrend = !string.IsNullOrEmpty(summary.DailyTrend)
                    ? JsonSerializer.Deserialize<List<DailyTrend>>(summary.DailyTrend)
                    : new List<DailyTrend>();

                // Map to DTO
                var summaryDto = new MonthlySummaryDto
                {
                    Id = summary.Id,
                    Month = summary.Month,
                    Year = summary.Year,
                    TotalIncome = summary.TotalIncome,
                    TotalExpense = summary.TotalExpense,
                    NetSavings = summary.NetSavings,
                    SavingsRate = summary.SavingsRate,
                    TransactionCount = summary.TransactionCount,
                    AverageExpense = summary.AverageExpense,
                    LargestExpense = summary.LargestExpense,
                    TopExpenseCategory = summary.TopExpenseCategory ?? string.Empty,
                    TopIncomeSource = summary.TopIncomeSource ?? string.Empty,
                    ExpensesByCategory = expensesByCategory ?? new List<CategoryAmount>(),
                    DailyTrend = dailyTrend ?? new List<DailyTrend>(),
                    CreatedAt = summary.CreatedAt,
                    UpdatedAt = summary.UpdatedAt
                };

                return Ok(summaryDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving monthly summary");
                return StatusCode(500, new { message = "An error occurred while retrieving the monthly summary" });
            }
        }

        #endregion
    }
}
