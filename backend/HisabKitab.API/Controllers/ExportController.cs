using System;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.API.Helpers;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ExportController : ControllerBase
    {
        private readonly IExportService _exportService;
        private readonly IReportService _reportService;
        private readonly IFamilyService _familyService;
        private readonly ILogger<ExportController> _logger;

        public ExportController(
            IExportService exportService,
            IReportService reportService,
            IFamilyService familyService,
            ILogger<ExportController> logger)
        {
            _exportService = exportService;
            _reportService = reportService;
            _familyService = familyService;
            _logger = logger;
        }

        [HttpGet("history")]
        public async Task<IActionResult> GetExportHistory(
            [FromQuery] int limit = 10,
            [FromQuery] int offset = 0)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }
                var exports = await _exportService.GetExportHistoryByUserIdAsync(userId.Value, limit, offset);

                var exportDtos = exports.Select(e => new
                {
                    e.Id,
                    e.ExportType,
                    e.FileName,
                    e.ContentType,
                    e.ReportId,
                    ReportName = e.Report?.Name,
                    e.Parameters,
                    e.ExportedAt,
                    Status = e.Status.ToString(),
                    e.CreatedAt,
                    e.UpdatedAt
                }).ToList();

                return Ok(exportDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting export history");
                return StatusCode(500, new { message = "An error occurred while retrieving export history" });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetExport(int id)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }
                var export = await _exportService.GetExportByIdAsync(id);

                // Check if user owns the export
                if (export.UserId != userId)
                {
                    return Forbid();
                }

                var exportDto = new
                {
                    export.Id,
                    export.ExportType,
                    export.FileName,
                    export.ContentType,
                    export.ReportId,
                    ReportName = export.Report?.Name,
                    export.Parameters,
                    export.ExportedAt,
                    Status = export.Status.ToString(),
                    export.FilePath,
                    export.CreatedAt,
                    export.UpdatedAt
                };

                return Ok(exportDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting export");
                return StatusCode(500, new { message = "An error occurred while retrieving the export" });
            }
        }

        [HttpPost("csv")]
        public async Task<IActionResult> ExportToCsv([FromBody] ExportRequestDto exportRequestDto)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }

                // If exporting a report, check if user can access it
                if (exportRequestDto.ReportId.HasValue)
                {
                    var report = await _reportService.GetReportByIdAsync(exportRequestDto.ReportId.Value);

                    if (report.UserId != userId)
                    {
                        // If report is shared with a family, check if user is a member
                        if (report.FamilyId.HasValue && report.IsShared)
                        {
                            var isFamilyMember = await _familyService.IsFamilyMemberAsync(report.FamilyId.Value, userId.Value);
                            if (!isFamilyMember)
                            {
                                return Forbid();
                            }
                        }
                        else
                        {
                            return Forbid();
                        }
                    }
                }

                var export = await _exportService.ExportToCsvAsync(
                    exportRequestDto.ContentType,
                    exportRequestDto.Parameters,
                    userId.Value,
                    exportRequestDto.ReportId);

                var exportDto = new
                {
                    export.Id,
                    export.ExportType,
                    export.FileName,
                    export.ContentType,
                    export.ReportId,
                    export.Parameters,
                    export.ExportedAt,
                    Status = export.Status.ToString(),
                    export.CreatedAt,
                    export.UpdatedAt
                };

                return Ok(exportDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting to CSV");
                return StatusCode(500, new { message = "An error occurred while exporting to CSV" });
            }
        }

        [HttpPost("pdf")]
        public async Task<IActionResult> ExportToPdf([FromBody] ExportRequestDto exportRequestDto)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }

                // If exporting a report, check if user can access it
                if (exportRequestDto.ReportId.HasValue)
                {
                    var report = await _reportService.GetReportByIdAsync(exportRequestDto.ReportId.Value);

                    if (report.UserId != userId)
                    {
                        // If report is shared with a family, check if user is a member
                        if (report.FamilyId.HasValue && report.IsShared)
                        {
                            var isFamilyMember = await _familyService.IsFamilyMemberAsync(report.FamilyId.Value, userId.Value);
                            if (!isFamilyMember)
                            {
                                return Forbid();
                            }
                        }
                        else
                        {
                            return Forbid();
                        }
                    }
                }

                var export = await _exportService.ExportToPdfAsync(
                    exportRequestDto.ContentType,
                    exportRequestDto.Parameters,
                    userId.Value,
                    exportRequestDto.ReportId);

                var exportDto = new
                {
                    export.Id,
                    export.ExportType,
                    export.FileName,
                    export.ContentType,
                    export.ReportId,
                    export.Parameters,
                    export.ExportedAt,
                    Status = export.Status.ToString(),
                    export.CreatedAt,
                    export.UpdatedAt
                };

                return Ok(exportDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting to PDF");
                return StatusCode(500, new { message = "An error occurred while exporting to PDF" });
            }
        }

        [HttpGet("{id}/download")]
        public async Task<IActionResult> DownloadExport(int id)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }
                var export = await _exportService.GetExportByIdAsync(id);

                // Check if user owns the export
                if (export.UserId != userId)
                {
                    return Forbid();
                }

                // Get file path
                var filePath = await _exportService.GetExportFilePathAsync(id);

                // Check if file exists
                if (!System.IO.File.Exists(filePath))
                {
                    return NotFound(new { message = "Export file not found" });
                }

                // Determine content type
                string contentType;
                switch (export.ExportType.ToLower())
                {
                    case "csv":
                        contentType = "text/csv";
                        break;
                    case "pdf":
                        contentType = "application/pdf";
                        break;
                    default:
                        contentType = "application/octet-stream";
                        break;
                }

                // Return file
                var fileBytes = await System.IO.File.ReadAllBytesAsync(filePath);
                return File(fileBytes, contentType, export.FileName);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (FileNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = "Export file not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading export");
                return StatusCode(500, new { message = "An error occurred while downloading the export" });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteExport(int id)
        {
            try
            {
                var userId = ControllerHelpers.GetUserIdFromClaims(this);
                if (!userId.HasValue)
                {
                    return BadRequest(new { message = "User ID not found in token" });
                }
                var export = await _exportService.GetExportByIdAsync(id);

                // Check if user owns the export
                if (export.UserId != userId)
                {
                    return Forbid();
                }

                await _exportService.DeleteExportAsync(id);

                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting export");
                return StatusCode(500, new { message = "An error occurred while deleting the export" });
            }
        }
    }

    public class ExportRequestDto
    {
        [Required]
        [StringLength(50)]
        public string ContentType { get; set; } = string.Empty;

        public string Parameters { get; set; } = string.Empty;

        public int? ReportId { get; set; }
    }
}
