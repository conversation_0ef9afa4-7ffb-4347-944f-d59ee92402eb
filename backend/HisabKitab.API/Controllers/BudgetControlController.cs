using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.API.Middleware;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class BudgetControlController : ControllerBase
    {
        private readonly IBudgetControlService _budgetControlService;
        private readonly IFamilyService _familyService;
        private readonly ILogger<BudgetControlController> _logger;

        public BudgetControlController(
            IBudgetControlService budgetControlService,
            IFamilyService familyService,
            ILogger<BudgetControlController> logger)
        {
            _budgetControlService = budgetControlService;
            _familyService = familyService;
            _logger = logger;
        }

        private int GetUserIdFromClaims()
        {
            var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userIdClaim))
            {
                throw new UnauthorizedAccessException("User ID not found in token");
            }
            return int.Parse(userIdClaim);
        }

        [HttpGet("budget-limits")]
        public async Task<IActionResult> GetBudgetLimits()
        {
            try
            {
                var userId = GetUserIdFromClaims();
                var budgetLimits = await _budgetControlService.GetBudgetLimitsByUserIdAsync(userId);

                var budgetLimitDtos = budgetLimits.Select(bl => new
                {
                    bl.Id,
                    bl.CategoryId,
                    CategoryName = bl.Category?.Name,
                    bl.Amount,
                    Period = bl.Period.ToString(),
                    bl.StartDate,
                    bl.EndDate,
                    bl.UserId,
                    bl.FamilyId,
                    bl.FamilyMemberId,
                    bl.NotificationThreshold,
                    bl.RolloverUnused,
                    bl.CreatedAt,
                    bl.UpdatedAt
                }).ToList();

                return Ok(budgetLimitDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting budget limits");
                return StatusCode(500, new { message = "An error occurred while retrieving budget limits" });
            }
        }

        [HttpGet("budget-limits/{id}")]
        public async Task<IActionResult> GetBudgetLimit(int id)
        {
            try
            {
                var userId = GetUserIdFromClaims();
                var budgetLimit = await _budgetControlService.GetBudgetLimitByIdAsync(id);

                // Check if user can access the budget limit
                if (budgetLimit.UserId != userId)
                {
                    // If budget limit is for a family, check if user is a member
                    if (budgetLimit.FamilyId.HasValue)
                    {
                        var isFamilyMember = await _familyService.IsFamilyMemberAsync(budgetLimit.FamilyId.Value, userId);
                        if (!isFamilyMember)
                        {
                            return Forbid();
                        }
                    }
                    else
                    {
                        return Forbid();
                    }
                }

                var budgetLimitDto = new
                {
                    budgetLimit.Id,
                    budgetLimit.CategoryId,
                    CategoryName = budgetLimit.Category?.Name,
                    budgetLimit.Amount,
                    Period = budgetLimit.Period.ToString(),
                    budgetLimit.StartDate,
                    budgetLimit.EndDate,
                    budgetLimit.UserId,
                    budgetLimit.FamilyId,
                    budgetLimit.FamilyMemberId,
                    budgetLimit.NotificationThreshold,
                    budgetLimit.RolloverUnused,
                    budgetLimit.CreatedAt,
                    budgetLimit.UpdatedAt
                };

                return Ok(budgetLimitDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Resource not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting budget limit");
                return StatusCode(500, new { message = "An error occurred while retrieving the budget limit" });
            }
        }

        [HttpPost("budget-limits")]
        public async Task<IActionResult> CreateBudgetLimit([FromBody] CreateBudgetLimitDto createBudgetLimitDto)
        {
            try
            {
                var userId = GetUserIdFromClaims();

                // If family ID is provided, check if user is a family admin
                if (createBudgetLimitDto.FamilyId.HasValue)
                {
                    var isFamilyAdmin = await _familyService.IsFamilyAdminAsync(createBudgetLimitDto.FamilyId.Value, userId);
                    if (!isFamilyAdmin)
                    {
                        return Forbid();
                    }
                }

                // If family member ID is provided, check if user is a family admin
                if (createBudgetLimitDto.FamilyMemberUserId.HasValue)
                {
                    if (!createBudgetLimitDto.FamilyId.HasValue)
                    {
                        return BadRequest(new { message = "Family ID is required when specifying a family member" });
                    }

                    var isFamilyAdmin = await _familyService.IsFamilyAdminAsync(createBudgetLimitDto.FamilyId.Value, userId);
                    if (!isFamilyAdmin)
                    {
                        return Forbid();
                    }
                }

                var budgetLimit = await _budgetControlService.CreateBudgetLimitAsync(
                    createBudgetLimitDto.CategoryId,
                    createBudgetLimitDto.Amount,
                    createBudgetLimitDto.Period,
                    createBudgetLimitDto.StartDate,
                    createBudgetLimitDto.EndDate,
                    createBudgetLimitDto.FamilyId.HasValue ? null : userId, // If family ID is provided, user ID is null
                    createBudgetLimitDto.FamilyId,
                    createBudgetLimitDto.FamilyMemberUserId,
                    createBudgetLimitDto.NotificationThreshold,
                    createBudgetLimitDto.RolloverUnused);

                var budgetLimitDto = new
                {
                    budgetLimit.Id,
                    budgetLimit.CategoryId,
                    budgetLimit.Amount,
                    Period = budgetLimit.Period.ToString(),
                    budgetLimit.StartDate,
                    budgetLimit.EndDate,
                    budgetLimit.UserId,
                    budgetLimit.FamilyId,
                    budgetLimit.FamilyMemberId,
                    budgetLimit.NotificationThreshold,
                    budgetLimit.RolloverUnused,
                    budgetLimit.CreatedAt,
                    budgetLimit.UpdatedAt
                };

                return CreatedAtAction(nameof(GetBudgetLimit), new { id = budgetLimit.Id }, budgetLimitDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Resource not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating budget limit");
                return StatusCode(500, new { message = "An error occurred while creating the budget limit" });
            }
        }

        [HttpPut("budget-limits/{id}")]
        public async Task<IActionResult> UpdateBudgetLimit(int id, [FromBody] UpdateBudgetLimitDto updateBudgetLimitDto)
        {
            try
            {
                var userId = GetUserIdFromClaims();
                var budgetLimit = await _budgetControlService.GetBudgetLimitByIdAsync(id);

                // Check if user can update the budget limit
                if (budgetLimit.UserId != userId)
                {
                    // If budget limit is for a family, check if user is a family admin
                    if (budgetLimit.FamilyId.HasValue)
                    {
                        var isFamilyAdmin = await _familyService.IsFamilyAdminAsync(budgetLimit.FamilyId.Value, userId);
                        if (!isFamilyAdmin)
                        {
                            return Forbid();
                        }
                    }
                    else
                    {
                        return Forbid();
                    }
                }

                var updatedBudgetLimit = await _budgetControlService.UpdateBudgetLimitAsync(
                    id,
                    updateBudgetLimitDto.Amount,
                    updateBudgetLimitDto.Period,
                    updateBudgetLimitDto.StartDate,
                    updateBudgetLimitDto.EndDate,
                    updateBudgetLimitDto.NotificationThreshold,
                    updateBudgetLimitDto.RolloverUnused);

                var budgetLimitDto = new
                {
                    updatedBudgetLimit.Id,
                    updatedBudgetLimit.CategoryId,
                    updatedBudgetLimit.Amount,
                    Period = updatedBudgetLimit.Period.ToString(),
                    updatedBudgetLimit.StartDate,
                    updatedBudgetLimit.EndDate,
                    updatedBudgetLimit.UserId,
                    updatedBudgetLimit.FamilyId,
                    updatedBudgetLimit.FamilyMemberId,
                    updatedBudgetLimit.NotificationThreshold,
                    updatedBudgetLimit.RolloverUnused,
                    updatedBudgetLimit.CreatedAt,
                    updatedBudgetLimit.UpdatedAt
                };

                return Ok(budgetLimitDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Resource not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating budget limit");
                return StatusCode(500, new { message = "An error occurred while updating the budget limit" });
            }
        }

        [HttpDelete("budget-limits/{id}")]
        public async Task<IActionResult> DeleteBudgetLimit(int id)
        {
            try
            {
                var userId = GetUserIdFromClaims();
                var budgetLimit = await _budgetControlService.GetBudgetLimitByIdAsync(id);

                // Check if user can delete the budget limit
                if (budgetLimit.UserId != userId)
                {
                    // If budget limit is for a family, check if user is a family admin
                    if (budgetLimit.FamilyId.HasValue)
                    {
                        var isFamilyAdmin = await _familyService.IsFamilyAdminAsync(budgetLimit.FamilyId.Value, userId);
                        if (!isFamilyAdmin)
                        {
                            return Forbid();
                        }
                    }
                    else
                    {
                        return Forbid();
                    }
                }

                await _budgetControlService.DeleteBudgetLimitAsync(id);

                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Resource not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting budget limit");
                return StatusCode(500, new { message = "An error occurred while deleting the budget limit" });
            }
        }

        [HttpGet("budget-limits/{id}/remaining")]
        public async Task<IActionResult> GetBudgetRemaining(int id)
        {
            try
            {
                var userId = GetUserIdFromClaims();
                var budgetLimit = await _budgetControlService.GetBudgetLimitByIdAsync(id);

                // Check if user can access the budget limit
                if (budgetLimit.UserId != userId)
                {
                    // If budget limit is for a family, check if user is a member
                    if (budgetLimit.FamilyId.HasValue)
                    {
                        var isFamilyMember = await _familyService.IsFamilyMemberAsync(budgetLimit.FamilyId.Value, userId);
                        if (!isFamilyMember)
                        {
                            return Forbid();
                        }
                    }
                    else
                    {
                        return Forbid();
                    }
                }

                var remaining = await _budgetControlService.GetBudgetRemainingAsync(id);

                return Ok(new { BudgetLimitId = id, Remaining = remaining });
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Resource not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting budget remaining");
                return StatusCode(500, new { message = "An error occurred while retrieving the budget remaining" });
            }
        }

        [HttpGet("family/{familyId}/budget-limits")]
        [RequireFamilyRole("familyId", "Admin", "Member")]
        public async Task<IActionResult> GetFamilyBudgetLimits(int familyId)
        {
            try
            {
                var budgetLimits = await _budgetControlService.GetBudgetLimitsByFamilyIdAsync(familyId);

                var budgetLimitDtos = budgetLimits.Select(bl => new
                {
                    bl.Id,
                    bl.CategoryId,
                    CategoryName = bl.Category?.Name,
                    bl.Amount,
                    Period = bl.Period.ToString(),
                    bl.StartDate,
                    bl.EndDate,
                    bl.UserId,
                    bl.FamilyId,
                    bl.FamilyMemberId,
                    bl.NotificationThreshold,
                    bl.RolloverUnused,
                    bl.CreatedAt,
                    bl.UpdatedAt
                }).ToList();

                return Ok(budgetLimitDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting family budget limits");
                return StatusCode(500, new { message = "An error occurred while retrieving family budget limits" });
            }
        }

        [HttpGet("family/{familyId}/spending-limits")]
        [RequireFamilyRole("familyId", "Admin")]
        public async Task<IActionResult> GetFamilySpendingLimits(int familyId)
        {
            try
            {
                var spendingLimits = await _budgetControlService.GetSpendingLimitsByFamilyIdAsync(familyId);

                var spendingLimitDtos = spendingLimits.Select(sl => new
                {
                    sl.Id,
                    sl.FamilyId,
                    sl.FamilyMemberUserId,
                    MemberUsername = sl.FamilyMemberUser?.Username,
                    MemberFullName = $"{sl.FamilyMemberUser?.FirstName} {sl.FamilyMemberUser?.LastName}",
                    sl.Amount,
                    Period = sl.Period.ToString(),
                    sl.StartDate,
                    sl.EndDate,
                    sl.CategoryId,
                    CategoryName = sl.Category?.Name,
                    sl.NotificationThreshold,
                    sl.RequireApprovalOverLimit,
                    sl.IsActive,
                    sl.CreatedAt,
                    sl.UpdatedAt
                }).ToList();

                return Ok(spendingLimitDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting family spending limits");
                return StatusCode(500, new { message = "An error occurred while retrieving family spending limits" });
            }
        }

        [HttpPost("family/{familyId}/spending-limits")]
        [RequireFamilyRole("familyId", "Admin")]
        public async Task<IActionResult> CreateSpendingLimit(int familyId, [FromBody] CreateSpendingLimitDto createSpendingLimitDto)
        {
            try
            {
                var spendingLimit = await _budgetControlService.CreateSpendingLimitAsync(
                    familyId,
                    createSpendingLimitDto.FamilyMemberUserId,
                    createSpendingLimitDto.Amount,
                    createSpendingLimitDto.Period,
                    createSpendingLimitDto.StartDate,
                    createSpendingLimitDto.EndDate,
                    createSpendingLimitDto.CategoryId,
                    createSpendingLimitDto.NotificationThreshold,
                    createSpendingLimitDto.RequireApprovalOverLimit);

                var spendingLimitDto = new
                {
                    spendingLimit.Id,
                    spendingLimit.FamilyId,
                    spendingLimit.FamilyMemberUserId,
                    spendingLimit.Amount,
                    Period = spendingLimit.Period.ToString(),
                    spendingLimit.StartDate,
                    spendingLimit.EndDate,
                    spendingLimit.CategoryId,
                    spendingLimit.NotificationThreshold,
                    spendingLimit.RequireApprovalOverLimit,
                    spendingLimit.IsActive,
                    spendingLimit.CreatedAt,
                    spendingLimit.UpdatedAt
                };

                return CreatedAtAction(nameof(GetSpendingLimit), new { id = spendingLimit.Id }, spendingLimitDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Resource not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating spending limit");
                return StatusCode(500, new { message = "An error occurred while creating the spending limit" });
            }
        }

        [HttpGet("spending-limits/{id}")]
        public async Task<IActionResult> GetSpendingLimit(int id)
        {
            try
            {
                var userId = GetUserIdFromClaims();
                var spendingLimit = await _budgetControlService.GetSpendingLimitByIdAsync(id);

                // Check if user can access the spending limit
                var isFamilyMember = await _familyService.IsFamilyMemberAsync(spendingLimit.FamilyId, userId);
                if (!isFamilyMember)
                {
                    return Forbid();
                }

                var spendingLimitDto = new
                {
                    spendingLimit.Id,
                    spendingLimit.FamilyId,
                    spendingLimit.FamilyMemberUserId,
                    MemberUsername = spendingLimit.FamilyMemberUser?.Username,
                    MemberFullName = $"{spendingLimit.FamilyMemberUser?.FirstName} {spendingLimit.FamilyMemberUser?.LastName}",
                    spendingLimit.Amount,
                    Period = spendingLimit.Period.ToString(),
                    spendingLimit.StartDate,
                    spendingLimit.EndDate,
                    spendingLimit.CategoryId,
                    CategoryName = spendingLimit.Category?.Name,
                    spendingLimit.NotificationThreshold,
                    spendingLimit.RequireApprovalOverLimit,
                    spendingLimit.IsActive,
                    spendingLimit.CreatedAt,
                    spendingLimit.UpdatedAt
                };

                return Ok(spendingLimitDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Resource not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting spending limit");
                return StatusCode(500, new { message = "An error occurred while retrieving the spending limit" });
            }
        }

        [HttpPut("spending-limits/{id}")]
        [RequireFamilyRole("id", "Admin")]
        public async Task<IActionResult> UpdateSpendingLimit(int id, [FromBody] UpdateSpendingLimitDto updateSpendingLimitDto)
        {
            try
            {
                // Get the spending limit to check the family ID
                var existingSpendingLimit = await _budgetControlService.GetSpendingLimitByIdAsync(id);

                // Get the user ID from claims
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Check if user is an admin of the family that the spending limit belongs to
                var isFamilyAdmin = await _familyService.IsFamilyAdminAsync(existingSpendingLimit.FamilyId, userId);
                if (!isFamilyAdmin)
                {
                    return Forbid();
                }

                var spendingLimit = await _budgetControlService.UpdateSpendingLimitAsync(
                    id,
                    updateSpendingLimitDto.Amount,
                    updateSpendingLimitDto.Period,
                    updateSpendingLimitDto.StartDate,
                    updateSpendingLimitDto.EndDate,
                    updateSpendingLimitDto.CategoryId,
                    updateSpendingLimitDto.NotificationThreshold,
                    updateSpendingLimitDto.RequireApprovalOverLimit,
                    updateSpendingLimitDto.IsActive);

                var spendingLimitDto = new
                {
                    spendingLimit.Id,
                    spendingLimit.FamilyId,
                    spendingLimit.FamilyMemberUserId,
                    spendingLimit.Amount,
                    Period = spendingLimit.Period.ToString(),
                    spendingLimit.StartDate,
                    spendingLimit.EndDate,
                    spendingLimit.CategoryId,
                    spendingLimit.NotificationThreshold,
                    spendingLimit.RequireApprovalOverLimit,
                    spendingLimit.IsActive,
                    spendingLimit.CreatedAt,
                    spendingLimit.UpdatedAt
                };

                return Ok(spendingLimitDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Resource not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating spending limit");
                return StatusCode(500, new { message = "An error occurred while updating the spending limit" });
            }
        }

        [HttpDelete("spending-limits/{id}")]
        [RequireFamilyRole("id", "Admin")]
        public async Task<IActionResult> DeleteSpendingLimit(int id)
        {
            try
            {
                // Get the spending limit to check the family ID
                var existingSpendingLimit = await _budgetControlService.GetSpendingLimitByIdAsync(id);

                // Get the user ID from claims
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Check if user is an admin of the family that the spending limit belongs to
                var isFamilyAdmin = await _familyService.IsFamilyAdminAsync(existingSpendingLimit.FamilyId, userId);
                if (!isFamilyAdmin)
                {
                    return Forbid();
                }

                await _budgetControlService.DeleteSpendingLimitAsync(id);

                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Resource not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting spending limit");
                return StatusCode(500, new { message = "An error occurred while deleting the spending limit" });
            }
        }
    }

    public class CreateBudgetLimitDto
    {
        [Required]
        public int CategoryId { get; set; }

        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal Amount { get; set; }

        [Required]
        public FrequencyType Period { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public int? FamilyId { get; set; }

        public int? FamilyMemberUserId { get; set; }

        [Range(0, 100)]
        public decimal NotificationThreshold { get; set; } = 80;

        public bool RolloverUnused { get; set; } = false;
    }

    public class UpdateBudgetLimitDto
    {
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal Amount { get; set; }

        [Required]
        public FrequencyType Period { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        [Range(0, 100)]
        public decimal NotificationThreshold { get; set; } = 80;

        public bool RolloverUnused { get; set; } = false;
    }

    public class CreateSpendingLimitDto
    {
        [Required]
        public int FamilyMemberUserId { get; set; }

        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal Amount { get; set; }

        [Required]
        public FrequencyType Period { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public int? CategoryId { get; set; }

        [Range(0, 100)]
        public decimal NotificationThreshold { get; set; } = 80;

        public bool RequireApprovalOverLimit { get; set; } = true;
    }

    public class UpdateSpendingLimitDto
    {
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal Amount { get; set; }

        [Required]
        public FrequencyType Period { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public int? CategoryId { get; set; }

        [Range(0, 100)]
        public decimal NotificationThreshold { get; set; } = 80;

        public bool RequireApprovalOverLimit { get; set; } = true;

        public bool IsActive { get; set; } = true;
    }
}
