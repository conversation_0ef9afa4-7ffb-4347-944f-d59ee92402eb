using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.API.DTOs;
using HisabKitab.API.Middleware;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/loans")]
    [Authorize]
    public class LoanController : ControllerBase
    {
        private readonly ILoanService _loanService;
        private readonly IUserService _userService;
        private readonly ILogger<LoanController> _logger;

        public LoanController(
            ILoanService loanService,
            IUserService userService,
            ILogger<LoanController> logger)
        {
            _loanService = loanService;
            _userService = userService;
            _logger = logger;
        }

        [HttpPost]
        public async Task<IActionResult> CreateLoan([FromBody] CreateLoanDto createLoanDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                // Set lender or borrower based on the loan type
                int? lenderUserId = createLoanDto.LenderUserId;
                int? borrowerUserId = createLoanDto.BorrowerUserId;

                // If neither is provided, set the current user as the lender
                if (lenderUserId == null && borrowerUserId == null && !createLoanDto.IsExternalEntity)
                {
                    lenderUserId = userId;
                }

                // Create the loan
                var loan = await _loanService.CreateLoanAsync(
                    createLoanDto.Title,
                    createLoanDto.Amount,
                    createLoanDto.InterestRate,
                    createLoanDto.InterestType,
                    createLoanDto.PaymentFrequency,
                    createLoanDto.StartDate,
                    createLoanDto.EndDate,
                    lenderUserId,
                    borrowerUserId,
                    createLoanDto.IsExternalEntity,
                    createLoanDto.ExternalEntityName,
                    createLoanDto.Status,
                    createLoanDto.Notes);

                // Generate payment schedule
                var paymentSchedule = await _loanService.GeneratePaymentScheduleAsync(loan.Id);

                // Map to DTO
                var loanDto = MapLoanToDto(loan);
                loanDto.Payments = paymentSchedule.Select(p => MapLoanPaymentToDto(p)).ToList();
                loanDto.MonthlyEMI = await _loanService.CalculateEMIAsync(loan.Id);
                loanDto.PredictedCompletionDate = await _loanService.PredictCompletionDateAsync(loan.Id);

                _logger.LogInformation($"Loan created: {loan.Title} with ID {loan.Id}");

                return CreatedAtAction(nameof(GetLoanById), new { id = loan.Id }, loanDto);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning($"Invalid loan creation attempt: {ex.Message}");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating loan");
                return StatusCode(500, new { message = "An error occurred while creating the loan" });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetLoanById(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                // Check if user can access this loan
                if (!await _loanService.UserCanAccessLoanAsync(id, userId))
                {
                    return Forbid();
                }

                var loan = await _loanService.GetLoanByIdAsync(id);
                var payments = await _loanService.GetPaymentsByLoanIdAsync(id);
                var reminders = await _loanService.GetRemindersByLoanIdAsync(id);

                // Map to DTO
                var loanDto = MapLoanToDto(loan);
                loanDto.Payments = payments.Select(p => MapLoanPaymentToDto(p)).ToList();
                loanDto.Reminders = reminders.Select(r => MapLoanReminderToDto(r)).ToList();
                loanDto.MonthlyEMI = await _loanService.CalculateEMIAsync(id);
                loanDto.PredictedCompletionDate = await _loanService.PredictCompletionDateAsync(id);
                loanDto.RemainingAmount = await _loanService.CalculateRemainingBalanceAsync(id);
                loanDto.PaidAmount = loanDto.TotalPayableAmount - loanDto.RemainingAmount;

                return Ok(loanDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning($"Loan not found: {ex.Message}");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving loan with ID {id}");
                return StatusCode(500, new { message = "An error occurred while retrieving the loan" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetLoans(
            [FromQuery] bool? asLender = null,
            [FromQuery] bool? asBorrower = null,
            [FromQuery] string? status = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                var loans = await _loanService.GetLoansByUserIdAsync(
                    userId,
                    asLender ?? false,
                    asBorrower ?? false,
                    status,
                    startDate,
                    endDate);

                // Map to DTOs
                var loanDtos = new List<LoanDto>();
                foreach (var loan in loans)
                {
                    var loanDto = MapLoanToDto(loan);
                    loanDto.MonthlyEMI = await _loanService.CalculateEMIAsync(loan.Id);
                    loanDto.RemainingAmount = await _loanService.CalculateRemainingBalanceAsync(loan.Id);
                    loanDto.PaidAmount = loanDto.TotalPayableAmount - loanDto.RemainingAmount;
                    loanDtos.Add(loanDto);
                }

                return Ok(loanDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving loans");
                return StatusCode(500, new { message = "An error occurred while retrieving loans" });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateLoan(int id, [FromBody] UpdateLoanDto updateLoanDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                // Check if user can modify this loan
                if (!await _loanService.UserCanModifyLoanAsync(id, userId))
                {
                    return Forbid();
                }

                var loan = await _loanService.UpdateLoanAsync(
                    id,
                    updateLoanDto.Title,
                    updateLoanDto.Amount,
                    updateLoanDto.InterestRate,
                    updateLoanDto.InterestType,
                    updateLoanDto.PaymentFrequency,
                    updateLoanDto.StartDate,
                    updateLoanDto.EndDate,
                    updateLoanDto.LenderUserId,
                    updateLoanDto.BorrowerUserId,
                    updateLoanDto.IsExternalEntity,
                    updateLoanDto.ExternalEntityName,
                    updateLoanDto.Status,
                    updateLoanDto.Notes);

                // Map to DTO
                var loanDto = MapLoanToDto(loan);
                loanDto.MonthlyEMI = await _loanService.CalculateEMIAsync(id);
                loanDto.PredictedCompletionDate = await _loanService.PredictCompletionDateAsync(id);
                loanDto.RemainingAmount = await _loanService.CalculateRemainingBalanceAsync(id);
                loanDto.PaidAmount = loanDto.TotalPayableAmount - loanDto.RemainingAmount;

                _logger.LogInformation($"Loan updated: {loan.Title} with ID {loan.Id}");

                return Ok(loanDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning($"Loan not found: {ex.Message}");
                return NotFound(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning($"Invalid loan update attempt: {ex.Message}");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating loan with ID {id}");
                return StatusCode(500, new { message = "An error occurred while updating the loan" });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteLoan(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                // Check if user can modify this loan
                if (!await _loanService.UserCanModifyLoanAsync(id, userId))
                {
                    return Forbid();
                }

                await _loanService.DeleteLoanAsync(id);

                _logger.LogInformation($"Loan deleted with ID {id}");

                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning($"Loan not found: {ex.Message}");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting loan with ID {id}");
                return StatusCode(500, new { message = "An error occurred while deleting the loan" });
            }
        }

        [HttpGet("{id}/timeline")]
        public async Task<IActionResult> GetLoanTimeline(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                int userId = int.Parse(userIdClaim);

                // Check if user can access this loan
                if (!await _loanService.UserCanAccessLoanAsync(id, userId))
                {
                    return Forbid();
                }

                var loan = await _loanService.GetLoanByIdAsync(id);
                var timeline = await _loanService.GenerateLoanTimelineAsync(id);
                var remainingBalance = await _loanService.CalculateRemainingBalanceAsync(id);
                var predictedCompletionDate = await _loanService.PredictCompletionDateAsync(id);

                // Convert timeline to string dates for JSON serialization
                var timelineDto = new LoanTimelineDto
                {
                    LoanId = id,
                    LoanTitle = loan.Title,
                    Timeline = timeline.ToDictionary(
                        kvp => kvp.Key.ToString("yyyy-MM-dd"),
                        kvp => kvp.Value),
                    TotalAmount = loan.TotalPayableAmount,
                    PaidAmount = loan.TotalPayableAmount - remainingBalance,
                    RemainingAmount = remainingBalance,
                    PredictedCompletionDate = predictedCompletionDate
                };

                return Ok(timelineDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning($"Loan not found: {ex.Message}");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving loan timeline for ID {id}");
                return StatusCode(500, new { message = "An error occurred while retrieving the loan timeline" });
            }
        }

        [HttpPost("calculate-emi")]
        public async Task<IActionResult> CalculateEMI([FromBody] LoanEMICalculationDto calculationDto)
        {
            try
            {
                var emi = await _loanService.CalculateEMIAsync(
                    calculationDto.Principal,
                    calculationDto.InterestRate,
                    calculationDto.InterestType,
                    calculationDto.PaymentFrequency,
                    calculationDto.StartDate,
                    calculationDto.EndDate);

                return Ok(new { emi });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning($"Invalid EMI calculation attempt: {ex.Message}");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating EMI");
                return StatusCode(500, new { message = "An error occurred while calculating EMI" });
            }
        }

        #region Helper Methods

        private LoanDto MapLoanToDto(Loan loan)
        {
            return new LoanDto
            {
                Id = loan.Id,
                Title = loan.Title,
                Amount = loan.Amount,
                InterestRate = loan.InterestRate,
                InterestType = loan.InterestType,
                InterestTypeName = loan.InterestType.ToString(),
                PaymentFrequency = loan.PaymentFrequency,
                PaymentFrequencyName = loan.PaymentFrequency.ToString(),
                TotalPayableAmount = loan.TotalPayableAmount,
                StartDate = loan.StartDate,
                EndDate = loan.EndDate,
                LenderUserId = loan.LenderUserId,
                LenderUsername = loan.LenderUser?.Username ?? string.Empty,
                BorrowerUserId = loan.BorrowerUserId,
                BorrowerUsername = loan.BorrowerUser?.Username ?? string.Empty,
                IsExternalEntity = loan.IsExternalEntity,
                ExternalEntityName = loan.ExternalEntityName ?? string.Empty,
                Status = loan.Status,
                Notes = loan.Notes ?? string.Empty,
                LastUpdatedAt = loan.LastUpdatedAt,
                CreatedAt = loan.CreatedAt,
                UpdatedAt = loan.UpdatedAt,
                Payments = new List<LoanPaymentDto>(),
                Reminders = new List<LoanReminderDto>()
            };
        }

        private LoanPaymentDto MapLoanPaymentToDto(LoanPayment payment)
        {
            return new LoanPaymentDto
            {
                Id = payment.Id,
                LoanId = payment.LoanId,
                Amount = payment.Amount,
                PrincipalAmount = payment.PrincipalAmount,
                InterestAmount = payment.InterestAmount,
                PaymentDate = payment.PaymentDate,
                PaymentMethod = payment.PaymentMethod ?? string.Empty,
                IsScheduled = payment.IsScheduled,
                TransactionId = payment.TransactionId,
                Notes = payment.Notes ?? string.Empty,
                CreatedAt = payment.CreatedAt,
                UpdatedAt = payment.UpdatedAt
            };
        }

        private LoanReminderDto MapLoanReminderToDto(LoanReminder reminder)
        {
            return new LoanReminderDto
            {
                Id = reminder.Id,
                LoanId = reminder.LoanId,
                ReminderDate = reminder.ReminderDate,
                Message = reminder.Message,
                IsActive = reminder.IsActive,
                IsSent = reminder.IsSent,
                SentAt = reminder.SentAt,
                CreatedAt = reminder.CreatedAt,
                UpdatedAt = reminder.UpdatedAt
            };
        }

        #endregion
    }
}
