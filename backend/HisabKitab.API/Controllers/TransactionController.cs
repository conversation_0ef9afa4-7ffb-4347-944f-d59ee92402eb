using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.API.DTOs;
using HisabKitab.API.Middleware;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/transactions")]
    [Authorize]
    public class TransactionController : ControllerBase
    {
        private readonly ITransactionService _transactionService;
        private readonly IAccountService _accountService;
        private readonly ICategoryService _categoryService;
        private readonly ILogger<TransactionController> _logger;

        public TransactionController(
            ITransactionService transactionService,
            IAccountService accountService,
            ICategoryService categoryService,
            ILogger<TransactionController> logger)
        {
            _transactionService = transactionService;
            _accountService = accountService;
            _categoryService = categoryService;
            _logger = logger;
        }

        private TransactionDto MapTransactionToDto(Transaction transaction)
        {
            var transactionDto = new TransactionDto
            {
                Id = transaction.Id,
                Amount = transaction.Amount,
                Description = transaction.Description,
                Date = transaction.Date,
                Type = transaction.Type,
                AccountId = transaction.AccountId,
                AccountName = transaction.Account?.Name ?? string.Empty,
                ToAccountId = transaction.ToAccountId,
                ToAccountName = transaction.ToAccount?.Name ?? string.Empty,
                CategoryId = transaction.CategoryId,
                CategoryName = transaction.Category?.Name ?? string.Empty,
                PriorityId = transaction.PriorityId,
                PriorityName = transaction.Priority?.Name ?? string.Empty,
                UserId = transaction.UserId,
                Username = transaction.User?.Username ?? string.Empty,
                Status = transaction.Status,
                ApprovedByUserId = transaction.ApprovedByUserId,
                ApprovedByUsername = transaction.ApprovedByUser?.Username ?? string.Empty,
                ReceiptImage = transaction.ReceiptImage,
                Tags = transaction.Tags,
                Location = transaction.Location,
                ExchangeRate = transaction.ExchangeRate,
                RecurringTransactionId = transaction.RecurringTransactionId,
                IsSynced = transaction.IsSynced,
                SyncStatus = transaction.SyncStatus,
                LastUpdatedAt = transaction.LastUpdatedAt,
                CreatedAt = transaction.CreatedAt,
                UpdatedAt = transaction.UpdatedAt,
                Items = transaction.Items?.Select(item => new TransactionItemDto
                {
                    Id = item.Id,
                    TransactionId = item.TransactionId,
                    Name = item.Name,
                    Amount = item.Amount,
                    Quantity = item.Quantity,
                    CategoryId = item.CategoryId,
                    CategoryName = item.Category?.Name ?? string.Empty,
                    Notes = item.Notes,
                    CreatedAt = item.CreatedAt,
                    UpdatedAt = item.UpdatedAt
                }).ToList() ?? new List<TransactionItemDto>()
            };

            return transactionDto;
        }

        [HttpGet]
        public async Task<IActionResult> GetTransactions([FromQuery] TransactionFilterDto filter)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID claim not found" });
                }
                var userId = int.Parse(userIdClaim);

                var transactions = await _transactionService.GetTransactionsByUserIdAsync(
                    userId,
                    filter.StartDate,
                    filter.EndDate,
                    filter.Type,
                    filter.AccountId,
                    filter.CategoryId,
                    filter.PriorityId,
                    filter.Status,
                    filter.SearchTerm,
                    filter.SortBy,
                    filter.Ascending,
                    filter.Skip,
                    filter.Take
                );

                var transactionDtos = transactions.Select(MapTransactionToDto).ToList();

                return Ok(transactionDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transactions");
                return StatusCode(500, new { message = "An error occurred while retrieving transactions" });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetTransaction(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID claim not found" });
                }
                var userId = int.Parse(userIdClaim);

                // Check if the user can access this transaction
                var canAccess = await _transactionService.UserCanAccessTransactionAsync(id, userId);
                if (!canAccess)
                {
                    return Forbid();
                }

                var transaction = await _transactionService.GetTransactionByIdAsync(id);
                var transactionDto = MapTransactionToDto(transaction);

                return Ok(transactionDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Transaction not found");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transaction");
                return StatusCode(500, new { message = "An error occurred while retrieving the transaction" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateTransaction([FromBody] CreateTransactionDto createTransactionDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID claim not found" });
                }
                var userId = int.Parse(userIdClaim);

                // Check if the user can access the account
                var canAccessAccount = await _accountService.UserCanAccessAccountAsync(createTransactionDto.AccountId, userId);
                if (!canAccessAccount)
                {
                    return Forbid();
                }

                // Check if the user can access the toAccount if provided
                if (createTransactionDto.ToAccountId.HasValue)
                {
                    var canAccessToAccount = await _accountService.UserCanAccessAccountAsync(createTransactionDto.ToAccountId.Value, userId);
                    if (!canAccessToAccount)
                    {
                        return Forbid();
                    }
                }

                // Create transaction items if provided
                var transactionItems = new List<TransactionItem>();
                if (createTransactionDto.Items != null && createTransactionDto.Items.Any())
                {
                    foreach (var itemDto in createTransactionDto.Items)
                    {
                        var item = new TransactionItem
                        {
                            Name = itemDto.Name,
                            Amount = itemDto.Amount,
                            Quantity = itemDto.Quantity,
                            CategoryId = itemDto.CategoryId,
                            Notes = itemDto.Notes
                        };
                        transactionItems.Add(item);
                    }
                }

                var transaction = await _transactionService.CreateTransactionAsync(
                    createTransactionDto.Amount,
                    createTransactionDto.Description,
                    createTransactionDto.Date,
                    createTransactionDto.Type,
                    createTransactionDto.AccountId,
                    createTransactionDto.ToAccountId,
                    createTransactionDto.CategoryId,
                    createTransactionDto.PriorityId,
                    userId,
                    createTransactionDto.Status,
                    null, // approvedByUserId
                    createTransactionDto.ReceiptImage,
                    createTransactionDto.Tags,
                    createTransactionDto.Location,
                    createTransactionDto.ExchangeRate,
                    createTransactionDto.RecurringTransactionId,
                    true, // isSynced
                    SyncStatus.Synced,
                    transactionItems
                );

                var transactionDto = MapTransactionToDto(transaction);

                return CreatedAtAction(nameof(GetTransaction), new { id = transaction.Id }, transactionDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Entity not found");
                return BadRequest(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid transaction data");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating transaction");
                return StatusCode(500, new { message = "An error occurred while creating the transaction" });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateTransaction(int id, [FromBody] UpdateTransactionDto updateTransactionDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID claim not found" });
                }
                var userId = int.Parse(userIdClaim);

                // Check if the user can modify this transaction
                var canModify = await _transactionService.UserCanModifyTransactionAsync(id, userId);
                if (!canModify)
                {
                    return Forbid();
                }

                // Check if the user can access the account if provided
                if (updateTransactionDto.AccountId.HasValue)
                {
                    var canAccessAccount = await _accountService.UserCanAccessAccountAsync(updateTransactionDto.AccountId.Value, userId);
                    if (!canAccessAccount)
                    {
                        return Forbid();
                    }
                }

                // Check if the user can access the toAccount if provided
                if (updateTransactionDto.ToAccountId.HasValue && updateTransactionDto.ToAccountId.Value > 0)
                {
                    var canAccessToAccount = await _accountService.UserCanAccessAccountAsync(updateTransactionDto.ToAccountId.Value, userId);
                    if (!canAccessToAccount)
                    {
                        return Forbid();
                    }
                }

                var transaction = await _transactionService.UpdateTransactionAsync(
                    id,
                    updateTransactionDto.Amount,
                    updateTransactionDto.Description,
                    updateTransactionDto.Date,
                    updateTransactionDto.Type,
                    updateTransactionDto.AccountId,
                    updateTransactionDto.ToAccountId,
                    updateTransactionDto.CategoryId,
                    updateTransactionDto.PriorityId,
                    updateTransactionDto.Status,
                    null, // approvedByUserId
                    updateTransactionDto.ReceiptImage,
                    updateTransactionDto.Tags,
                    updateTransactionDto.Location,
                    updateTransactionDto.ExchangeRate,
                    updateTransactionDto.RecurringTransactionId
                );

                // Update transaction items if provided
                if (updateTransactionDto.Items != null && updateTransactionDto.Items.Any())
                {
                    // Get existing items
                    var existingItems = (await _transactionService.GetTransactionByIdAsync(id)).Items.ToList();

                    // Process each item in the update DTO
                    foreach (var itemDto in updateTransactionDto.Items)
                    {
                        if (itemDto.Id > 0)
                        {
                            // Update existing item
                            var existingItem = existingItems.FirstOrDefault(i => i.Id == itemDto.Id);
                            if (existingItem != null)
                            {
                                await _transactionService.UpdateTransactionItemAsync(
                                    itemDto.Id,
                                    itemDto.Name,
                                    itemDto.Amount,
                                    itemDto.Quantity,
                                    itemDto.CategoryId,
                                    itemDto.Notes
                                );
                            }
                        }
                        else
                        {
                            // Add new item
                            await _transactionService.AddTransactionItemAsync(
                                id,
                                itemDto.Name,
                                itemDto.Amount ?? 0,
                                itemDto.Quantity ?? 1,
                                itemDto.CategoryId,
                                itemDto.Notes
                            );
                        }
                    }

                    // Delete items that are not in the update DTO
                    var itemIdsToKeep = updateTransactionDto.Items
                        .Where(i => i.Id > 0)
                        .Select(i => i.Id)
                        .ToList();

                    var itemsToDelete = existingItems
                        .Where(i => !itemIdsToKeep.Contains(i.Id))
                        .ToList();

                    foreach (var item in itemsToDelete)
                    {
                        await _transactionService.DeleteTransactionItemAsync(item.Id);
                    }
                }

                var updatedTransaction = await _transactionService.GetTransactionByIdAsync(id);
                var transactionDto = MapTransactionToDto(updatedTransaction);

                return Ok(transactionDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Entity not found");
                return NotFound(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid transaction data");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating transaction");
                return StatusCode(500, new { message = "An error occurred while updating the transaction" });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTransaction(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID claim not found" });
                }
                var userId = int.Parse(userIdClaim);

                // Check if the user can modify this transaction
                var canModify = await _transactionService.UserCanModifyTransactionAsync(id, userId);
                if (!canModify)
                {
                    return Forbid();
                }

                var result = await _transactionService.DeleteTransactionAsync(id);

                return Ok(new { success = result });
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Transaction not found");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting transaction");
                return StatusCode(500, new { message = "An error occurred while deleting the transaction" });
            }
        }

        [HttpGet("account/{accountId}")]
        public async Task<IActionResult> GetTransactionsByAccount(int accountId, [FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate, [FromQuery] TransactionType? type)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID claim not found" });
                }
                var userId = int.Parse(userIdClaim);

                // Check if the user can access this account
                var canAccess = await _accountService.UserCanAccessAccountAsync(accountId, userId);
                if (!canAccess)
                {
                    return Forbid();
                }

                var transactions = await _transactionService.GetTransactionsByAccountIdAsync(
                    accountId,
                    startDate,
                    endDate,
                    type
                );

                var transactionDtos = transactions.Select(MapTransactionToDto).ToList();

                return Ok(transactionDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transactions by account");
                return StatusCode(500, new { message = "An error occurred while retrieving transactions" });
            }
        }

        [HttpPost("batch")]
        public async Task<IActionResult> BatchCreateTransactions([FromBody] BatchTransactionDto batchTransactionDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID claim not found" });
                }
                var userId = int.Parse(userIdClaim);

                // Convert DTOs to entities
                var transactions = new List<Transaction>();
                foreach (var transactionDto in batchTransactionDto.Transactions)
                {
                    // Check if the user can access the account
                    var canAccessAccount = await _accountService.UserCanAccessAccountAsync(transactionDto.AccountId, userId);
                    if (!canAccessAccount)
                    {
                        return Forbid();
                    }

                    // Check if the user can access the toAccount if provided
                    if (transactionDto.ToAccountId.HasValue)
                    {
                        var canAccessToAccount = await _accountService.UserCanAccessAccountAsync(transactionDto.ToAccountId.Value, userId);
                        if (!canAccessToAccount)
                        {
                            return Forbid();
                        }
                    }

                    // Create transaction items if provided
                    var transactionItems = new List<TransactionItem>();
                    if (transactionDto.Items != null && transactionDto.Items.Any())
                    {
                        foreach (var itemDto in transactionDto.Items)
                        {
                            var item = new TransactionItem
                            {
                                Name = itemDto.Name,
                                Amount = itemDto.Amount,
                                Quantity = itemDto.Quantity,
                                CategoryId = itemDto.CategoryId,
                                Notes = itemDto.Notes
                            };
                            transactionItems.Add(item);
                        }
                    }

                    var transaction = new Transaction
                    {
                        Amount = transactionDto.Amount,
                        Description = transactionDto.Description,
                        Date = transactionDto.Date,
                        Type = transactionDto.Type,
                        AccountId = transactionDto.AccountId,
                        ToAccountId = transactionDto.ToAccountId,
                        CategoryId = transactionDto.CategoryId,
                        PriorityId = transactionDto.PriorityId,
                        UserId = userId,
                        Status = transactionDto.Status,
                        ReceiptImage = transactionDto.ReceiptImage,
                        Tags = transactionDto.Tags,
                        Location = transactionDto.Location,
                        ExchangeRate = transactionDto.ExchangeRate,
                        RecurringTransactionId = transactionDto.RecurringTransactionId,
                        IsSynced = true,
                        SyncStatus = SyncStatus.Synced,
                        Items = transactionItems
                    };

                    transactions.Add(transaction);
                }

                var createdTransactions = await _transactionService.BatchCreateTransactionsAsync(transactions);
                var transactionDtos = createdTransactions.Select(MapTransactionToDto).ToList();

                return Ok(transactionDtos);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Entity not found");
                return BadRequest(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid transaction data");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch creating transactions");
                return StatusCode(500, new { message = "An error occurred while creating transactions" });
            }
        }

        [HttpPut("batch")]
        public async Task<IActionResult> BatchUpdateTransactions([FromBody] BatchUpdateTransactionDto batchUpdateTransactionDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID claim not found" });
                }
                var userId = int.Parse(userIdClaim);

                // Convert DTOs to entities
                var transactions = new List<Transaction>();
                foreach (var transactionDto in batchUpdateTransactionDto.Transactions)
                {
                    // Check if the user can modify this transaction
                    var canModify = await _transactionService.UserCanModifyTransactionAsync(transactionDto.Id, userId);
                    if (!canModify)
                    {
                        return Forbid();
                    }

                    // Check if the user can access the account if provided
                    if (transactionDto.AccountId.HasValue)
                    {
                        var canAccessAccount = await _accountService.UserCanAccessAccountAsync(transactionDto.AccountId.Value, userId);
                        if (!canAccessAccount)
                        {
                            return Forbid();
                        }
                    }

                    // Check if the user can access the toAccount if provided
                    if (transactionDto.ToAccountId.HasValue && transactionDto.ToAccountId.Value > 0)
                    {
                        var canAccessToAccount = await _accountService.UserCanAccessAccountAsync(transactionDto.ToAccountId.Value, userId);
                        if (!canAccessToAccount)
                        {
                            return Forbid();
                        }
                    }

                    // Get existing transaction
                    var existingTransaction = await _transactionService.GetTransactionByIdAsync(transactionDto.Id);

                    // Update transaction properties
                    existingTransaction.Amount = transactionDto.Amount ?? existingTransaction.Amount;
                    existingTransaction.Description = transactionDto.Description ?? existingTransaction.Description;
                    existingTransaction.Date = transactionDto.Date ?? existingTransaction.Date;
                    existingTransaction.Type = transactionDto.Type ?? existingTransaction.Type;
                    existingTransaction.AccountId = transactionDto.AccountId ?? existingTransaction.AccountId;
                    existingTransaction.ToAccountId = transactionDto.ToAccountId ?? existingTransaction.ToAccountId;
                    existingTransaction.CategoryId = transactionDto.CategoryId ?? existingTransaction.CategoryId;
                    existingTransaction.PriorityId = transactionDto.PriorityId ?? existingTransaction.PriorityId;
                    existingTransaction.Status = transactionDto.Status ?? existingTransaction.Status;
                    existingTransaction.ReceiptImage = transactionDto.ReceiptImage ?? existingTransaction.ReceiptImage;
                    existingTransaction.Tags = transactionDto.Tags ?? existingTransaction.Tags;
                    existingTransaction.Location = transactionDto.Location ?? existingTransaction.Location;
                    existingTransaction.ExchangeRate = transactionDto.ExchangeRate ?? existingTransaction.ExchangeRate;
                    existingTransaction.RecurringTransactionId = transactionDto.RecurringTransactionId ?? existingTransaction.RecurringTransactionId;
                    existingTransaction.IsSynced = true;
                    existingTransaction.SyncStatus = SyncStatus.Synced;
                    existingTransaction.LastUpdatedAt = DateTime.UtcNow;

                    transactions.Add(existingTransaction);
                }

                var updatedTransactions = await _transactionService.BatchUpdateTransactionsAsync(transactions);
                var transactionDtos = updatedTransactions.Select(MapTransactionToDto).ToList();

                return Ok(transactionDtos);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Entity not found");
                return NotFound(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid transaction data");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch updating transactions");
                return StatusCode(500, new { message = "An error occurred while updating transactions" });
            }
        }

        [HttpDelete("batch")]
        public async Task<IActionResult> BatchDeleteTransactions([FromBody] BatchDeleteTransactionDto batchDeleteTransactionDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID claim not found" });
                }
                var userId = int.Parse(userIdClaim);

                // Check if the user can modify each transaction
                foreach (var transactionId in batchDeleteTransactionDto.TransactionIds)
                {
                    var canModify = await _transactionService.UserCanModifyTransactionAsync(transactionId, userId);
                    if (!canModify)
                    {
                        return Forbid();
                    }
                }

                var result = await _transactionService.BatchDeleteTransactionsAsync(batchDeleteTransactionDto.TransactionIds);

                return Ok(new { success = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch deleting transactions");
                return StatusCode(500, new { message = "An error occurred while deleting transactions" });
            }
        }

        [HttpGet("statistics")]
        public async Task<IActionResult> GetTransactionStatistics([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] int? accountId = null)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID claim not found" });
                }
                var userId = int.Parse(userIdClaim);

                // Check if the user can access the account if provided
                if (accountId.HasValue)
                {
                    var canAccessAccount = await _accountService.UserCanAccessAccountAsync(accountId.Value, userId);
                    if (!canAccessAccount)
                    {
                        return Forbid();
                    }
                }

                var income = await _transactionService.GetTotalIncomeAsync(userId, startDate, endDate, accountId);
                var expense = await _transactionService.GetTotalExpenseAsync(userId, startDate, endDate, accountId);
                var balance = await _transactionService.GetBalanceAsync(userId, startDate, endDate, accountId);

                return Ok(new
                {
                    startDate,
                    endDate,
                    accountId,
                    income,
                    expense,
                    balance
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transaction statistics");
                return StatusCode(500, new { message = "An error occurred while retrieving transaction statistics" });
            }
        }
    }
}