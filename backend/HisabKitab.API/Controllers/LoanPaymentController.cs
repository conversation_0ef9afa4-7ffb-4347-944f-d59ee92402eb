using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.API.DTOs;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/loan-payments")]
    [Authorize]
    public class LoanPaymentController : ControllerBase
    {
        private readonly ILoanService _loanService;
        private readonly ILogger<LoanPaymentController> _logger;

        public LoanPaymentController(
            ILoanService loanService,
            ILogger<LoanPaymentController> logger)
        {
            _loanService = loanService;
            _logger = logger;
        }

        private int GetUserIdFromClaims()
        {
            var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userIdClaim))
            {
                throw new UnauthorizedAccessException("User ID not found in token");
            }
            return int.Parse(userIdClaim);
        }

        [HttpPost]
        public async Task<IActionResult> CreatePayment([FromBody] CreateLoanPaymentDto createPaymentDto)
        {
            try
            {
                int userId = GetUserIdFromClaims();

                // Check if user can access this loan
                if (!await _loanService.UserCanAccessLoanAsync(createPaymentDto.LoanId, userId))
                {
                    return Forbid();
                }

                var payment = await _loanService.RecordPaymentAsync(
                    createPaymentDto.LoanId,
                    createPaymentDto.Amount,
                    createPaymentDto.PrincipalAmount,
                    createPaymentDto.InterestAmount,
                    createPaymentDto.PaymentDate,
                    createPaymentDto.PaymentMethod,
                    createPaymentDto.IsScheduled,
                    createPaymentDto.TransactionId,
                    createPaymentDto.Notes);

                // Map to DTO
                var paymentDto = new LoanPaymentDto
                {
                    Id = payment.Id,
                    LoanId = payment.LoanId,
                    Amount = payment.Amount,
                    PrincipalAmount = payment.PrincipalAmount,
                    InterestAmount = payment.InterestAmount,
                    PaymentDate = payment.PaymentDate,
                    PaymentMethod = payment.PaymentMethod ?? string.Empty,
                    IsScheduled = payment.IsScheduled,
                    TransactionId = payment.TransactionId,
                    Notes = payment.Notes ?? string.Empty,
                    CreatedAt = payment.CreatedAt,
                    UpdatedAt = payment.UpdatedAt
                };

                _logger.LogInformation($"Payment created for loan ID {payment.LoanId}: {payment.Amount:C}");

                return CreatedAtAction(nameof(GetPaymentById), new { id = payment.Id }, paymentDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning($"Loan not found: {ex.Message}");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment");
                return StatusCode(500, new { message = "An error occurred while creating the payment" });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetPaymentById(int id)
        {
            try
            {
                int userId = GetUserIdFromClaims();

                var payment = await _loanService.GetPaymentByIdAsync(id);

                // Check if user can access this loan
                if (!await _loanService.UserCanAccessLoanAsync(payment.LoanId, userId))
                {
                    return Forbid();
                }

                // Map to DTO
                var paymentDto = new LoanPaymentDto
                {
                    Id = payment.Id,
                    LoanId = payment.LoanId,
                    Amount = payment.Amount,
                    PrincipalAmount = payment.PrincipalAmount,
                    InterestAmount = payment.InterestAmount,
                    PaymentDate = payment.PaymentDate,
                    PaymentMethod = payment.PaymentMethod ?? string.Empty,
                    IsScheduled = payment.IsScheduled,
                    TransactionId = payment.TransactionId,
                    Notes = payment.Notes ?? string.Empty,
                    CreatedAt = payment.CreatedAt,
                    UpdatedAt = payment.UpdatedAt
                };

                return Ok(paymentDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning($"Payment not found: {ex.Message}");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving payment with ID {id}");
                return StatusCode(500, new { message = "An error occurred while retrieving the payment" });
            }
        }

        [HttpGet("by-loan/{loanId}")]
        public async Task<IActionResult> GetPaymentsByLoanId(int loanId)
        {
            try
            {
                int userId = GetUserIdFromClaims();

                // Check if user can access this loan
                if (!await _loanService.UserCanAccessLoanAsync(loanId, userId))
                {
                    return Forbid();
                }

                var payments = await _loanService.GetPaymentsByLoanIdAsync(loanId);

                // Map to DTOs
                var paymentDtos = payments.Select(p => new LoanPaymentDto
                {
                    Id = p.Id,
                    LoanId = p.LoanId,
                    Amount = p.Amount,
                    PrincipalAmount = p.PrincipalAmount,
                    InterestAmount = p.InterestAmount,
                    PaymentDate = p.PaymentDate,
                    PaymentMethod = p.PaymentMethod ?? string.Empty,
                    IsScheduled = p.IsScheduled,
                    TransactionId = p.TransactionId,
                    Notes = p.Notes ?? string.Empty,
                    CreatedAt = p.CreatedAt,
                    UpdatedAt = p.UpdatedAt
                }).ToList();

                return Ok(paymentDtos);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning($"Loan not found: {ex.Message}");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving payments for loan ID {loanId}");
                return StatusCode(500, new { message = "An error occurred while retrieving the payments" });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdatePayment(int id, [FromBody] UpdateLoanPaymentDto updatePaymentDto)
        {
            try
            {
                int userId = GetUserIdFromClaims();

                // Get payment to check loan access
                var existingPayment = await _loanService.GetPaymentByIdAsync(id);

                // Check if user can access this loan
                if (!await _loanService.UserCanAccessLoanAsync(existingPayment.LoanId, userId))
                {
                    return Forbid();
                }

                var payment = await _loanService.UpdatePaymentAsync(
                    id,
                    updatePaymentDto.Amount,
                    updatePaymentDto.PrincipalAmount,
                    updatePaymentDto.InterestAmount,
                    updatePaymentDto.PaymentDate,
                    updatePaymentDto.PaymentMethod,
                    updatePaymentDto.IsScheduled,
                    updatePaymentDto.TransactionId,
                    updatePaymentDto.Notes);

                // Map to DTO
                var paymentDto = new LoanPaymentDto
                {
                    Id = payment.Id,
                    LoanId = payment.LoanId,
                    Amount = payment.Amount,
                    PrincipalAmount = payment.PrincipalAmount,
                    InterestAmount = payment.InterestAmount,
                    PaymentDate = payment.PaymentDate,
                    PaymentMethod = payment.PaymentMethod ?? string.Empty,
                    IsScheduled = payment.IsScheduled,
                    TransactionId = payment.TransactionId,
                    Notes = payment.Notes ?? string.Empty,
                    CreatedAt = payment.CreatedAt,
                    UpdatedAt = payment.UpdatedAt
                };

                _logger.LogInformation($"Payment updated: ID {payment.Id} for loan ID {payment.LoanId}");

                return Ok(paymentDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning($"Payment not found: {ex.Message}");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating payment with ID {id}");
                return StatusCode(500, new { message = "An error occurred while updating the payment" });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeletePayment(int id)
        {
            try
            {
                int userId = GetUserIdFromClaims();

                // Get payment to check loan access
                var existingPayment = await _loanService.GetPaymentByIdAsync(id);

                // Check if user can access this loan
                if (!await _loanService.UserCanAccessLoanAsync(existingPayment.LoanId, userId))
                {
                    return Forbid();
                }

                await _loanService.DeletePaymentAsync(id);

                _logger.LogInformation($"Payment deleted with ID {id}");

                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning($"Payment not found: {ex.Message}");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting payment with ID {id}");
                return StatusCode(500, new { message = "An error occurred while deleting the payment" });
            }
        }
    }
}
