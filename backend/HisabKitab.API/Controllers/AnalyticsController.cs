using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.API.Middleware;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AnalyticsController : ControllerBase
    {
        private readonly IAnalyticsService _analyticsService;
        private readonly IFamilyService _familyService;
        private readonly ILogger<AnalyticsController> _logger;

        public AnalyticsController(
            IAnalyticsService analyticsService,
            IFamilyService familyService,
            ILogger<AnalyticsController> logger)
        {
            _analyticsService = analyticsService;
            _familyService = familyService;
            _logger = logger;
        }

        [HttpGet("metrics")]
        public async Task<IActionResult> GetMetrics(
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate,
            [FromQuery] string period = "Monthly",
            [FromQuery] int? familyId = null,
            [FromQuery] int? categoryId = null,
            [FromQuery] int? accountId = null)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // If family ID is provided, check if user is a member
                if (familyId.HasValue)
                {
                    var isFamilyMember = await _familyService.IsFamilyMemberAsync(familyId.Value, userId);
                    if (!isFamilyMember)
                    {
                        return Forbid();
                    }
                }

                var metrics = await _analyticsService.GetMetricsAsync(
                    userId,
                    startDate,
                    endDate,
                    period,
                    familyId,
                    categoryId,
                    accountId);

                var metricDtos = metrics.Select(m => new
                {
                    m.Id,
                    m.Name,
                    m.Type,
                    m.Value,
                    m.Date,
                    m.Period,
                    m.CategoryId,
                    m.AccountId,
                    m.UserId,
                    m.FamilyId,
                    m.Metadata,
                    m.CreatedAt,
                    m.UpdatedAt
                }).ToList();

                return Ok(metricDtos);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Resource not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting metrics");
                return StatusCode(500, new { message = "An error occurred while retrieving metrics" });
            }
        }

        [HttpGet("category-breakdown")]
        public async Task<IActionResult> GetCategoryBreakdown(
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate,
            [FromQuery] int? familyId = null)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // If family ID is provided, check if user is a member
                if (familyId.HasValue)
                {
                    var isFamilyMember = await _familyService.IsFamilyMemberAsync(familyId.Value, userId);
                    if (!isFamilyMember)
                    {
                        return Forbid();
                    }
                }

                var categoryBreakdown = await _analyticsService.GetCategoryBreakdownAsync(
                    userId,
                    startDate,
                    endDate,
                    familyId);

                return Ok(categoryBreakdown);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting category breakdown");
                return StatusCode(500, new { message = "An error occurred while retrieving category breakdown" });
            }
        }

        [HttpGet("trend")]
        public async Task<IActionResult> GetTrendData(
            [FromQuery] string metricType,
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate,
            [FromQuery] string period = "Monthly",
            [FromQuery] int? familyId = null,
            [FromQuery] int? categoryId = null)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // If family ID is provided, check if user is a member
                if (familyId.HasValue)
                {
                    var isFamilyMember = await _familyService.IsFamilyMemberAsync(familyId.Value, userId);
                    if (!isFamilyMember)
                    {
                        return Forbid();
                    }
                }

                var trendData = await _analyticsService.GetTrendDataAsync(
                    userId,
                    metricType,
                    startDate,
                    endDate,
                    period,
                    familyId,
                    categoryId);

                // Convert to array of objects for JSON serialization
                var result = trendData.Select(kv => new
                {
                    Date = kv.Key,
                    Value = kv.Value
                }).ToList();

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting trend data");
                return StatusCode(500, new { message = "An error occurred while retrieving trend data" });
            }
        }

        [HttpGet("net-worth")]
        public async Task<IActionResult> GetNetWorth(
            [FromQuery] DateTime? asOfDate = null,
            [FromQuery] int? familyId = null)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // If family ID is provided, check if user is a member
                if (familyId.HasValue)
                {
                    var isFamilyMember = await _familyService.IsFamilyMemberAsync(familyId.Value, userId);
                    if (!isFamilyMember)
                    {
                        return Forbid();
                    }
                }

                var netWorth = await _analyticsService.CalculateNetWorthAsync(
                    userId,
                    asOfDate,
                    familyId);

                return Ok(new { NetWorth = netWorth, AsOfDate = asOfDate ?? DateTime.UtcNow });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating net worth");
                return StatusCode(500, new { message = "An error occurred while calculating net worth" });
            }
        }

        [HttpGet("net-worth-trend")]
        public async Task<IActionResult> GetNetWorthTrend(
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate,
            [FromQuery] string period = "Monthly",
            [FromQuery] int? familyId = null)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // If family ID is provided, check if user is a member
                if (familyId.HasValue)
                {
                    var isFamilyMember = await _familyService.IsFamilyMemberAsync(familyId.Value, userId);
                    if (!isFamilyMember)
                    {
                        return Forbid();
                    }
                }

                var netWorthTrend = await _analyticsService.GetNetWorthTrendAsync(
                    userId,
                    startDate,
                    endDate,
                    period,
                    familyId);

                // Convert to array of objects for JSON serialization
                var result = netWorthTrend.Select(kv => new
                {
                    Date = kv.Key,
                    NetWorth = kv.Value
                }).ToList();

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting net worth trend");
                return StatusCode(500, new { message = "An error occurred while retrieving net worth trend" });
            }
        }

        [HttpGet("cashflow")]
        public async Task<IActionResult> AnalyzeCashflow(
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate,
            [FromQuery] int? familyId = null)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // If family ID is provided, check if user is a member
                if (familyId.HasValue)
                {
                    var isFamilyMember = await _familyService.IsFamilyMemberAsync(familyId.Value, userId);
                    if (!isFamilyMember)
                    {
                        return Forbid();
                    }
                }

                var cashflowAnalysis = await _analyticsService.AnalyzeCashflowAsync(
                    userId,
                    startDate,
                    endDate,
                    familyId);

                return Ok(cashflowAnalysis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error analyzing cashflow");
                return StatusCode(500, new { message = "An error occurred while analyzing cashflow" });
            }
        }

        [HttpGet("anomalies")]
        public async Task<IActionResult> DetectAnomalies(
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate,
            [FromQuery] int? familyId = null)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // If family ID is provided, check if user is a member
                if (familyId.HasValue)
                {
                    var isFamilyMember = await _familyService.IsFamilyMemberAsync(familyId.Value, userId);
                    if (!isFamilyMember)
                    {
                        return Forbid();
                    }
                }

                var anomalies = await _analyticsService.DetectAnomaliesAsync(
                    userId,
                    startDate,
                    endDate,
                    familyId);

                var anomalyDtos = anomalies.Select(a => new
                {
                    a.Id,
                    a.AnomalyType,
                    a.Severity,
                    a.Description,
                    a.TransactionId,
                    a.CategoryId,
                    a.AccountId,
                    a.DetectedAt,
                    Status = a.Status.ToString(),
                    a.ResolvedAt,
                    a.ResolutionNotes,
                    a.CreatedAt,
                    a.UpdatedAt
                }).ToList();

                return Ok(anomalyDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error detecting anomalies");
                return StatusCode(500, new { message = "An error occurred while detecting anomalies" });
            }
        }

        [HttpGet("anomalies/{id}")]
        public async Task<IActionResult> GetAnomaly(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }
                var anomaly = await _analyticsService.GetAnomalyByIdAsync(id);

                // Check if user can access the anomaly
                if (anomaly.UserId != userId)
                {
                    // If anomaly is for a family, check if user is a member
                    if (anomaly.FamilyId.HasValue)
                    {
                        var isFamilyMember = await _familyService.IsFamilyMemberAsync(anomaly.FamilyId.Value, userId);
                        if (!isFamilyMember)
                        {
                            return Forbid();
                        }
                    }
                    else
                    {
                        return Forbid();
                    }
                }

                var anomalyDto = new
                {
                    anomaly.Id,
                    anomaly.AnomalyType,
                    anomaly.Severity,
                    anomaly.Description,
                    anomaly.TransactionId,
                    TransactionDescription = anomaly.Transaction?.Description,
                    anomaly.CategoryId,
                    CategoryName = anomaly.Category?.Name,
                    anomaly.AccountId,
                    AccountName = anomaly.Account?.Name,
                    anomaly.DetectedAt,
                    Status = anomaly.Status.ToString(),
                    anomaly.ResolvedAt,
                    anomaly.ResolutionNotes,
                    anomaly.CreatedAt,
                    anomaly.UpdatedAt
                };

                return Ok(anomalyDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Resource not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting anomaly");
                return StatusCode(500, new { message = "An error occurred while retrieving the anomaly" });
            }
        }

        [HttpPut("anomalies/{id}/status")]
        public async Task<IActionResult> UpdateAnomalyStatus(int id, [FromBody] UpdateAnomalyStatusDto updateDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }
                var anomaly = await _analyticsService.GetAnomalyByIdAsync(id);

                // Check if user can access the anomaly
                if (anomaly.UserId != userId)
                {
                    // If anomaly is for a family, check if user is a member
                    if (anomaly.FamilyId.HasValue)
                    {
                        var isFamilyMember = await _familyService.IsFamilyMemberAsync(anomaly.FamilyId.Value, userId);
                        if (!isFamilyMember)
                        {
                            return Forbid();
                        }
                    }
                    else
                    {
                        return Forbid();
                    }
                }

                await _analyticsService.UpdateAnomalyStatusAsync(
                    id,
                    updateDto.Status,
                    updateDto.ResolutionNotes ?? string.Empty);

                return Ok(new { message = $"Anomaly status updated to {updateDto.Status}" });
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Resource not found: {Message}", ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating anomaly status");
                return StatusCode(500, new { message = "An error occurred while updating the anomaly status" });
            }
        }
    }

    public class UpdateAnomalyStatusDto
    {
        [Required]
        public AnomalyStatus Status { get; set; }

        public string? ResolutionNotes { get; set; }
    }
}
