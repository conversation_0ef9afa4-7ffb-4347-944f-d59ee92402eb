using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.API.Middleware;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ExpenseApprovalController : ControllerBase
    {
        private readonly IApprovalService _approvalService;
        private readonly IFamilyService _familyService;
        private readonly ILogger<ExpenseApprovalController> _logger;

        public ExpenseApprovalController(
            IApprovalService approvalService,
            IFamilyService familyService,
            ILogger<ExpenseApprovalController> logger)
        {
            _approvalService = approvalService;
            _familyService = familyService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetApprovalRequests(
            [FromQuery] bool asRequester = true,
            [FromQuery] string? status = null)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                ApprovalStatus? statusEnum = null;
                if (!string.IsNullOrEmpty(status) && Enum.TryParse<ApprovalStatus>(status, true, out var parsedStatus))
                {
                    statusEnum = parsedStatus;
                }

                var approvalRequests = await _approvalService.GetApprovalRequestsByUserIdAsync(userId, asRequester, statusEnum);

                var approvalRequestDtos = approvalRequests.Select(ar => new
                {
                    ar.Id,
                    ar.RequestedByUserId,
                    RequesterUsername = ar.RequestedByUser?.Username,
                    RequesterFullName = $"{ar.RequestedByUser?.FirstName} {ar.RequestedByUser?.LastName}",
                    ar.ApprovedByUserId,
                    ApproverUsername = ar.ApprovedByUser?.Username,
                    ApproverFullName = ar.ApprovedByUser != null ? $"{ar.ApprovedByUser.FirstName} {ar.ApprovedByUser.LastName}" : null,
                    ar.FamilyId,
                    FamilyName = ar.Family?.Name,
                    ar.Amount,
                    ar.Description,
                    ar.CategoryId,
                    CategoryName = ar.Category?.Name,
                    Status = ar.Status.ToString(),
                    ar.RejectionReason,
                    ar.RequestedAt,
                    ar.RespondedAt,
                    ar.ResultingTransactionId,
                    ar.CreatedAt,
                    ar.UpdatedAt
                }).ToList();

                return Ok(approvalRequestDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting approval requests");
                return StatusCode(500, new { message = "An error occurred while retrieving approval requests" });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetApprovalRequest(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }
                var approvalRequest = await _approvalService.GetApprovalRequestByIdAsync(id);

                // Check if user can access the approval request
                if (approvalRequest.RequestedByUserId != userId)
                {
                    // If user is not the requester, check if user is a family admin
                    var isFamilyAdmin = await _familyService.IsFamilyAdminAsync(approvalRequest.FamilyId, userId);
                    if (!isFamilyAdmin)
                    {
                        return Forbid();
                    }
                }

                var approvalRequestDto = new
                {
                    approvalRequest.Id,
                    approvalRequest.RequestedByUserId,
                    RequesterUsername = approvalRequest.RequestedByUser?.Username,
                    RequesterFullName = $"{approvalRequest.RequestedByUser?.FirstName} {approvalRequest.RequestedByUser?.LastName}",
                    approvalRequest.ApprovedByUserId,
                    ApproverUsername = approvalRequest.ApprovedByUser?.Username,
                    ApproverFullName = approvalRequest.ApprovedByUser != null ? $"{approvalRequest.ApprovedByUser.FirstName} {approvalRequest.ApprovedByUser.LastName}" : null,
                    approvalRequest.FamilyId,
                    FamilyName = approvalRequest.Family?.Name,
                    approvalRequest.Amount,
                    approvalRequest.Description,
                    approvalRequest.CategoryId,
                    CategoryName = approvalRequest.Category?.Name,
                    Status = approvalRequest.Status.ToString(),
                    approvalRequest.RejectionReason,
                    approvalRequest.RequestedAt,
                    approvalRequest.RespondedAt,
                    approvalRequest.ResultingTransactionId,
                    approvalRequest.CreatedAt,
                    approvalRequest.UpdatedAt
                };

                return Ok(approvalRequestDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting approval request");
                return StatusCode(500, new { message = "An error occurred while retrieving the approval request" });
            }
        }

        [HttpGet("family/{familyId}")]
        [RequireFamilyRole("familyId", "Admin")]
        public async Task<IActionResult> GetFamilyApprovalRequests(
            int familyId,
            [FromQuery] string? status = null)
        {
            try
            {
                ApprovalStatus? statusEnum = null;
                if (!string.IsNullOrEmpty(status) && Enum.TryParse<ApprovalStatus>(status, true, out var parsedStatus))
                {
                    statusEnum = parsedStatus;
                }

                var approvalRequests = await _approvalService.GetApprovalRequestsByFamilyIdAsync(familyId, statusEnum);

                var approvalRequestDtos = approvalRequests.Select(ar => new
                {
                    ar.Id,
                    ar.RequestedByUserId,
                    RequesterUsername = ar.RequestedByUser?.Username,
                    RequesterFullName = $"{ar.RequestedByUser?.FirstName} {ar.RequestedByUser?.LastName}",
                    ar.ApprovedByUserId,
                    ApproverUsername = ar.ApprovedByUser?.Username,
                    ApproverFullName = ar.ApprovedByUser != null ? $"{ar.ApprovedByUser.FirstName} {ar.ApprovedByUser.LastName}" : null,
                    ar.FamilyId,
                    ar.Amount,
                    ar.Description,
                    ar.CategoryId,
                    CategoryName = ar.Category?.Name,
                    Status = ar.Status.ToString(),
                    ar.RejectionReason,
                    ar.RequestedAt,
                    ar.RespondedAt,
                    ar.ResultingTransactionId,
                    ar.CreatedAt,
                    ar.UpdatedAt
                }).ToList();

                return Ok(approvalRequestDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting family approval requests");
                return StatusCode(500, new { message = "An error occurred while retrieving family approval requests" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateApprovalRequest([FromBody] CreateApprovalRequestDto createApprovalRequestDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Check if user is a member of the family
                var isFamilyMember = await _familyService.IsFamilyMemberAsync(createApprovalRequestDto.FamilyId, userId);
                if (!isFamilyMember)
                {
                    return Forbid();
                }

                var approvalRequest = await _approvalService.CreateApprovalRequestAsync(
                    userId,
                    createApprovalRequestDto.FamilyId,
                    createApprovalRequestDto.Amount,
                    createApprovalRequestDto.Description,
                    createApprovalRequestDto.CategoryId);

                var approvalRequestDto = new
                {
                    approvalRequest.Id,
                    approvalRequest.RequestedByUserId,
                    approvalRequest.FamilyId,
                    approvalRequest.Amount,
                    approvalRequest.Description,
                    approvalRequest.CategoryId,
                    Status = approvalRequest.Status.ToString(),
                    approvalRequest.RequestedAt,
                    approvalRequest.CreatedAt
                };

                return CreatedAtAction(nameof(GetApprovalRequest), new { id = approvalRequest.Id }, approvalRequestDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating approval request");
                return StatusCode(500, new { message = "An error occurred while creating the approval request" });
            }
        }

        [HttpPost("{id}/approve")]
        public async Task<IActionResult> ApproveRequest(int id, [FromBody] ApproveRequestDto approveRequestDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }
                var approvalRequest = await _approvalService.GetApprovalRequestByIdAsync(id);

                // Check if user is a family admin
                var isFamilyAdmin = await _familyService.IsFamilyAdminAsync(approvalRequest.FamilyId, userId);
                if (!isFamilyAdmin)
                {
                    return Forbid();
                }

                var updatedRequest = await _approvalService.ApproveRequestAsync(
                    id,
                    userId,
                    approveRequestDto.AccountId);

                var approvalRequestDto = new
                {
                    updatedRequest.Id,
                    updatedRequest.RequestedByUserId,
                    RequesterUsername = updatedRequest.RequestedByUser?.Username,
                    updatedRequest.ApprovedByUserId,
                    ApproverUsername = updatedRequest.ApprovedByUser?.Username,
                    updatedRequest.FamilyId,
                    updatedRequest.Amount,
                    updatedRequest.Description,
                    updatedRequest.CategoryId,
                    CategoryName = updatedRequest.Category?.Name,
                    Status = updatedRequest.Status.ToString(),
                    updatedRequest.RequestedAt,
                    updatedRequest.RespondedAt,
                    updatedRequest.ResultingTransactionId,
                    updatedRequest.CreatedAt,
                    updatedRequest.UpdatedAt
                };

                return Ok(approvalRequestDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving request");
                return StatusCode(500, new { message = "An error occurred while approving the request" });
            }
        }

        [HttpPost("{id}/reject")]
        public async Task<IActionResult> RejectRequest(int id, [FromBody] RejectRequestDto rejectRequestDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }
                var approvalRequest = await _approvalService.GetApprovalRequestByIdAsync(id);

                // Check if user is a family admin
                var isFamilyAdmin = await _familyService.IsFamilyAdminAsync(approvalRequest.FamilyId, userId);
                if (!isFamilyAdmin)
                {
                    return Forbid();
                }

                var updatedRequest = await _approvalService.RejectRequestAsync(
                    id,
                    userId,
                    rejectRequestDto.RejectionReason);

                var approvalRequestDto = new
                {
                    updatedRequest.Id,
                    updatedRequest.RequestedByUserId,
                    RequesterUsername = updatedRequest.RequestedByUser?.Username,
                    updatedRequest.ApprovedByUserId,
                    ApproverUsername = updatedRequest.ApprovedByUser?.Username,
                    updatedRequest.FamilyId,
                    updatedRequest.Amount,
                    updatedRequest.Description,
                    updatedRequest.CategoryId,
                    CategoryName = updatedRequest.Category?.Name,
                    Status = updatedRequest.Status.ToString(),
                    updatedRequest.RejectionReason,
                    updatedRequest.RequestedAt,
                    updatedRequest.RespondedAt,
                    updatedRequest.CreatedAt,
                    updatedRequest.UpdatedAt
                };

                return Ok(approvalRequestDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting request");
                return StatusCode(500, new { message = "An error occurred while rejecting the request" });
            }
        }

        [HttpGet("check-required")]
        public async Task<IActionResult> CheckApprovalRequired(
            [FromQuery] int familyId,
            [FromQuery] decimal amount,
            [FromQuery] int categoryId)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Check if user is a member of the family
                var isFamilyMember = await _familyService.IsFamilyMemberAsync(familyId, userId);
                if (!isFamilyMember)
                {
                    return Forbid();
                }

                var requiresApproval = await _approvalService.RequiresApprovalAsync(
                    familyId,
                    userId,
                    amount,
                    categoryId);

                return Ok(new { RequiresApproval = requiresApproval });
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if approval is required");
                return StatusCode(500, new { message = "An error occurred while checking if approval is required" });
            }
        }
    }

    public class CreateApprovalRequestDto
    {
        [Required]
        public int FamilyId { get; set; }

        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal Amount { get; set; }

        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [Required]
        public int CategoryId { get; set; }
    }

    public class ApproveRequestDto
    {
        [Required]
        public int AccountId { get; set; }
    }

    public class RejectRequestDto
    {
        [Required]
        [StringLength(255)]
        public string RejectionReason { get; set; } = string.Empty;
    }
}
