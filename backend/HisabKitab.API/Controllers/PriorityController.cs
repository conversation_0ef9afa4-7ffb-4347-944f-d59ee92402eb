using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.API.DTOs;
using HisabKitab.API.Middleware;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/priorities")]
    [Authorize]
    public class PriorityController : ControllerBase
    {
        private readonly IPriorityService _priorityService;
        private readonly ILogger<PriorityController> _logger;

        public PriorityController(
            IPriorityService priorityService,
            ILogger<PriorityController> logger)
        {
            _priorityService = priorityService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllPriorities()
        {
            try
            {
                var priorities = await _priorityService.GetAllPrioritiesAsync();
                var priorityDtos = priorities.Select(p => new PriorityDto
                {
                    Id = p.Id,
                    Name = p.Name,
                    Description = p.Description,
                    Level = p.Level,
                    Color = p.Color,
                    CreatedAt = p.CreatedAt,
                    UpdatedAt = p.UpdatedAt
                }).ToList();

                return Ok(priorityDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting priorities");
                return StatusCode(500, new { message = "An error occurred while retrieving priorities" });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetPriority(int id)
        {
            try
            {
                var priority = await _priorityService.GetPriorityByIdAsync(id);
                var priorityDto = new PriorityDto
                {
                    Id = priority.Id,
                    Name = priority.Name,
                    Description = priority.Description,
                    Level = priority.Level,
                    Color = priority.Color,
                    CreatedAt = priority.CreatedAt,
                    UpdatedAt = priority.UpdatedAt
                };

                return Ok(priorityDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Priority not found");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting priority");
                return StatusCode(500, new { message = "An error occurred while retrieving the priority" });
            }
        }

        [HttpPost]
        [RequireRole("PlatformAdmin")]
        public async Task<IActionResult> CreatePriority([FromBody] CreatePriorityDto createPriorityDto)
        {
            try
            {
                var priority = await _priorityService.CreatePriorityAsync(
                    createPriorityDto.Name,
                    createPriorityDto.Description,
                    createPriorityDto.Level,
                    createPriorityDto.Color
                );

                var priorityDto = new PriorityDto
                {
                    Id = priority.Id,
                    Name = priority.Name,
                    Description = priority.Description,
                    Level = priority.Level,
                    Color = priority.Color,
                    CreatedAt = priority.CreatedAt,
                    UpdatedAt = priority.UpdatedAt
                };

                return CreatedAtAction(nameof(GetPriority), new { id = priority.Id }, priorityDto);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid priority data");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating priority");
                return StatusCode(500, new { message = "An error occurred while creating the priority" });
            }
        }

        [HttpPut("{id}")]
        [RequireRole("PlatformAdmin")]
        public async Task<IActionResult> UpdatePriority(int id, [FromBody] UpdatePriorityDto updatePriorityDto)
        {
            try
            {
                var priority = await _priorityService.UpdatePriorityAsync(
                    id,
                    updatePriorityDto.Name,
                    updatePriorityDto.Description,
                    updatePriorityDto.Level,
                    updatePriorityDto.Color
                );

                var priorityDto = new PriorityDto
                {
                    Id = priority.Id,
                    Name = priority.Name,
                    Description = priority.Description,
                    Level = priority.Level,
                    Color = priority.Color,
                    CreatedAt = priority.CreatedAt,
                    UpdatedAt = priority.UpdatedAt
                };

                return Ok(priorityDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Priority not found");
                return NotFound(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid priority data");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating priority");
                return StatusCode(500, new { message = "An error occurred while updating the priority" });
            }
        }

        [HttpDelete("{id}")]
        [RequireRole("PlatformAdmin")]
        public async Task<IActionResult> DeletePriority(int id)
        {
            try
            {
                await _priorityService.DeletePriorityAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Priority not found");
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Cannot delete priority");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting priority");
                return StatusCode(500, new { message = "An error occurred while deleting the priority" });
            }
        }

    }
}
