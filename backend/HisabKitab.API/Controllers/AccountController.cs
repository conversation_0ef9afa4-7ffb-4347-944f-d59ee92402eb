using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.API.DTOs;
using HisabKitab.API.Middleware;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/accounts")]
    [Authorize]
    public class AccountController : ControllerBase
    {
        private readonly IAccountService _accountService;
        private readonly IFamilyService _familyService;
        private readonly ILogger<AccountController> _logger;

        public AccountController(
            IAccountService accountService,
            IFamilyService familyService,
            ILogger<AccountController> logger)
        {
            _accountService = accountService;
            _familyService = familyService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetUserAccounts()
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                var userId = int.Parse(userIdClaim);
                var accounts = await _accountService.GetAccountsByUserIdAsync(userId);
                var sharedAccounts = await _accountService.GetSharedAccountsForUserAsync(userId);

                var allAccounts = accounts.Concat(sharedAccounts).Distinct();

                var accountDtos = allAccounts.Select(a => new AccountDto
                {
                    Id = a.Id,
                    Name = a.Name,
                    AccountType = a.AccountType,
                    Balance = a.Balance,
                    InitialBalance = a.InitialBalance,
                    Currency = a.Currency,
                    UserId = a.UserId,
                    FamilyId = a.FamilyId,
                    IsActive = a.IsActive,
                    ExcludeFromStats = a.ExcludeFromStats,
                    LastUpdatedAt = a.LastUpdatedAt,
                    CreatedAt = a.CreatedAt,
                    UpdatedAt = a.UpdatedAt,
                    SharedWith = a.SharedWith?.Select(s => new AccountShareDto
                    {
                        Id = s.Id,
                        AccountId = s.AccountId,
                        FamilyMemberId = s.FamilyMemberId,
                        Permissions = s.Permissions,
                        SharedAt = s.SharedAt,
                        MemberUsername = s.FamilyMember?.User?.Username ?? string.Empty,
                        MemberFullName = $"{s.FamilyMember?.User?.FirstName ?? ""} {s.FamilyMember?.User?.LastName ?? ""}"
                    }).ToList() ?? new List<AccountShareDto>()
                }).ToList();

                return Ok(accountDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user accounts");
                return StatusCode(500, new { message = "An error occurred while retrieving accounts" });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetAccount(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                var userId = int.Parse(userIdClaim);

                // Check if the user can access this account
                var canAccess = await _accountService.UserCanAccessAccountAsync(id, userId);
                if (!canAccess)
                {
                    return Forbid();
                }

                var account = await _accountService.GetAccountByIdAsync(id);
                var accountShares = await _accountService.GetAccountSharesAsync(id);

                var accountDto = new AccountDto
                {
                    Id = account.Id,
                    Name = account.Name,
                    AccountType = account.AccountType,
                    Balance = account.Balance,
                    InitialBalance = account.InitialBalance,
                    Currency = account.Currency,
                    UserId = account.UserId,
                    FamilyId = account.FamilyId,
                    IsActive = account.IsActive,
                    ExcludeFromStats = account.ExcludeFromStats,
                    LastUpdatedAt = account.LastUpdatedAt,
                    CreatedAt = account.CreatedAt,
                    UpdatedAt = account.UpdatedAt,
                    SharedWith = accountShares.Select(s => new AccountShareDto
                    {
                        Id = s.Id,
                        AccountId = s.AccountId,
                        FamilyMemberId = s.FamilyMemberId,
                        Permissions = s.Permissions,
                        SharedAt = s.SharedAt,
                        MemberUsername = s.FamilyMember?.User?.Username ?? string.Empty,
                        MemberFullName = $"{s.FamilyMember?.User?.FirstName ?? ""} {s.FamilyMember?.User?.LastName ?? ""}"
                    }).ToList()
                };

                return Ok(accountDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Account not found");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting account");
                return StatusCode(500, new { message = "An error occurred while retrieving the account" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateAccount([FromBody] CreateAccountDto createAccountDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                var userId = int.Parse(userIdClaim);

                // If familyId is provided, check if the user is a member of the family
                if (createAccountDto.FamilyId.HasValue)
                {
                    var isFamilyAdmin = await _familyService.IsFamilyAdminAsync(createAccountDto.FamilyId.Value, userId);
                    if (!isFamilyAdmin)
                    {
                        return Forbid();
                    }
                }

                var account = await _accountService.CreateAccountAsync(
                    createAccountDto.Name,
                    createAccountDto.InitialBalance,
                    (int)createAccountDto.AccountType,
                    createAccountDto.Currency,
                    createAccountDto.FamilyId.HasValue ? null : userId,
                    createAccountDto.FamilyId,
                    createAccountDto.ExcludeFromStats
                );

                var accountDto = new AccountDto
                {
                    Id = account.Id,
                    Name = account.Name,
                    AccountType = account.AccountType,
                    Balance = account.Balance,
                    InitialBalance = account.InitialBalance,
                    Currency = account.Currency,
                    UserId = account.UserId,
                    FamilyId = account.FamilyId,
                    IsActive = account.IsActive,
                    ExcludeFromStats = account.ExcludeFromStats,
                    LastUpdatedAt = account.LastUpdatedAt,
                    CreatedAt = account.CreatedAt,
                    UpdatedAt = account.UpdatedAt,
                    SharedWith = new List<AccountShareDto>()
                };

                return CreatedAtAction(nameof(GetAccount), new { id = account.Id }, accountDto);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid account data");
                return BadRequest(new { message = ex.Message });
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Entity not found");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating account");
                return StatusCode(500, new { message = "An error occurred while creating the account" });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateAccount(int id, [FromBody] UpdateAccountDto updateAccountDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                var userId = int.Parse(userIdClaim);

                // Check if the user can modify this account
                var canModify = await _accountService.UserCanModifyAccountAsync(id, userId);
                if (!canModify)
                {
                    return Forbid();
                }

                var account = await _accountService.UpdateAccountAsync(
                    id,
                    updateAccountDto.Name,
                    (int)updateAccountDto.AccountType,
                    updateAccountDto.InitialBalance,
                    updateAccountDto.Currency,
                    updateAccountDto.IsActive,
                    updateAccountDto.ExcludeFromStats
                );

                var accountDto = new AccountDto
                {
                    Id = account.Id,
                    Name = account.Name,
                    AccountType = account.AccountType,
                    Balance = account.Balance,
                    InitialBalance = account.InitialBalance,
                    Currency = account.Currency,
                    UserId = account.UserId,
                    FamilyId = account.FamilyId,
                    IsActive = account.IsActive,
                    ExcludeFromStats = account.ExcludeFromStats,
                    LastUpdatedAt = account.LastUpdatedAt,
                    CreatedAt = account.CreatedAt,
                    UpdatedAt = account.UpdatedAt
                };

                return Ok(accountDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Account not found");
                return NotFound(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid account data");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating account");
                return StatusCode(500, new { message = "An error occurred while updating the account" });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteAccount(int id)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                var userId = int.Parse(userIdClaim);

                // Check if the user can modify this account
                var canModify = await _accountService.UserCanModifyAccountAsync(id, userId);
                if (!canModify)
                {
                    return Forbid();
                }

                await _accountService.DeleteAccountAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Account not found");
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Cannot delete account");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting account");
                return StatusCode(500, new { message = "An error occurred while deleting the account" });
            }
        }

        [HttpPut("{id}/balance")]
        public async Task<IActionResult> UpdateBalance(int id, [FromBody] UpdateBalanceDto updateBalanceDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                var userId = int.Parse(userIdClaim);

                // Check if the user can modify this account
                var canModify = await _accountService.UserCanModifyAccountAsync(id, userId);
                if (!canModify)
                {
                    return Forbid();
                }

                var account = await _accountService.UpdateAccountBalanceAsync(id, updateBalanceDto.NewBalance);

                var accountDto = new AccountDto
                {
                    Id = account.Id,
                    Name = account.Name,
                    AccountType = account.AccountType,
                    Balance = account.Balance,
                    InitialBalance = account.InitialBalance,
                    Currency = account.Currency,
                    UserId = account.UserId,
                    FamilyId = account.FamilyId,
                    IsActive = account.IsActive,
                    ExcludeFromStats = account.ExcludeFromStats,
                    LastUpdatedAt = account.LastUpdatedAt,
                    CreatedAt = account.CreatedAt,
                    UpdatedAt = account.UpdatedAt
                };

                return Ok(accountDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Account not found");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating account balance");
                return StatusCode(500, new { message = "An error occurred while updating the account balance" });
            }
        }

        [HttpPost("{id}/share")]
        public async Task<IActionResult> ShareAccount(int id, [FromBody] ShareAccountDto shareAccountDto)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                var userId = int.Parse(userIdClaim);

                // Check if the user can modify this account
                var canModify = await _accountService.UserCanModifyAccountAsync(id, userId);
                if (!canModify)
                {
                    return Forbid();
                }

                var accountShare = await _accountService.ShareAccountWithFamilyMemberAsync(
                    id,
                    shareAccountDto.FamilyMemberId,
                    shareAccountDto.Permissions
                );

                var accountShareDto = new AccountShareDto
                {
                    Id = accountShare.Id,
                    AccountId = accountShare.AccountId,
                    FamilyMemberId = accountShare.FamilyMemberId,
                    Permissions = accountShare.Permissions,
                    SharedAt = accountShare.SharedAt,
                    MemberUsername = accountShare.FamilyMember?.User?.Username ?? string.Empty,
                    MemberFullName = $"{accountShare.FamilyMember?.User?.FirstName ?? ""} {accountShare.FamilyMember?.User?.LastName ?? ""}"
                };

                return Ok(accountShareDto);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Entity not found");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sharing account");
                return StatusCode(500, new { message = "An error occurred while sharing the account" });
            }
        }

        [HttpDelete("share/{shareId}")]
        public async Task<IActionResult> RemoveAccountShare(int shareId)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                var userId = int.Parse(userIdClaim);

                // First get the account share to check permissions
                var accountShare = await _accountService.GetAccountShareByIdAsync(shareId);

                if (accountShare == null)
                {
                    return NotFound(new { message = $"Account share with ID {shareId} not found" });
                }

                // Check if the user can modify this account
                var canModify = await _accountService.UserCanModifyAccountAsync(accountShare.AccountId, userId);
                if (!canModify)
                {
                    return Forbid();
                }

                // Try to remove the share
                await _accountService.RemoveAccountShareAsync(shareId);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Account share not found");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing account share");
                return StatusCode(500, new { message = "An error occurred while removing the account share" });
            }
        }

        [HttpGet("family/{familyId}")]
        public async Task<IActionResult> GetFamilyAccounts(int familyId)
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                var userId = int.Parse(userIdClaim);

                // Check if the user is a member of the family
                var isFamilyMember = await _familyService.IsFamilyMemberAsync(familyId, userId);
                if (!isFamilyMember)
                {
                    return Forbid();
                }

                var accounts = await _accountService.GetAccountsByFamilyIdAsync(familyId);

                var accountDtos = accounts.Select(a => new AccountDto
                {
                    Id = a.Id,
                    Name = a.Name,
                    AccountType = a.AccountType,
                    Balance = a.Balance,
                    InitialBalance = a.InitialBalance,
                    Currency = a.Currency,
                    UserId = a.UserId,
                    FamilyId = a.FamilyId,
                    IsActive = a.IsActive,
                    ExcludeFromStats = a.ExcludeFromStats,
                    LastUpdatedAt = a.LastUpdatedAt,
                    CreatedAt = a.CreatedAt,
                    UpdatedAt = a.UpdatedAt
                }).ToList();

                return Ok(accountDtos);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Family not found");
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting family accounts");
                return StatusCode(500, new { message = "An error occurred while retrieving family accounts" });
            }
        }
    }
}
