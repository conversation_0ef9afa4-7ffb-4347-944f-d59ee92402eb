using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using HisabKitab.API.DTOs;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Interfaces;

namespace HisabKitab.API.Controllers
{
    [Route("api/wishlist-items")]
    [Authorize]
    public class WishlistItemController : BaseApiController
    {
        private readonly ISavingsService _savingsService;
        private readonly ILogger<WishlistItemController> _logger;

        public WishlistItemController(ISavingsService savingsService, ILogger<WishlistItemController> logger)
        {
            _savingsService = savingsService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<WishlistItemDto>>> GetWishlistItems([FromQuery] string? status = null)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }
                var wishlistItems = await _savingsService.GetWishlistItemsByUserIdAsync(userId, status);

                var wishlistItemDtos = wishlistItems.Select(MapToWishlistItemDto).ToList();

                return Ok(wishlistItemDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting wishlist items");
                return StatusCode(500, "An error occurred while retrieving wishlist items");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<WishlistItemDto>> GetWishlistItem(int id)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Check if user can access the wishlist item
                if (!await _savingsService.UserCanAccessWishlistItemAsync(id, userId))
                {
                    return Forbid();
                }

                var wishlistItem = await _savingsService.GetWishlistItemByIdAsync(id);
                var wishlistItemDto = MapToWishlistItemDto(wishlistItem);

                return Ok(wishlistItemDto);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting wishlist item with ID {id}");
                return StatusCode(500, "An error occurred while retrieving the wishlist item");
            }
        }

        [HttpPost]
        public async Task<ActionResult<WishlistItemDto>> CreateWishlistItem(CreateWishlistItemDto createDto)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                var wishlistItem = await _savingsService.CreateWishlistItemAsync(
                    createDto.Title,
                    createDto.Description,
                    createDto.EstimatedPrice,
                    createDto.ProductUrl,
                    createDto.ImageUrl,
                    createDto.TargetPurchaseDate,
                    userId,
                    createDto.IsShared ? createDto.FamilyId : null,
                    createDto.IsShared,
                    createDto.PriorityId,
                    createDto.LinkedSavingsGoalId);

                var wishlistItemDto = MapToWishlistItemDto(wishlistItem);

                return CreatedAtAction(nameof(GetWishlistItem), new { id = wishlistItemDto.Id }, wishlistItemDto);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating wishlist item");
                return StatusCode(500, "An error occurred while creating the wishlist item");
            }
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<WishlistItemDto>> UpdateWishlistItem(int id, UpdateWishlistItemDto updateDto)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Check if user can modify the wishlist item
                if (!await _savingsService.UserCanModifyWishlistItemAsync(id, userId))
                {
                    return Forbid();
                }

                var wishlistItem = await _savingsService.UpdateWishlistItemAsync(
                    id,
                    updateDto.Title,
                    updateDto.Description,
                    updateDto.EstimatedPrice,
                    updateDto.ProductUrl,
                    updateDto.ImageUrl,
                    updateDto.TargetPurchaseDate,
                    updateDto.PriorityId,
                    updateDto.Status);

                var wishlistItemDto = MapToWishlistItemDto(wishlistItem);

                return Ok(wishlistItemDto);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating wishlist item with ID {id}");
                return StatusCode(500, "An error occurred while updating the wishlist item");
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteWishlistItem(int id)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Check if user can modify the wishlist item
                if (!await _savingsService.UserCanModifyWishlistItemAsync(id, userId))
                {
                    return Forbid();
                }

                await _savingsService.DeleteWishlistItemAsync(id);

                return Ok();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting wishlist item with ID {id}");
                return StatusCode(500, "An error occurred while deleting the wishlist item");
            }
        }

        [HttpPost("{id}/link/{goalId}")]
        public async Task<ActionResult> LinkToSavingsGoal(int id, int goalId)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Check if user can modify the wishlist item
                if (!await _savingsService.UserCanModifyWishlistItemAsync(id, userId))
                {
                    return Forbid();
                }

                // Check if user can access the savings goal
                if (!await _savingsService.UserCanAccessSavingsGoalAsync(goalId, userId))
                {
                    return Forbid();
                }

                await _savingsService.LinkWishlistItemToSavingsGoalAsync(id, goalId);

                return Ok();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error linking wishlist item {id} to savings goal {goalId}");
                return StatusCode(500, "An error occurred while linking the wishlist item to the savings goal");
            }
        }

        [HttpPost("{id}/unlink")]
        public async Task<ActionResult> UnlinkFromSavingsGoal(int id)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                // Check if user can modify the wishlist item
                if (!await _savingsService.UserCanModifyWishlistItemAsync(id, userId))
                {
                    return Forbid();
                }

                await _savingsService.UnlinkWishlistItemFromSavingsGoalAsync(id);

                return Ok();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error unlinking wishlist item {id} from savings goal");
                return StatusCode(500, "An error occurred while unlinking the wishlist item from the savings goal");
            }
        }

        #region Helper Methods

        private WishlistItemDto MapToWishlistItemDto(WishlistItem wishlistItem)
        {
            if (wishlistItem == null) return new WishlistItemDto();

            return new WishlistItemDto
            {
                Id = wishlistItem.Id,
                Title = wishlistItem.Title,
                Description = wishlistItem.Description ?? string.Empty,
                EstimatedPrice = wishlistItem.EstimatedPrice,
                ProductUrl = wishlistItem.ProductUrl ?? string.Empty,
                ImageUrl = wishlistItem.ImageUrl ?? string.Empty,
                AddedDate = wishlistItem.AddedDate,
                TargetPurchaseDate = wishlistItem.TargetPurchaseDate,
                UserId = wishlistItem.UserId,
                UserName = wishlistItem.User?.Username,
                FamilyId = wishlistItem.FamilyId,
                FamilyName = wishlistItem.Family?.Name,
                IsShared = wishlistItem.IsShared,
                PriorityId = wishlistItem.PriorityId,
                Priority = wishlistItem.Priority != null ? new PriorityDto
                {
                    Id = wishlistItem.Priority.Id,
                    Name = wishlistItem.Priority.Name,
                    Color = wishlistItem.Priority.Color ?? string.Empty
                } : new PriorityDto(),
                LinkedSavingsGoalId = wishlistItem.LinkedSavingsGoalId,
                LinkedSavingsGoal = wishlistItem.LinkedSavingsGoal != null ? new SavingsGoalDto
                {
                    Id = wishlistItem.LinkedSavingsGoal.Id,
                    Title = wishlistItem.LinkedSavingsGoal.Title ?? string.Empty,
                    TargetAmount = wishlistItem.LinkedSavingsGoal.TargetAmount,
                    CurrentAmount = wishlistItem.LinkedSavingsGoal.CurrentAmount,
                    Progress = wishlistItem.LinkedSavingsGoal.TargetAmount > 0
                        ? (wishlistItem.LinkedSavingsGoal.CurrentAmount / wishlistItem.LinkedSavingsGoal.TargetAmount) * 100
                        : 0
                } : new SavingsGoalDto(),
                Status = wishlistItem.Status,
                CreatedAt = wishlistItem.CreatedAt,
                UpdatedAt = wishlistItem.UpdatedAt.GetValueOrDefault()
            };
        }

        #endregion
    }
}
