using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.API.DTOs;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly IJwtService _jwtService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IUserService userService, IJwtService jwtService, ILogger<AuthController> logger)
        {
            _userService = userService;
            _jwtService = jwtService;
            _logger = logger;
        }

        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] RegisterDto registerDto)
        {
            try
            {
                var user = await _userService.RegisterAsync(
                    registerDto.Username,
                    registerDto.Email,
                    registerDto.Password,
                    registerDto.FirstName,
                    registerDto.LastName,
                    registerDto.PhoneNumber,
                    registerDto.Language);

                _logger.LogInformation($"User {registerDto.Username} registered successfully");

                return Ok(new { message = "Registration successful" });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning($"Registration failed: {ex.Message}");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during registration");
                return StatusCode(500, new { message = "An error occurred during registration" });
            }
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginDto loginDto)
        {
            try
            {
                var user = await _userService.AuthenticateAsync(loginDto.Username, loginDto.Password);

                if (user == null)
                {
                    _logger.LogWarning($"Failed login attempt for username: {loginDto.Username}");
                    return Unauthorized(new { message = "Invalid username or password" });
                }

                var (accessToken, refreshToken) = await _jwtService.GenerateTokensAsync(user);

                var roles = user.UserRoles?.Select(ur => ur.Role?.Name ?? string.Empty).ToList() ?? new List<string>();
                var isPlatformAdmin = roles.Contains("PlatformAdmin");
                var isFamilyAdmin = roles.Contains("FamilyAdmin");

                var tokenHandler = new JwtSecurityTokenHandler();
                var jwtToken = tokenHandler.ReadJwtToken(accessToken);
                var expiryTimeStamp = jwtToken.ValidTo;

                _logger.LogInformation($"User {loginDto.Username} logged in successfully");

                return Ok(new TokenResponseDto
                {
                    AccessToken = accessToken,
                    RefreshToken = refreshToken,
                    UserId = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    Roles = roles,
                    ExpiresAt = expiryTimeStamp,
                    IsPlatformAdmin = isPlatformAdmin,
                    IsFamilyAdmin = isFamilyAdmin
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login");
                return StatusCode(500, new { message = "An error occurred during login" });
            }
        }

        [HttpPost("refresh-token")]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenDto refreshTokenDto)
        {
            try
            {
                // Extract the token from the Authorization header if available
                string? accessToken = null;
                var authHeader = HttpContext.Request.Headers["Authorization"].ToString();
                if (!string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("Bearer "))
                {
                    accessToken = authHeader.Substring("Bearer ".Length);
                }

                // If no token in header, return error
                if (string.IsNullOrEmpty(accessToken))
                {
                    return BadRequest(new { message = "Access token is required" });
                }

                var principal = _jwtService.GetPrincipalFromExpiredToken(accessToken);
                var userIdClaim = principal.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "Invalid token: user ID not found" });
                }
                var userId = int.Parse(userIdClaim);

                var isValid = await _jwtService.ValidateRefreshTokenAsync(refreshTokenDto.RefreshToken, userId);
                if (!isValid)
                {
                    return Unauthorized(new { message = "Invalid refresh token" });
                }

                var user = await _userService.GetByIdAsync(userId);
                if (user == null)
                {
                    return Unauthorized(new { message = "User not found" });
                }

                var (newAccessToken, newRefreshToken) = await _jwtService.GenerateTokensAsync(user);

                var roles = user.UserRoles?.Select(ur => ur.Role?.Name ?? string.Empty).ToList() ?? new List<string>();
                var isPlatformAdmin = roles.Contains("PlatformAdmin");
                var isFamilyAdmin = roles.Contains("FamilyAdmin");

                var tokenHandler = new JwtSecurityTokenHandler();
                var jwtToken = tokenHandler.ReadJwtToken(newAccessToken);
                var expiryTimeStamp = jwtToken.ValidTo;

                _logger.LogInformation($"Token refreshed for user {user.Username}");

                return Ok(new TokenResponseDto
                {
                    AccessToken = newAccessToken,
                    RefreshToken = newRefreshToken,
                    UserId = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    Roles = roles,
                    ExpiresAt = expiryTimeStamp,
                    IsPlatformAdmin = isPlatformAdmin,
                    IsFamilyAdmin = isFamilyAdmin
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token refresh");
                return StatusCode(500, new { message = "An error occurred during token refresh" });
            }
        }

        [Authorize]
        [HttpPost("logout")]
        public async Task<IActionResult> Logout()
        {
            try
            {
                var refreshToken = Request.Cookies["refreshToken"];
                if (!string.IsNullOrEmpty(refreshToken))
                {
                    await _jwtService.RevokeRefreshTokenAsync(refreshToken);
                }

                _logger.LogInformation("User logged out successfully");

                return Ok(new { message = "Logout successful" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                return StatusCode(500, new { message = "An error occurred during logout" });
            }
        }

        [Authorize]
        [HttpGet("check-admin")]
        public async Task<IActionResult> CheckIfAdmin()
        {
            try
            {
                var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userIdClaim))
                {
                    return Unauthorized(new { message = "User ID not found in token" });
                }
                var userId = int.Parse(userIdClaim);
                var user = await _userService.GetByIdAsync(userId);

                if (user == null)
                {
                    return NotFound(new { message = "User not found" });
                }

                var isPlatformAdmin = user.UserRoles?.Any(ur => ur.Role?.Name == "PlatformAdmin") ?? false;
                var isFamilyAdmin = user.UserRoles?.Any(ur => ur.Role?.Name == "FamilyAdmin") ?? false;

                return Ok(new {
                    isPlatformAdmin = isPlatformAdmin,
                    isFamilyAdmin = isFamilyAdmin
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking admin status");
                return StatusCode(500, new { message = "An error occurred while checking admin status" });
            }
        }
    }
}
