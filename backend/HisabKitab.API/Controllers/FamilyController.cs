using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.API.DTOs;
using HisabKitab.API.Middleware;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [Route("api/[controller]")]
    [Authorize]
    public class FamilyController : BaseApiController
    {
        private readonly IFamilyService _familyService;
        private readonly IUserService _userService;
        private readonly ILogger<FamilyController> _logger;

        public FamilyController(
            IFamilyService familyService,
            IUserService userService,
            ILogger<FamilyController> logger)
        {
            _familyService = familyService;
            _userService = userService;
            _logger = logger;
        }

        [HttpPost]
        public async Task<IActionResult> CreateFamily([FromBody] CreateFamilyDto createFamilyDto)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }

                var family = await _familyService.CreateFamilyAsync(
                    createFamilyDto.Name,
                    createFamilyDto.Description,
                    userId,
                    createFamilyDto.Settings);

                var inviteCode = await _familyService.GenerateInviteCodeAsync(family.Id);

                var response = new FamilyDto
                {
                    Id = family.Id,
                    Name = family.Name,
                    Description = family.Description,
                    InviteCode = inviteCode,
                    Settings = family.Settings,
                    CreatedByUserId = family.CreatedByUserId,
                    CreatedByUsername = family.CreatedByUser?.Username ?? string.Empty,
                    CreatedAt = family.CreatedAt,
                    UpdatedAt = family.UpdatedAt,
                    Members = new List<FamilyMemberDto>()
                };

                _logger.LogInformation($"Family {family.Id} created by user {userId}");

                return CreatedAtAction(nameof(GetFamily), new { id = family.Id }, response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating family");
                return StatusCode(500, new { message = "An error occurred while creating the family" });
            }
        }

        [HttpGet("{id}")]
        [RequireFamilyRole("id", "Admin", "Member")]
        public async Task<IActionResult> GetFamily(int id)
        {
            try
            {
                var family = await _familyService.GetFamilyByIdAsync(id);
                var members = await _familyService.GetFamilyMembersAsync(id);

                var memberDtos = members.Select(m => new FamilyMemberDto
                {
                    UserId = m.UserId,
                    Username = m.User?.Username ?? string.Empty,
                    FirstName = m.User?.FirstName ?? string.Empty,
                    LastName = m.User?.LastName ?? string.Empty,
                    Role = m.Role,
                    Permissions = m.Permissions,
                    JoinedAt = m.JoinedAt,
                    IsActive = m.IsActive
                }).ToList();

                var response = new FamilyDto
                {
                    Id = family.Id,
                    Name = family.Name,
                    Description = family.Description,
                    InviteCode = family.InviteCode,
                    Settings = family.Settings,
                    CreatedByUserId = family.CreatedByUserId,
                    CreatedByUsername = family.CreatedByUser?.Username ?? string.Empty,
                    CreatedAt = family.CreatedAt,
                    UpdatedAt = family.UpdatedAt,
                    Members = memberDtos
                };

                return Ok(response);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving family");
                return StatusCode(500, new { message = "An error occurred while retrieving the family" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetUserFamilies()
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }
                var families = await _familyService.GetFamiliesByUserIdAsync(userId);

                var response = families.Select(f => new FamilyDto
                {
                    Id = f.Id,
                    Name = f.Name,
                    Description = f.Description,
                    InviteCode = f.InviteCode,
                    Settings = f.Settings,
                    CreatedByUserId = f.CreatedByUserId,
                    CreatedByUsername = f.CreatedByUser?.Username ?? string.Empty,
                    CreatedAt = f.CreatedAt,
                    UpdatedAt = f.UpdatedAt
                }).ToList();

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user families");
                return StatusCode(500, new { message = "An error occurred while retrieving user families" });
            }
        }

        [HttpPut("{id}")]
        [RequireFamilyRole("id", "Admin")]
        public async Task<IActionResult> UpdateFamily(int id, [FromBody] UpdateFamilyDto updateFamilyDto)
        {
            try
            {
                var family = await _familyService.UpdateFamilyAsync(
                    id,
                    updateFamilyDto.Name,
                    updateFamilyDto.Description,
                    updateFamilyDto.Settings);

                var response = new FamilyDto
                {
                    Id = family.Id,
                    Name = family.Name,
                    Description = family.Description,
                    InviteCode = family.InviteCode,
                    Settings = family.Settings,
                    CreatedByUserId = family.CreatedByUserId,
                    CreatedAt = family.CreatedAt,
                    UpdatedAt = family.UpdatedAt
                };

                _logger.LogInformation($"Family {id} updated");

                return Ok(response);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating family");
                return StatusCode(500, new { message = "An error occurred while updating the family" });
            }
        }

        [HttpDelete("{id}")]
        [RequireFamilyRole("id", "Admin")]
        public async Task<IActionResult> DeleteFamily(int id)
        {
            try
            {
                await _familyService.DeleteFamilyAsync(id);
                _logger.LogInformation($"Family {id} deleted");
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting family");
                return StatusCode(500, new { message = "An error occurred while deleting the family" });
            }
        }

        [HttpPost("{id}/invite")]
        [RequireFamilyRole("id", "Admin")]
        public async Task<IActionResult> GenerateInviteCode(int id)
        {
            try
            {
                var inviteCode = await _familyService.GenerateInviteCodeAsync(id);
                var family = await _familyService.GetFamilyByIdAsync(id);

                var response = new FamilyInviteResponseDto
                {
                    InviteCode = inviteCode,
                    FamilyId = id,
                    FamilyName = family.Name,
                    ExpiresAt = DateTime.UtcNow.AddDays(7) // Invite codes expire after 7 days
                };

                _logger.LogInformation($"Invite code generated for family {id}");

                return Ok(response);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating invite code");
                return StatusCode(500, new { message = "An error occurred while generating the invite code" });
            }
        }

        [HttpPost("join")]
        public async Task<IActionResult> JoinFamily([FromBody] JoinFamilyDto joinFamilyDto)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }
                var familyMember = await _familyService.JoinFamilyWithInviteCodeAsync(joinFamilyDto.InviteCode, userId);

                var family = await _familyService.GetFamilyByIdAsync(familyMember.FamilyId);

                var response = new FamilyDto
                {
                    Id = family.Id,
                    Name = family.Name,
                    Description = family.Description,
                    Settings = family.Settings,
                    CreatedByUserId = family.CreatedByUserId,
                    CreatedByUsername = family.CreatedByUser?.Username ?? string.Empty,
                    CreatedAt = family.CreatedAt,
                    UpdatedAt = family.UpdatedAt
                };

                _logger.LogInformation($"User {userId} joined family {family.Id}");

                return Ok(response);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error joining family");
                return StatusCode(500, new { message = "An error occurred while joining the family" });
            }
        }

        [HttpPost("{id}/members")]
        [RequireFamilyRole("id", "Admin")]
        public async Task<IActionResult> AddMember(int id, [FromBody] UpdateMemberRoleDto addMemberDto)
        {
            try
            {
                var familyMember = await _familyService.AddMemberAsync(id, addMemberDto.UserId, addMemberDto.Role);

                var response = new FamilyMemberDto
                {
                    UserId = familyMember.UserId,
                    Username = familyMember.User?.Username ?? string.Empty,
                    FirstName = familyMember.User?.FirstName ?? string.Empty,
                    LastName = familyMember.User?.LastName ?? string.Empty,
                    Role = familyMember.Role,
                    Permissions = familyMember.Permissions,
                    JoinedAt = familyMember.JoinedAt,
                    IsActive = familyMember.IsActive
                };

                _logger.LogInformation($"User {addMemberDto.UserId} added to family {id} with role {addMemberDto.Role}");

                return Ok(response);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding member to family");
                return StatusCode(500, new { message = "An error occurred while adding the member to the family" });
            }
        }

        [HttpDelete("{id}/members/{userId}")]
        [RequireFamilyRole("id", "Admin")]
        public async Task<IActionResult> RemoveMember(int id, int userId)
        {
            try
            {
                await _familyService.RemoveMemberAsync(id, userId);
                _logger.LogInformation($"User {userId} removed from family {id}");
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing member from family");
                return StatusCode(500, new { message = "An error occurred while removing the member from the family" });
            }
        }

        [HttpPost("{id}/leave")]
        [RequireFamilyRole("id", "Admin", "Member")]
        public async Task<IActionResult> LeaveFamily(int id)
        {
            try
            {
                if (!TryGetUserId(out int userId))
                {
                    return Unauthorized(new { message = "Invalid user identity" });
                }
                await _familyService.LeaveFamilyAsync(id, userId);
                _logger.LogInformation($"User {userId} left family {id}");
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error leaving family");
                return StatusCode(500, new { message = "An error occurred while leaving the family" });
            }
        }

        [HttpPut("{id}/members/{userId}/role")]
        [RequireFamilyRole("id", "Admin")]
        public async Task<IActionResult> UpdateMemberRole(int id, int userId, [FromBody] UpdateMemberRoleDto updateRoleDto)
        {
            try
            {
                await _familyService.UpdateMemberRoleAsync(id, userId, updateRoleDto.Role);
                _logger.LogInformation($"User {userId} role updated to {updateRoleDto.Role} in family {id}");
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating member role");
                return StatusCode(500, new { message = "An error occurred while updating the member role" });
            }
        }

        [HttpGet("{id}/members")]
        [RequireFamilyRole("id", "Admin", "Member")]
        public async Task<IActionResult> GetFamilyMembers(int id)
        {
            try
            {
                var members = await _familyService.GetFamilyMembersAsync(id);

                var response = members.Select(m => new FamilyMemberDto
                {
                    UserId = m.UserId,
                    Username = m.User?.Username ?? string.Empty,
                    FirstName = m.User?.FirstName ?? string.Empty,
                    LastName = m.User?.LastName ?? string.Empty,
                    Role = m.Role,
                    Permissions = m.Permissions,
                    JoinedAt = m.JoinedAt,
                    IsActive = m.IsActive
                }).ToList();

                return Ok(response);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving family members");
                return StatusCode(500, new { message = "An error occurred while retrieving family members" });
            }
        }
    }
}
