using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.API.DTOs;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SyncController : ControllerBase
    {
        private readonly ILogger<SyncController> _logger;
        private readonly ISyncService _syncService;

        public SyncController(
            ILogger<SyncController> logger,
            ISyncService syncService)
        {
            _logger = logger;
            _syncService = syncService;
        }

        private int GetUserIdFromClaims()
        {
            var userIdClaim = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (userIdClaim == null)
            {
                throw new UnauthorizedAccessException("User ID not found in token");
            }
            return int.Parse(userIdClaim);
        }

        [HttpPost("batch")]
        public async Task<IActionResult> SyncBatch([FromBody] SyncBatchRequestDto request)
        {
            try
            {
                var userId = GetUserIdFromClaims();

                // Map DTOs to entities
                var syncItems = request.Items.Select(item => new SyncQueue
                {
                    UserId = userId,
                    EntityType = item.EntityType,
                    EntityId = item.EntityId,
                    Action = item.Action,
                    EntityData = item.EntityData,
                    Status = item.Status,
                    RetryCount = item.RetryCount,
                    ErrorMessage = item.ErrorMessage,
                    CreatedAt = item.CreatedAt
                }).ToList();

                // Process the batch
                var (processed, conflicts) = await _syncService.ProcessSyncBatchAsync(syncItems, userId);

                // Map results to DTOs
                var response = new SyncBatchResponseDto
                {
                    Processed = processed.Select(item => new ProcessedItemDto
                    {
                        Id = item.Id,
                        EntityType = item.EntityType,
                        EntityId = item.EntityId
                    }).ToList(),

                    Conflicts = conflicts.Select(conflict => new ConflictDto
                    {
                        Id = conflict.Id,
                        EntityType = conflict.EntityType,
                        EntityId = conflict.EntityId,
                        LocalData = System.Text.Json.JsonSerializer.Deserialize<object>(conflict.LocalData),
                        RemoteData = System.Text.Json.JsonSerializer.Deserialize<object>(conflict.RemoteData),
                        Resolved = conflict.Resolved,
                        Resolution = conflict.Resolution,
                        CreatedAt = conflict.CreatedAt
                    }).ToList()
                };

                // Update last sync time
                await _syncService.SetLastSyncTimeAsync(userId, DateTime.UtcNow);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing sync batch");
                return StatusCode(500, new { message = "An error occurred while processing the sync batch" });
            }
        }

        [HttpGet("lastSyncTime")]
        public async Task<IActionResult> GetLastSyncTime()
        {
            try
            {
                var userId = GetUserIdFromClaims();

                var lastSyncTime = await _syncService.GetLastSyncTimeAsync(userId);

                return Ok(new LastSyncTimeResponseDto
                {
                    LastSyncTime = lastSyncTime.ToString("o")
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting last sync time");
                return StatusCode(500, new { message = "An error occurred while retrieving the last sync time" });
            }
        }

        [HttpGet("changes")]
        public async Task<IActionResult> GetChanges([FromQuery] DateTime since, [FromQuery] string? cursor = null, [FromQuery] int limit = 100)
        {
            try
            {
                var userId = GetUserIdFromClaims();

                var changes = await _syncService.GetChangesSinceAsync(userId, since, limit, cursor);

                // Map to DTOs
                var response = new SyncChangesResponseDto
                {
                    Changes = changes.Select(item => new SyncQueueItemDto
                    {
                        Id = item.Id,
                        EntityType = item.EntityType,
                        EntityId = item.EntityId,
                        Action = item.Action,
                        EntityData = item.EntityData,
                        Status = item.Status,
                        RetryCount = item.RetryCount,
                        ErrorMessage = item.ErrorMessage,
                        CreatedAt = item.CreatedAt,
                        SyncedAt = item.SyncedAt
                    }).ToList(),

                    HasMore = changes.Count() >= limit,
                    NextCursor = changes.Any() ? changes.Last().CreatedAt.ToString("o") : null
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting changes");
                return StatusCode(500, new { message = "An error occurred while retrieving changes" });
            }
        }

        [HttpGet("conflicts")]
        public async Task<IActionResult> GetConflicts([FromQuery] bool? resolved = null)
        {
            try
            {
                var userId = GetUserIdFromClaims();

                var conflicts = await _syncService.GetConflictsByUserIdAsync(userId, resolved);

                // Map to DTOs
                var conflictDtos = conflicts.Select(conflict => new ConflictDto
                {
                    Id = conflict.Id,
                    EntityType = conflict.EntityType,
                    EntityId = conflict.EntityId,
                    LocalData = System.Text.Json.JsonSerializer.Deserialize<object>(conflict.LocalData),
                    RemoteData = System.Text.Json.JsonSerializer.Deserialize<object>(conflict.RemoteData),
                    Resolved = conflict.Resolved,
                    Resolution = conflict.Resolution,
                    CreatedAt = conflict.CreatedAt
                }).ToList();

                return Ok(conflictDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting conflicts");
                return StatusCode(500, new { message = "An error occurred while retrieving conflicts" });
            }
        }

        [HttpPost("conflicts/{id}/resolve")]
        public async Task<IActionResult> ResolveConflict(int id, [FromBody] ResolveConflictRequestDto request)
        {
            try
            {
                var userId = GetUserIdFromClaims();

                // Get the conflict
                var conflicts = await _syncService.GetConflictsByUserIdAsync(userId);
                var conflict = conflicts.FirstOrDefault(c => c.Id == id);

                if (conflict == null)
                {
                    return NotFound();
                }

                // Validate resolution
                if (request.Resolution != "local" && request.Resolution != "remote" && request.Resolution != "merge")
                {
                    return BadRequest(new { message = "Invalid resolution. Must be 'local', 'remote', or 'merge'." });
                }

                // If resolution is merge, merged data is required
                if (request.Resolution == "merge" && request.MergedData == null)
                {
                    return BadRequest(new { message = "Merged data is required for 'merge' resolution." });
                }

                // Convert merged data to JSON string
                string? resolvedData = null;
                if (request.MergedData != null)
                {
                    resolvedData = System.Text.Json.JsonSerializer.Serialize(request.MergedData);
                }

                // Resolve the conflict
                var resolvedConflict = await _syncService.ResolveConflictAsync(id, request.Resolution, resolvedData ?? string.Empty);

                // Map to DTO
                var conflictDto = new ConflictDto
                {
                    Id = resolvedConflict.Id,
                    EntityType = resolvedConflict.EntityType,
                    EntityId = resolvedConflict.EntityId,
                    LocalData = System.Text.Json.JsonSerializer.Deserialize<object>(resolvedConflict.LocalData),
                    RemoteData = System.Text.Json.JsonSerializer.Deserialize<object>(resolvedConflict.RemoteData),
                    Resolved = resolvedConflict.Resolved,
                    Resolution = resolvedConflict.Resolution,
                    CreatedAt = resolvedConflict.CreatedAt
                };

                return Ok(conflictDto);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error resolving conflict {id}");
                return StatusCode(500, new { message = "An error occurred while resolving the conflict" });
            }
        }
    }
}
