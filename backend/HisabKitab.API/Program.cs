using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using HisabKitab.API.Extensions;
using HisabKitab.Core.Interfaces;
using HisabKitab.Core.Models;
using HisabKitab.Infrastructure.Data;
using HisabKitab.Infrastructure.Repositories;
using HisabKitab.Infrastructure.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();

// Configure JWT Authentication
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
builder.Services.Configure<JwtSettings>(jwtSettings);

var secret = jwtSettings.Get<JwtSettings>()?.Secret;
var key = Encoding.ASCII.GetBytes(secret ?? "fallbacksecretkey12345678901234567890");

builder.WebHost.ConfigureKestrel(options =>
{
    options.ListenAnyIP(5046); // HTTP
    options.ListenAnyIP(7290, listenOptions => listenOptions.UseHttps()); // HTTPS
});

builder.Services.AddAuthentication(x =>
{
    x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(x =>
{
    x.RequireHttpsMetadata = false;
    x.SaveToken = true;
    x.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidIssuer = jwtSettings["Issuer"],
        ValidAudience = jwtSettings["Audience"],
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
});

// Add database services
builder.Services.AddDatabaseServices(builder.Configuration);

// Register repositories and services
builder.Services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
builder.Services.AddScoped<IJwtService, JwtService>();
builder.Services.AddScoped<IRoleService, RoleService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IFamilyService, FamilyService>();
builder.Services.AddScoped<IAccountService, AccountService>();
builder.Services.AddScoped<ICategoryService, CategoryService>();
builder.Services.AddScoped<IPriorityService, PriorityService>();
builder.Services.AddScoped<ITransactionService, TransactionService>();
builder.Services.AddScoped<ILoanService, LoanService>();
builder.Services.AddScoped<INotificationService, NotificationService>();
builder.Services.AddScoped<ISavingsService, SavingsService>();
builder.Services.AddScoped<IReportService, ReportService>();
builder.Services.AddScoped<IAnalyticsService, AnalyticsService>();
builder.Services.AddScoped<IExportService, ExportService>();
builder.Services.AddScoped<IBudgetControlService, BudgetControlService>();
builder.Services.AddScoped<IApprovalService, ApprovalService>();

// Add OCR and Receipt services
builder.Services.AddScoped<IOCRService, OCRService>();
builder.Services.AddScoped<IReceiptService, ReceiptService>();
builder.Services.AddScoped<IFileStorageService, FileStorageService>();

// Add Sync services
builder.Services.AddScoped<ISyncService, SyncService>();

// Add background services
builder.Services.AddHostedService<ReminderBackgroundService>();
builder.Services.AddHostedService<NotificationBackgroundService>();
builder.Services.AddHostedService<MonthlySummaryBackgroundService>();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", builder =>
    {
        builder.AllowAnyOrigin()
               .AllowAnyMethod()
               .AllowAnyHeader();
    });
});

// Add logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

// Use custom middleware
app.UseErrorHandling();
app.UseRequestLogging();

// Only use HTTPS redirection in production
if (!app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
}

app.UseCors("AllowAll");

// Configure static file serving for storage directory
var storageDir = Path.Combine(app.Environment.ContentRootPath, "Storage");
if (!Directory.Exists(storageDir))
{
    Directory.CreateDirectory(storageDir);
}
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(storageDir),
    RequestPath = "/storage"
});

app.UseAuthentication();
app.UseAuthorization();
app.UseRoleAuthorization();
app.UseFamilyRoleAuthorization();

app.MapControllers();

// Seed database with default data
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        // Seed default roles
        var roleService = services.GetRequiredService<IRoleService>();
        await roleService.SeedDefaultRolesAsync();
        app.Logger.LogInformation("Default roles seeded successfully");

        // Seed default priorities
        var priorityService = services.GetRequiredService<IPriorityService>();
        await priorityService.SeedDefaultPrioritiesAsync();
        app.Logger.LogInformation("Default priorities seeded successfully");

        // Seed default categories
        var categoryService = services.GetRequiredService<ICategoryService>();
        await categoryService.SeedDefaultCategoriesAsync();
        app.Logger.LogInformation("Default categories seeded successfully");
    }
    catch (Exception ex)
    {
        app.Logger.LogError(ex, "An error occurred while seeding default data");
    }
}

app.Run();
