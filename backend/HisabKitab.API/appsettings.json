{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=HisabKitab;Username=postgres;Password=*****"}, "JwtSettings": {"Secret": "HisabKitabSecretKey*****67890*****67890", "Issuer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Audience": "HisabKitabClient", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "FileStorage": {"BaseUrl": "/storage", "MaxFileSizeMB": 10, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".pdf"], "ImageCompressionQuality": 85}, "OCR": {"TessdataPath": "tessdata", "DefaultLanguage": "eng", "SupportedLanguages": ["eng", "nep"], "MaxImageSizeMB": 5}, "Sync": {"BatchSize": 100, "MaxRetries": 3, "DefaultSyncInterval": 15}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}