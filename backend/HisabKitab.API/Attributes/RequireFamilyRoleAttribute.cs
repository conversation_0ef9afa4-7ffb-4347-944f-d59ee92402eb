using System;

namespace HisabKitab.API.Middleware
{
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public class RequireFamilyRoleAttribute : Attribute
    {
        public string[] Roles { get; }
        public string FamilyIdParameterName { get; }

        public RequireFamilyRoleAttribute(string familyIdParameterName, params string[] roles)
        {
            FamilyIdParameterName = familyIdParameterName;
            Roles = roles ?? Array.Empty<string>();
        }
    }
}
