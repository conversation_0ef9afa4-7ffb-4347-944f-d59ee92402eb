using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Middleware
{
    public class FamilyRoleAuthorizationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<FamilyRoleAuthorizationMiddleware> _logger;

        public FamilyRoleAuthorizationMiddleware(RequestDelegate next, ILogger<FamilyRoleAuthorizationMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IFamilyService familyService)
        {
            // Get the endpoint metadata
            var endpoint = context.GetEndpoint();
            if (endpoint == null)
            {
                await _next(context);
                return;
            }

            // Check if the endpoint has RequireFamilyRoleAttribute
            var requireFamilyRoleAttribute = endpoint.Metadata.GetMetadata<RequireFamilyRoleAttribute>();
            if (requireFamilyRoleAttribute == null)
            {
                await _next(context);
                return;
            }

            // Check if the user is authenticated
            if (context.User?.Identity == null || !context.User.Identity.IsAuthenticated)
            {
                _logger.LogWarning("Unauthorized access attempt to family role-protected endpoint");
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await context.Response.WriteAsJsonAsync(new { message = "Unauthorized" });
                return;
            }

            // Get user ID from claims
            if (!int.TryParse(context.User.FindFirstValue(ClaimTypes.NameIdentifier), out int userId))
            {
                _logger.LogWarning("Invalid user ID in claims");
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await context.Response.WriteAsJsonAsync(new { message = "Invalid user identity" });
                return;
            }

            // Check if the user is a platform admin (they can access any family)
            var isPlatformAdmin = context.User.Claims
                .Any(c => c.Type == ClaimTypes.Role && c.Value == "PlatformAdmin");

            if (isPlatformAdmin)
            {
                await _next(context);
                return;
            }

            // Get family ID from route data
            var routeData = context.GetRouteData();
            if (!routeData.Values.TryGetValue(requireFamilyRoleAttribute.FamilyIdParameterName, out var familyIdObj) ||
                !int.TryParse(familyIdObj?.ToString(), out int familyId))
            {
                _logger.LogWarning($"Family ID not found in route data with parameter name: {requireFamilyRoleAttribute.FamilyIdParameterName}");
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
                await context.Response.WriteAsJsonAsync(new { message = "Family ID not provided or invalid" });
                return;
            }

            // Get the user's role in the family
            var isMember = await familyService.IsFamilyMemberAsync(familyId, userId);
            if (!isMember)
            {
                _logger.LogWarning($"User {userId} is not a member of family {familyId}");
                context.Response.StatusCode = StatusCodes.Status403Forbidden;
                await context.Response.WriteAsJsonAsync(new { message = "You are not a member of this family" });
                return;
            }

            // Check if the user has the required role in the family
            var requiredRoles = requireFamilyRoleAttribute.Roles;

            // If "Admin" is one of the required roles, check if the user is a family admin
            if (requiredRoles.Contains("Admin"))
            {
                var isAdmin = await familyService.IsFamilyAdminAsync(familyId, userId);
                if (!isAdmin)
                {
                    _logger.LogWarning($"User {userId} is not an admin of family {familyId}");
                    context.Response.StatusCode = StatusCodes.Status403Forbidden;
                    await context.Response.WriteAsJsonAsync(new { message = "You do not have the required role in this family" });
                    return;
                }
            }

            await _next(context);
        }
    }
}
