using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace HisabKitab.API.Middleware
{
    public class RoleAuthorizationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<RoleAuthorizationMiddleware> _logger;

        public RoleAuthorizationMiddleware(RequestDelegate next, ILogger<RoleAuthorizationMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Get the endpoint metadata
            var endpoint = context.GetEndpoint();
            if (endpoint == null)
            {
                await _next(context);
                return;
            }

            // Check if the endpoint has RequireRoleAttribute
            var requireRoleAttribute = endpoint.Metadata.GetMetadata<RequireRoleAttribute>();
            if (requireRoleAttribute == null)
            {
                await _next(context);
                return;
            }

            // Check if the user is authenticated
            if (context.User?.Identity == null || !context.User.Identity.IsAuthenticated)
            {
                _logger.LogWarning("Unauthorized access attempt to role-protected endpoint");
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await context.Response.WriteAsJsonAsync(new { message = "Unauthorized" });
                return;
            }

            // Get user roles from claims
            var userRoles = context.User.Claims
                .Where(c => c.Type == ClaimTypes.Role)
                .Select(c => c.Value)
                .ToList();

            // Check if the user has any of the required roles
            var requiredRoles = requireRoleAttribute.Roles;
            var hasRequiredRole = requiredRoles.Any(role => userRoles.Contains(role));

            if (!hasRequiredRole)
            {
                _logger.LogWarning($"Access forbidden: User does not have required roles. Required: {string.Join(", ", requiredRoles)}, User has: {string.Join(", ", userRoles)}");
                context.Response.StatusCode = StatusCodes.Status403Forbidden;
                await context.Response.WriteAsJsonAsync(new { message = "Forbidden: You do not have the required role to access this resource" });
                return;
            }

            await _next(context);
        }
    }
}
