<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.15" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.15" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.15" />
    <PackageReference Include="Microsoft.IO.RecyclableMemoryStream" Version="2.3.2" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="7.3.1" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.3.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\HisabKitab.Core\HisabKitab.Core.csproj" />
    <ProjectReference Include="..\HisabKitab.Infrastructure\HisabKitab.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers/" />
    <Folder Include="DTOs/" />
    <Folder Include="Middleware/" />
  </ItemGroup>

</Project>
