using HisabKitab.API.Middleware;
using Microsoft.AspNetCore.Builder;

namespace HisabKitab.API.Extensions
{
    public static class MiddlewareExtensions
    {
        public static IApplicationBuilder UseErrorHandling(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<ErrorHandlingMiddleware>();
        }

        public static IApplicationBuilder UseRequestLogging(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<RequestLoggingMiddleware>();
        }

        public static IApplicationBuilder UseRoleAuthorization(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<RoleAuthorizationMiddleware>();
        }

        public static IApplicationBuilder UseFamilyRoleAuthorization(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<FamilyRoleAuthorizationMiddleware>();
        }
    }
}
