using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace HisabKitab.Infrastructure.Services
{
    public class RoleService : IRoleService
    {
        private readonly ApplicationDbContext _dbContext;

        public RoleService(ApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<Role?> GetRoleByNameAsync(string roleName)
        {
            return await _dbContext.Roles
                .FirstOrDefaultAsync(r => r.Name == roleName);
        }

        public async Task<Role?> GetRoleByIdAsync(int roleId)
        {
            return await _dbContext.Roles
                .FirstOrDefaultAsync(r => r.Id == roleId);
        }

        public async Task<IEnumerable<Role>> GetAllRolesAsync()
        {
            return await _dbContext.Roles.ToListAsync();
        }

        public async Task<Role> CreateRoleAsync(string name, string? description = null)
        {
            var existingRole = await GetRoleByNameAsync(name);
            if (existingRole != null)
            {
                throw new ArgumentException($"Role '{name}' already exists.");
            }

            var role = new Role
            {
                Name = name,
                Description = description ?? string.Empty,
                CreatedAt = DateTime.UtcNow
            };

            _dbContext.Roles.Add(role);
            await _dbContext.SaveChangesAsync();

            return role;
        }

        public async Task<bool> AssignRoleToUserAsync(int userId, int roleId)
        {
            var user = await _dbContext.Users.FindAsync(userId);
            if (user == null)
            {
                throw new ArgumentException($"User with ID {userId} not found.");
            }

            var role = await _dbContext.Roles.FindAsync(roleId);
            if (role == null)
            {
                throw new ArgumentException($"Role with ID {roleId} not found.");
            }

            var existingUserRole = await _dbContext.UserRoles
                .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId);

            if (existingUserRole != null)
            {
                return false; // Role already assigned
            }

            var userRole = new UserRole
            {
                UserId = userId,
                RoleId = roleId,
                AssignedAt = DateTime.UtcNow
            };

            _dbContext.UserRoles.Add(userRole);
            await _dbContext.SaveChangesAsync();

            return true;
        }

        public async Task<bool> RemoveRoleFromUserAsync(int userId, int roleId)
        {
            var userRole = await _dbContext.UserRoles
                .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId);

            if (userRole == null)
            {
                return false; // Role not assigned
            }

            _dbContext.UserRoles.Remove(userRole);
            await _dbContext.SaveChangesAsync();

            return true;
        }

        public async Task<IEnumerable<Role?>> GetUserRolesAsync(int userId)
        {
            return await _dbContext.UserRoles
                .Where(ur => ur.UserId == userId)
                .Include(ur => ur.Role)
                .Select(ur => ur.Role)
                .Where(r => r != null)
                .ToListAsync();
        }

        public async Task SeedDefaultRolesAsync()
        {
            var roles = new List<(string Name, string Description)>
            {
                ("User", "Regular user with basic permissions"),
                ("FamilyAdmin", "Administrator of a family group with extended permissions"),
                ("PlatformAdmin", "Administrator of the platform with full permissions")
            };

            foreach (var (name, description) in roles)
            {
                var existingRole = await GetRoleByNameAsync(name);
                if (existingRole == null)
                {
                    await CreateRoleAsync(name, description);
                }
            }
        }
    }
}
