using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using HisabKitab.Core.Interfaces;

namespace HisabKitab.Infrastructure.Services
{
    // Custom implementation of a background service
    public class ReminderBackgroundService : IHostedService, IDisposable
    {
        private Task? _executingTask;
        private readonly CancellationTokenSource _stoppingCts = new();
        private readonly ILogger<ReminderBackgroundService> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(15); // Check every 15 minutes

        public ReminderBackgroundService(
            ILogger<ReminderBackgroundService> logger,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Reminder Background Service is starting.");

            // Store the task we're executing
            _executingTask = ExecuteAsync(_stoppingCts.Token);

            // If the task is completed then return it, otherwise it's running
            return _executingTask.IsCompleted ? _executingTask : Task.CompletedTask;
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            // Stop called without start
            if (_executingTask == null)
            {
                return;
            }

            // Signal cancellation to the executing method
            _stoppingCts.Cancel();

            // Wait until the task completes or the stop token triggers
            await Task.WhenAny(_executingTask, Task.Delay(-1, cancellationToken));

            _logger.LogInformation("Reminder Background Service has stopped.");
        }

        public void Dispose()
        {
            _stoppingCts.Cancel();
            _stoppingCts.Dispose();
            GC.SuppressFinalize(this);
        }

        private async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Reminder Background Service is starting.");

            while (!stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("Reminder Background Service is checking for due reminders.");

                try
                {
                    await ProcessDueReminders(stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while processing reminders.");
                }

                _logger.LogInformation("Reminder Background Service is sleeping for {Interval} minutes.", _checkInterval.TotalMinutes);
                await Task.Delay(_checkInterval, stoppingToken);
            }

            _logger.LogInformation("Reminder Background Service is stopping.");
        }

        private async Task ProcessDueReminders(CancellationToken stoppingToken)
        {
            using var scope = _serviceProvider.CreateScope();
            var loanService = scope.ServiceProvider.GetRequiredService<ILoanService>();
            var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();

            // Get all active reminders that are due (up to current time)
            var dueReminders = await loanService.GetActiveRemindersAsync(DateTime.UtcNow);

            foreach (var reminder in dueReminders)
            {
                if (stoppingToken.IsCancellationRequested)
                    break;

                try
                {
                    _logger.LogInformation("Processing reminder ID {ReminderId} for loan ID {LoanId}",
                        reminder.Id, reminder.LoanId);

                    // Get the loan to determine who should receive the notification
                    var loan = reminder.Loan;

                    if (loan != null)
                    {
                        // Determine recipients based on loan type
                        int? recipientId = null;

                        if (loan.LenderUserId.HasValue)
                            recipientId = loan.LenderUserId;
                        else if (loan.BorrowerUserId.HasValue)
                            recipientId = loan.BorrowerUserId;

                        if (recipientId.HasValue)
                        {
                            // Create notification for the user
                            await notificationService.CreateNotificationAsync(
                                recipientId.Value,
                                "Loan Reminder",
                                reminder.Message,
                                "Loan",
                                loan.Id);

                            _logger.LogInformation("Notification created for user ID {UserId} for reminder ID {ReminderId}",
                                recipientId.Value, reminder.Id);
                        }
                    }

                    // Mark reminder as sent
                    await loanService.MarkReminderAsSentAsync(reminder.Id);
                    _logger.LogInformation("Reminder ID {ReminderId} marked as sent", reminder.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing reminder ID {ReminderId}", reminder.Id);
                }
            }
        }
    }
}
