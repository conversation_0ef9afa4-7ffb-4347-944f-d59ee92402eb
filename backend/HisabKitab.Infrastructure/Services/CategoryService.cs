using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Helpers;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HisabKitab.Infrastructure.Services
{
    public class CategoryService : ICategoryService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<CategoryService> _logger;

        public CategoryService(
            ApplicationDbContext dbContext,
            ILogger<CategoryService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<Category> CreateCategoryAsync(string name, CategoryType type, string icon, string color, int? parentCategoryId, int? userId, int? familyId, bool isSystem = false)
        {
            // Validate that either userId or familyId is provided, but not both (unless it's a system category)
            if (!isSystem && (userId == null && familyId == null) || (userId != null && familyId != null))
            {
                throw new ArgumentException("Either userId or familyId must be provided, but not both.");
            }

            // Validate that the parent category exists if provided
            if (parentCategoryId.HasValue)
            {
                var parentCategory = await _dbContext.Categories.FindAsync(parentCategoryId.Value);
                if (parentCategory == null)
                {
                    throw new KeyNotFoundException($"Parent category with ID {parentCategoryId.Value} not found");
                }

                // Ensure the parent category has the same type
                if (parentCategory.Type != type)
                {
                    throw new ArgumentException("Parent category must have the same type as the child category");
                }
            }

            // Validate that the user exists if userId is provided
            if (userId.HasValue)
            {
                var user = await _dbContext.Users.FindAsync(userId.Value);
                if (user == null)
                {
                    throw new KeyNotFoundException($"User with ID {userId.Value} not found");
                }
            }

            // Validate that the family exists if familyId is provided
            if (familyId.HasValue)
            {
                var family = await _dbContext.Families.FindAsync(familyId.Value);
                if (family == null)
                {
                    throw new KeyNotFoundException($"Family with ID {familyId.Value} not found");
                }
            }

            var category = new Category
            {
                Name = name,
                Type = type,
                Icon = icon ?? "",
                Color = color ?? "#CCCCCC",
                ParentCategoryId = parentCategoryId,
                UserId = userId,
                FamilyId = familyId,
                IsSystem = isSystem,
                CreatedAt = DateTime.UtcNow
            };

            // Ensure all string properties are initialized to prevent not-null constraint violations
            category = EntityHelper.EnsureStringPropertiesInitialized(category);

            _dbContext.Categories.Add(category);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Category created: {category.Id} - {category.Name}");

            return category;
        }

        public async Task<Category> GetCategoryByIdAsync(int categoryId)
        {
            var category = await _dbContext.Categories
                .Include(c => c.ParentCategory)
                .Include(c => c.Subcategories)
                .Include(c => c.User)
                .Include(c => c.Family)
                .Include(c => c.BudgetLimits)
                .FirstOrDefaultAsync(c => c.Id == categoryId);

            if (category == null)
            {
                throw new KeyNotFoundException($"Category with ID {categoryId} not found");
            }

            return category;
        }

        public async Task<IEnumerable<Category>> GetCategoriesByUserIdAsync(int userId)
        {
            // Get user-defined categories
            var userCategories = await _dbContext.Categories
                .Include(c => c.ParentCategory)
                .Include(c => c.Subcategories)
                .Where(c => c.UserId == userId)
                .ToListAsync();

            // Get system categories
            var systemCategories = await _dbContext.Categories
                .Include(c => c.ParentCategory)
                .Include(c => c.Subcategories)
                .Where(c => c.IsSystem)
                .ToListAsync();

            // Combine and return
            return userCategories.Concat(systemCategories);
        }

        public async Task<IEnumerable<Category>> GetCategoriesByFamilyIdAsync(int familyId)
        {
            // Get family-defined categories
            var familyCategories = await _dbContext.Categories
                .Include(c => c.ParentCategory)
                .Include(c => c.Subcategories)
                .Where(c => c.FamilyId == familyId)
                .ToListAsync();

            // Get system categories
            var systemCategories = await _dbContext.Categories
                .Include(c => c.ParentCategory)
                .Include(c => c.Subcategories)
                .Where(c => c.IsSystem)
                .ToListAsync();

            // Combine and return
            return familyCategories.Concat(systemCategories);
        }

        public async Task<IEnumerable<Category>> GetSystemCategoriesAsync()
        {
            return await _dbContext.Categories
                .Include(c => c.ParentCategory)
                .Include(c => c.Subcategories)
                .Where(c => c.IsSystem)
                .ToListAsync();
        }

        public async Task<IEnumerable<Category>> GetCategoriesByTypeAsync(CategoryType type)
        {
            return await _dbContext.Categories
                .Include(c => c.ParentCategory)
                .Include(c => c.Subcategories)
                .Where(c => c.Type == type)
                .ToListAsync();
        }

        public async Task<Category> UpdateCategoryAsync(int categoryId, string name, CategoryType type, string icon, string color, int? parentCategoryId)
        {
            var category = await _dbContext.Categories.FindAsync(categoryId);
            if (category == null)
            {
                throw new KeyNotFoundException($"Category with ID {categoryId} not found");
            }

            // System categories cannot be modified
            if (category.IsSystem)
            {
                throw new InvalidOperationException("System categories cannot be modified");
            }

            // Validate that the parent category exists if provided
            if (parentCategoryId.HasValue)
            {
                var parentCategory = await _dbContext.Categories.FindAsync(parentCategoryId.Value);
                if (parentCategory == null)
                {
                    throw new KeyNotFoundException($"Parent category with ID {parentCategoryId.Value} not found");
                }

                // Ensure the parent category has the same type
                if (parentCategory.Type != type)
                {
                    throw new ArgumentException("Parent category must have the same type as the child category");
                }

                // Prevent circular references
                if (parentCategoryId.Value == categoryId)
                {
                    throw new ArgumentException("A category cannot be its own parent");
                }
            }

            // Update category properties
            category.Name = name;
            category.Type = type;
            category.Icon = icon ?? category.Icon;
            category.Color = color ?? category.Color ?? string.Empty;
            category.ParentCategoryId = parentCategoryId;
            category.UpdatedAt = DateTime.UtcNow;

            // Ensure all string properties are initialized to prevent not-null constraint violations
            category = EntityHelper.EnsureStringPropertiesInitialized(category);

            _dbContext.Categories.Update(category);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Category updated: {category.Id} - {category.Name}");

            return category;
        }

        public async Task<bool> DeleteCategoryAsync(int categoryId)
        {
            var category = await _dbContext.Categories
                .Include(c => c.Subcategories)
                .FirstOrDefaultAsync(c => c.Id == categoryId);

            if (category == null)
            {
                throw new KeyNotFoundException($"Category with ID {categoryId} not found");
            }

            // System categories cannot be deleted
            if (category.IsSystem)
            {
                throw new InvalidOperationException("System categories cannot be deleted");
            }

            // Check if the category has any transactions
            var hasTransactions = await _dbContext.Transactions.AnyAsync(t => t.CategoryId == categoryId);
            if (hasTransactions)
            {
                throw new InvalidOperationException("Cannot delete a category that has transactions");
            }

            // Check if the category has any subcategories
            if (category.Subcategories != null && category.Subcategories.Any())
            {
                throw new InvalidOperationException("Cannot delete a category that has subcategories");
            }

            // Remove any budget limits
            var budgetLimits = await _dbContext.BudgetLimits.Where(b => b.CategoryId == categoryId).ToListAsync();
            _dbContext.BudgetLimits.RemoveRange(budgetLimits);

            _dbContext.Categories.Remove(category);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Category deleted: {categoryId}");

            return true;
        }

        public async Task<BudgetLimit> SetBudgetLimitAsync(int categoryId, decimal amount, FrequencyType period, int? userId, int? familyId, int? familyMemberId, decimal notificationThreshold = 80, bool rolloverUnused = false)
        {
            // Validate that the category exists
            var category = await _dbContext.Categories.FindAsync(categoryId);
            if (category == null)
            {
                throw new KeyNotFoundException($"Category with ID {categoryId} not found");
            }

            // Validate that either userId or familyId is provided, but not both
            if ((userId == null && familyId == null) || (userId != null && familyId != null))
            {
                throw new ArgumentException("Either userId or familyId must be provided, but not both.");
            }

            // Validate that the user exists if userId is provided
            if (userId.HasValue)
            {
                var user = await _dbContext.Users.FindAsync(userId.Value);
                if (user == null)
                {
                    throw new KeyNotFoundException($"User with ID {userId.Value} not found");
                }
            }

            // Validate that the family exists if familyId is provided
            if (familyId.HasValue)
            {
                var family = await _dbContext.Families.FindAsync(familyId.Value);
                if (family == null)
                {
                    throw new KeyNotFoundException($"Family with ID {familyId.Value} not found");
                }
            }

            // Validate that the family member exists if familyMemberId is provided
            if (familyMemberId.HasValue)
            {
                var familyMember = await _dbContext.FamilyMembers.FindAsync(familyMemberId.Value);
                if (familyMember == null)
                {
                    throw new KeyNotFoundException($"Family member with ID {familyMemberId.Value} not found");
                }

                // Ensure the family member belongs to the specified family
                if (familyId.HasValue && familyMember.FamilyId != familyId.Value)
                {
                    throw new ArgumentException($"Family member with ID {familyMemberId.Value} does not belong to family with ID {familyId.Value}");
                }
            }

            // Check if a budget limit already exists for this category and user/family/family member
            var existingBudgetLimit = await _dbContext.BudgetLimits
                .FirstOrDefaultAsync(b => b.CategoryId == categoryId &&
                                         b.UserId == userId &&
                                         b.FamilyId == familyId &&
                                         b.FamilyMemberId == familyMemberId);

            if (existingBudgetLimit != null)
            {
                // Update the existing budget limit
                existingBudgetLimit.Amount = amount;
                existingBudgetLimit.Period = period;
                existingBudgetLimit.NotificationThreshold = notificationThreshold;
                existingBudgetLimit.RolloverUnused = rolloverUnused;
                existingBudgetLimit.UpdatedAt = DateTime.UtcNow;

                _dbContext.BudgetLimits.Update(existingBudgetLimit);
                await _dbContext.SaveChangesAsync();

                _logger.LogInformation($"Budget limit updated: {existingBudgetLimit.Id} for category {categoryId}");

                return existingBudgetLimit;
            }

            // Create a new budget limit
            var budgetLimit = new BudgetLimit
            {
                CategoryId = categoryId,
                Amount = amount,
                Period = period,
                StartDate = DateTime.UtcNow,
                UserId = userId,
                FamilyId = familyId,
                FamilyMemberId = familyMemberId,
                NotificationThreshold = notificationThreshold,
                RolloverUnused = rolloverUnused,
                CreatedAt = DateTime.UtcNow
            };

            _dbContext.BudgetLimits.Add(budgetLimit);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Budget limit created: {budgetLimit.Id} for category {categoryId}");

            return budgetLimit;
        }

        public async Task<BudgetLimit> GetBudgetLimitAsync(int budgetLimitId)
        {
            var budgetLimit = await _dbContext.BudgetLimits
                .Include(b => b.Category)
                .Include(b => b.User)
                .Include(b => b.Family)
                .Include(b => b.FamilyMember)
                    .ThenInclude(fm => fm != null ? fm.User : null)
                .FirstOrDefaultAsync(b => b.Id == budgetLimitId);

            if (budgetLimit == null)
            {
                throw new KeyNotFoundException($"Budget limit with ID {budgetLimitId} not found");
            }

            return budgetLimit;
        }

        public async Task<IEnumerable<BudgetLimit>> GetBudgetLimitsByCategoryAsync(int categoryId)
        {
            return await _dbContext.BudgetLimits
                .Include(b => b.User)
                .Include(b => b.Family)
                .Include(b => b.FamilyMember)
                    .ThenInclude(fm => fm != null ? fm.User : null)
                .Where(b => b.CategoryId == categoryId)
                .ToListAsync();
        }

        public async Task<IEnumerable<BudgetLimit>> GetBudgetLimitsByUserAsync(int userId)
        {
            return await _dbContext.BudgetLimits
                .Include(b => b.Category)
                .Where(b => b.UserId == userId)
                .ToListAsync();
        }

        public async Task<IEnumerable<BudgetLimit>> GetBudgetLimitsByFamilyAsync(int familyId)
        {
            return await _dbContext.BudgetLimits
                .Include(b => b.Category)
                .Include(b => b.FamilyMember)
                    .ThenInclude(fm => fm != null ? fm.User : null)
                .Where(b => b.FamilyId == familyId)
                .ToListAsync();
        }

        public async Task<BudgetLimit> UpdateBudgetLimitAsync(int budgetLimitId, decimal amount, FrequencyType period, decimal notificationThreshold, bool rolloverUnused)
        {
            var budgetLimit = await _dbContext.BudgetLimits.FindAsync(budgetLimitId);
            if (budgetLimit == null)
            {
                throw new KeyNotFoundException($"Budget limit with ID {budgetLimitId} not found");
            }

            budgetLimit.Amount = amount;
            budgetLimit.Period = period;
            budgetLimit.NotificationThreshold = notificationThreshold;
            budgetLimit.RolloverUnused = rolloverUnused;
            budgetLimit.UpdatedAt = DateTime.UtcNow;

            _dbContext.BudgetLimits.Update(budgetLimit);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Budget limit updated: {budgetLimit.Id}");

            return budgetLimit;
        }

        public async Task<bool> DeleteBudgetLimitAsync(int budgetLimitId)
        {
            var budgetLimit = await _dbContext.BudgetLimits.FindAsync(budgetLimitId);
            if (budgetLimit == null)
            {
                throw new KeyNotFoundException($"Budget limit with ID {budgetLimitId} not found");
            }

            _dbContext.BudgetLimits.Remove(budgetLimit);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Budget limit deleted: {budgetLimitId}");

            return true;
        }

        public async Task<bool> CategoryExistsAsync(int categoryId)
        {
            return await _dbContext.Categories.AnyAsync(c => c.Id == categoryId);
        }

        public async Task<bool> UserCanAccessCategoryAsync(int categoryId, int userId)
        {
            var category = await _dbContext.Categories.FindAsync(categoryId);
            if (category == null)
            {
                return false;
            }

            // System categories are accessible to all users
            if (category.IsSystem)
            {
                return true;
            }

            // User-defined categories are accessible to the user who created them
            if (category.UserId == userId)
            {
                return true;
            }

            // Family-defined categories are accessible to family members
            if (category.FamilyId.HasValue)
            {
                var isFamilyMember = await _dbContext.FamilyMembers
                    .AnyAsync(fm => fm.FamilyId == category.FamilyId && fm.UserId == userId && fm.IsActive);

                return isFamilyMember;
            }

            return false;
        }

        public async Task<bool> UserCanModifyCategoryAsync(int categoryId, int userId)
        {
            var category = await _dbContext.Categories.FindAsync(categoryId);
            if (category == null)
            {
                return false;
            }

            // System categories cannot be modified
            if (category.IsSystem)
            {
                return false;
            }

            // User-defined categories can be modified by the user who created them
            if (category.UserId == userId)
            {
                return true;
            }

            // Family-defined categories can be modified by family admins
            if (category.FamilyId.HasValue)
            {
                var isFamilyAdmin = await _dbContext.FamilyMembers
                    .AnyAsync(fm => fm.FamilyId == category.FamilyId && fm.UserId == userId && fm.IsActive && fm.Role == "Admin");

                return isFamilyAdmin;
            }

            return false;
        }

        public async Task SeedDefaultCategoriesAsync()
        {
            // Check if default categories already exist
            var hasDefaultCategories = await _dbContext.Categories.AnyAsync(c => c.IsSystem);
            if (hasDefaultCategories)
            {
                _logger.LogInformation("Default categories already exist");
                return;
            }

            // Create default income categories
            var incomeCategories = new List<(string Name, string Icon, string Color)>
            {
                ("Salary", "money-bill", "#4CAF50"),
                ("Freelance", "laptop", "#2196F3"),
                ("Investments", "chart-line", "#9C27B0"),
                ("Gifts", "gift", "#E91E63"),
                ("Other Income", "plus-circle", "#607D8B")
            };

            foreach (var (name, icon, color) in incomeCategories)
            {
                await CreateCategoryAsync(name, CategoryType.Income, icon, color, null, null, null, true);
            }

            // Create a special Transfer category (using Income type as it's a positive transaction)
            await CreateCategoryAsync("Transfer", CategoryType.Income, "exchange", "#2196F3", null, null, null, true);

            // Create default expense categories
            var expenseCategories = new List<(string Name, string Icon, string Color, int? ParentId)>
            {
                ("Food & Dining", "utensils", "#FF5722", null),
                ("Groceries", "shopping-basket", "#FF9800", null),
                ("Housing", "home", "#795548", null),
                ("Transportation", "car", "#3F51B5", null),
                ("Entertainment", "film", "#673AB7", null),
                ("Shopping", "shopping-cart", "#F44336", null),
                ("Health & Fitness", "heartbeat", "#4CAF50", null),
                ("Education", "graduation-cap", "#2196F3", null),
                ("Personal Care", "user", "#9C27B0", null),
                ("Bills & Utilities", "file-invoice", "#607D8B", null),
                ("Travel", "plane", "#009688", null),
                ("Gifts & Donations", "gift", "#E91E63", null),
                ("Business", "briefcase", "#795548", null),
                ("Taxes", "file-invoice-dollar", "#9E9E9E", null),
                ("Other Expenses", "question-circle", "#607D8B", null)
            };

            foreach (var (name, icon, color, parentId) in expenseCategories)
            {
                await CreateCategoryAsync(name, CategoryType.Expense, icon, color, parentId, null, null, true);
            }

            _logger.LogInformation("Default categories seeded successfully");
        }
    }
}
