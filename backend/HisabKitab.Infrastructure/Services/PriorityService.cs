using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Helpers;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HisabKitab.Infrastructure.Services
{
    public class PriorityService : IPriorityService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<PriorityService> _logger;

        public PriorityService(
            ApplicationDbContext dbContext,
            ILogger<PriorityService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<Priority> CreatePriorityAsync(string name, string description, PriorityLevel level, string color)
        {
            // Check if a priority with the same level already exists
            var existingPriority = await _dbContext.Priorities.FirstOrDefaultAsync(p => p.Level == level);
            if (existingPriority != null)
            {
                throw new ArgumentException($"A priority with level {level} already exists");
            }

            var priority = new Priority
            {
                Name = name,
                Description = description ?? "",
                Level = level,
                Color = color ?? "#CCCCCC",
                CreatedAt = DateTime.UtcNow
            };

            // Ensure all string properties are initialized to prevent not-null constraint violations
            priority = EntityHelper.EnsureStringPropertiesInitialized(priority);

            _dbContext.Priorities.Add(priority);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Priority created: {priority.Id} - {priority.Name}");

            return priority;
        }

        public async Task<Priority> GetPriorityByIdAsync(int priorityId)
        {
            var priority = await _dbContext.Priorities.FindAsync(priorityId);
            if (priority == null)
            {
                throw new KeyNotFoundException($"Priority with ID {priorityId} not found");
            }

            return priority;
        }

        public async Task<Priority> GetPriorityByLevelAsync(PriorityLevel level)
        {
            var priority = await _dbContext.Priorities.FirstOrDefaultAsync(p => p.Level == level);
            if (priority == null)
            {
                throw new KeyNotFoundException($"Priority with level {level} not found");
            }

            return priority;
        }

        public async Task<IEnumerable<Priority>> GetAllPrioritiesAsync()
        {
            return await _dbContext.Priorities
                .OrderBy(p => p.Level)
                .ToListAsync();
        }

        public async Task<Priority> UpdatePriorityAsync(int priorityId, string name, string description, PriorityLevel level, string color)
        {
            var priority = await _dbContext.Priorities.FindAsync(priorityId);
            if (priority == null)
            {
                throw new KeyNotFoundException($"Priority with ID {priorityId} not found");
            }

            // Check if another priority with the same level already exists
            if (priority.Level != level)
            {
                var existingPriority = await _dbContext.Priorities.FirstOrDefaultAsync(p => p.Level == level && p.Id != priorityId);
                if (existingPriority != null)
                {
                    throw new ArgumentException($"Another priority with level {level} already exists");
                }
            }

            priority.Name = name;
            priority.Description = description ?? priority.Description;
            priority.Level = level;
            priority.Color = color ?? priority.Color;
            priority.UpdatedAt = DateTime.UtcNow;

            // Ensure all string properties are initialized to prevent not-null constraint violations
            priority = EntityHelper.EnsureStringPropertiesInitialized(priority);

            _dbContext.Priorities.Update(priority);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Priority updated: {priority.Id} - {priority.Name}");

            return priority;
        }

        public async Task<bool> DeletePriorityAsync(int priorityId)
        {
            var priority = await _dbContext.Priorities.FindAsync(priorityId);
            if (priority == null)
            {
                throw new KeyNotFoundException($"Priority with ID {priorityId} not found");
            }

            // Check if the priority is being used by any transactions
            var isUsedInTransactions = await _dbContext.Transactions.AnyAsync(t => t.PriorityId == priorityId);
            if (isUsedInTransactions)
            {
                throw new InvalidOperationException("Cannot delete a priority that is being used by transactions");
            }

            // Check if the priority is being used by any savings goals
            var isUsedInSavingsGoals = await _dbContext.SavingsGoals.AnyAsync(s => s.PriorityId == priorityId);
            if (isUsedInSavingsGoals)
            {
                throw new InvalidOperationException("Cannot delete a priority that is being used by savings goals");
            }

            // Check if the priority is being used by any wishlist items
            var isUsedInWishlistItems = await _dbContext.WishlistItems.AnyAsync(w => w.PriorityId == priorityId);
            if (isUsedInWishlistItems)
            {
                throw new InvalidOperationException("Cannot delete a priority that is being used by wishlist items");
            }

            _dbContext.Priorities.Remove(priority);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Priority deleted: {priorityId}");

            return true;
        }

        public async Task SeedDefaultPrioritiesAsync()
        {
            // Check if default priorities already exist
            var hasDefaultPriorities = await _dbContext.Priorities.AnyAsync();
            if (hasDefaultPriorities)
            {
                _logger.LogInformation("Default priorities already exist");
                return;
            }

            // Create default priorities
            var defaultPriorities = new List<(string Name, string Description, PriorityLevel Level, string Color)>
            {
                ("Urgent", "Critical items that need immediate attention", PriorityLevel.Urgent, "#F44336"), // Red
                ("High", "Important items that should be addressed soon", PriorityLevel.High, "#FF9800"), // Orange
                ("Normal", "Regular items with standard priority", PriorityLevel.Normal, "#4CAF50"), // Green
                ("Low", "Items that can be addressed when time permits", PriorityLevel.Low, "#2196F3") // Blue
            };

            foreach (var (name, description, level, color) in defaultPriorities)
            {
                var priority = new Priority
                {
                    Name = name,
                    Description = description,
                    Level = level,
                    Color = color,
                    CreatedAt = DateTime.UtcNow
                };

                // Ensure all string properties are initialized to prevent not-null constraint violations
                priority = EntityHelper.EnsureStringPropertiesInitialized(priority);

                _dbContext.Priorities.Add(priority);
            }

            await _dbContext.SaveChangesAsync();
            _logger.LogInformation("Default priorities seeded successfully");
        }
    }
}
