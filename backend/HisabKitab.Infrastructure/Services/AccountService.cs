using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Helpers;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HisabKitab.Infrastructure.Services
{
    public class AccountService : IAccountService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<AccountService> _logger;

        public AccountService(
            ApplicationDbContext dbContext,
            ILogger<AccountService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<Account> CreateAccountAsync(string name, decimal initialBalance, int accountTypeId, string currency, int? userId, int? familyId, bool excludeFromStats = false)
        {
            // Validate that either userId or familyId is provided, but not both
            if ((userId == null && familyId == null) || (userId != null && familyId != null))
            {
                throw new ArgumentException("Either userId or familyId must be provided, but not both.");
            }

            // Validate that the account type exists
            if (!Enum.IsDefined(typeof(AccountType), accountTypeId))
            {
                throw new ArgumentException($"Invalid account type ID: {accountTypeId}");
            }

            // Validate that the user exists if userId is provided
            if (userId.HasValue)
            {
                var user = await _dbContext.Users.FindAsync(userId.Value);
                if (user == null)
                {
                    throw new KeyNotFoundException($"User with ID {userId.Value} not found");
                }
            }

            // Validate that the family exists if familyId is provided
            if (familyId.HasValue)
            {
                var family = await _dbContext.Families.FindAsync(familyId.Value);
                if (family == null)
                {
                    throw new KeyNotFoundException($"Family with ID {familyId.Value} not found");
                }
            }

            var account = new Account
            {
                Name = name,
                AccountType = (AccountType)accountTypeId,
                Balance = initialBalance,
                InitialBalance = initialBalance,
                Currency = currency,
                UserId = userId,
                FamilyId = familyId,
                IsActive = true,
                ExcludeFromStats = excludeFromStats,
                LastUpdatedAt = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow
            };

            // Ensure all string properties are initialized to prevent not-null constraint violations
            account = EntityHelper.EnsureStringPropertiesInitialized(account);

            _dbContext.Accounts.Add(account);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Account created: {account.Id} - {account.Name}");

            return account;
        }

        public async Task<Account> GetAccountByIdAsync(int accountId)
        {
            var account = await _dbContext.Accounts
                .Include(a => a.User)
                .Include(a => a.Family)
                .Include(a => a.SharedWith)
                    .ThenInclude(s => s.FamilyMember != null ? s.FamilyMember : null)
                        .ThenInclude(fm => fm != null ? fm.User : null)
                .FirstOrDefaultAsync(a => a.Id == accountId);

            if (account == null)
            {
                throw new KeyNotFoundException($"Account with ID {accountId} not found");
            }

            return account;
        }

        public async Task<IEnumerable<Account>> GetAccountsByUserIdAsync(int userId)
        {
            // Get personal accounts
            var personalAccounts = await _dbContext.Accounts
                .Where(a => a.UserId == userId)
                .ToListAsync();

            return personalAccounts;
        }

        public async Task<IEnumerable<Account>> GetAccountsByFamilyIdAsync(int familyId)
        {
            // Get family accounts
            var familyAccounts = await _dbContext.Accounts
                .Where(a => a.FamilyId == familyId)
                .ToListAsync();

            return familyAccounts;
        }

        public async Task<Account> UpdateAccountAsync(int accountId, string name, int accountTypeId, decimal initialBalance, string currency, bool isActive, bool excludeFromStats)
        {
            var account = await _dbContext.Accounts.FindAsync(accountId);
            if (account == null)
            {
                throw new KeyNotFoundException($"Account with ID {accountId} not found");
            }

            // Validate that the account type exists
            if (!Enum.IsDefined(typeof(AccountType), accountTypeId))
            {
                throw new ArgumentException($"Invalid account type ID: {accountTypeId}");
            }

            // Calculate the difference between the new and old initial balance
            var balanceDifference = initialBalance - account.InitialBalance;

            // Update account properties
            account.Name = name;
            account.AccountType = (AccountType)accountTypeId;
            account.InitialBalance = initialBalance;
            account.Balance += balanceDifference; // Adjust the current balance by the difference in initial balance
            account.Currency = currency;
            account.IsActive = isActive;
            account.ExcludeFromStats = excludeFromStats;
            account.LastUpdatedAt = DateTime.UtcNow;
            account.UpdatedAt = DateTime.UtcNow;

            // Ensure all string properties are initialized to prevent not-null constraint violations
            account = EntityHelper.EnsureStringPropertiesInitialized(account);

            _dbContext.Accounts.Update(account);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Account updated: {account.Id} - {account.Name}");

            return account;
        }

        public async Task<bool> DeleteAccountAsync(int accountId)
        {
            var account = await _dbContext.Accounts.FindAsync(accountId);
            if (account == null)
            {
                throw new KeyNotFoundException($"Account with ID {accountId} not found");
            }

            // Check if the account has any transactions
            var hasTransactions = await _dbContext.Transactions.AnyAsync(t => t.AccountId == accountId || t.ToAccountId == accountId);
            if (hasTransactions)
            {
                throw new InvalidOperationException("Cannot delete an account that has transactions. Consider marking it as inactive instead.");
            }

            // Remove any account shares
            var accountShares = await _dbContext.AccountShares.Where(s => s.AccountId == accountId).ToListAsync();
            _dbContext.AccountShares.RemoveRange(accountShares);

            _dbContext.Accounts.Remove(account);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Account deleted: {accountId}");

            return true;
        }

        public async Task<Account> UpdateAccountBalanceAsync(int accountId, decimal newBalance)
        {
            var account = await _dbContext.Accounts.FindAsync(accountId);
            if (account == null)
            {
                throw new KeyNotFoundException($"Account with ID {accountId} not found");
            }

            account.Balance = newBalance;
            account.LastUpdatedAt = DateTime.UtcNow;
            account.UpdatedAt = DateTime.UtcNow;

            _dbContext.Accounts.Update(account);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Account balance updated: {account.Id} - {account.Name}, New balance: {newBalance}");

            return account;
        }

        public async Task<AccountShare> ShareAccountWithFamilyMemberAsync(int accountId, int familyMemberId, string permissions)
        {
            var account = await _dbContext.Accounts.FindAsync(accountId);
            if (account == null)
            {
                throw new KeyNotFoundException($"Account with ID {accountId} not found");
            }

            // For this method, familyMemberId is actually the FamilyId
            // We need to get the family first to validate it exists
            var family = await _dbContext.Families.FindAsync(familyMemberId);
            if (family == null)
            {
                throw new KeyNotFoundException($"Family with ID {familyMemberId} not found");
            }

            // We'll handle the specific family member when checking for existing shares

            // Check if the account is already shared with this family member
            var existingShare = await _dbContext.AccountShares
                .FirstOrDefaultAsync(s => s.AccountId == accountId && s.FamilyMemberId == familyMemberId);

            if (existingShare != null)
            {
                // Update the existing share
                existingShare.Permissions = permissions;
                existingShare.UpdatedAt = DateTime.UtcNow;

                _dbContext.AccountShares.Update(existingShare);
                await _dbContext.SaveChangesAsync();

                _logger.LogInformation($"Account share updated: Account {accountId} with family member {familyMemberId}");

                return existingShare;
            }

            // Create a new share
            var accountShare = new AccountShare
            {
                AccountId = accountId,
                FamilyMemberId = familyMemberId,
                Permissions = permissions,
                SharedAt = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow
            };

            // Ensure all string properties are initialized to prevent not-null constraint violations
            accountShare = EntityHelper.EnsureStringPropertiesInitialized(accountShare);

            _dbContext.AccountShares.Add(accountShare);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Account shared: Account {accountId} with family member {familyMemberId}");

            return accountShare;
        }

        public async Task<bool> RemoveAccountShareAsync(int accountShareId)
        {
            var accountShare = await _dbContext.AccountShares.FindAsync(accountShareId);
            if (accountShare == null)
            {
                throw new KeyNotFoundException($"Account share with ID {accountShareId} not found");
            }

            _dbContext.AccountShares.Remove(accountShare);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Account share removed: {accountShareId}");

            return true;
        }

        public async Task<AccountShare> GetAccountShareByIdAsync(int accountShareId)
        {
            var accountShare = await _dbContext.AccountShares
                .Include(s => s.Account)
                .Include(s => s.FamilyMember != null ? s.FamilyMember : null)
                    .ThenInclude(fm => fm != null ? fm.User : null)
                .FirstOrDefaultAsync(s => s.Id == accountShareId);

            if (accountShare == null)
            {
                throw new KeyNotFoundException($"Account share with ID {accountShareId} not found");
            }

            return accountShare;
        }

        public async Task<IEnumerable<AccountShare>> GetAccountSharesAsync(int accountId)
        {
            var accountShares = await _dbContext.AccountShares
                .Include(s => s.FamilyMember != null ? s.FamilyMember : null)
                    .ThenInclude(fm => fm != null ? fm.User : null)
                .Where(s => s.AccountId == accountId)
                .ToListAsync();

            return accountShares;
        }

        public async Task<IEnumerable<Account>> GetSharedAccountsForUserAsync(int userId)
        {
            // Get family memberships for the user
            var familyMemberships = await _dbContext.FamilyMembers
                .Where(fm => fm.UserId == userId && fm.IsActive)
                .ToListAsync();

            // Get accounts shared with the user through family memberships
            var sharedAccounts = new List<Account>();
            foreach (var membership in familyMemberships)
            {
                // Get family accounts
                var familyAccounts = await _dbContext.Accounts
                    .Where(a => a.FamilyId == membership.FamilyId)
                    .ToListAsync();
                sharedAccounts.AddRange(familyAccounts);

                // Get accounts shared specifically with this family member
                // First, get all account shares for the family
                var accountShares = await _dbContext.AccountShares
                    .Include(s => s.Account)
                    .Include(s => s.FamilyMember)
                    .Where(s => s.FamilyMemberId == membership.FamilyId)
                    .ToListAsync();

                // Then filter to only include shares for this user
                accountShares = accountShares
                    .Where(s => s.FamilyMember != null && s.FamilyMember.UserId == membership.UserId)
                    .ToList();

                foreach (var share in accountShares)
                {
                    if (!sharedAccounts.Any(a => a.Id == share.AccountId))
                    {
                        if (share.Account != null)
                            sharedAccounts.Add(share.Account);
                    }
                }
            }

            return sharedAccounts;
        }

        public async Task<bool> AccountExistsAsync(int accountId)
        {
            return await _dbContext.Accounts.AnyAsync(a => a.Id == accountId);
        }

        public async Task<bool> UserCanAccessAccountAsync(int accountId, int userId)
        {
            // Check if it's a personal account owned by the user
            var isPersonalAccount = await _dbContext.Accounts
                .AnyAsync(a => a.Id == accountId && a.UserId == userId);

            if (isPersonalAccount)
            {
                return true;
            }

            // Check if it's a family account and the user is a member of the family
            var account = await _dbContext.Accounts
                .FirstOrDefaultAsync(a => a.Id == accountId);

            if (account == null)
            {
                return false;
            }

            if (account.FamilyId.HasValue)
            {
                var isFamilyMember = await _dbContext.FamilyMembers
                    .AnyAsync(fm => fm.FamilyId == account.FamilyId && fm.UserId == userId && fm.IsActive);

                if (isFamilyMember)
                {
                    return true;
                }
            }

            // Check if the account is shared with the user
            var familyMemberships = await _dbContext.FamilyMembers
                .Where(fm => fm.UserId == userId && fm.IsActive)
                .ToListAsync();

            foreach (var membership in familyMemberships)
            {
                var isShared = await _dbContext.AccountShares
                    .Include(s => s.FamilyMember)
                    .Where(s => s.AccountId == accountId && s.FamilyMemberId == membership.FamilyId)
                    .AnyAsync(s => s.FamilyMember != null && s.FamilyMember.UserId == membership.UserId);

                if (isShared)
                {
                    return true;
                }
            }

            return false;
        }

        public async Task<bool> UserCanModifyAccountAsync(int accountId, int userId)
        {
            // Check if it's a personal account owned by the user
            var isPersonalAccount = await _dbContext.Accounts
                .AnyAsync(a => a.Id == accountId && a.UserId == userId);

            if (isPersonalAccount)
            {
                return true;
            }

            // Check if it's a family account and the user is an admin of the family
            var account = await _dbContext.Accounts
                .FirstOrDefaultAsync(a => a.Id == accountId);

            if (account == null)
            {
                return false;
            }

            if (account.FamilyId.HasValue)
            {
                var isFamilyAdmin = await _dbContext.FamilyMembers
                    .AnyAsync(fm => fm.FamilyId == account.FamilyId && fm.UserId == userId && fm.IsActive && fm.Role == "Admin");

                if (isFamilyAdmin)
                {
                    return true;
                }
            }

            // Check if the account is shared with the user with edit permissions
            var familyMemberships = await _dbContext.FamilyMembers
                .Where(fm => fm.UserId == userId && fm.IsActive)
                .ToListAsync();

            foreach (var membership in familyMemberships)
            {
                var shares = await _dbContext.AccountShares
                    .Include(s => s.FamilyMember)
                    .Where(s => s.AccountId == accountId && s.FamilyMemberId == membership.FamilyId)
                    .ToListAsync();

                var share = shares
                    .FirstOrDefault(s => s.FamilyMember != null && s.FamilyMember.UserId == membership.UserId);

                if (share != null && (share.Permissions.Contains("Edit") || share.Permissions.Contains("Delete")))
                {
                    return true;
                }
            }

            return false;
        }
    }
}
