using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Helpers;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HisabKitab.Infrastructure.Services
{
    public class LoanService : ILoanService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<LoanService> _logger;

        public LoanService(ApplicationDbContext dbContext, ILogger<LoanService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        #region Loan CRUD Operations

        public async Task<Loan> CreateLoanAsync(
            string title,
            decimal amount,
            decimal interestRate,
            InterestType interestType,
            FrequencyType paymentFrequency,
            DateTime startDate,
            DateTime? endDate,
            int? lenderUserId,
            int? borrowerUserId,
            bool isExternalEntity,
            string externalEntityName,
            string status,
            string notes)
        {
            // Validate that at least one of lenderUserId or borrowerUserId is provided
            if (lenderUserId == null && borrowerUserId == null && !isExternalEntity)
            {
                throw new ArgumentException("Either lenderUserId, borrowerUserId, or isExternalEntity must be provided.");
            }

            // If it's an external entity, validate that externalEntityName is provided
            if (isExternalEntity && string.IsNullOrWhiteSpace(externalEntityName))
            {
                throw new ArgumentException("External entity name must be provided when isExternalEntity is true.");
            }

            // Calculate total payable amount based on interest type
            decimal totalPayableAmount = CalculateTotalPayableAmount(amount, interestRate, interestType, paymentFrequency, startDate, endDate);

            // Create loan entity
            var loan = new Loan
            {
                Title = title,
                Amount = amount,
                InterestRate = interestRate,
                InterestType = interestType,
                PaymentFrequency = paymentFrequency,
                TotalPayableAmount = totalPayableAmount,
                StartDate = startDate,
                EndDate = endDate,
                LenderUserId = lenderUserId,
                BorrowerUserId = borrowerUserId,
                IsExternalEntity = isExternalEntity,
                ExternalEntityName = externalEntityName,
                Status = status ?? "Active",
                Notes = notes,
                LastUpdatedAt = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow
            };

            // Ensure all string properties are initialized to prevent not-null constraint violations
            loan = EntityHelper.EnsureStringPropertiesInitialized(loan);

            _dbContext.Loans.Add(loan);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Loan created: {loan.Title} with ID {loan.Id}");

            return loan;
        }

        public async Task<Loan> GetLoanByIdAsync(int loanId)
        {
            var loan = await _dbContext.Loans
                .Include(l => l.LenderUser)
                .Include(l => l.BorrowerUser)
                .Include(l => l.Payments)
                .Include(l => l.Reminders)
                .FirstOrDefaultAsync(l => l.Id == loanId);

            if (loan == null)
            {
                throw new KeyNotFoundException($"Loan with ID {loanId} not found");
            }

            return loan;
        }

        public async Task<IEnumerable<Loan>> GetLoansByUserIdAsync(
            int userId,
            bool asLender = false,
            bool asBorrower = false,
            string? status = null,
            DateTime? startDate = null,
            DateTime? endDate = null)
        {
            // If neither asLender nor asBorrower is specified, return loans where the user is either
            if (!asLender && !asBorrower)
            {
                asLender = true;
                asBorrower = true;
            }

            var query = _dbContext.Loans
                .Include(l => l.LenderUser)
                .Include(l => l.BorrowerUser)
                .Include(l => l.Payments)
                .Include(l => l.Reminders)
                .Where(l => (asLender && l.LenderUserId == userId) || (asBorrower && l.BorrowerUserId == userId));

            // Apply filters
            if (!string.IsNullOrWhiteSpace(status))
            {
                query = query.Where(l => l.Status == status);
            }

            if (startDate.HasValue)
            {
                query = query.Where(l => l.StartDate >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(l => l.StartDate <= endDate.Value);
            }

            // Order by start date descending (newest first)
            query = query.OrderByDescending(l => l.StartDate);

            return await query.ToListAsync();
        }

        public async Task<Loan> UpdateLoanAsync(
            int loanId,
            string? title = null,
            decimal? amount = null,
            decimal? interestRate = null,
            InterestType? interestType = null,
            FrequencyType? paymentFrequency = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int? lenderUserId = null,
            int? borrowerUserId = null,
            bool? isExternalEntity = null,
            string? externalEntityName = null,
            string? status = null,
            string? notes = null)
        {
            var loan = await _dbContext.Loans.FindAsync(loanId);
            if (loan == null)
            {
                throw new KeyNotFoundException($"Loan with ID {loanId} not found");
            }

            // Update properties if provided
            if (title != null)
                loan.Title = title;

            if (amount.HasValue)
                loan.Amount = amount.Value;

            if (interestRate.HasValue)
                loan.InterestRate = interestRate.Value;

            if (interestType.HasValue)
                loan.InterestType = interestType.Value;

            if (paymentFrequency.HasValue)
                loan.PaymentFrequency = paymentFrequency.Value;

            if (startDate.HasValue)
                loan.StartDate = startDate.Value;

            if (endDate.HasValue)
                loan.EndDate = endDate;

            if (lenderUserId.HasValue)
                loan.LenderUserId = lenderUserId;

            if (borrowerUserId.HasValue)
                loan.BorrowerUserId = borrowerUserId;

            if (isExternalEntity.HasValue)
                loan.IsExternalEntity = isExternalEntity.Value;

            if (externalEntityName != null)
                loan.ExternalEntityName = externalEntityName;

            if (status != null)
                loan.Status = status;

            if (notes != null)
                loan.Notes = notes;

            // Recalculate total payable amount if any of the relevant properties changed
            if (amount.HasValue || interestRate.HasValue || interestType.HasValue ||
                paymentFrequency.HasValue || startDate.HasValue || endDate.HasValue)
            {
                loan.TotalPayableAmount = CalculateTotalPayableAmount(
                    loan.Amount,
                    loan.InterestRate,
                    loan.InterestType,
                    loan.PaymentFrequency,
                    loan.StartDate,
                    loan.EndDate);
            }

            loan.LastUpdatedAt = DateTime.UtcNow;
            loan.UpdatedAt = DateTime.UtcNow;

            // Ensure all string properties are initialized to prevent not-null constraint violations
            loan = EntityHelper.EnsureStringPropertiesInitialized(loan);

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Loan updated: {loan.Title} with ID {loan.Id}");

            return loan;
        }

        public async Task<bool> DeleteLoanAsync(int loanId)
        {
            var loan = await _dbContext.Loans.FindAsync(loanId);
            if (loan == null)
            {
                throw new KeyNotFoundException($"Loan with ID {loanId} not found");
            }

            // Begin transaction to ensure all related entities are deleted
            using var transaction = await _dbContext.Database.BeginTransactionAsync();
            try
            {
                // Delete related payments
                var payments = await _dbContext.LoanPayments.Where(p => p.LoanId == loanId).ToListAsync();
                _dbContext.LoanPayments.RemoveRange(payments);

                // Delete related reminders
                var reminders = await _dbContext.LoanReminders.Where(r => r.LoanId == loanId).ToListAsync();
                _dbContext.LoanReminders.RemoveRange(reminders);

                // Delete the loan
                _dbContext.Loans.Remove(loan);
                await _dbContext.SaveChangesAsync();

                // Commit transaction
                await transaction.CommitAsync();

                _logger.LogInformation($"Loan deleted: {loan.Title} with ID {loan.Id}");
                return true;
            }
            catch (Exception ex)
            {
                // Rollback transaction on error
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"Error deleting loan with ID {loanId}");
                throw;
            }
        }

        #endregion

        #region EMI and Payment Operations

        public async Task<decimal> CalculateEMIAsync(int loanId)
        {
            var loan = await GetLoanByIdAsync(loanId);
            return CalculateEMI(
                loan.Amount,
                loan.InterestRate,
                loan.InterestType,
                loan.PaymentFrequency,
                loan.StartDate,
                loan.EndDate);
        }

        public Task<decimal> CalculateEMIAsync(
            decimal principal,
            decimal interestRate,
            InterestType interestType,
            FrequencyType paymentFrequency,
            DateTime startDate,
            DateTime? endDate)
        {
            return Task.FromResult(CalculateEMI(principal, interestRate, interestType, paymentFrequency, startDate, endDate));
        }

        private decimal CalculateEMI(
            decimal principal,
            decimal interestRate,
            InterestType interestType,
            FrequencyType paymentFrequency,
            DateTime startDate,
            DateTime? endDate)
        {
            // Convert annual interest rate to monthly
            decimal monthlyInterestRate = interestRate / 100 / 12;

            // Calculate number of payments based on frequency and loan duration
            int numberOfPayments = CalculateNumberOfPayments(paymentFrequency, startDate, endDate);

            // Calculate EMI based on interest type
            switch (interestType)
            {
                case InterestType.Flat:
                    return CalculateFlatEMI(principal, monthlyInterestRate, numberOfPayments);

                case InterestType.ReducingBalance:
                    return CalculateReducingBalanceEMI(principal, monthlyInterestRate, numberOfPayments);

                case InterestType.Compound:
                    return CalculateCompoundEMI(principal, monthlyInterestRate, numberOfPayments);

                default:
                    throw new ArgumentException($"Unsupported interest type: {interestType}");
            }
        }

        private decimal CalculateFlatEMI(decimal principal, decimal monthlyInterestRate, int numberOfPayments)
        {
            // In flat interest, interest is calculated on the original principal for the entire loan period
            decimal totalInterest = principal * monthlyInterestRate * numberOfPayments;
            decimal totalAmount = principal + totalInterest;

            return totalAmount / numberOfPayments;
        }

        private decimal CalculateReducingBalanceEMI(decimal principal, decimal monthlyInterestRate, int numberOfPayments)
        {
            // Formula: EMI = P * r * (1+r)^n / ((1+r)^n - 1)
            // Where P = Principal, r = monthly interest rate, n = number of payments

            if (monthlyInterestRate == 0)
            {
                return principal / numberOfPayments;
            }

            double rate = (double)monthlyInterestRate;
            double pvif = Math.Pow(1 + rate, numberOfPayments);

            return principal * (decimal)(rate * pvif / (pvif - 1));
        }

        private decimal CalculateCompoundEMI(decimal principal, decimal monthlyInterestRate, int numberOfPayments)
        {
            // For compound interest, we use the same formula as reducing balance but with compounding
            return CalculateReducingBalanceEMI(principal, monthlyInterestRate, numberOfPayments);
        }

        private int CalculateNumberOfPayments(FrequencyType paymentFrequency, DateTime startDate, DateTime? endDate)
        {
            // If end date is not specified, assume a default loan term based on frequency
            if (!endDate.HasValue)
            {
                endDate = paymentFrequency switch
                {
                    FrequencyType.Daily => startDate.AddYears(1),
                    FrequencyType.Weekly => startDate.AddYears(1),
                    FrequencyType.Monthly => startDate.AddYears(3),
                    FrequencyType.Quarterly => startDate.AddYears(5),
                    FrequencyType.Yearly => startDate.AddYears(10),
                    _ => startDate.AddYears(3)
                };
            }

            // Calculate number of payments based on frequency
            TimeSpan duration = endDate.Value - startDate;
            int totalDays = (int)duration.TotalDays;

            return paymentFrequency switch
            {
                FrequencyType.Daily => totalDays,
                FrequencyType.Weekly => totalDays / 7,
                FrequencyType.Monthly => (endDate.Value.Year - startDate.Year) * 12 + endDate.Value.Month - startDate.Month,
                FrequencyType.Quarterly => ((endDate.Value.Year - startDate.Year) * 12 + endDate.Value.Month - startDate.Month) / 3,
                FrequencyType.Yearly => endDate.Value.Year - startDate.Year,
                _ => 36 // Default to 3 years monthly (36 payments)
            };
        }

        private decimal CalculateTotalPayableAmount(
            decimal principal,
            decimal interestRate,
            InterestType interestType,
            FrequencyType paymentFrequency,
            DateTime startDate,
            DateTime? endDate)
        {
            decimal emi = CalculateEMI(principal, interestRate, interestType, paymentFrequency, startDate, endDate);
            int numberOfPayments = CalculateNumberOfPayments(paymentFrequency, startDate, endDate);

            return emi * numberOfPayments;
        }

        public async Task<IEnumerable<LoanPayment>> GeneratePaymentScheduleAsync(int loanId)
        {
            var loan = await GetLoanByIdAsync(loanId);
            var emi = await CalculateEMIAsync(loanId);

            // Calculate payment dates and amounts based on loan properties
            var paymentSchedule = new List<LoanPayment>();
            var currentDate = loan.StartDate;
            var remainingPrincipal = loan.Amount;

            int numberOfPayments = CalculateNumberOfPayments(
                loan.PaymentFrequency,
                loan.StartDate,
                loan.EndDate);

            for (int i = 0; i < numberOfPayments; i++)
            {
                // Calculate next payment date based on frequency
                DateTime paymentDate = GetNextPaymentDate(currentDate, loan.PaymentFrequency);

                // Calculate interest and principal components based on interest type
                (decimal principalAmount, decimal interestAmount) = CalculatePaymentComponents(
                    remainingPrincipal,
                    loan.InterestRate,
                    loan.InterestType,
                    emi,
                    i,
                    numberOfPayments);

                // Create payment record
                var payment = new LoanPayment
                {
                    LoanId = loanId,
                    Amount = emi,
                    PrincipalAmount = principalAmount,
                    InterestAmount = interestAmount,
                    PaymentDate = paymentDate,
                    PaymentMethod = "",
                    IsScheduled = true,
                    Notes = "Scheduled payment",
                    CreatedAt = DateTime.UtcNow
                };

                paymentSchedule.Add(payment);

                // Update for next iteration
                currentDate = paymentDate;
                remainingPrincipal -= principalAmount;

                // Break if principal is fully paid
                if (remainingPrincipal <= 0)
                {
                    break;
                }
            }

            return paymentSchedule;
        }

        private DateTime GetNextPaymentDate(DateTime currentDate, FrequencyType frequency)
        {
            return frequency switch
            {
                FrequencyType.Daily => currentDate.AddDays(1),
                FrequencyType.Weekly => currentDate.AddDays(7),
                FrequencyType.Monthly => currentDate.AddMonths(1),
                FrequencyType.Quarterly => currentDate.AddMonths(3),
                FrequencyType.Yearly => currentDate.AddYears(1),
                _ => currentDate.AddMonths(1) // Default to monthly
            };
        }

        private (decimal principalAmount, decimal interestAmount) CalculatePaymentComponents(
            decimal remainingPrincipal,
            decimal interestRate,
            InterestType interestType,
            decimal emi,
            int paymentNumber,
            int totalPayments)
        {
            // Convert annual interest rate to monthly
            decimal monthlyInterestRate = interestRate / 100 / 12;

            switch (interestType)
            {
                case InterestType.Flat:
                    // In flat interest, principal component is constant
                    decimal principalPerPayment = remainingPrincipal / totalPayments;
                    decimal interestPerPayment = remainingPrincipal * monthlyInterestRate;
                    return (principalPerPayment, interestPerPayment);

                case InterestType.ReducingBalance:
                    // In reducing balance, interest is calculated on the remaining principal
                    decimal interestAmount = remainingPrincipal * monthlyInterestRate;
                    decimal principalAmount = emi - interestAmount;

                    // Ensure we don't exceed the remaining principal
                    if (principalAmount > remainingPrincipal)
                    {
                        principalAmount = remainingPrincipal;
                        interestAmount = emi - principalAmount;
                    }

                    return (principalAmount, interestAmount);

                case InterestType.Compound:
                    // For compound interest, we use the same approach as reducing balance
                    return CalculatePaymentComponents(
                        remainingPrincipal,
                        interestRate,
                        InterestType.ReducingBalance,
                        emi,
                        paymentNumber,
                        totalPayments);

                default:
                    throw new ArgumentException($"Unsupported interest type: {interestType}");
            }
        }

        public async Task<LoanPayment> RecordPaymentAsync(
            int loanId,
            decimal amount,
            decimal principalAmount,
            decimal interestAmount,
            DateTime paymentDate,
            string paymentMethod,
            bool isScheduled,
            int? transactionId,
            string notes)
        {
            // Validate loan exists
            var loan = await _dbContext.Loans.FindAsync(loanId);
            if (loan == null)
            {
                throw new KeyNotFoundException($"Loan with ID {loanId} not found");
            }

            // Validate transaction if provided
            if (transactionId.HasValue)
            {
                var transaction = await _dbContext.Transactions.FindAsync(transactionId.Value);
                if (transaction == null)
                {
                    throw new KeyNotFoundException($"Transaction with ID {transactionId.Value} not found");
                }
            }

            // Create payment record
            var payment = new LoanPayment
            {
                LoanId = loanId,
                Amount = amount,
                PrincipalAmount = principalAmount,
                InterestAmount = interestAmount,
                PaymentDate = paymentDate,
                PaymentMethod = paymentMethod,
                IsScheduled = isScheduled,
                TransactionId = transactionId,
                Notes = notes,
                CreatedAt = DateTime.UtcNow
            };

            // Ensure all string properties are initialized to prevent not-null constraint violations
            payment = EntityHelper.EnsureStringPropertiesInitialized(payment);

            _dbContext.LoanPayments.Add(payment);

            // Update loan status if fully paid
            var totalPaid = await _dbContext.LoanPayments
                .Where(p => p.LoanId == loanId)
                .SumAsync(p => p.Amount) + amount;

            if (totalPaid >= loan.TotalPayableAmount)
            {
                loan.Status = "Completed";
                loan.EndDate = paymentDate;
            }

            loan.LastUpdatedAt = DateTime.UtcNow;
            loan.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Payment recorded for loan ID {loanId}: {amount:C}");

            return payment;
        }

        public async Task<LoanPayment> GetPaymentByIdAsync(int paymentId)
        {
            var payment = await _dbContext.LoanPayments
                .Include(p => p.Loan)
                .Include(p => p.Transaction)
                .FirstOrDefaultAsync(p => p.Id == paymentId);

            if (payment == null)
            {
                throw new KeyNotFoundException($"Loan payment with ID {paymentId} not found");
            }

            return payment;
        }

        public async Task<IEnumerable<LoanPayment>> GetPaymentsByLoanIdAsync(int loanId)
        {
            // Validate loan exists
            if (!await _dbContext.Loans.AnyAsync(l => l.Id == loanId))
            {
                throw new KeyNotFoundException($"Loan with ID {loanId} not found");
            }

            return await _dbContext.LoanPayments
                .Include(p => p.Transaction)
                .Where(p => p.LoanId == loanId)
                .OrderBy(p => p.PaymentDate)
                .ToListAsync();
        }

        public async Task<LoanPayment> UpdatePaymentAsync(
            int paymentId,
            decimal? amount = null,
            decimal? principalAmount = null,
            decimal? interestAmount = null,
            DateTime? paymentDate = null,
            string? paymentMethod = null,
            bool? isScheduled = null,
            int? transactionId = null,
            string? notes = null)
        {
            var payment = await _dbContext.LoanPayments.FindAsync(paymentId);
            if (payment == null)
            {
                throw new KeyNotFoundException($"Loan payment with ID {paymentId} not found");
            }

            // Update properties if provided
            if (amount.HasValue)
                payment.Amount = amount.Value;

            if (principalAmount.HasValue)
                payment.PrincipalAmount = principalAmount.Value;

            if (interestAmount.HasValue)
                payment.InterestAmount = interestAmount.Value;

            if (paymentDate.HasValue)
                payment.PaymentDate = paymentDate.Value;

            if (paymentMethod != null)
                payment.PaymentMethod = paymentMethod;

            if (isScheduled.HasValue)
                payment.IsScheduled = isScheduled.Value;

            if (transactionId.HasValue)
            {
                // Validate transaction exists
                var transaction = await _dbContext.Transactions.FindAsync(transactionId.Value);
                if (transaction == null)
                {
                    throw new KeyNotFoundException($"Transaction with ID {transactionId.Value} not found");
                }

                payment.TransactionId = transactionId;
            }

            if (notes != null)
                payment.Notes = notes;

            payment.UpdatedAt = DateTime.UtcNow;

            // Ensure all string properties are initialized to prevent not-null constraint violations
            payment = EntityHelper.EnsureStringPropertiesInitialized(payment);

            // Update loan last updated timestamp
            var loan = await _dbContext.Loans.FindAsync(payment.LoanId);
            if (loan != null)
            {
                loan.LastUpdatedAt = DateTime.UtcNow;
                loan.UpdatedAt = DateTime.UtcNow;
            }

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Payment updated: ID {paymentId} for loan ID {payment.LoanId}");

            return payment;
        }

        public async Task<bool> DeletePaymentAsync(int paymentId)
        {
            var payment = await _dbContext.LoanPayments.FindAsync(paymentId);
            if (payment == null)
            {
                throw new KeyNotFoundException($"Loan payment with ID {paymentId} not found");
            }

            _dbContext.LoanPayments.Remove(payment);

            // Update loan last updated timestamp
            var loan = await _dbContext.Loans.FindAsync(payment.LoanId);
            if (loan != null)
            {
                loan.LastUpdatedAt = DateTime.UtcNow;
                loan.UpdatedAt = DateTime.UtcNow;

                // Recheck loan status
                var remainingPayments = await _dbContext.LoanPayments
                    .Where(p => p.LoanId == loan.Id && p.Id != paymentId)
                    .ToListAsync();

                decimal totalPaid = remainingPayments.Sum(p => p.Amount);

                if (totalPaid < loan.TotalPayableAmount && loan.Status == "Completed")
                {
                    loan.Status = "Active";
                }
            }

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Payment deleted: ID {paymentId} for loan ID {payment.LoanId}");

            return true;
        }

        #endregion

        #region Loan Timeline and Prediction

        public async Task<Dictionary<DateTime, decimal>> GenerateLoanTimelineAsync(int loanId)
        {
            var loan = await GetLoanByIdAsync(loanId);
            var payments = await GetPaymentsByLoanIdAsync(loanId);

            // Create timeline dictionary
            var timeline = new Dictionary<DateTime, decimal>();

            // Add actual payments to timeline
            foreach (var payment in payments.Where(p => !p.IsScheduled))
            {
                if (timeline.ContainsKey(payment.PaymentDate.Date))
                {
                    timeline[payment.PaymentDate.Date] += payment.Amount;
                }
                else
                {
                    timeline[payment.PaymentDate.Date] = payment.Amount;
                }
            }

            // If loan is not completed, generate future payments
            if (loan.Status != "Completed")
            {
                var remainingBalance = await CalculateRemainingBalanceAsync(loanId);
                if (remainingBalance > 0)
                {
                    var emi = await CalculateEMIAsync(loanId);
                    var lastPaymentDate = payments.Any()
                        ? payments.Max(p => p.PaymentDate)
                        : loan.StartDate;

                    // Generate future payments until loan is fully paid
                    while (remainingBalance > 0)
                    {
                        lastPaymentDate = GetNextPaymentDate(lastPaymentDate, loan.PaymentFrequency);

                        // Ensure we don't exceed the remaining balance
                        var paymentAmount = Math.Min(emi, remainingBalance);

                        if (timeline.ContainsKey(lastPaymentDate.Date))
                        {
                            timeline[lastPaymentDate.Date] += paymentAmount;
                        }
                        else
                        {
                            timeline[lastPaymentDate.Date] = paymentAmount;
                        }

                        remainingBalance -= paymentAmount;
                    }
                }
            }

            return timeline;
        }

        public async Task<DateTime> PredictCompletionDateAsync(int loanId)
        {
            var loan = await GetLoanByIdAsync(loanId);

            // If loan is already completed, return the end date
            if (loan.Status == "Completed" && loan.EndDate.HasValue)
            {
                return loan.EndDate.Value;
            }

            // Calculate remaining balance
            var remainingBalance = await CalculateRemainingBalanceAsync(loanId);
            if (remainingBalance <= 0)
            {
                // Loan is fully paid, update status if needed
                if (loan.Status != "Completed")
                {
                    loan.Status = "Completed";
                    loan.EndDate = DateTime.UtcNow;
                    loan.LastUpdatedAt = DateTime.UtcNow;
                    loan.UpdatedAt = DateTime.UtcNow;
                    await _dbContext.SaveChangesAsync();
                }

                return loan.EndDate ?? DateTime.UtcNow;
            }

            // Get payment history to analyze payment pattern
            var payments = await GetPaymentsByLoanIdAsync(loanId);
            var actualPayments = payments.Where(p => !p.IsScheduled).ToList();

            // If no actual payments yet, use the calculated EMI and payment frequency
            if (!actualPayments.Any())
            {
                var emi = await CalculateEMIAsync(loanId);
                var paymentsNeeded = (int)Math.Ceiling(remainingBalance / emi);

                var lastDate = loan.StartDate;
                for (int i = 0; i < paymentsNeeded; i++)
                {
                    lastDate = GetNextPaymentDate(lastDate, loan.PaymentFrequency);
                }

                return lastDate;
            }

            // Analyze payment pattern
            var averagePaymentAmount = actualPayments.Average(p => p.Amount);
            var numberOfPaymentsRemaining = (int)Math.Ceiling(remainingBalance / averagePaymentAmount);

            // Calculate average time between payments
            TimeSpan averageTimeBetweenPayments;
            if (actualPayments.Count > 1)
            {
                // Sort payments by date
                var sortedPayments = actualPayments.OrderBy(p => p.PaymentDate).ToList();

                // Calculate average days between payments
                double totalDays = 0;
                for (int i = 1; i < sortedPayments.Count; i++)
                {
                    totalDays += (sortedPayments[i].PaymentDate - sortedPayments[i - 1].PaymentDate).TotalDays;
                }

                averageTimeBetweenPayments = TimeSpan.FromDays(totalDays / (sortedPayments.Count - 1));
            }
            else
            {
                // Default to payment frequency if only one payment
                averageTimeBetweenPayments = loan.PaymentFrequency switch
                {
                    FrequencyType.Daily => TimeSpan.FromDays(1),
                    FrequencyType.Weekly => TimeSpan.FromDays(7),
                    FrequencyType.Monthly => TimeSpan.FromDays(30),
                    FrequencyType.Quarterly => TimeSpan.FromDays(90),
                    FrequencyType.Yearly => TimeSpan.FromDays(365),
                    _ => TimeSpan.FromDays(30) // Default to monthly
                };
            }

            // Calculate predicted completion date
            var lastPaymentDate = actualPayments.Max(p => p.PaymentDate);
            var predictedCompletionDate = lastPaymentDate.Add(averageTimeBetweenPayments * numberOfPaymentsRemaining);

            return predictedCompletionDate;
        }

        public async Task<decimal> CalculateRemainingBalanceAsync(int loanId, DateTime? asOfDate = null)
        {
            var loan = await GetLoanByIdAsync(loanId);
            var payments = await GetPaymentsByLoanIdAsync(loanId);

            // Filter payments by date if specified
            if (asOfDate.HasValue)
            {
                payments = payments.Where(p => p.PaymentDate <= asOfDate.Value).ToList();
            }

            // Calculate total paid amount
            decimal totalPaid = payments.Sum(p => p.Amount);

            // Calculate remaining balance
            decimal remainingBalance = loan.TotalPayableAmount - totalPaid;

            // Ensure we don't return negative balance
            return Math.Max(0, remainingBalance);
        }

        #endregion

        #region Reminder Operations

        public async Task<LoanReminder> CreateReminderAsync(
            int loanId,
            DateTime reminderDate,
            string message,
            bool isActive = true)
        {
            // Validate loan exists
            var loan = await _dbContext.Loans.FindAsync(loanId);
            if (loan == null)
            {
                throw new KeyNotFoundException($"Loan with ID {loanId} not found");
            }

            // Create reminder
            var reminder = new LoanReminder
            {
                LoanId = loanId,
                ReminderDate = reminderDate,
                Message = message,
                IsActive = isActive,
                IsSent = false,
                SentAt = null,
                CreatedAt = DateTime.UtcNow
            };

            // Ensure all string properties are initialized to prevent not-null constraint violations
            reminder = EntityHelper.EnsureStringPropertiesInitialized(reminder);

            _dbContext.LoanReminders.Add(reminder);

            // Update loan last updated timestamp
            loan.LastUpdatedAt = DateTime.UtcNow;
            loan.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Reminder created for loan ID {loanId}: {message}");

            return reminder;
        }

        public async Task<LoanReminder> GetReminderByIdAsync(int reminderId)
        {
            var reminder = await _dbContext.LoanReminders
                .Include(r => r.Loan)
                .FirstOrDefaultAsync(r => r.Id == reminderId);

            if (reminder == null)
            {
                throw new KeyNotFoundException($"Loan reminder with ID {reminderId} not found");
            }

            return reminder;
        }

        public async Task<IEnumerable<LoanReminder>> GetRemindersByLoanIdAsync(int loanId)
        {
            // Validate loan exists
            if (!await _dbContext.Loans.AnyAsync(l => l.Id == loanId))
            {
                throw new KeyNotFoundException($"Loan with ID {loanId} not found");
            }

            return await _dbContext.LoanReminders
                .Where(r => r.LoanId == loanId)
                .OrderBy(r => r.ReminderDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<LoanReminder>> GetActiveRemindersAsync(DateTime? beforeDate = null)
        {
            var query = _dbContext.LoanReminders
                .Include(r => r.Loan)
                .Where(r => r.IsActive && !r.IsSent);

            if (beforeDate.HasValue)
            {
                query = query.Where(r => r.ReminderDate <= beforeDate.Value);
            }

            return await query.OrderBy(r => r.ReminderDate).ToListAsync();
        }

        public async Task<LoanReminder> UpdateReminderAsync(
            int reminderId,
            DateTime? reminderDate = null,
            string? message = null,
            bool? isActive = null,
            bool? isSent = null,
            DateTime? sentAt = null)
        {
            var reminder = await _dbContext.LoanReminders.FindAsync(reminderId);
            if (reminder == null)
            {
                throw new KeyNotFoundException($"Loan reminder with ID {reminderId} not found");
            }

            // Update properties if provided
            if (reminderDate.HasValue)
                reminder.ReminderDate = reminderDate.Value;

            if (message != null)
                reminder.Message = message;

            if (isActive.HasValue)
                reminder.IsActive = isActive.Value;

            if (isSent.HasValue)
                reminder.IsSent = isSent.Value;

            if (sentAt.HasValue)
                reminder.SentAt = sentAt;

            reminder.UpdatedAt = DateTime.UtcNow;

            // Ensure all string properties are initialized to prevent not-null constraint violations
            reminder = EntityHelper.EnsureStringPropertiesInitialized(reminder);

            // Update loan last updated timestamp
            var loan = await _dbContext.Loans.FindAsync(reminder.LoanId);
            if (loan != null)
            {
                loan.LastUpdatedAt = DateTime.UtcNow;
                loan.UpdatedAt = DateTime.UtcNow;
            }

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Reminder updated: ID {reminderId} for loan ID {reminder.LoanId}");

            return reminder;
        }

        public async Task<bool> DeleteReminderAsync(int reminderId)
        {
            var reminder = await _dbContext.LoanReminders.FindAsync(reminderId);
            if (reminder == null)
            {
                throw new KeyNotFoundException($"Loan reminder with ID {reminderId} not found");
            }

            _dbContext.LoanReminders.Remove(reminder);

            // Update loan last updated timestamp
            var loan = await _dbContext.Loans.FindAsync(reminder.LoanId);
            if (loan != null)
            {
                loan.LastUpdatedAt = DateTime.UtcNow;
                loan.UpdatedAt = DateTime.UtcNow;
            }

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Reminder deleted: ID {reminderId} for loan ID {reminder.LoanId}");

            return true;
        }

        public async Task<bool> MarkReminderAsSentAsync(int reminderId)
        {
            var reminder = await _dbContext.LoanReminders.FindAsync(reminderId);
            if (reminder == null)
            {
                throw new KeyNotFoundException($"Loan reminder with ID {reminderId} not found");
            }

            reminder.IsSent = true;
            reminder.SentAt = DateTime.UtcNow;
            reminder.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Reminder marked as sent: ID {reminderId} for loan ID {reminder.LoanId}");

            return true;
        }

        #endregion

        #region Validation Methods

        public async Task<bool> LoanExistsAsync(int loanId)
        {
            return await _dbContext.Loans.AnyAsync(l => l.Id == loanId);
        }

        public async Task<bool> UserCanAccessLoanAsync(int loanId, int userId)
        {
            var loan = await _dbContext.Loans.FindAsync(loanId);
            if (loan == null)
            {
                return false;
            }

            // User can access if they are the lender or borrower
            return loan.LenderUserId == userId || loan.BorrowerUserId == userId;
        }

        public async Task<bool> UserCanModifyLoanAsync(int loanId, int userId)
        {
            var loan = await _dbContext.Loans.FindAsync(loanId);
            if (loan == null)
            {
                return false;
            }

            // Only the creator (lender or borrower) can modify the loan
            // For simplicity, we'll allow both lender and borrower to modify
            return loan.LenderUserId == userId || loan.BorrowerUserId == userId;
        }

        #endregion
    }
}