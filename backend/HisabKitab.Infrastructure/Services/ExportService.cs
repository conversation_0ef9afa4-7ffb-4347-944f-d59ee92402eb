using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HisabKitab.Infrastructure.Services
{
    public class ExportService : IExportService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<ExportService> _logger;
        private readonly IReportService _reportService;
        private readonly string _exportDirectory;

        public ExportService(
            ApplicationDbContext dbContext,
            ILogger<ExportService> logger,
            IReportService reportService)
        {
            _dbContext = dbContext;
            _logger = logger;
            _reportService = reportService;

            // Create export directory if it doesn't exist
            _exportDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Exports");
            if (!Directory.Exists(_exportDirectory))
            {
                Directory.CreateDirectory(_exportDirectory);
            }
        }

        public async Task<ExportHistory> ExportToCsvAsync(
            string contentType,
            string parameters,
            int userId,
            int? reportId = null)
        {
            // Validate user exists
            if (!await _dbContext.Users.AnyAsync(u => u.Id == userId))
            {
                throw new KeyNotFoundException($"User with ID {userId} not found");
            }

            // Create export history record
            var exportHistory = new ExportHistory
            {
                UserId = userId,
                ExportType = "CSV",
                ContentType = contentType,
                ReportId = reportId,
                Parameters = parameters ?? "{}",
                ExportedAt = DateTime.UtcNow,
                Status = ExportStatus.InProgress,
                FileName = $"{contentType}_{DateTime.UtcNow:yyyyMMdd_HHmmss}.csv"
            };

            _dbContext.ExportHistory.Add(exportHistory);
            await _dbContext.SaveChangesAsync();

            try
            {
                // Generate CSV content based on content type
                string csvContent;

                switch (contentType.ToLower())
                {
                    case "transactions":
                        csvContent = await ExportTransactionsToCsvAsync(userId, parameters ?? "{}");
                        break;
                    case "accounts":
                        csvContent = await ExportAccountsToCsvAsync(userId, parameters ?? "{}");
                        break;
                    case "report":
                        if (!reportId.HasValue)
                        {
                            throw new ArgumentException("Report ID is required for report exports");
                        }
                        csvContent = await ExportReportToCsvAsync(reportId.Value);
                        break;
                    default:
                        throw new NotImplementedException($"Export type '{contentType}' is not implemented");
                }

                // Save CSV file
                var filePath = Path.Combine(_exportDirectory, exportHistory.FileName);
                await File.WriteAllTextAsync(filePath, csvContent);

                // Update export history
                exportHistory.Status = ExportStatus.Completed;
                exportHistory.FilePath = filePath;
                await _dbContext.SaveChangesAsync();

                return exportHistory;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error exporting {contentType} to CSV");

                // Update export history with error status
                exportHistory.Status = ExportStatus.Failed;
                await _dbContext.SaveChangesAsync();

                throw;
            }
        }

        public async Task<ExportHistory> ExportToPdfAsync(
            string contentType,
            string parameters,
            int userId,
            int? reportId = null)
        {
            // Validate user exists
            if (!await _dbContext.Users.AnyAsync(u => u.Id == userId))
            {
                throw new KeyNotFoundException($"User with ID {userId} not found");
            }

            // Create export history record
            var exportHistory = new ExportHistory
            {
                UserId = userId,
                ExportType = "PDF",
                ContentType = contentType,
                ReportId = reportId,
                Parameters = parameters ?? "{}",
                ExportedAt = DateTime.UtcNow,
                Status = ExportStatus.InProgress,
                FileName = $"{contentType}_{DateTime.UtcNow:yyyyMMdd_HHmmss}.pdf"
            };

            _dbContext.ExportHistory.Add(exportHistory);
            await _dbContext.SaveChangesAsync();

            try
            {
                // Generate PDF content based on content type
                byte[] pdfContent;

                switch (contentType.ToLower())
                {
                    case "transactions":
                        pdfContent = await ExportTransactionsToPdfAsync(userId, parameters ?? "{}");
                        break;
                    case "accounts":
                        pdfContent = await ExportAccountsToPdfAsync(userId, parameters ?? "{}");
                        break;
                    case "report":
                        if (!reportId.HasValue)
                        {
                            throw new ArgumentException("Report ID is required for report exports");
                        }
                        pdfContent = await ExportReportToPdfAsync(reportId.Value);
                        break;
                    default:
                        throw new NotImplementedException($"Export type '{contentType}' is not implemented");
                }

                // Save PDF file
                var filePath = Path.Combine(_exportDirectory, exportHistory.FileName);
                await File.WriteAllBytesAsync(filePath, pdfContent);

                // Update export history
                exportHistory.Status = ExportStatus.Completed;
                exportHistory.FilePath = filePath;
                await _dbContext.SaveChangesAsync();

                return exportHistory;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error exporting {contentType} to PDF");

                // Update export history with error status
                exportHistory.Status = ExportStatus.Failed;
                await _dbContext.SaveChangesAsync();

                throw;
            }
        }

        public async Task<ExportHistory> GetExportByIdAsync(int exportId)
        {
            var export = await _dbContext.ExportHistory
                .Include(e => e.User)
                .Include(e => e.Report)
                .FirstOrDefaultAsync(e => e.Id == exportId);

            if (export == null)
            {
                throw new KeyNotFoundException($"Export with ID {exportId} not found");
            }

            return export;
        }

        public async Task<IEnumerable<ExportHistory>> GetExportHistoryByUserIdAsync(
            int userId,
            int limit = 10,
            int offset = 0)
        {
            return await _dbContext.ExportHistory
                .Where(e => e.UserId == userId)
                .OrderByDescending(e => e.ExportedAt)
                .Skip(offset)
                .Take(limit)
                .Include(e => e.User)
                .Include(e => e.Report)
                .ToListAsync();
        }

        public async Task<bool> DeleteExportAsync(int exportId)
        {
            var export = await _dbContext.ExportHistory.FindAsync(exportId);
            if (export == null)
            {
                throw new KeyNotFoundException($"Export with ID {exportId} not found");
            }

            // Delete file if it exists
            if (!string.IsNullOrEmpty(export.FilePath) && File.Exists(export.FilePath))
            {
                File.Delete(export.FilePath);
            }

            _dbContext.ExportHistory.Remove(export);
            await _dbContext.SaveChangesAsync();

            return true;
        }

        public async Task<string> GetExportFilePathAsync(int exportId)
        {
            var export = await _dbContext.ExportHistory.FindAsync(exportId);
            if (export == null)
            {
                throw new KeyNotFoundException($"Export with ID {exportId} not found");
            }

            if (string.IsNullOrEmpty(export.FilePath) || !File.Exists(export.FilePath))
            {
                throw new FileNotFoundException($"Export file not found for export ID {exportId}");
            }

            return export.FilePath;
        }

        // Helper methods for exporting different content types
        private async Task<string> ExportTransactionsToCsvAsync(int userId, string parameters)
        {
            // Parse parameters
            var paramDict = JsonSerializer.Deserialize<Dictionary<string, object>>(parameters);

            // Build CSV content
            var csv = new StringBuilder();

            // Add header
            csv.AppendLine("Date,Description,Amount,Type,Category,Account,Status");

            // Get transactions based on parameters
            var query = _dbContext.Transactions.AsQueryable();

            // Filter by user
            query = query.Where(t => t.UserId == userId);

            // Apply additional filters from parameters
            // Implementation will be added

            // Get transactions
            var transactions = await query
                .Include(t => t.Category)
                .Include(t => t.Account)
                .OrderByDescending(t => t.Date)
                .ToListAsync();

            // Add transaction rows
            foreach (var transaction in transactions)
            {
                csv.AppendLine($"{transaction.Date:yyyy-MM-dd},{EscapeCsvField(transaction.Description)},{transaction.Amount},{transaction.Type},{EscapeCsvField(transaction.Category?.Name ?? "Uncategorized")},{EscapeCsvField(transaction.Account?.Name ?? "Unknown")},{transaction.Status}");
            }

            return csv.ToString();
        }

        private Task<byte[]> ExportTransactionsToPdfAsync(int userId, string parameters)
        {
            // PDF generation implementation will be added
            // For now, return empty byte array
            return Task.FromResult(new byte[0]);
        }

        private Task<string> ExportAccountsToCsvAsync(int userId, string parameters)
        {
            // Implementation will be added
            return Task.FromResult("");
        }

        private Task<byte[]> ExportAccountsToPdfAsync(int userId, string parameters)
        {
            // Implementation will be added
            return Task.FromResult(new byte[0]);
        }

        private Task<string> ExportReportToCsvAsync(int reportId)
        {
            // Implementation will be added
            return Task.FromResult("");
        }

        private Task<byte[]> ExportReportToPdfAsync(int reportId)
        {
            // Implementation will be added
            return Task.FromResult(new byte[0]);
        }

        // Helper method to escape CSV fields
        private string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
            {
                return "";
            }

            // If the field contains a comma, quote, or newline, wrap it in quotes
            if (field.Contains(',') || field.Contains('"') || field.Contains('\n'))
            {
                // Replace any quotes with double quotes
                field = field.Replace("\"", "\"\"");

                // Wrap in quotes
                return $"\"{field}\"";
            }

            return field;
        }
    }
}
