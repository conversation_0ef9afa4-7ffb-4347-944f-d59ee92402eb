using System;
using System.IO;
using System.Threading.Tasks;
using HisabKitab.Core.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Processing;

namespace HisabKitab.Infrastructure.Services
{
    public class FileStorageService : IFileStorageService
    {
        private readonly ILogger<FileStorageService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _baseStoragePath;
        private readonly string _baseUrl;

        public FileStorageService(
            ILogger<FileStorageService> logger,
            IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;

            // Set up storage paths
            string contentRootPath = AppDomain.CurrentDomain.BaseDirectory;
            _baseStoragePath = Path.Combine(contentRootPath, "Storage");
            _baseUrl = _configuration["FileStorage:BaseUrl"] ?? "/storage";

            // Create base directory if it doesn't exist
            if (!Directory.Exists(_baseStoragePath))
            {
                Directory.CreateDirectory(_baseStoragePath);
            }
        }

        public async Task<string> SaveFileAsync(IFormFile file, string containerName, string? fileName = null)
        {
            try
            {
                // Create container directory if it doesn't exist
                var containerPath = Path.Combine(_baseStoragePath, containerName);
                if (!Directory.Exists(containerPath))
                {
                    Directory.CreateDirectory(containerPath);
                }

                // Generate file name if not provided
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.FileName)}";
                }

                // Full path to save the file
                var filePath = Path.Combine(containerPath, fileName);

                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // Return the relative path
                return Path.Combine(containerName, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving file: {FileName}", fileName);
                throw;
            }
        }

        public async Task<string> SaveBase64FileAsync(string base64String, string containerName, string? fileName = null, string contentType = "image/jpeg")
        {
            try
            {
                // Remove data:image/jpeg;base64, prefix if present
                if (base64String.Contains(','))
                {
                    base64String = base64String.Split(',')[1];
                }

                // Convert base64 to byte array
                byte[] fileData = Convert.FromBase64String(base64String);

                // Create container directory if it doesn't exist
                var containerPath = Path.Combine(_baseStoragePath, containerName);
                if (!Directory.Exists(containerPath))
                {
                    Directory.CreateDirectory(containerPath);
                }

                // Generate file name if not provided
                if (string.IsNullOrEmpty(fileName))
                {
                    var extension = ".jpg";
                    if (contentType.Contains("png"))
                        extension = ".png";
                    else if (contentType.Contains("gif"))
                        extension = ".gif";

                    fileName = $"{Guid.NewGuid()}{extension}";
                }

                // Full path to save the file
                var filePath = Path.Combine(containerPath, fileName);

                // Save the file
                await File.WriteAllBytesAsync(filePath, fileData);

                // Return the relative path
                return Path.Combine(containerName, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving base64 file: {FileName}", fileName);
                throw;
            }
        }

        public async Task<string> SaveStreamAsync(Stream stream, string containerName, string fileName, string? contentType = null)
        {
            try
            {
                // Create container directory if it doesn't exist
                var containerPath = Path.Combine(_baseStoragePath, containerName);
                if (!Directory.Exists(containerPath))
                {
                    Directory.CreateDirectory(containerPath);
                }

                // Full path to save the file
                var filePath = Path.Combine(containerPath, fileName);

                // Save the file
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    stream.Position = 0;
                    await stream.CopyToAsync(fileStream);
                }

                // Return the relative path
                return Path.Combine(containerName, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving stream to file: {FileName}", fileName);
                throw;
            }
        }

        public async Task<Stream> GetFileStreamAsync(string filePath)
        {
            try
            {
                // Full path to the file
                var fullPath = Path.Combine(_baseStoragePath, filePath);

                // Check if file exists
                if (!await Task.Run(() => File.Exists(fullPath)))
                {
                    throw new FileNotFoundException($"File not found: {filePath}");
                }

                // Return file stream
                return await Task.Run(() => new FileStream(fullPath, FileMode.Open, FileAccess.Read));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting file stream: {FilePath}", filePath);
                throw;
            }
        }

        public async Task<byte[]> GetFileAsync(string filePath)
        {
            try
            {
                // Full path to the file
                var fullPath = Path.Combine(_baseStoragePath, filePath);

                // Check if file exists
                if (!File.Exists(fullPath))
                {
                    throw new FileNotFoundException($"File not found: {filePath}");
                }

                // Return file bytes
                return await File.ReadAllBytesAsync(fullPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting file: {FilePath}", filePath);
                throw;
            }
        }

        public async Task<bool> DeleteFileAsync(string filePath)
        {
            try
            {
                // Full path to the file
                var fullPath = Path.Combine(_baseStoragePath, filePath);

                // Check if file exists
                if (!await Task.Run(() => File.Exists(fullPath)))
                {
                    return false;
                }

                // Delete the file
                await Task.Run(() => File.Delete(fullPath));

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file: {FilePath}", filePath);
                throw;
            }
        }

        public async Task<bool> FileExistsAsync(string filePath)
        {
            try
            {
                // Full path to the file
                var fullPath = Path.Combine(_baseStoragePath, filePath);

                // Check if file exists
                return await Task.Run(() => File.Exists(fullPath));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if file exists: {FilePath}", filePath);
                throw;
            }
        }

        public string GetFileUrl(string filePath)
        {
            // Return the URL to access the file
            return $"{_baseUrl}/{filePath.Replace("\\", "/")}";
        }

        public async Task<byte[]> OptimizeImageAsync(byte[] imageData, int maxWidth = 1920, int maxHeight = 1080, int quality = 85)
        {
            try
            {
                using var image = Image.Load(imageData);

                // Resize if needed
                if (image.Width > maxWidth || image.Height > maxHeight)
                {
                    image.Mutate(x => x.Resize(new ResizeOptions
                    {
                        Mode = ResizeMode.Max,
                        Size = new Size(maxWidth, maxHeight)
                    }));
                }

                // Save with compression
                using var ms = new MemoryStream();
                await image.SaveAsJpegAsync(ms, new JpegEncoder
                {
                    Quality = quality
                });

                return ms.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error optimizing image");
                return imageData; // Return original if optimization fails
            }
        }

        public async Task<string> OptimizeBase64ImageAsync(string base64Image, int maxWidth = 1920, int maxHeight = 1080, int quality = 85)
        {
            try
            {
                // Remove data:image/jpeg;base64, prefix if present
                string prefix = "";
                if (base64Image.Contains(','))
                {
                    var parts = base64Image.Split(',');
                    prefix = parts[0] + ",";
                    base64Image = parts[1];
                }

                // Convert base64 to byte array
                byte[] imageData = Convert.FromBase64String(base64Image);

                // Optimize the image
                byte[] optimizedData = await OptimizeImageAsync(imageData, maxWidth, maxHeight, quality);

                // Convert back to base64
                return prefix + Convert.ToBase64String(optimizedData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error optimizing base64 image");
                return base64Image; // Return original if optimization fails
            }
        }
    }
}
