using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HisabKitab.Infrastructure.Services
{
    public class BudgetControlService : IBudgetControlService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<BudgetControlService> _logger;
        private readonly INotificationService _notificationService;

        public BudgetControlService(
            ApplicationDbContext dbContext,
            ILogger<BudgetControlService> logger,
            INotificationService notificationService)
        {
            _dbContext = dbContext;
            _logger = logger;
            _notificationService = notificationService;
        }

        public async Task<BudgetLimit> CreateBudgetLimitAsync(
            int categoryId,
            decimal amount,
            FrequencyType period,
            DateTime startDate,
            DateTime? endDate,
            int? userId,
            int? familyId,
            int? familyMemberUserId,
            decimal notificationThreshold = 80,
            bool rolloverUnused = false)
        {
            // Validate category exists
            if (!await _dbContext.Categories.AnyAsync(c => c.Id == categoryId))
            {
                throw new KeyNotFoundException($"Category with ID {categoryId} not found");
            }

            // Validate user exists if provided
            if (userId.HasValue && !await _dbContext.Users.AnyAsync(u => u.Id == userId.Value))
            {
                throw new KeyNotFoundException($"User with ID {userId.Value} not found");
            }

            // Validate family exists if provided
            if (familyId.HasValue && !await _dbContext.Families.AnyAsync(f => f.Id == familyId.Value))
            {
                throw new KeyNotFoundException($"Family with ID {familyId.Value} not found");
            }

            // Validate family member exists if provided
            if (familyId.HasValue && familyMemberUserId.HasValue)
            {
                var familyMember = await _dbContext.FamilyMembers
                    .FirstOrDefaultAsync(fm => fm.FamilyId == familyId.Value && fm.UserId == familyMemberUserId.Value);

                if (familyMember == null)
                {
                    throw new KeyNotFoundException($"User with ID {familyMemberUserId.Value} is not a member of family with ID {familyId.Value}");
                }
            }

            // Create budget limit
            var budgetLimit = new BudgetLimit
            {
                CategoryId = categoryId,
                Amount = amount,
                Period = period,
                StartDate = startDate,
                EndDate = endDate,
                UserId = userId,
                FamilyId = familyId,
                FamilyMemberId = familyMemberUserId,
                NotificationThreshold = notificationThreshold,
                RolloverUnused = rolloverUnused
            };

            _dbContext.BudgetLimits.Add(budgetLimit);
            await _dbContext.SaveChangesAsync();

            return budgetLimit;
        }

        public async Task<BudgetLimit> GetBudgetLimitByIdAsync(int budgetLimitId)
        {
            var budgetLimit = await _dbContext.BudgetLimits
                .Include(bl => bl.Category)
                .Include(bl => bl.User)
                .Include(bl => bl.Family)
                .FirstOrDefaultAsync(bl => bl.Id == budgetLimitId);

            if (budgetLimit == null)
            {
                throw new KeyNotFoundException($"Budget limit with ID {budgetLimitId} not found");
            }

            return budgetLimit;
        }

        public async Task<IEnumerable<BudgetLimit>> GetBudgetLimitsByUserIdAsync(int userId)
        {
            return await _dbContext.BudgetLimits
                .Where(bl => bl.UserId == userId)
                .Include(bl => bl.Category)
                .OrderBy(bl => bl.Category != null ? bl.Category.Name : string.Empty)
                .ToListAsync();
        }

        public async Task<IEnumerable<BudgetLimit>> GetBudgetLimitsByFamilyIdAsync(int familyId)
        {
            return await _dbContext.BudgetLimits
                .Where(bl => bl.FamilyId == familyId)
                .Include(bl => bl.Category)
                .OrderBy(bl => bl.Category != null ? bl.Category.Name : string.Empty)
                .ToListAsync();
        }

        public async Task<IEnumerable<BudgetLimit>> GetBudgetLimitsByFamilyMemberAsync(int familyId, int userId)
        {
            return await _dbContext.BudgetLimits
                .Where(bl => bl.FamilyId == familyId && bl.FamilyMemberId == userId)
                .Include(bl => bl.Category)
                .OrderBy(bl => bl.Category != null ? bl.Category.Name : string.Empty)
                .ToListAsync();
        }

        public async Task<BudgetLimit> UpdateBudgetLimitAsync(
            int budgetLimitId,
            decimal amount,
            FrequencyType period,
            DateTime startDate,
            DateTime? endDate,
            decimal notificationThreshold,
            bool rolloverUnused)
        {
            var budgetLimit = await _dbContext.BudgetLimits.FindAsync(budgetLimitId);
            if (budgetLimit == null)
            {
                throw new KeyNotFoundException($"Budget limit with ID {budgetLimitId} not found");
            }

            budgetLimit.Amount = amount;
            budgetLimit.Period = period;
            budgetLimit.StartDate = startDate;
            budgetLimit.EndDate = endDate;
            budgetLimit.NotificationThreshold = notificationThreshold;
            budgetLimit.RolloverUnused = rolloverUnused;
            budgetLimit.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            return budgetLimit;
        }

        public async Task<bool> DeleteBudgetLimitAsync(int budgetLimitId)
        {
            var budgetLimit = await _dbContext.BudgetLimits.FindAsync(budgetLimitId);
            if (budgetLimit == null)
            {
                throw new KeyNotFoundException($"Budget limit with ID {budgetLimitId} not found");
            }

            _dbContext.BudgetLimits.Remove(budgetLimit);
            await _dbContext.SaveChangesAsync();

            return true;
        }

        public async Task<SpendingLimit> CreateSpendingLimitAsync(
            int familyId,
            int familyMemberUserId,
            decimal amount,
            FrequencyType period,
            DateTime startDate,
            DateTime? endDate,
            int? categoryId,
            decimal notificationThreshold = 80,
            bool requireApprovalOverLimit = true)
        {
            // Validate family exists
            if (!await _dbContext.Families.AnyAsync(f => f.Id == familyId))
            {
                throw new KeyNotFoundException($"Family with ID {familyId} not found");
            }

            // Validate family member exists
            var familyMember = await _dbContext.FamilyMembers
                .FirstOrDefaultAsync(fm => fm.FamilyId == familyId && fm.UserId == familyMemberUserId);

            if (familyMember == null)
            {
                throw new KeyNotFoundException($"User with ID {familyMemberUserId} is not a member of family with ID {familyId}");
            }

            // Validate category exists if provided
            if (categoryId.HasValue && !await _dbContext.Categories.AnyAsync(c => c.Id == categoryId.Value))
            {
                throw new KeyNotFoundException($"Category with ID {categoryId.Value} not found");
            }

            // Create spending limit
            var spendingLimit = new SpendingLimit
            {
                FamilyId = familyId,
                FamilyMemberUserId = familyMemberUserId,
                Amount = amount,
                Period = period,
                StartDate = startDate,
                EndDate = endDate,
                CategoryId = categoryId,
                NotificationThreshold = notificationThreshold,
                RequireApprovalOverLimit = requireApprovalOverLimit,
                IsActive = true
            };

            _dbContext.SpendingLimits.Add(spendingLimit);
            await _dbContext.SaveChangesAsync();

            return spendingLimit;
        }

        public async Task<SpendingLimit> GetSpendingLimitByIdAsync(int spendingLimitId)
        {
            var spendingLimit = await _dbContext.SpendingLimits
                .Include(sl => sl.Family)
                .Include(sl => sl.FamilyMemberUser)
                .Include(sl => sl.Category)
                .FirstOrDefaultAsync(sl => sl.Id == spendingLimitId);

            if (spendingLimit == null)
            {
                throw new KeyNotFoundException($"Spending limit with ID {spendingLimitId} not found");
            }

            return spendingLimit;
        }

        public async Task<IEnumerable<SpendingLimit>> GetSpendingLimitsByFamilyIdAsync(int familyId)
        {
            return await _dbContext.SpendingLimits
                .Where(sl => sl.FamilyId == familyId)
                .Include(sl => sl.FamilyMemberUser)
                .Include(sl => sl.Category)
                .OrderBy(sl => sl.FamilyMemberUser != null ? sl.FamilyMemberUser.Username : string.Empty)
                .ToListAsync();
        }

        public async Task<IEnumerable<SpendingLimit>> GetSpendingLimitsByFamilyMemberAsync(int familyId, int userId)
        {
            return await _dbContext.SpendingLimits
                .Where(sl => sl.FamilyId == familyId && sl.FamilyMemberUserId == userId)
                .Include(sl => sl.Category)
                .OrderBy(sl => sl.Category != null ? sl.Category.Name : string.Empty)
                .ToListAsync();
        }

        public async Task<SpendingLimit> UpdateSpendingLimitAsync(
            int spendingLimitId,
            decimal amount,
            FrequencyType period,
            DateTime startDate,
            DateTime? endDate,
            int? categoryId,
            decimal notificationThreshold,
            bool requireApprovalOverLimit,
            bool isActive)
        {
            var spendingLimit = await _dbContext.SpendingLimits.FindAsync(spendingLimitId);
            if (spendingLimit == null)
            {
                throw new KeyNotFoundException($"Spending limit with ID {spendingLimitId} not found");
            }

            // Validate category exists if provided
            if (categoryId.HasValue && !await _dbContext.Categories.AnyAsync(c => c.Id == categoryId.Value))
            {
                throw new KeyNotFoundException($"Category with ID {categoryId.Value} not found");
            }

            spendingLimit.Amount = amount;
            spendingLimit.Period = period;
            spendingLimit.StartDate = startDate;
            spendingLimit.EndDate = endDate;
            spendingLimit.CategoryId = categoryId;
            spendingLimit.NotificationThreshold = notificationThreshold;
            spendingLimit.RequireApprovalOverLimit = requireApprovalOverLimit;
            spendingLimit.IsActive = isActive;
            spendingLimit.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            return spendingLimit;
        }

        public async Task<bool> DeleteSpendingLimitAsync(int spendingLimitId)
        {
            var spendingLimit = await _dbContext.SpendingLimits.FindAsync(spendingLimitId);
            if (spendingLimit == null)
            {
                throw new KeyNotFoundException($"Spending limit with ID {spendingLimitId} not found");
            }

            _dbContext.SpendingLimits.Remove(spendingLimit);
            await _dbContext.SaveChangesAsync();

            return true;
        }

        public async Task<decimal> GetCurrentSpendingAsync(
            int categoryId,
            int? userId,
            int? familyId,
            int? familyMemberUserId,
            DateTime? startDate,
            DateTime? endDate)
        {
            var query = _dbContext.Transactions.AsQueryable();

            // Filter by category
            query = query.Where(t => t.CategoryId == categoryId);

            // Filter by transaction type (expenses only)
            query = query.Where(t => t.Type == TransactionType.Expense);

            // Filter by date range
            if (startDate.HasValue)
            {
                query = query.Where(t => t.Date >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(t => t.Date <= endDate.Value);
            }

            // Filter by user or family
            if (userId.HasValue)
            {
                query = query.Where(t => t.UserId == userId.Value);
            }
            else if (familyId.HasValue)
            {
                // Get family accounts
                var familyAccountIds = await _dbContext.Accounts
                    .Where(a => a.FamilyId == familyId.Value)
                    .Select(a => a.Id)
                    .ToListAsync();

                // Filter by family accounts
                query = query.Where(t => familyAccountIds.Contains(t.AccountId));

                // Further filter by family member if provided
                if (familyMemberUserId.HasValue)
                {
                    query = query.Where(t => t.UserId == familyMemberUserId.Value);
                }
            }

            // Sum the amounts
            var totalSpending = await query.SumAsync(t => t.Amount);

            return totalSpending;
        }

        public async Task<decimal> GetBudgetRemainingAsync(
            int budgetLimitId,
            DateTime? asOfDate = null)
        {
            var budgetLimit = await GetBudgetLimitByIdAsync(budgetLimitId);
            var currentDate = asOfDate ?? DateTime.UtcNow;

            // Calculate date range based on budget period
            DateTime periodStart;
            DateTime periodEnd;

            switch (budgetLimit.Period)
            {
                case FrequencyType.Daily:
                    periodStart = currentDate.Date;
                    periodEnd = periodStart.AddDays(1).AddSeconds(-1);
                    break;
                case FrequencyType.Weekly:
                    // Start of the week (Sunday)
                    periodStart = currentDate.Date.AddDays(-(int)currentDate.DayOfWeek);
                    periodEnd = periodStart.AddDays(7).AddSeconds(-1);
                    break;
                case FrequencyType.Monthly:
                    periodStart = new DateTime(currentDate.Year, currentDate.Month, 1);
                    periodEnd = periodStart.AddMonths(1).AddSeconds(-1);
                    break;
                case FrequencyType.Yearly:
                    periodStart = new DateTime(currentDate.Year, 1, 1);
                    periodEnd = periodStart.AddYears(1).AddSeconds(-1);
                    break;
                default:
                    throw new ArgumentException($"Unsupported frequency type: {budgetLimit.Period}");
            }

            // Get current spending
            var currentSpending = await GetCurrentSpendingAsync(
                budgetLimit.CategoryId,
                budgetLimit.UserId,
                budgetLimit.FamilyId,
                budgetLimit.FamilyMemberId,
                periodStart,
                periodEnd);

            // Calculate remaining budget
            var remaining = budgetLimit.Amount - currentSpending;

            return remaining;
        }

        public async Task<decimal> GetSpendingLimitRemainingAsync(
            int spendingLimitId,
            DateTime? asOfDate = null)
        {
            var spendingLimit = await GetSpendingLimitByIdAsync(spendingLimitId);
            var currentDate = asOfDate ?? DateTime.UtcNow;

            // Calculate date range based on spending limit period
            DateTime periodStart;
            DateTime periodEnd;

            switch (spendingLimit.Period)
            {
                case FrequencyType.Daily:
                    periodStart = currentDate.Date;
                    periodEnd = periodStart.AddDays(1).AddSeconds(-1);
                    break;
                case FrequencyType.Weekly:
                    // Start of the week (Sunday)
                    periodStart = currentDate.Date.AddDays(-(int)currentDate.DayOfWeek);
                    periodEnd = periodStart.AddDays(7).AddSeconds(-1);
                    break;
                case FrequencyType.Monthly:
                    periodStart = new DateTime(currentDate.Year, currentDate.Month, 1);
                    periodEnd = periodStart.AddMonths(1).AddSeconds(-1);
                    break;
                case FrequencyType.Yearly:
                    periodStart = new DateTime(currentDate.Year, 1, 1);
                    periodEnd = periodStart.AddYears(1).AddSeconds(-1);
                    break;
                default:
                    throw new ArgumentException($"Unsupported frequency type: {spendingLimit.Period}");
            }

            // Get current spending
            var currentSpending = await GetCurrentSpendingAsync(
                spendingLimit.CategoryId ?? 0, // 0 means all categories
                null,
                spendingLimit.FamilyId,
                spendingLimit.FamilyMemberUserId,
                periodStart,
                periodEnd);

            // Calculate remaining spending limit
            var remaining = spendingLimit.Amount - currentSpending;

            return remaining;
        }

        public async Task<bool> IsOverBudgetAsync(
            int categoryId,
            decimal amount,
            int? userId,
            int? familyId,
            int? familyMemberUserId)
        {
            // Find applicable budget limit
            BudgetLimit? budgetLimit = null;

            if (userId.HasValue)
            {
                // Check for user-specific budget limit
                budgetLimit = await _dbContext.BudgetLimits
                    .Where(bl => bl.CategoryId == categoryId && bl.UserId == userId.Value)
                    .OrderByDescending(bl => bl.CreatedAt)
                    .FirstOrDefaultAsync();
            }
            else if (familyId.HasValue && familyMemberUserId.HasValue)
            {
                // Check for family member-specific budget limit
                budgetLimit = await _dbContext.BudgetLimits
                    .Where(bl => bl.CategoryId == categoryId && bl.FamilyId == familyId.Value && bl.FamilyMemberId == familyMemberUserId.Value)
                    .OrderByDescending(bl => bl.CreatedAt)
                    .FirstOrDefaultAsync();
            }
            else if (familyId.HasValue)
            {
                // Check for family-wide budget limit
                budgetLimit = await _dbContext.BudgetLimits
                    .Where(bl => bl.CategoryId == categoryId && bl.FamilyId == familyId.Value && bl.FamilyMemberId == null)
                    .OrderByDescending(bl => bl.CreatedAt)
                    .FirstOrDefaultAsync();
            }

            // If no budget limit found, not over budget
            if (budgetLimit == null)
            {
                return false;
            }

            // Get remaining budget
            var remaining = await GetBudgetRemainingAsync(budgetLimit.Id);

            // Check if transaction would exceed budget
            return amount > remaining;
        }

        public async Task<bool> IsOverSpendingLimitAsync(
            int familyId,
            int familyMemberUserId,
            decimal amount,
            int? categoryId = null)
        {
            // Find applicable spending limit
            SpendingLimit? spendingLimit = null;

            if (categoryId.HasValue)
            {
                // Check for category-specific spending limit
                spendingLimit = await _dbContext.SpendingLimits
                    .Where(sl => sl.FamilyId == familyId &&
                                sl.FamilyMemberUserId == familyMemberUserId &&
                                sl.CategoryId == categoryId.Value &&
                                sl.IsActive)
                    .OrderByDescending(sl => sl.CreatedAt)
                    .FirstOrDefaultAsync();
            }

            // If no category-specific limit, check for general spending limit
            if (spendingLimit == null)
            {
                spendingLimit = await _dbContext.SpendingLimits
                    .Where(sl => sl.FamilyId == familyId &&
                                sl.FamilyMemberUserId == familyMemberUserId &&
                                sl.CategoryId == null &&
                                sl.IsActive)
                    .OrderByDescending(sl => sl.CreatedAt)
                    .FirstOrDefaultAsync();
            }

            // If no spending limit found, not over limit
            if (spendingLimit == null)
            {
                return false;
            }

            // Get remaining spending limit
            var remaining = await GetSpendingLimitRemainingAsync(spendingLimit.Id);

            // Check if transaction would exceed spending limit
            return amount > remaining;
        }
    }
}
