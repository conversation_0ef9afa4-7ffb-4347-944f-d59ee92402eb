using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HisabKitab.Infrastructure.Services
{
    public class ApprovalService : IApprovalService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<ApprovalService> _logger;
        private readonly INotificationService _notificationService;
        private readonly IBudgetControlService _budgetControlService;
        private readonly ITransactionService _transactionService;

        public ApprovalService(
            ApplicationDbContext dbContext,
            ILogger<ApprovalService> logger,
            INotificationService notificationService,
            IBudgetControlService budgetControlService,
            ITransactionService transactionService)
        {
            _dbContext = dbContext;
            _logger = logger;
            _notificationService = notificationService;
            _budgetControlService = budgetControlService;
            _transactionService = transactionService;
        }

        public async Task<ExpenseApprovalRequest> CreateApprovalRequestAsync(
            int requestedByUserId,
            int familyId,
            decimal amount,
            string description,
            int categoryId)
        {
            // Validate user exists
            if (!await _dbContext.Users.AnyAsync(u => u.Id == requestedByUserId))
            {
                throw new KeyNotFoundException($"User with ID {requestedByUserId} not found");
            }

            // Validate family exists
            if (!await _dbContext.Families.AnyAsync(f => f.Id == familyId))
            {
                throw new KeyNotFoundException($"Family with ID {familyId} not found");
            }

            // Validate user is a member of the family
            var isFamilyMember = await _dbContext.FamilyMembers
                .AnyAsync(fm => fm.FamilyId == familyId && fm.UserId == requestedByUserId && fm.IsActive);
            
            if (!isFamilyMember)
            {
                throw new InvalidOperationException($"User with ID {requestedByUserId} is not a member of family with ID {familyId}");
            }

            // Validate category exists
            if (!await _dbContext.Categories.AnyAsync(c => c.Id == categoryId))
            {
                throw new KeyNotFoundException($"Category with ID {categoryId} not found");
            }

            // Create approval request
            var approvalRequest = new ExpenseApprovalRequest
            {
                RequestedByUserId = requestedByUserId,
                FamilyId = familyId,
                Amount = amount,
                Description = description,
                CategoryId = categoryId,
                Status = ApprovalStatus.Pending,
                RequestedAt = DateTime.UtcNow
            };

            _dbContext.ExpenseApprovalRequests.Add(approvalRequest);
            await _dbContext.SaveChangesAsync();

            // Notify family admins
            await NotifyFamilyAdminsAsync(familyId, approvalRequest);

            return approvalRequest;
        }

        public async Task<ExpenseApprovalRequest> GetApprovalRequestByIdAsync(int requestId)
        {
            var approvalRequest = await _dbContext.ExpenseApprovalRequests
                .Include(ar => ar.RequestedByUser)
                .Include(ar => ar.ApprovedByUser)
                .Include(ar => ar.Family)
                .Include(ar => ar.Category)
                .Include(ar => ar.ResultingTransaction)
                .FirstOrDefaultAsync(ar => ar.Id == requestId);

            if (approvalRequest == null)
            {
                throw new KeyNotFoundException($"Approval request with ID {requestId} not found");
            }

            return approvalRequest;
        }

        public async Task<IEnumerable<ExpenseApprovalRequest>> GetApprovalRequestsByFamilyIdAsync(
            int familyId,
            ApprovalStatus? status = null)
        {
            var query = _dbContext.ExpenseApprovalRequests
                .Where(ar => ar.FamilyId == familyId);

            if (status.HasValue)
            {
                query = query.Where(ar => ar.Status == status.Value);
            }

            return await query
                .Include(ar => ar.RequestedByUser)
                .Include(ar => ar.ApprovedByUser)
                .Include(ar => ar.Category)
                .OrderByDescending(ar => ar.RequestedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<ExpenseApprovalRequest>> GetApprovalRequestsByUserIdAsync(
            int userId,
            bool asRequester = true,
            ApprovalStatus? status = null)
        {
            var query = _dbContext.ExpenseApprovalRequests.AsQueryable();

            if (asRequester)
            {
                query = query.Where(ar => ar.RequestedByUserId == userId);
            }
            else
            {
                // Get families where user is an admin
                var adminFamilyIds = await _dbContext.FamilyMembers
                    .Where(fm => fm.UserId == userId && fm.Role == "Admin" && fm.IsActive)
                    .Select(fm => fm.FamilyId)
                    .ToListAsync();

                query = query.Where(ar => adminFamilyIds.Contains(ar.FamilyId));
            }

            if (status.HasValue)
            {
                query = query.Where(ar => ar.Status == status.Value);
            }

            return await query
                .Include(ar => ar.RequestedByUser)
                .Include(ar => ar.ApprovedByUser)
                .Include(ar => ar.Family)
                .Include(ar => ar.Category)
                .OrderByDescending(ar => ar.RequestedAt)
                .ToListAsync();
        }

        public async Task<ExpenseApprovalRequest> ApproveRequestAsync(
            int requestId,
            int approvedByUserId,
            int? accountId = null)
        {
            var approvalRequest = await GetApprovalRequestByIdAsync(requestId);

            // Validate request is pending
            if (approvalRequest.Status != ApprovalStatus.Pending)
            {
                throw new InvalidOperationException($"Approval request with ID {requestId} is not pending");
            }

            // Validate user exists
            if (!await _dbContext.Users.AnyAsync(u => u.Id == approvedByUserId))
            {
                throw new KeyNotFoundException($"User with ID {approvedByUserId} not found");
            }

            // Validate user is a family admin
            var isFamilyAdmin = await _dbContext.FamilyMembers
                .AnyAsync(fm => fm.FamilyId == approvalRequest.FamilyId && fm.UserId == approvedByUserId && fm.Role == "Admin" && fm.IsActive);
            
            if (!isFamilyAdmin)
            {
                throw new InvalidOperationException($"User with ID {approvedByUserId} is not an admin of family with ID {approvalRequest.FamilyId}");
            }

            // Update approval request
            approvalRequest.Status = ApprovalStatus.Approved;
            approvalRequest.ApprovedByUserId = approvedByUserId;
            approvalRequest.RespondedAt = DateTime.UtcNow;
            approvalRequest.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            // Create transaction if account ID is provided
            if (accountId.HasValue)
            {
                var transaction = await CreateTransactionFromApprovalAsync(requestId, accountId.Value);
                approvalRequest.ResultingTransactionId = transaction.Id;
                await _dbContext.SaveChangesAsync();
            }

            // Notify requester
            await _notificationService.CreateNotificationAsync(
                approvalRequest.RequestedByUserId,
                "Expense Approved",
                $"Your expense request for {approvalRequest.Amount:C} has been approved",
                "ExpenseApproval",
                approvalRequest.Id);

            return approvalRequest;
        }

        public async Task<ExpenseApprovalRequest> RejectRequestAsync(
            int requestId,
            int rejectedByUserId,
            string rejectionReason)
        {
            var approvalRequest = await GetApprovalRequestByIdAsync(requestId);

            // Validate request is pending
            if (approvalRequest.Status != ApprovalStatus.Pending)
            {
                throw new InvalidOperationException($"Approval request with ID {requestId} is not pending");
            }

            // Validate user exists
            if (!await _dbContext.Users.AnyAsync(u => u.Id == rejectedByUserId))
            {
                throw new KeyNotFoundException($"User with ID {rejectedByUserId} not found");
            }

            // Validate user is a family admin
            var isFamilyAdmin = await _dbContext.FamilyMembers
                .AnyAsync(fm => fm.FamilyId == approvalRequest.FamilyId && fm.UserId == rejectedByUserId && fm.Role == "Admin" && fm.IsActive);
            
            if (!isFamilyAdmin)
            {
                throw new InvalidOperationException($"User with ID {rejectedByUserId} is not an admin of family with ID {approvalRequest.FamilyId}");
            }

            // Update approval request
            approvalRequest.Status = ApprovalStatus.Rejected;
            approvalRequest.ApprovedByUserId = rejectedByUserId; // Using the same field for rejection
            approvalRequest.RejectionReason = rejectionReason;
            approvalRequest.RespondedAt = DateTime.UtcNow;
            approvalRequest.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            // Notify requester
            await _notificationService.CreateNotificationAsync(
                approvalRequest.RequestedByUserId,
                "Expense Rejected",
                $"Your expense request for {approvalRequest.Amount:C} has been rejected: {rejectionReason}",
                "ExpenseApproval",
                approvalRequest.Id);

            return approvalRequest;
        }

        public async Task<bool> RequiresApprovalAsync(
            int familyId,
            int userId,
            decimal amount,
            int categoryId)
        {
            // Check if user is a family member
            var isFamilyMember = await _dbContext.FamilyMembers
                .AnyAsync(fm => fm.FamilyId == familyId && fm.UserId == userId && fm.IsActive);
            
            if (!isFamilyMember)
            {
                throw new InvalidOperationException($"User with ID {userId} is not a member of family with ID {familyId}");
            }

            // Check if user is a family admin (admins don't need approval)
            var isFamilyAdmin = await _dbContext.FamilyMembers
                .AnyAsync(fm => fm.FamilyId == familyId && fm.UserId == userId && fm.Role == "Admin" && fm.IsActive);
            
            if (isFamilyAdmin)
            {
                return false;
            }

            // Check if over spending limit
            var isOverLimit = await _budgetControlService.IsOverSpendingLimitAsync(familyId, userId, amount, categoryId);
            
            // If over limit and the limit requires approval, then approval is required
            if (isOverLimit)
            {
                // Find applicable spending limit
                var spendingLimit = await _dbContext.SpendingLimits
                    .Where(sl => sl.FamilyId == familyId && 
                                sl.FamilyMemberUserId == userId && 
                                (sl.CategoryId == categoryId || sl.CategoryId == null) &&
                                sl.IsActive)
                    .OrderByDescending(sl => sl.CategoryId.HasValue) // Prioritize category-specific limits
                    .ThenByDescending(sl => sl.CreatedAt)
                    .FirstOrDefaultAsync();
                
                if (spendingLimit != null && spendingLimit.RequireApprovalOverLimit)
                {
                    return true;
                }
            }

            // Check if over budget
            var isOverBudget = await _budgetControlService.IsOverBudgetAsync(categoryId, amount, null, familyId, userId);
            
            // If over budget, approval is required
            if (isOverBudget)
            {
                return true;
            }

            // No approval required
            return false;
        }

        public async Task<Transaction> CreateTransactionFromApprovalAsync(
            int requestId,
            int accountId)
        {
            var approvalRequest = await GetApprovalRequestByIdAsync(requestId);

            // Validate request is approved
            if (approvalRequest.Status != ApprovalStatus.Approved)
            {
                throw new InvalidOperationException($"Approval request with ID {requestId} is not approved");
            }

            // Validate account exists
            if (!await _dbContext.Accounts.AnyAsync(a => a.Id == accountId))
            {
                throw new KeyNotFoundException($"Account with ID {accountId} not found");
            }

            // Create transaction
            var transaction = await _transactionService.CreateTransactionAsync(
                approvalRequest.Amount,
                approvalRequest.Description,
                DateTime.UtcNow,
                TransactionType.Expense,
                accountId,
                null, // toAccountId
                approvalRequest.CategoryId,
                null, // priorityId
                approvalRequest.RequestedByUserId,
                "Completed",
                approvalRequest.ApprovedByUserId);

            return transaction;
        }

        // Helper method to notify family admins about a new approval request
        private async Task NotifyFamilyAdminsAsync(int familyId, ExpenseApprovalRequest approvalRequest)
        {
            // Get family admins
            var familyAdmins = await _dbContext.FamilyMembers
                .Where(fm => fm.FamilyId == familyId && fm.Role == "Admin" && fm.IsActive)
                .Select(fm => fm.UserId)
                .ToListAsync();

            // Create notification for each admin
            foreach (var adminId in familyAdmins)
            {
                // Skip if admin is the requester
                if (adminId == approvalRequest.RequestedByUserId)
                {
                    continue;
                }

                await _notificationService.CreateNotificationAsync(
                    adminId,
                    "New Expense Approval Request",
                    $"{approvalRequest.RequestedByUser?.Username} has requested approval for {approvalRequest.Amount:C}",
                    "ExpenseApproval",
                    approvalRequest.Id);
            }
        }
    }
}
