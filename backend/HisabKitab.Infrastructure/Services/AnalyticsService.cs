using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HisabKitab.Infrastructure.Services
{
    public class AnalyticsService : IAnalyticsService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<AnalyticsService> _logger;
        private readonly INotificationService _notificationService;

        public AnalyticsService(
            ApplicationDbContext dbContext,
            ILogger<AnalyticsService> logger,
            INotificationService notificationService)
        {
            _dbContext = dbContext;
            _logger = logger;
            _notificationService = notificationService;
        }

        public async Task<IEnumerable<AnalyticsMetric>> GetMetricsAsync(
            int userId,
            DateTime startDate,
            DateTime endDate,
            string period = "Monthly",
            int? familyId = null,
            int? categoryId = null,
            int? accountId = null)
        {
            // Validate user exists
            if (!await _dbContext.Users.AnyAsync(u => u.Id == userId))
            {
                throw new KeyNotFoundException($"User with ID {userId} not found");
            }

            // Get or generate metrics
            var metrics = await _dbContext.AnalyticsMetrics
                .Where(m => m.UserId == userId &&
                           m.Date >= startDate &&
                           m.Date <= endDate &&
                           m.Period == period &&
                           (familyId == null || m.FamilyId == familyId) &&
                           (categoryId == null || m.CategoryId == categoryId) &&
                           (accountId == null || m.AccountId == accountId))
                .OrderBy(m => m.Date)
                .ToListAsync();

            // If metrics don't exist, generate them
            if (metrics.Count == 0)
            {
                metrics = await GenerateMetricsAsync(userId, startDate, endDate, period, familyId, categoryId, accountId);
            }

            return metrics;
        }

        public async Task<Dictionary<string, decimal>> GetCategoryBreakdownAsync(
            int userId,
            DateTime startDate,
            DateTime endDate,
            int? familyId = null)
        {
            // Get user's transactions
            var query = _dbContext.Transactions.AsQueryable();

            // Filter by user and date range
            query = query.Where(t => t.UserId == userId &&
                                    t.Date >= startDate &&
                                    t.Date <= endDate);

            // If family ID is provided, include family transactions
            if (familyId.HasValue)
            {
                // Get family accounts
                var familyAccountIds = await _dbContext.Accounts
                    .Where(a => a.FamilyId == familyId)
                    .Select(a => a.Id)
                    .ToListAsync();

                // Include transactions from family accounts
                query = query.Where(t => t.UserId == userId || familyAccountIds.Contains(t.AccountId));
            }

            // Group by category and sum amounts
            var categoryBreakdown = await query
                .Where(t => t.Type == TransactionType.Expense) // Only expenses
                .GroupBy(t => t.Category != null ? t.Category.Name : "Uncategorized")
                .Select(g => new
                {
                    CategoryName = g.Key,
                    TotalAmount = g.Sum(t => t.Amount)
                })
                .ToDictionaryAsync(x => x.CategoryName, x => x.TotalAmount);

            return categoryBreakdown;
        }

        public async Task<Dictionary<DateTime, decimal>> GetTrendDataAsync(
            int userId,
            string metricType,
            DateTime startDate,
            DateTime endDate,
            string period = "Monthly",
            int? familyId = null,
            int? categoryId = null)
        {
            // Get metrics
            var metrics = await GetMetricsAsync(userId, startDate, endDate, period, familyId, categoryId);

            // Filter by metric type and create trend data
            var trendData = metrics
                .Where(m => m.Type.Equals(metricType, StringComparison.OrdinalIgnoreCase))
                .OrderBy(m => m.Date)
                .ToDictionary(m => m.Date, m => m.Value);

            return trendData;
        }

        public async Task<decimal> CalculateNetWorthAsync(
            int userId,
            DateTime? asOfDate = null,
            int? familyId = null)
        {
            var date = asOfDate ?? DateTime.UtcNow;

            // Get user's accounts
            var query = _dbContext.Accounts.AsQueryable();

            // Filter by user
            query = query.Where(a => a.UserId == userId && a.IsActive && !a.ExcludeFromStats);

            // If family ID is provided, include family accounts
            if (familyId.HasValue)
            {
                // Include family accounts
                query = query.Where(a => a.UserId == userId || a.FamilyId == familyId);
            }

            // Calculate net worth (sum of all account balances)
            var netWorth = await query.SumAsync(a => a.Balance);

            return netWorth;
        }

        public async Task<Dictionary<DateTime, decimal>> GetNetWorthTrendAsync(
            int userId,
            DateTime startDate,
            DateTime endDate,
            string period = "Monthly",
            int? familyId = null)
        {
            // Get net worth metrics
            var metrics = await GetMetricsAsync(userId, startDate, endDate, period, familyId);

            // Filter by net worth metric type and create trend data
            var trendData = metrics
                .Where(m => m.Type.Equals("NetWorth", StringComparison.OrdinalIgnoreCase))
                .OrderBy(m => m.Date)
                .ToDictionary(m => m.Date, m => m.Value);

            return trendData;
        }

        public async Task<Dictionary<string, decimal>> AnalyzeCashflowAsync(
            int userId,
            DateTime startDate,
            DateTime endDate,
            int? familyId = null)
        {
            // Get user's transactions
            var query = _dbContext.Transactions.AsQueryable();

            // Filter by user and date range
            query = query.Where(t => t.UserId == userId &&
                                    t.Date >= startDate &&
                                    t.Date <= endDate);

            // If family ID is provided, include family transactions
            if (familyId.HasValue)
            {
                // Get family accounts
                var familyAccountIds = await _dbContext.Accounts
                    .Where(a => a.FamilyId == familyId)
                    .Select(a => a.Id)
                    .ToListAsync();

                // Include transactions from family accounts
                query = query.Where(t => t.UserId == userId || familyAccountIds.Contains(t.AccountId));
            }

            // Calculate total income
            var totalIncome = await query
                .Where(t => t.Type == TransactionType.Income)
                .SumAsync(t => t.Amount);

            // Calculate total expenses
            var totalExpenses = await query
                .Where(t => t.Type == TransactionType.Expense)
                .SumAsync(t => t.Amount);

            // Calculate net cashflow
            var netCashflow = totalIncome - totalExpenses;

            // Return cashflow analysis
            return new Dictionary<string, decimal>
            {
                { "TotalIncome", totalIncome },
                { "TotalExpenses", totalExpenses },
                { "NetCashflow", netCashflow }
            };
        }

        public async Task<IEnumerable<AnomalyDetection>> DetectAnomaliesAsync(
            int userId,
            DateTime startDate,
            DateTime endDate,
            int? familyId = null)
        {
            // Get existing anomalies
            var existingAnomalies = await _dbContext.AnomalyDetections
                .Where(a => a.UserId == userId &&
                           a.DetectedAt >= startDate &&
                           a.DetectedAt <= endDate &&
                           (familyId == null || a.FamilyId == familyId))
                .ToListAsync();

            // If anomalies exist and are recent, return them
            if (existingAnomalies.Any() && existingAnomalies.Max(a => a.DetectedAt) > DateTime.UtcNow.AddDays(-1))
            {
                return existingAnomalies;
            }

            // Detect new anomalies
            var anomalies = new List<AnomalyDetection>();

            // Detect spending spikes
            var spendingSpikes = await DetectSpendingSpikesAsync(userId, startDate, endDate, familyId);
            anomalies.AddRange(spendingSpikes);

            // Detect unusual category spending
            var unusualCategories = await DetectUnusualCategorySpendingAsync(userId, startDate, endDate, familyId);
            anomalies.AddRange(unusualCategories);

            // Save detected anomalies
            if (anomalies.Any())
            {
                _dbContext.AnomalyDetections.AddRange(anomalies);
                await _dbContext.SaveChangesAsync();

                // Create notifications for anomalies
                foreach (var anomaly in anomalies)
                {
                    await _notificationService.CreateNotificationAsync(
                        userId,
                        "Anomaly Detected",
                        anomaly.Description,
                        "Anomaly",
                        anomaly.Id);
                }
            }

            return anomalies;
        }

        public async Task<AnomalyDetection> GetAnomalyByIdAsync(int anomalyId)
        {
            var anomaly = await _dbContext.AnomalyDetections
                .Include(a => a.User)
                .Include(a => a.Family)
                .Include(a => a.Transaction)
                .Include(a => a.Category)
                .Include(a => a.Account)
                .FirstOrDefaultAsync(a => a.Id == anomalyId);

            if (anomaly == null)
            {
                throw new KeyNotFoundException($"Anomaly with ID {anomalyId} not found");
            }

            return anomaly;
        }

        public async Task<bool> UpdateAnomalyStatusAsync(
            int anomalyId,
            AnomalyStatus status,
            string? resolutionNotes = null)
        {
            var anomaly = await _dbContext.AnomalyDetections.FindAsync(anomalyId);
            if (anomaly == null)
            {
                throw new KeyNotFoundException($"Anomaly with ID {anomalyId} not found");
            }

            anomaly.Status = status;
            anomaly.UpdatedAt = DateTime.UtcNow;

            if (status == AnomalyStatus.Resolved || status == AnomalyStatus.FalsePositive)
            {
                anomaly.ResolvedAt = DateTime.UtcNow;
                anomaly.ResolutionNotes = resolutionNotes ?? string.Empty;
            }

            await _dbContext.SaveChangesAsync();

            return true;
        }

        // Helper methods for generating metrics and detecting anomalies
        private Task<List<AnalyticsMetric>> GenerateMetricsAsync(
            int userId,
            DateTime startDate,
            DateTime endDate,
            string period,
            int? familyId,
            int? categoryId,
            int? accountId)
        {
            var metrics = new List<AnalyticsMetric>();

            // Implementation will be added

            return Task.FromResult(metrics);
        }

        private Task<List<AnomalyDetection>> DetectSpendingSpikesAsync(
            int userId,
            DateTime startDate,
            DateTime endDate,
            int? familyId)
        {
            var anomalies = new List<AnomalyDetection>();

            // Implementation will be added

            return Task.FromResult(anomalies);
        }

        private Task<List<AnomalyDetection>> DetectUnusualCategorySpendingAsync(
            int userId,
            DateTime startDate,
            DateTime endDate,
            int? familyId)
        {
            var anomalies = new List<AnomalyDetection>();

            // Implementation will be added

            return Task.FromResult(anomalies);
        }
    }
}
