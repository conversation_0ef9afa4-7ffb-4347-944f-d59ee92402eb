using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Interfaces;
using HisabKitab.Core.Helpers;
using HisabKitab.Infrastructure.Data;

namespace HisabKitab.Infrastructure.Services
{
    public class SavingsService : ISavingsService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<SavingsService> _logger;

        public SavingsService(ApplicationDbContext dbContext, ILogger<SavingsService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        #region Savings Goal Operations

        public async Task<SavingsGoal> CreateSavingsGoalAsync(
            string title,
            string description,
            decimal targetAmount,
            DateTime startDate,
            DateTime targetDate,
            int? userId,
            int? familyId,
            bool isShared,
            int? priorityId,
            bool autoContribute,
            decimal? autoContributeAmount,
            FrequencyType? autoContributeFrequency)
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(title))
            {
                throw new ArgumentException("Title is required");
            }

            if (targetAmount <= 0)
            {
                throw new ArgumentException("Target amount must be greater than zero");
            }

            if (startDate > targetDate)
            {
                throw new ArgumentException("Start date must be before target date");
            }

            if (isShared && !familyId.HasValue)
            {
                throw new ArgumentException("Family ID is required for shared goals");
            }

            if (!isShared && !userId.HasValue)
            {
                throw new ArgumentException("User ID is required for personal goals");
            }

            if (autoContribute)
            {
                if (!autoContributeAmount.HasValue || autoContributeAmount.Value <= 0)
                {
                    throw new ArgumentException("Auto-contribute amount must be greater than zero");
                }

                if (!autoContributeFrequency.HasValue)
                {
                    throw new ArgumentException("Auto-contribute frequency is required");
                }
            }

            // Validate priority if provided
            if (priorityId.HasValue)
            {
                var priority = await _dbContext.Priorities.FindAsync(priorityId.Value);
                if (priority == null)
                {
                    throw new KeyNotFoundException($"Priority with ID {priorityId.Value} not found");
                }
            }

            // Validate family if provided
            if (familyId.HasValue)
            {
                var family = await _dbContext.Families.FindAsync(familyId.Value);
                if (family == null)
                {
                    throw new KeyNotFoundException($"Family with ID {familyId.Value} not found");
                }
            }

            // Create savings goal
            var savingsGoal = new SavingsGoal
            {
                Title = title,
                Description = description,
                TargetAmount = targetAmount,
                CurrentAmount = 0,
                StartDate = startDate,
                TargetDate = targetDate,
                UserId = userId,
                FamilyId = familyId,
                IsShared = isShared,
                PriorityId = priorityId,
                AutoContribute = autoContribute,
                AutoContributeAmount = autoContributeAmount,
                AutoContributeFrequency = autoContributeFrequency,
                Status = "Active",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Ensure all string properties are initialized to prevent not-null constraint violations
            savingsGoal = EntityHelper.EnsureStringPropertiesInitialized(savingsGoal);

            _dbContext.SavingsGoals.Add(savingsGoal);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Savings goal created: {savingsGoal.Title} with ID {savingsGoal.Id}");

            return savingsGoal;
        }

        public async Task<SavingsGoal> GetSavingsGoalByIdAsync(int goalId)
        {
            var savingsGoal = await _dbContext.SavingsGoals
                .Include(g => g.User)
                .Include(g => g.Family)
                .Include(g => g.Priority)
                .Include(g => g.Contributions)
                .Include(g => g.LinkedWishlistItems)
                .FirstOrDefaultAsync(g => g.Id == goalId);

            if (savingsGoal == null)
            {
                throw new KeyNotFoundException($"Savings goal with ID {goalId} not found");
            }

            return savingsGoal;
        }

        public async Task<IEnumerable<SavingsGoal>> GetSavingsGoalsByUserIdAsync(
            int userId,
            string? status = null,
            DateTime? startDate = null,
            DateTime? endDate = null)
        {
            var query = _dbContext.SavingsGoals
                .Include(g => g.Priority)
                .Include(g => g.Contributions)
                .Where(g => g.UserId == userId || (g.IsShared && g.Family != null && g.Family.Members.Any(m => m.UserId == userId)));

            if (!string.IsNullOrWhiteSpace(status))
            {
                query = query.Where(g => g.Status == status);
            }

            if (startDate.HasValue)
            {
                query = query.Where(g => g.StartDate >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(g => g.TargetDate <= endDate.Value);
            }

            return await query.ToListAsync();
        }

        public async Task<IEnumerable<SavingsGoal>> GetSavingsGoalsByFamilyIdAsync(
            int familyId,
            string? status = null,
            DateTime? startDate = null,
            DateTime? endDate = null)
        {
            var query = _dbContext.SavingsGoals
                .Include(g => g.Priority)
                .Include(g => g.Contributions)
                .Where(g => g.FamilyId == familyId && g.IsShared);

            if (!string.IsNullOrWhiteSpace(status))
            {
                query = query.Where(g => g.Status == status);
            }

            if (startDate.HasValue)
            {
                query = query.Where(g => g.StartDate >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(g => g.TargetDate <= endDate.Value);
            }

            return await query.ToListAsync();
        }

        public async Task<SavingsGoal> UpdateSavingsGoalAsync(
            int goalId,
            string? title = null,
            string? description = null,
            decimal? targetAmount = null,
            DateTime? startDate = null,
            DateTime? targetDate = null,
            int? priorityId = null,
            bool? autoContribute = null,
            decimal? autoContributeAmount = null,
            FrequencyType? autoContributeFrequency = null,
            string? status = null)
        {
            var savingsGoal = await _dbContext.SavingsGoals.FindAsync(goalId);
            if (savingsGoal == null)
            {
                throw new KeyNotFoundException($"Savings goal with ID {goalId} not found");
            }

            // Update properties if provided
            if (!string.IsNullOrWhiteSpace(title))
            {
                savingsGoal.Title = title;
            }

            if (description != null)
            {
                savingsGoal.Description = description;
            }

            if (targetAmount.HasValue)
            {
                if (targetAmount.Value <= 0)
                {
                    throw new ArgumentException("Target amount must be greater than zero");
                }
                savingsGoal.TargetAmount = targetAmount.Value;
            }

            if (startDate.HasValue)
            {
                savingsGoal.StartDate = startDate.Value;
            }

            if (targetDate.HasValue)
            {
                if (savingsGoal.StartDate > targetDate.Value)
                {
                    throw new ArgumentException("Start date must be before target date");
                }
                savingsGoal.TargetDate = targetDate.Value;
            }

            if (priorityId.HasValue)
            {
                var priority = await _dbContext.Priorities.FindAsync(priorityId.Value);
                if (priority == null)
                {
                    throw new KeyNotFoundException($"Priority with ID {priorityId.Value} not found");
                }
                savingsGoal.PriorityId = priorityId.Value;
            }

            if (autoContribute.HasValue)
            {
                savingsGoal.AutoContribute = autoContribute.Value;
            }

            if (autoContributeAmount.HasValue)
            {
                if (autoContributeAmount.Value <= 0)
                {
                    throw new ArgumentException("Auto-contribute amount must be greater than zero");
                }
                savingsGoal.AutoContributeAmount = autoContributeAmount.Value;
            }

            if (autoContributeFrequency.HasValue)
            {
                savingsGoal.AutoContributeFrequency = autoContributeFrequency.Value;
            }

            if (!string.IsNullOrWhiteSpace(status))
            {
                if (status != "Active" && status != "Achieved" && status != "Abandoned")
                {
                    throw new ArgumentException("Invalid status. Must be 'Active', 'Achieved', or 'Abandoned'");
                }
                savingsGoal.Status = status;
            }

            savingsGoal.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Savings goal updated: {savingsGoal.Title} with ID {savingsGoal.Id}");

            return savingsGoal;
        }

        public async Task<bool> DeleteSavingsGoalAsync(int goalId)
        {
            var savingsGoal = await _dbContext.SavingsGoals
                .Include(g => g.Contributions)
                .Include(g => g.LinkedWishlistItems)
                .FirstOrDefaultAsync(g => g.Id == goalId);

            if (savingsGoal == null)
            {
                throw new KeyNotFoundException($"Savings goal with ID {goalId} not found");
            }

            // Start a transaction
            using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                // Unlink any wishlist items
                foreach (var wishlistItem in savingsGoal.LinkedWishlistItems)
                {
                    wishlistItem.LinkedSavingsGoalId = null;
                    wishlistItem.UpdatedAt = DateTime.UtcNow;
                }

                // Remove contributions
                _dbContext.SavingsContributions.RemoveRange(savingsGoal.Contributions);

                // Remove the savings goal
                _dbContext.SavingsGoals.Remove(savingsGoal);

                await _dbContext.SaveChangesAsync();

                // Commit transaction
                await transaction.CommitAsync();

                _logger.LogInformation($"Savings goal deleted: {savingsGoal.Title} with ID {savingsGoal.Id}");

                return true;
            }
            catch (Exception ex)
            {
                // Rollback transaction on error
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"Error deleting savings goal with ID {goalId}");
                throw;
            }
        }

        #endregion

        #region Forecasting Operations

        public async Task<(DateTime projectedCompletionDate, decimal monthlyContributionNeeded, decimal weeklyContributionNeeded, bool isAchievable, IEnumerable<(DateTime date, decimal amount)> timelineData)>
            GenerateSavingsGoalForecastAsync(int goalId)
        {
            var savingsGoal = await GetSavingsGoalByIdAsync(goalId);

            // Calculate days remaining
            var today = DateTime.UtcNow.Date;
            var daysRemaining = Math.Max(0, (savingsGoal.TargetDate.Date - today).Days);

            // Calculate remaining amount
            var remainingAmount = Math.Max(0, savingsGoal.TargetAmount - savingsGoal.CurrentAmount);

            // Calculate if achievable
            bool isAchievable = daysRemaining > 0;

            // Calculate projected completion date based on contribution history
            DateTime projectedCompletionDate;
            decimal monthlyContributionNeeded = 0;
            decimal weeklyContributionNeeded = 0;

            if (remainingAmount <= 0)
            {
                // Goal already achieved
                projectedCompletionDate = today;
            }
            else if (daysRemaining <= 0)
            {
                // Target date already passed
                projectedCompletionDate = savingsGoal.TargetDate.AddDays(30); // Default to 30 days from target
                isAchievable = false;
            }
            else
            {
                // Calculate average contribution rate
                var contributions = await GetContributionsBySavingsGoalIdAsync(goalId);

                if (!contributions.Any())
                {
                    // No contributions yet, use simple projection
                    monthlyContributionNeeded = remainingAmount / (daysRemaining / 30.0m);
                    weeklyContributionNeeded = remainingAmount / (daysRemaining / 7.0m);
                    projectedCompletionDate = savingsGoal.TargetDate;
                }
                else
                {
                    // Calculate average daily contribution
                    var firstContribution = contributions.OrderBy(c => c.ContributionDate).First();
                    var daysSinceStart = Math.Max(1, (today - firstContribution.ContributionDate.Date).Days);
                    var totalContributed = savingsGoal.CurrentAmount;
                    var avgDailyContribution = totalContributed / daysSinceStart;

                    // Calculate projected completion
                    if (avgDailyContribution > 0)
                    {
                        var daysToCompletion = remainingAmount / avgDailyContribution;
                        projectedCompletionDate = today.AddDays((double)daysToCompletion);

                        // Check if achievable
                        isAchievable = projectedCompletionDate <= savingsGoal.TargetDate;
                    }
                    else
                    {
                        // No progress being made
                        projectedCompletionDate = savingsGoal.TargetDate;
                        isAchievable = false;
                    }

                    // Calculate needed contribution rates
                    monthlyContributionNeeded = remainingAmount / (daysRemaining / 30.0m);
                    weeklyContributionNeeded = remainingAmount / (daysRemaining / 7.0m);
                }
            }

            // Generate timeline data
            var timelineData = new List<(DateTime date, decimal amount)>();

            // Add current point
            timelineData.Add((today, savingsGoal.CurrentAmount));

            if (remainingAmount > 0 && daysRemaining > 0)
            {
                // Add projected points (monthly intervals)
                var monthlyIncrement = remainingAmount / Math.Ceiling(daysRemaining / 30.0m);
                var currentAmount = savingsGoal.CurrentAmount;
                var currentDate = today;

                while (currentDate < savingsGoal.TargetDate)
                {
                    currentDate = currentDate.AddMonths(1);
                    currentAmount += monthlyIncrement;

                    if (currentAmount >= savingsGoal.TargetAmount)
                    {
                        timelineData.Add((currentDate, savingsGoal.TargetAmount));
                        break;
                    }

                    timelineData.Add((currentDate, currentAmount));
                }

                // Ensure target date is included
                if (timelineData.Last().date != savingsGoal.TargetDate)
                {
                    timelineData.Add((savingsGoal.TargetDate, Math.Min(currentAmount + monthlyIncrement, savingsGoal.TargetAmount)));
                }
            }

            return (projectedCompletionDate, monthlyContributionNeeded, weeklyContributionNeeded, isAchievable, timelineData);
        }

        #endregion

        #region Forecasting Operations

        #endregion

        #region Contribution Operations

        public async Task<SavingsContribution> AddContributionAsync(
            int savingsGoalId,
            decimal amount,
            DateTime contributionDate,
            int? userId,
            int? transactionId,
            string notes)
        {
            // Validate input
            if (amount <= 0)
            {
                throw new ArgumentException("Contribution amount must be greater than zero");
            }

            // Validate savings goal
            var savingsGoal = await _dbContext.SavingsGoals.FindAsync(savingsGoalId);
            if (savingsGoal == null)
            {
                throw new KeyNotFoundException($"Savings goal with ID {savingsGoalId} not found");
            }

            // Validate transaction if provided
            if (transactionId.HasValue)
            {
                var transaction = await _dbContext.Transactions.FindAsync(transactionId.Value);
                if (transaction == null)
                {
                    throw new KeyNotFoundException($"Transaction with ID {transactionId.Value} not found");
                }
            }

            // Create contribution
            var contribution = new SavingsContribution
            {
                SavingsGoalId = savingsGoalId,
                Amount = amount,
                ContributionDate = contributionDate,
                UserId = userId,
                TransactionId = transactionId,
                Notes = notes,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Ensure all string properties are initialized to prevent not-null constraint violations
            contribution = EntityHelper.EnsureStringPropertiesInitialized(contribution);

            // Update savings goal current amount
            savingsGoal.CurrentAmount += amount;
            savingsGoal.UpdatedAt = DateTime.UtcNow;

            // Check if goal is achieved
            if (savingsGoal.CurrentAmount >= savingsGoal.TargetAmount && savingsGoal.Status == "Active")
            {
                savingsGoal.Status = "Achieved";
            }

            _dbContext.SavingsContributions.Add(contribution);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Contribution added to savings goal ID {savingsGoalId}: {amount}");

            return contribution;
        }

        public async Task<SavingsContribution> GetContributionByIdAsync(int contributionId)
        {
            var contribution = await _dbContext.SavingsContributions
                .Include(c => c.SavingsGoal)
                .Include(c => c.User)
                .FirstOrDefaultAsync(c => c.Id == contributionId);

            if (contribution == null)
            {
                throw new KeyNotFoundException($"Contribution with ID {contributionId} not found");
            }

            return contribution;
        }

        public async Task<IEnumerable<SavingsContribution>> GetContributionsBySavingsGoalIdAsync(int savingsGoalId)
        {
            // Validate savings goal
            if (!await SavingsGoalExistsAsync(savingsGoalId))
            {
                throw new KeyNotFoundException($"Savings goal with ID {savingsGoalId} not found");
            }

            return await _dbContext.SavingsContributions
                .Include(c => c.User)
                .Where(c => c.SavingsGoalId == savingsGoalId)
                .OrderByDescending(c => c.ContributionDate)
                .ToListAsync();
        }

        public async Task<SavingsContribution> UpdateContributionAsync(
            int contributionId,
            decimal? amount = null,
            DateTime? contributionDate = null,
            string? notes = null)
        {
            var contribution = await _dbContext.SavingsContributions
                .Include(c => c.SavingsGoal)
                .FirstOrDefaultAsync(c => c.Id == contributionId);

            if (contribution == null)
            {
                throw new KeyNotFoundException($"Contribution with ID {contributionId} not found");
            }

            // Start a transaction
            using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var savingsGoal = contribution.SavingsGoal;
                decimal oldAmount = contribution.Amount;

                // Update properties if provided
                if (amount.HasValue)
                {
                    if (amount.Value <= 0)
                    {
                        throw new ArgumentException("Contribution amount must be greater than zero");
                    }

                    // Update savings goal current amount
                    if (savingsGoal != null)
                        savingsGoal.CurrentAmount = savingsGoal.CurrentAmount - oldAmount + amount.Value;
                    contribution.Amount = amount.Value;
                }

                if (contributionDate.HasValue)
                {
                    contribution.ContributionDate = contributionDate.Value;
                }

                if (notes != null)
                {
                    contribution.Notes = notes;
                }

                contribution.UpdatedAt = DateTime.UtcNow;
                if (savingsGoal != null)
                    savingsGoal.UpdatedAt = DateTime.UtcNow;

                // Check if goal is achieved or no longer achieved
                if (savingsGoal != null)
                {
                    if (savingsGoal.CurrentAmount >= savingsGoal.TargetAmount && savingsGoal.Status == "Active")
                    {
                        savingsGoal.Status = "Achieved";
                    }
                    else if (savingsGoal.CurrentAmount < savingsGoal.TargetAmount && savingsGoal.Status == "Achieved")
                    {
                        savingsGoal.Status = "Active";
                    }
                }

                await _dbContext.SaveChangesAsync();

                // Commit transaction
                await transaction.CommitAsync();

                _logger.LogInformation($"Contribution updated: ID {contributionId}");

                return contribution;
            }
            catch (Exception ex)
            {
                // Rollback transaction on error
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"Error updating contribution with ID {contributionId}");
                throw;
            }
        }

        public async Task<bool> DeleteContributionAsync(int contributionId)
        {
            var contribution = await _dbContext.SavingsContributions
                .Include(c => c.SavingsGoal)
                .FirstOrDefaultAsync(c => c.Id == contributionId);

            if (contribution == null)
            {
                throw new KeyNotFoundException($"Contribution with ID {contributionId} not found");
            }

            // Start a transaction
            using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var savingsGoal = contribution.SavingsGoal;

                // Update savings goal current amount
                if (savingsGoal != null)
                {
                    savingsGoal.CurrentAmount -= contribution.Amount;
                    savingsGoal.UpdatedAt = DateTime.UtcNow;
                }

                // Check if goal is no longer achieved
                if (savingsGoal != null && savingsGoal.CurrentAmount < savingsGoal.TargetAmount && savingsGoal.Status == "Achieved")
                {
                    savingsGoal.Status = "Active";
                }

                // Remove the contribution
                _dbContext.SavingsContributions.Remove(contribution);

                await _dbContext.SaveChangesAsync();

                // Commit transaction
                await transaction.CommitAsync();

                _logger.LogInformation($"Contribution deleted: ID {contributionId}");

                return true;
            }
            catch (Exception ex)
            {
                // Rollback transaction on error
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"Error deleting contribution with ID {contributionId}");
                throw;
            }
        }

        #endregion

        #region Wishlist Item Operations

        public async Task<WishlistItem> CreateWishlistItemAsync(
            string title,
            string description,
            decimal estimatedPrice,
            string productUrl,
            string imageUrl,
            DateTime? targetPurchaseDate,
            int? userId,
            int? familyId,
            bool isShared,
            int? priorityId,
            int? linkedSavingsGoalId)
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(title))
            {
                throw new ArgumentException("Title is required");
            }

            if (estimatedPrice <= 0)
            {
                throw new ArgumentException("Estimated price must be greater than zero");
            }

            if (isShared && !familyId.HasValue)
            {
                throw new ArgumentException("Family ID is required for shared wishlist items");
            }

            if (!isShared && !userId.HasValue)
            {
                throw new ArgumentException("User ID is required for personal wishlist items");
            }

            // Validate priority if provided
            if (priorityId.HasValue)
            {
                var priority = await _dbContext.Priorities.FindAsync(priorityId.Value);
                if (priority == null)
                {
                    throw new KeyNotFoundException($"Priority with ID {priorityId.Value} not found");
                }
            }

            // Validate family if provided
            if (familyId.HasValue)
            {
                var family = await _dbContext.Families.FindAsync(familyId.Value);
                if (family == null)
                {
                    throw new KeyNotFoundException($"Family with ID {familyId.Value} not found");
                }
            }

            // Validate linked savings goal if provided
            if (linkedSavingsGoalId.HasValue)
            {
                var savingsGoal = await _dbContext.SavingsGoals.FindAsync(linkedSavingsGoalId.Value);
                if (savingsGoal == null)
                {
                    throw new KeyNotFoundException($"Savings goal with ID {linkedSavingsGoalId.Value} not found");
                }
            }

            // Create wishlist item
            var wishlistItem = new WishlistItem
            {
                Title = title,
                Description = description,
                EstimatedPrice = estimatedPrice,
                ProductUrl = productUrl,
                ImageUrl = imageUrl,
                AddedDate = DateTime.UtcNow,
                TargetPurchaseDate = targetPurchaseDate,
                UserId = userId,
                FamilyId = familyId,
                IsShared = isShared,
                PriorityId = priorityId,
                LinkedSavingsGoalId = linkedSavingsGoalId,
                Status = "Pending",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Ensure all string properties are initialized to prevent not-null constraint violations
            wishlistItem = EntityHelper.EnsureStringPropertiesInitialized(wishlistItem);

            _dbContext.WishlistItems.Add(wishlistItem);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Wishlist item created: {wishlistItem.Title} with ID {wishlistItem.Id}");

            return wishlistItem;
        }

        public async Task<WishlistItem> GetWishlistItemByIdAsync(int itemId)
        {
            var wishlistItem = await _dbContext.WishlistItems
                .Include(w => w.User)
                .Include(w => w.Family)
                .Include(w => w.Priority)
                .Include(w => w.LinkedSavingsGoal)
                .FirstOrDefaultAsync(w => w.Id == itemId);

            if (wishlistItem == null)
            {
                throw new KeyNotFoundException($"Wishlist item with ID {itemId} not found");
            }

            return wishlistItem;
        }

        public async Task<IEnumerable<WishlistItem>> GetWishlistItemsByUserIdAsync(
            int userId,
            string? status = null)
        {
            var query = _dbContext.WishlistItems
                .Include(w => w.Priority)
                .Include(w => w.LinkedSavingsGoal)
                .Where(w => w.UserId == userId || (w.IsShared && w.Family != null && w.Family.Members.Any(m => m.UserId == userId)));

            if (!string.IsNullOrWhiteSpace(status))
            {
                query = query.Where(w => w.Status == status);
            }

            return await query.ToListAsync();
        }

        public async Task<IEnumerable<WishlistItem>> GetWishlistItemsByFamilyIdAsync(
            int familyId,
            string? status = null)
        {
            var query = _dbContext.WishlistItems
                .Include(w => w.Priority)
                .Include(w => w.LinkedSavingsGoal)
                .Where(w => w.FamilyId == familyId && w.IsShared);

            if (!string.IsNullOrWhiteSpace(status))
            {
                query = query.Where(w => w.Status == status);
            }

            return await query.ToListAsync();
        }

        public async Task<WishlistItem> UpdateWishlistItemAsync(
            int itemId,
            string? title = null,
            string? description = null,
            decimal? estimatedPrice = null,
            string? productUrl = null,
            string? imageUrl = null,
            DateTime? targetPurchaseDate = null,
            int? priorityId = null,
            string? status = null)
        {
            var wishlistItem = await _dbContext.WishlistItems.FindAsync(itemId);
            if (wishlistItem == null)
            {
                throw new KeyNotFoundException($"Wishlist item with ID {itemId} not found");
            }

            // Update properties if provided
            if (!string.IsNullOrWhiteSpace(title))
            {
                wishlistItem.Title = title;
            }

            if (description != null)
            {
                wishlistItem.Description = description;
            }

            if (estimatedPrice.HasValue)
            {
                if (estimatedPrice.Value <= 0)
                {
                    throw new ArgumentException("Estimated price must be greater than zero");
                }
                wishlistItem.EstimatedPrice = estimatedPrice.Value;
            }

            if (productUrl != null)
            {
                wishlistItem.ProductUrl = productUrl;
            }

            if (imageUrl != null)
            {
                wishlistItem.ImageUrl = imageUrl;
            }

            if (targetPurchaseDate.HasValue)
            {
                wishlistItem.TargetPurchaseDate = targetPurchaseDate;
            }

            if (priorityId.HasValue)
            {
                var priority = await _dbContext.Priorities.FindAsync(priorityId.Value);
                if (priority == null)
                {
                    throw new KeyNotFoundException($"Priority with ID {priorityId.Value} not found");
                }
                wishlistItem.PriorityId = priorityId.Value;
            }

            if (!string.IsNullOrWhiteSpace(status))
            {
                if (status != "Pending" && status != "Purchased" && status != "Abandoned")
                {
                    throw new ArgumentException("Invalid status. Must be 'Pending', 'Purchased', or 'Abandoned'");
                }
                wishlistItem.Status = status;
            }

            wishlistItem.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Wishlist item updated: {wishlistItem.Title} with ID {wishlistItem.Id}");

            return wishlistItem;
        }

        public async Task<bool> DeleteWishlistItemAsync(int itemId)
        {
            var wishlistItem = await _dbContext.WishlistItems.FindAsync(itemId);
            if (wishlistItem == null)
            {
                throw new KeyNotFoundException($"Wishlist item with ID {itemId} not found");
            }

            _dbContext.WishlistItems.Remove(wishlistItem);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Wishlist item deleted: {wishlistItem.Title} with ID {wishlistItem.Id}");

            return true;
        }

        public async Task<bool> LinkWishlistItemToSavingsGoalAsync(int wishlistItemId, int savingsGoalId)
        {
            var wishlistItem = await _dbContext.WishlistItems.FindAsync(wishlistItemId);
            if (wishlistItem == null)
            {
                throw new KeyNotFoundException($"Wishlist item with ID {wishlistItemId} not found");
            }

            var savingsGoal = await _dbContext.SavingsGoals.FindAsync(savingsGoalId);
            if (savingsGoal == null)
            {
                throw new KeyNotFoundException($"Savings goal with ID {savingsGoalId} not found");
            }

            // Check if the savings goal is active
            if (savingsGoal.Status != "Active")
            {
                throw new ArgumentException($"Cannot link to a savings goal with status '{savingsGoal.Status}'. Goal must be active.");
            }

            // Update the wishlist item
            wishlistItem.LinkedSavingsGoalId = savingsGoalId;
            wishlistItem.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Wishlist item {wishlistItemId} linked to savings goal {savingsGoalId}");

            return true;
        }

        public async Task<bool> UnlinkWishlistItemFromSavingsGoalAsync(int wishlistItemId)
        {
            var wishlistItem = await _dbContext.WishlistItems.FindAsync(wishlistItemId);
            if (wishlistItem == null)
            {
                throw new KeyNotFoundException($"Wishlist item with ID {wishlistItemId} not found");
            }

            if (!wishlistItem.LinkedSavingsGoalId.HasValue)
            {
                // Already unlinked
                return true;
            }

            // Update the wishlist item
            wishlistItem.LinkedSavingsGoalId = null;
            wishlistItem.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Wishlist item {wishlistItemId} unlinked from savings goal");

            return true;
        }

        #endregion

        #region Validation Methods

        public async Task<bool> SavingsGoalExistsAsync(int goalId)
        {
            return await _dbContext.SavingsGoals.AnyAsync(g => g.Id == goalId);
        }

        public async Task<bool> WishlistItemExistsAsync(int itemId)
        {
            return await _dbContext.WishlistItems.AnyAsync(w => w.Id == itemId);
        }

        public async Task<bool> UserCanAccessSavingsGoalAsync(int goalId, int userId)
        {
            var goal = await _dbContext.SavingsGoals
                .Include(g => g.Family)
                .ThenInclude(f => f != null ? f.Members : null)
                .FirstOrDefaultAsync(g => g.Id == goalId);

            if (goal == null)
            {
                return false;
            }

            // User is the owner
            if (goal.UserId == userId)
            {
                return true;
            }

            // Goal is shared and user is a member of the family
            if (goal.IsShared && goal.FamilyId.HasValue)
            {
                return goal.Family != null && goal.Family.Members.Any(m => m.UserId == userId);
            }

            return false;
        }

        public async Task<bool> UserCanAccessWishlistItemAsync(int itemId, int userId)
        {
            var item = await _dbContext.WishlistItems
                .Include(w => w.Family)
                .ThenInclude(f => f != null ? f.Members : null)
                .FirstOrDefaultAsync(w => w.Id == itemId);

            if (item == null)
            {
                return false;
            }

            // User is the owner
            if (item.UserId == userId)
            {
                return true;
            }

            // Item is shared and user is a member of the family
            if (item.IsShared && item.FamilyId.HasValue)
            {
                return item.Family != null && item.Family.Members.Any(m => m.UserId == userId);
            }

            return false;
        }

        public async Task<bool> UserCanModifySavingsGoalAsync(int goalId, int userId)
        {
            var goal = await _dbContext.SavingsGoals
                .Include(g => g.Family)
                .ThenInclude(f => f != null ? f.Members : null)
                .FirstOrDefaultAsync(g => g.Id == goalId);

            if (goal == null)
            {
                return false;
            }

            // User is the owner
            if (goal.UserId == userId)
            {
                return true;
            }

            // Goal is shared and user is a family admin
            if (goal.IsShared && goal.FamilyId.HasValue)
            {
                var familyMember = goal.Family != null ? goal.Family.Members.FirstOrDefault(m => m.UserId == userId) : null;
                return familyMember != null && familyMember.Role == "Admin";
            }

            return false;
        }

        public async Task<bool> UserCanModifyWishlistItemAsync(int itemId, int userId)
        {
            var item = await _dbContext.WishlistItems
                .Include(w => w.Family)
                .ThenInclude(f => f != null ? f.Members : null)
                .FirstOrDefaultAsync(w => w.Id == itemId);

            if (item == null)
            {
                return false;
            }

            // User is the owner
            if (item.UserId == userId)
            {
                return true;
            }

            // Item is shared and user is a family admin
            if (item.IsShared && item.FamilyId.HasValue)
            {
                var familyMember = item.Family != null && item.Family.Members != null ?
                    item.Family.Members.FirstOrDefault(m => m.UserId == userId) : null;
                return familyMember != null && familyMember.Role == "Admin";
            }

            return false;
        }

        #endregion
    }
}
