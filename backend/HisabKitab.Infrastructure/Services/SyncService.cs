using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HisabKitab.Infrastructure.Services
{
    public class SyncService : ISyncService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<SyncService> _logger;
        private readonly IUserService _userService;

        public SyncService(
            ApplicationDbContext dbContext,
            ILogger<SyncService> logger,
            IUserService userService)
        {
            _dbContext = dbContext;
            _logger = logger;
            _userService = userService;
        }

        public async Task<(IEnumerable<SyncQueue> Processed, IEnumerable<SyncConflict> Conflicts)> ProcessSyncBatchAsync(
            IEnumerable<SyncQueue> batch,
            int userId)
        {
            var processedItems = new List<SyncQueue>();
            var conflicts = new List<SyncConflict>();

            // Use a transaction to ensure all operations are atomic
            using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                // Process each item in the batch
                foreach (var item in batch)
                {
                    try
                    {
                        // Set the user ID
                        item.UserId = userId;

                        // Check if the entity exists
                        bool entityExists = await EntityExistsAsync(item.EntityType, item.EntityId);

                        // Process based on action
                        switch (item.Action.ToLower())
                        {
                            case "create":
                                if (entityExists)
                                {
                                    // Entity already exists, check for conflicts
                                    var conflict = await HandleCreateConflictAsync(item);
                                    if (conflict != null)
                                    {
                                        conflicts.Add(conflict);
                                        continue;
                                    }
                                }
                                else
                                {
                                    // Create the entity
                                    await CreateEntityAsync(item);
                                }
                                break;

                            case "update":
                                if (entityExists)
                                {
                                    // Check for conflicts
                                    var conflict = await HandleUpdateConflictAsync(item);
                                    if (conflict != null)
                                    {
                                        conflicts.Add(conflict);
                                        continue;
                                    }

                                    // Update the entity
                                    await UpdateEntityAsync(item);
                                }
                                else
                                {
                                    // Entity doesn't exist, create it
                                    await CreateEntityAsync(item);
                                }
                                break;

                            case "delete":
                                if (entityExists)
                                {
                                    // Check for conflicts
                                    var conflict = await HandleDeleteConflictAsync(item);
                                    if (conflict != null)
                                    {
                                        conflicts.Add(conflict);
                                        continue;
                                    }

                                    // Delete the entity
                                    await DeleteEntityAsync(item);
                                }
                                break;

                            default:
                                throw new ArgumentException($"Unknown action: {item.Action}");
                        }

                        // Mark as processed
                        item.Status = SyncStatus.Synced;
                        item.SyncedAt = DateTime.UtcNow;
                        processedItems.Add(item);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Error processing sync item: {item.EntityType}/{item.EntityId}");

                        // Mark as failed
                        item.Status = SyncStatus.Failed;
                        item.ErrorMessage = ex.Message;
                        item.RetryCount++;
                    }
                }

                // Save changes
                await _dbContext.SaveChangesAsync();

                // Commit the transaction
                await transaction.CommitAsync();

                return (processedItems, conflicts);
            }
            catch (Exception ex)
            {
                // Rollback the transaction on error
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error processing sync batch, transaction rolled back");
                throw;
            }
        }

        public async Task<DateTime> GetLastSyncTimeAsync(int userId)
        {
            try
            {
                var user = await _userService.GetByIdAsync(userId);
                if (user == null)
                {
                    throw new KeyNotFoundException($"User with ID {userId} not found");
                }

                // Get the last sync time from user settings
                var lastSyncTimeSetting = await _dbContext.UserSettings
                    .FirstOrDefaultAsync(s => s.UserId == userId && s.Key == "LastSyncTime");

                if (lastSyncTimeSetting != null && DateTime.TryParse(lastSyncTimeSetting.Value, out DateTime lastSyncTime))
                {
                    return lastSyncTime;
                }

                // Default to a week ago if no sync time is found
                return DateTime.UtcNow.AddDays(-7);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting last sync time for user {userId}");
                throw;
            }
        }

        public async Task SetLastSyncTimeAsync(int userId, DateTime syncTime)
        {
            try
            {
                var user = await _userService.GetByIdAsync(userId);
                if (user == null)
                {
                    throw new KeyNotFoundException($"User with ID {userId} not found");
                }

                // Update or create the last sync time setting
                var lastSyncTimeSetting = await _dbContext.UserSettings
                    .FirstOrDefaultAsync(s => s.UserId == userId && s.Key == "LastSyncTime");

                if (lastSyncTimeSetting != null)
                {
                    lastSyncTimeSetting.Value = syncTime.ToString("o");
                }
                else
                {
                    _dbContext.UserSettings.Add(new UserSetting
                    {
                        UserId = userId,
                        Key = "LastSyncTime",
                        Value = syncTime.ToString("o")
                    });
                }

                await _dbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error setting last sync time for user {userId}");
                throw;
            }
        }

        public async Task<IEnumerable<SyncQueue>> GetChangesSinceAsync(
            int userId,
            DateTime since,
            int limit = 100,
            string? cursor = null)
        {
            try
            {
                // Get changes for the user's entities
                var query = _dbContext.SyncQueue
                    .Where(s => s.UserId == userId && s.CreatedAt > since)
                    .OrderBy(s => s.CreatedAt);

                // Apply cursor-based pagination if cursor is provided
                if (!string.IsNullOrEmpty(cursor) && DateTime.TryParse(cursor, out DateTime cursorDate))
                {
                    query = query.Where(s => s.CreatedAt > cursorDate).OrderBy(s => s.CreatedAt);
                }

                // Get results with limit
                return await query.Take(limit).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting changes since {since} for user {userId}");
                throw;
            }
        }

        public async Task<SyncConflict> CreateConflictAsync(
            string entityType,
            int entityId,
            string localData,
            string remoteData,
            int userId)
        {
            try
            {
                var conflict = new SyncConflict
                {
                    UserId = userId,
                    EntityType = entityType,
                    EntityId = entityId,
                    LocalData = localData,
                    RemoteData = remoteData,
                    Resolved = false,
                    CreatedAt = DateTime.UtcNow
                };

                _dbContext.SyncConflicts.Add(conflict);
                await _dbContext.SaveChangesAsync();

                return conflict;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating conflict for {entityType}/{entityId}");
                throw;
            }
        }

        public async Task<IEnumerable<SyncConflict>> GetConflictsByUserIdAsync(
            int userId,
            bool? resolved = null)
        {
            try
            {
                var query = _dbContext.SyncConflicts
                    .Where(c => c.UserId == userId);

                if (resolved.HasValue)
                {
                    query = query.Where(c => c.Resolved == resolved.Value);
                }

                return await query
                    .OrderByDescending(c => c.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting conflicts for user {userId}");
                throw;
            }
        }

        public async Task<SyncConflict> ResolveConflictAsync(
            int conflictId,
            string resolution,
            string? resolvedData)
        {
            try
            {
                var conflict = await _dbContext.SyncConflicts.FindAsync(conflictId);
                if (conflict == null)
                {
                    throw new KeyNotFoundException($"Conflict with ID {conflictId} not found");
                }

                // Update conflict
                conflict.Resolved = true;
                conflict.Resolution = resolution;
                conflict.ResolvedData = resolvedData ?? string.Empty;
                conflict.ResolvedAt = DateTime.UtcNow;

                // Apply resolution
                await ApplyConflictResolutionAsync(conflict);

                await _dbContext.SaveChangesAsync();

                return conflict;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error resolving conflict {conflictId}");
                throw;
            }
        }

        public async Task<SyncQueue> AddToSyncQueueAsync(
            string entityType,
            int entityId,
            string action,
            string entityData,
            int userId)
        {
            try
            {
                var syncQueueItem = new SyncQueue
                {
                    UserId = userId,
                    EntityType = entityType,
                    EntityId = entityId,
                    Action = action,
                    EntityData = entityData,
                    Status = SyncStatus.Pending,
                    RetryCount = 0,
                    CreatedAt = DateTime.UtcNow
                };

                _dbContext.SyncQueue.Add(syncQueueItem);
                await _dbContext.SaveChangesAsync();

                return syncQueueItem;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error adding to sync queue: {entityType}/{entityId}");
                throw;
            }
        }

        public async Task<SyncQueue> UpdateSyncQueueItemStatusAsync(
            int syncQueueItemId,
            string status,
            string? errorMessage = null)
        {
            try
            {
                var syncQueueItem = await _dbContext.SyncQueue.FindAsync(syncQueueItemId);
                if (syncQueueItem == null)
                {
                    throw new KeyNotFoundException($"Sync queue item with ID {syncQueueItemId} not found");
                }

                // Update status
                syncQueueItem.Status = Enum.Parse<SyncStatus>(status);
                syncQueueItem.ErrorMessage = errorMessage ?? string.Empty;

                if (status == "Failed")
                {
                    syncQueueItem.RetryCount++;
                }
                else if (status == "Synced")
                {
                    syncQueueItem.SyncedAt = DateTime.UtcNow;
                }

                await _dbContext.SaveChangesAsync();

                return syncQueueItem;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating sync queue item {syncQueueItemId}");
                throw;
            }
        }

        public async Task<bool> HasConflictsAsync(string entityType, int entityId)
        {
            try
            {
                return await _dbContext.SyncConflicts
                    .AnyAsync(c => c.EntityType == entityType && c.EntityId == entityId && !c.Resolved);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking for conflicts: {entityType}/{entityId}");
                throw;
            }
        }

        public async Task<bool> DetectConflictAsync(
            string entityType,
            int entityId,
            string localData,
            string remoteData)
        {
            try
            {
                // Parse JSON data
                var localJson = JsonSerializer.Deserialize<JsonElement>(localData);
                var remoteJson = JsonSerializer.Deserialize<JsonElement>(remoteData);

                // Compare last updated timestamps
                if (localJson.TryGetProperty("LastUpdatedAt", out var localTimestamp) &&
                    remoteJson.TryGetProperty("LastUpdatedAt", out var remoteTimestamp))
                {
                    var localTimeStr = localTimestamp.GetString();
                    var remoteTimeStr = remoteTimestamp.GetString();

                    if (localTimeStr != null && remoteTimeStr != null)
                    {
                        var localTime = DateTime.Parse(localTimeStr);
                        var remoteTime = DateTime.Parse(remoteTimeStr);

                        // If local is newer, no conflict
                        if (localTime > remoteTime)
                        {
                            return false;
                        }

                        // If remote is newer, potential conflict
                        if (remoteTime > localTime)
                        {
                            return true;
                        }
                    }
                }

                // Compare data fields
                return await Task.FromResult(HasDataConflicts(localJson, remoteJson));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error detecting conflict: {entityType}/{entityId}");
                return true; // Assume conflict on error
            }
        }

        #region Helper Methods

        private async Task<bool> EntityExistsAsync(string entityType, int entityId)
        {
            switch (entityType.ToLower())
            {
                case "transaction":
                    return await _dbContext.Transactions.AnyAsync(t => t.Id == entityId);
                case "account":
                    return await _dbContext.Accounts.AnyAsync(a => a.Id == entityId);
                case "category":
                    return await _dbContext.Categories.AnyAsync(c => c.Id == entityId);
                case "receipt":
                    return await _dbContext.ReceiptData.AnyAsync(r => r.Id == entityId);
                default:
                    throw new ArgumentException($"Unknown entity type: {entityType}");
            }
        }

        private async Task<SyncConflict?> HandleCreateConflictAsync(SyncQueue item)
        {
            // For create operations, if the entity already exists, it's a conflict
            var remoteData = await GetEntityDataAsync(item.EntityType, item.EntityId);

            // Check if there's a real conflict
            if (await DetectConflictAsync(item.EntityType, item.EntityId, item.EntityData, remoteData))
            {
                return await CreateConflictAsync(
                    item.EntityType,
                    item.EntityId,
                    item.EntityData,
                    remoteData,
                    item.UserId);
            }

            return null;
        }

        private async Task<SyncConflict?> HandleUpdateConflictAsync(SyncQueue item)
        {
            // Get the current entity data
            var remoteData = await GetEntityDataAsync(item.EntityType, item.EntityId);

            // Check if there's a real conflict
            if (await DetectConflictAsync(item.EntityType, item.EntityId, item.EntityData, remoteData))
            {
                return await CreateConflictAsync(
                    item.EntityType,
                    item.EntityId,
                    item.EntityData,
                    remoteData,
                    item.UserId);
            }

            return null;
        }

        private async Task<SyncConflict?> HandleDeleteConflictAsync(SyncQueue item)
        {
            // For delete operations, check if the entity has been modified since last sync
            var remoteData = await GetEntityDataAsync(item.EntityType, item.EntityId);

            // Parse JSON data
            var localJson = JsonSerializer.Deserialize<JsonElement>(item.EntityData);
            var remoteJson = JsonSerializer.Deserialize<JsonElement>(remoteData);

            // Compare last updated timestamps
            if (localJson.TryGetProperty("LastUpdatedAt", out var localTimestamp) &&
                remoteJson.TryGetProperty("LastUpdatedAt", out var remoteTimestamp))
            {
                var localTimeStr = localTimestamp.GetString();
                var remoteTimeStr = remoteTimestamp.GetString();

                if (localTimeStr != null && remoteTimeStr != null)
                {
                    var localTime = DateTime.Parse(localTimeStr);
                    var remoteTime = DateTime.Parse(remoteTimeStr);

                    // If remote is newer, it's a conflict
                    if (remoteTime > localTime)
                    {
                        return await CreateConflictAsync(
                            item.EntityType,
                            item.EntityId,
                            item.EntityData,
                            remoteData,
                            item.UserId);
                    }
                }
            }

            return null;
        }

        private async Task<string> GetEntityDataAsync(string entityType, int entityId)
        {
            switch (entityType.ToLower())
            {
                case "transaction":
                    var transaction = await _dbContext.Transactions
                        .Include(t => t.Items)
                        .FirstOrDefaultAsync(t => t.Id == entityId);
                    return JsonSerializer.Serialize(transaction);

                case "account":
                    var account = await _dbContext.Accounts
                        .FirstOrDefaultAsync(a => a.Id == entityId);
                    return JsonSerializer.Serialize(account);

                case "category":
                    var category = await _dbContext.Categories
                        .FirstOrDefaultAsync(c => c.Id == entityId);
                    return JsonSerializer.Serialize(category);

                case "receipt":
                    var receipt = await _dbContext.ReceiptData
                        .FirstOrDefaultAsync(r => r.Id == entityId);
                    return JsonSerializer.Serialize(receipt);

                default:
                    throw new ArgumentException($"Unknown entity type: {entityType}");
            }
        }

        private async Task CreateEntityAsync(SyncQueue item)
        {
            switch (item.EntityType.ToLower())
            {
                case "transaction":
                    var transaction = JsonSerializer.Deserialize<Transaction>(item.EntityData);
                    if (transaction != null)
                    {
                        transaction.IsSynced = true;
                        transaction.SyncStatus = SyncStatus.Synced;
                        _dbContext.Transactions.Add(transaction);
                    }
                    break;

                case "account":
                    var account = JsonSerializer.Deserialize<Account>(item.EntityData);
                    if (account != null)
                    {
                        _dbContext.Accounts.Add(account);
                    }
                    break;

                case "category":
                    var category = JsonSerializer.Deserialize<Category>(item.EntityData);
                    if (category != null)
                    {
                        _dbContext.Categories.Add(category);
                    }
                    break;

                case "receipt":
                    var receipt = JsonSerializer.Deserialize<ReceiptData>(item.EntityData);
                    if (receipt != null)
                    {
                        receipt.IsSynced = true;
                        receipt.SyncStatus = SyncStatus.Synced;
                        _dbContext.ReceiptData.Add(receipt);
                    }
                    break;

                default:
                    throw new ArgumentException($"Unknown entity type: {item.EntityType}");
            }

            await _dbContext.SaveChangesAsync();
        }

        private async Task UpdateEntityAsync(SyncQueue item)
        {
            switch (item.EntityType.ToLower())
            {
                case "transaction":
                    var transaction = JsonSerializer.Deserialize<Transaction>(item.EntityData);
                    var existingTransaction = await _dbContext.Transactions.FindAsync(item.EntityId);
                    if (existingTransaction != null && transaction != null)
                    {
                        _dbContext.Entry(existingTransaction).CurrentValues.SetValues(transaction);
                        existingTransaction.IsSynced = true;
                        existingTransaction.SyncStatus = SyncStatus.Synced;
                    }
                    break;

                case "account":
                    var account = JsonSerializer.Deserialize<Account>(item.EntityData);
                    var existingAccount = await _dbContext.Accounts.FindAsync(item.EntityId);
                    if (existingAccount != null && account != null)
                    {
                        _dbContext.Entry(existingAccount).CurrentValues.SetValues(account);
                    }
                    break;

                case "category":
                    var category = JsonSerializer.Deserialize<Category>(item.EntityData);
                    var existingCategory = await _dbContext.Categories.FindAsync(item.EntityId);
                    if (existingCategory != null && category != null)
                    {
                        _dbContext.Entry(existingCategory).CurrentValues.SetValues(category);
                    }
                    break;

                case "receipt":
                    var receipt = JsonSerializer.Deserialize<ReceiptData>(item.EntityData);
                    var existingReceipt = await _dbContext.ReceiptData.FindAsync(item.EntityId);
                    if (existingReceipt != null && receipt != null)
                    {
                        _dbContext.Entry(existingReceipt).CurrentValues.SetValues(receipt);
                        existingReceipt.IsSynced = true;
                        existingReceipt.SyncStatus = SyncStatus.Synced;
                    }
                    break;

                default:
                    throw new ArgumentException($"Unknown entity type: {item.EntityType}");
            }

            await _dbContext.SaveChangesAsync();
        }

        private async Task DeleteEntityAsync(SyncQueue item)
        {
            switch (item.EntityType.ToLower())
            {
                case "transaction":
                    var transaction = await _dbContext.Transactions.FindAsync(item.EntityId);
                    if (transaction != null)
                    {
                        _dbContext.Transactions.Remove(transaction);
                    }
                    break;

                case "account":
                    var account = await _dbContext.Accounts.FindAsync(item.EntityId);
                    if (account != null)
                    {
                        _dbContext.Accounts.Remove(account);
                    }
                    break;

                case "category":
                    var category = await _dbContext.Categories.FindAsync(item.EntityId);
                    if (category != null)
                    {
                        _dbContext.Categories.Remove(category);
                    }
                    break;

                case "receipt":
                    var receipt = await _dbContext.ReceiptData.FindAsync(item.EntityId);
                    if (receipt != null)
                    {
                        _dbContext.ReceiptData.Remove(receipt);
                    }
                    break;

                default:
                    throw new ArgumentException($"Unknown entity type: {item.EntityType}");
            }

            await _dbContext.SaveChangesAsync();
        }

        private async Task ApplyConflictResolutionAsync(SyncConflict conflict)
        {
            switch (conflict.Resolution.ToLower())
            {
                case "local":
                    // Apply local changes
                    var localData = JsonSerializer.Deserialize<JsonElement>(conflict.LocalData);
                    await ApplyEntityDataAsync(conflict.EntityType, conflict.EntityId, conflict.LocalData);
                    break;

                case "remote":
                    // Keep remote data (do nothing)
                    break;

                case "merge":
                    // Apply merged data
                    if (!string.IsNullOrEmpty(conflict.ResolvedData))
                    {
                        await ApplyEntityDataAsync(conflict.EntityType, conflict.EntityId, conflict.ResolvedData);
                    }
                    break;

                default:
                    throw new ArgumentException($"Unknown resolution: {conflict.Resolution}");
            }
        }

        private async Task ApplyEntityDataAsync(string entityType, int entityId, string entityData)
        {
            switch (entityType.ToLower())
            {
                case "transaction":
                    var transaction = JsonSerializer.Deserialize<Transaction>(entityData);
                    if (transaction != null)
                    {
                        var existingTransaction = await _dbContext.Transactions.FindAsync(entityId);
                        if (existingTransaction != null)
                        {
                            _dbContext.Entry(existingTransaction).CurrentValues.SetValues(transaction);
                        }
                        else
                        {
                            _dbContext.Transactions.Add(transaction);
                        }
                    }
                    break;

                case "account":
                    var account = JsonSerializer.Deserialize<Account>(entityData);
                    if (account != null)
                    {
                        var existingAccount = await _dbContext.Accounts.FindAsync(entityId);
                        if (existingAccount != null)
                        {
                            _dbContext.Entry(existingAccount).CurrentValues.SetValues(account);
                        }
                        else
                        {
                            _dbContext.Accounts.Add(account);
                        }
                    }
                    break;

                case "category":
                    var category = JsonSerializer.Deserialize<Category>(entityData);
                    if (category != null)
                    {
                        var existingCategory = await _dbContext.Categories.FindAsync(entityId);
                        if (existingCategory != null)
                        {
                            _dbContext.Entry(existingCategory).CurrentValues.SetValues(category);
                        }
                        else
                        {
                            _dbContext.Categories.Add(category);
                        }
                    }
                    break;

                case "receipt":
                    var receipt = JsonSerializer.Deserialize<ReceiptData>(entityData);
                    if (receipt != null)
                    {
                        var existingReceipt = await _dbContext.ReceiptData.FindAsync(entityId);
                        if (existingReceipt != null)
                        {
                            _dbContext.Entry(existingReceipt).CurrentValues.SetValues(receipt);
                        }
                        else
                        {
                            _dbContext.ReceiptData.Add(receipt);
                        }
                    }
                    break;

                default:
                    throw new ArgumentException($"Unknown entity type: {entityType}");
            }
        }

        private bool HasDataConflicts(JsonElement localJson, JsonElement remoteJson)
        {
            // Compare important fields
            var importantFields = new[] { "Amount", "Description", "Date", "AccountId", "CategoryId", "Status" };

            foreach (var field in importantFields)
            {
                if (localJson.TryGetProperty(field, out var localValue) &&
                    remoteJson.TryGetProperty(field, out var remoteValue))
                {
                    if (localValue.ValueKind != remoteValue.ValueKind ||
                        localValue.ToString() != remoteValue.ToString())
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        #endregion
    }
}
