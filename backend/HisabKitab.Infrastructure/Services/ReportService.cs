using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HisabKitab.Infrastructure.Services
{
    public class ReportService : IReportService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<ReportService> _logger;

        public ReportService(
            ApplicationDbContext dbContext,
            ILogger<ReportService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<Report> CreateReportAsync(
            string name,
            string type,
            string? parameters,
            int userId,
            int? familyId = null,
            bool isShared = false,
            string? layout = null)
        {
            // Validate user exists
            if (!await _dbContext.Users.AnyAsync(u => u.Id == userId))
            {
                throw new KeyNotFoundException($"User with ID {userId} not found");
            }

            // Validate family exists if provided
            if (familyId.HasValue && !await _dbContext.Families.AnyAsync(f => f.Id == familyId.Value))
            {
                throw new KeyNotFoundException($"Family with ID {familyId.Value} not found");
            }

            // Create report
            var report = new Report
            {
                Name = name,
                Type = type,
                Parameters = parameters ?? "{}",
                UserId = userId,
                FamilyId = familyId,
                IsShared = isShared,
                Layout = layout ?? "{}",
                LastRunAt = DateTime.UtcNow
            };

            _dbContext.Reports.Add(report);
            await _dbContext.SaveChangesAsync();

            return report;
        }

        public async Task<Report> GetReportByIdAsync(int reportId)
        {
            var report = await _dbContext.Reports
                .Include(r => r.User)
                .Include(r => r.Family)
                .FirstOrDefaultAsync(r => r.Id == reportId);

            if (report == null)
            {
                throw new KeyNotFoundException($"Report with ID {reportId} not found");
            }

            return report;
        }

        public async Task<IEnumerable<Report>> GetReportsByUserIdAsync(int userId, bool includeShared = true)
        {
            var query = _dbContext.Reports.AsQueryable();

            if (includeShared)
            {
                // Get user's family IDs
                var familyIds = await _dbContext.FamilyMembers
                    .Where(fm => fm.UserId == userId && fm.IsActive)
                    .Select(fm => fm.FamilyId)
                    .ToListAsync();

                // Get reports created by user or shared with user's families
                query = query.Where(r =>
                    r.UserId == userId ||
                    (r.IsShared && r.FamilyId.HasValue && familyIds.Contains(r.FamilyId.Value)));
            }
            else
            {
                // Get only reports created by user
                query = query.Where(r => r.UserId == userId);
            }

            return await query
                .Include(r => r.User)
                .Include(r => r.Family)
                .OrderByDescending(r => r.LastRunAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Report>> GetReportsByFamilyIdAsync(int familyId)
        {
            return await _dbContext.Reports
                .Where(r => r.FamilyId == familyId && r.IsShared)
                .Include(r => r.User)
                .Include(r => r.Family)
                .OrderByDescending(r => r.LastRunAt)
                .ToListAsync();
        }

        public async Task<Report> UpdateReportAsync(
            int reportId,
            string name,
            string type,
            string parameters,
            bool isShared,
            string layout)
        {
            var report = await _dbContext.Reports.FindAsync(reportId);
            if (report == null)
            {
                throw new KeyNotFoundException($"Report with ID {reportId} not found");
            }

            report.Name = name;
            report.Type = type;
            report.Parameters = parameters ?? report.Parameters;
            report.IsShared = isShared;
            report.Layout = layout ?? report.Layout;
            report.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            return report;
        }

        public async Task<bool> DeleteReportAsync(int reportId)
        {
            var report = await _dbContext.Reports.FindAsync(reportId);
            if (report == null)
            {
                throw new KeyNotFoundException($"Report with ID {reportId} not found");
            }

            // Delete associated report data
            var reportData = await _dbContext.ReportData
                .Where(rd => rd.ReportId == reportId)
                .ToListAsync();

            _dbContext.ReportData.RemoveRange(reportData);
            _dbContext.Reports.Remove(report);
            await _dbContext.SaveChangesAsync();

            return true;
        }

        public async Task<ReportData> GenerateReportAsync(int reportId)
        {
            var report = await GetReportByIdAsync(reportId);

            // Update LastRunAt
            report.LastRunAt = DateTime.UtcNow;
            report.UpdatedAt = DateTime.UtcNow;
            await _dbContext.SaveChangesAsync();

            // Generate report data based on type
            var reportData = await GenerateReportDataAsync(report);

            return reportData;
        }

        public async Task<ReportData> GenerateReportAsync(
            string type,
            string? parameters,
            int userId,
            int? familyId = null)
        {
            // Create a temporary report object (not saved to database)
            var report = new Report
            {
                Type = type,
                Parameters = parameters ?? "{}",
                UserId = userId,
                FamilyId = familyId,
                LastRunAt = DateTime.UtcNow
            };

            // Generate report data
            var reportData = await GenerateReportDataAsync(report);

            return reportData;
        }

        public async Task<ReportData> GetReportDataAsync(int reportId, bool forceRefresh = false)
        {
            // Check for cached report data
            var cachedData = await _dbContext.ReportData
                .Where(rd => rd.ReportId == reportId && rd.IsCached)
                .OrderByDescending(rd => rd.GeneratedAt)
                .FirstOrDefaultAsync();

            // If cached data exists and is not expired and refresh not forced
            if (cachedData != null &&
                (cachedData.ExpiresAt == null || cachedData.ExpiresAt > DateTime.UtcNow) &&
                !forceRefresh)
            {
                return cachedData;
            }

            // Generate new report data
            return await GenerateReportAsync(reportId);
        }

        public async Task<bool> ShareReportAsync(int reportId, bool isShared)
        {
            var report = await _dbContext.Reports.FindAsync(reportId);
            if (report == null)
            {
                throw new KeyNotFoundException($"Report with ID {reportId} not found");
            }

            // Can only share reports that have a family ID
            if (isShared && !report.FamilyId.HasValue)
            {
                throw new InvalidOperationException("Cannot share a report that is not associated with a family");
            }

            report.IsShared = isShared;
            report.UpdatedAt = DateTime.UtcNow;
            await _dbContext.SaveChangesAsync();

            return true;
        }

        // Helper method to generate report data based on report type
        private async Task<ReportData> GenerateReportDataAsync(Report report)
        {
            // Parse parameters
            var parametersDict = JsonSerializer.Deserialize<Dictionary<string, object>>(report.Parameters);

            // Generate data based on report type
            string jsonData;

            switch (report.Type.ToLower())
            {
                case "expense":
                    jsonData = await GenerateExpenseReportAsync(report.UserId, report.FamilyId, parametersDict);
                    break;
                case "income":
                    jsonData = await GenerateIncomeReportAsync(report.UserId, report.FamilyId, parametersDict);
                    break;
                case "networth":
                    jsonData = await GenerateNetWorthReportAsync(report.UserId, report.FamilyId, parametersDict);
                    break;
                case "cashflow":
                    jsonData = await GenerateCashflowReportAsync(report.UserId, report.FamilyId, parametersDict);
                    break;
                case "category":
                    jsonData = await GenerateCategoryReportAsync(report.UserId, report.FamilyId, parametersDict);
                    break;
                default:
                    throw new NotImplementedException($"Report type '{report.Type}' is not implemented");
            }

            // Create report data entity
            var reportData = new ReportData
            {
                ReportId = report.Id,
                Data = jsonData,
                GeneratedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddDays(1), // Cache for 1 day
                IsCached = true
            };

            // Save to database if report is persisted
            if (report.Id > 0)
            {
                _dbContext.ReportData.Add(reportData);
                await _dbContext.SaveChangesAsync();
            }

            return reportData;
        }

        // Helper methods for generating different report types
        private Task<string> GenerateExpenseReportAsync(int userId, int? familyId, Dictionary<string, object>? parameters)
        {
            // Implementation will be added
            return Task.FromResult("{}");
        }

        private Task<string> GenerateIncomeReportAsync(int userId, int? familyId, Dictionary<string, object>? parameters)
        {
            // Implementation will be added
            return Task.FromResult("{}");
        }

        private Task<string> GenerateNetWorthReportAsync(int userId, int? familyId, Dictionary<string, object>? parameters)
        {
            // Implementation will be added
            return Task.FromResult("{}");
        }

        private Task<string> GenerateCashflowReportAsync(int userId, int? familyId, Dictionary<string, object>? parameters)
        {
            // Implementation will be added
            return Task.FromResult("{}");
        }

        private Task<string> GenerateCategoryReportAsync(int userId, int? familyId, Dictionary<string, object>? parameters)
        {
            // Implementation will be added
            return Task.FromResult("{}");
        }
    }
}
