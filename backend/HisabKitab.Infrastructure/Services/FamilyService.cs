using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Helpers;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HisabKitab.Infrastructure.Services
{
    public class FamilyService : IFamilyService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IRoleService _roleService;
        private readonly ILogger<FamilyService> _logger;

        public FamilyService(
            ApplicationDbContext dbContext,
            IRoleService roleService,
            ILogger<FamilyService> logger)
        {
            _dbContext = dbContext;
            _roleService = roleService;
            _logger = logger;
        }

        public async Task<Family> CreateFamilyAsync(string name, string description, int createdByUserId, string? settings = null)
        {
            var user = await _dbContext.Users.FindAsync(createdByUserId);
            if (user == null)
            {
                throw new KeyNotFoundException($"User with ID {createdByUserId} not found");
            }

            var family = new Family
            {
                Name = name,
                Description = description ?? "",
                Settings = settings ?? "{}",
                CreatedByUserId = createdByUserId,
                CreatedAt = DateTime.UtcNow
            };

            // Ensure all string properties are initialized to prevent not-null constraint violations
            family = EntityHelper.EnsureStringPropertiesInitialized(family);

            _dbContext.Families.Add(family);
            await _dbContext.SaveChangesAsync();

            // Add the creator as a family admin
            var familyMember = new FamilyMember
            {
                FamilyId = family.Id,
                UserId = createdByUserId,
                Role = "Admin",
                Permissions = "{}", // Default empty permissions
                JoinedAt = DateTime.UtcNow,
                IsActive = true
            };

            _dbContext.FamilyMembers.Add(familyMember);

            // Assign FamilyAdmin role to the user if they don't already have it
            var familyAdminRole = await _roleService.GetRoleByNameAsync("FamilyAdmin");
            if (familyAdminRole != null)
            {
                var userHasRole = await _dbContext.UserRoles
                    .AnyAsync(ur => ur.UserId == createdByUserId && ur.RoleId == familyAdminRole.Id);

                if (!userHasRole)
                {
                    await _roleService.AssignRoleToUserAsync(createdByUserId, familyAdminRole.Id);
                    _logger.LogInformation($"User {createdByUserId} assigned FamilyAdmin role");
                }
            }

            await _dbContext.SaveChangesAsync();

            return family;
        }

        public async Task<Family> GetFamilyByIdAsync(int familyId)
        {
            var family = await _dbContext.Families
                .Include(f => f.CreatedByUser)
                .Include(f => f.Members)
                    .ThenInclude(m => m.User)
                .FirstOrDefaultAsync(f => f.Id == familyId);

            if (family == null)
            {
                throw new KeyNotFoundException($"Family with ID {familyId} not found");
            }

            return family;
        }

        public async Task<IEnumerable<Family>> GetFamiliesByUserIdAsync(int userId)
        {
            var familyIds = await _dbContext.FamilyMembers
                .Where(fm => fm.UserId == userId && fm.IsActive)
                .Select(fm => fm.FamilyId)
                .ToListAsync();

            var families = await _dbContext.Families
                .Include(f => f.CreatedByUser)
                .Where(f => familyIds.Contains(f.Id))
                .ToListAsync();

            return families;
        }

        public async Task<Family> UpdateFamilyAsync(int familyId, string name, string description, string? settings = null)
        {
            var family = await _dbContext.Families.FindAsync(familyId);
            if (family == null)
            {
                throw new KeyNotFoundException($"Family with ID {familyId} not found");
            }

            family.Name = name;
            family.Description = description ?? family.Description;
            family.Settings = settings ?? family.Settings;
            family.UpdatedAt = DateTime.UtcNow;

            // Ensure all string properties are initialized to prevent not-null constraint violations
            family = EntityHelper.EnsureStringPropertiesInitialized(family);

            _dbContext.Families.Update(family);
            await _dbContext.SaveChangesAsync();

            return family;
        }

        public async Task<bool> DeleteFamilyAsync(int familyId)
        {
            var family = await _dbContext.Families.FindAsync(familyId);
            if (family == null)
            {
                throw new KeyNotFoundException($"Family with ID {familyId} not found");
            }

            _dbContext.Families.Remove(family);
            await _dbContext.SaveChangesAsync();

            return true;
        }

        public async Task<string> GenerateInviteCodeAsync(int familyId)
        {
            var family = await _dbContext.Families.FindAsync(familyId);
            if (family == null)
            {
                throw new KeyNotFoundException($"Family with ID {familyId} not found");
            }

            // Generate a random invite code
            var inviteCode = GenerateRandomCode(8);

            family.InviteCode = inviteCode;
            family.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            return inviteCode;
        }

        public async Task<FamilyMember> AddMemberAsync(int familyId, int userId, string role = "Member")
        {
            var family = await _dbContext.Families.FindAsync(familyId);
            if (family == null)
            {
                throw new KeyNotFoundException($"Family with ID {familyId} not found");
            }

            var user = await _dbContext.Users.FindAsync(userId);
            if (user == null)
            {
                throw new KeyNotFoundException($"User with ID {userId} not found");
            }

            // Check if user is already a member
            var existingMember = await _dbContext.FamilyMembers
                .FirstOrDefaultAsync(fm => fm.FamilyId == familyId && fm.UserId == userId);

            if (existingMember != null)
            {
                if (existingMember.IsActive)
                {
                    throw new InvalidOperationException($"User {userId} is already a member of family {familyId}");
                }
                else
                {
                    // Reactivate the member
                    existingMember.IsActive = true;
                    existingMember.Role = role;
                    await _dbContext.SaveChangesAsync();
                    return existingMember;
                }
            }

            var familyMember = new FamilyMember
            {
                FamilyId = familyId,
                UserId = userId,
                Role = role,
                Permissions = "{}", // Default empty permissions
                JoinedAt = DateTime.UtcNow,
                IsActive = true
            };

            _dbContext.FamilyMembers.Add(familyMember);

            // If the role is Admin, assign FamilyAdmin role to the user if they don't already have it
            if (role == "Admin")
            {
                var familyAdminRole = await _roleService.GetRoleByNameAsync("FamilyAdmin");
                if (familyAdminRole != null)
                {
                    var userHasRole = await _dbContext.UserRoles
                        .AnyAsync(ur => ur.UserId == userId && ur.RoleId == familyAdminRole.Id);

                    if (!userHasRole)
                    {
                        await _roleService.AssignRoleToUserAsync(userId, familyAdminRole.Id);
                        _logger.LogInformation($"User {userId} assigned FamilyAdmin role");
                    }
                }
            }

            await _dbContext.SaveChangesAsync();

            return familyMember;
        }

        public async Task<FamilyMember> JoinFamilyWithInviteCodeAsync(string inviteCode, int userId)
        {
            var family = await _dbContext.Families
                .FirstOrDefaultAsync(f => f.InviteCode == inviteCode);

            if (family == null)
            {
                throw new KeyNotFoundException($"Family with invite code {inviteCode} not found");
            }

            var user = await _dbContext.Users.FindAsync(userId);
            if (user == null)
            {
                throw new KeyNotFoundException($"User with ID {userId} not found");
            }

            // Check if user is already a member
            var existingMember = await _dbContext.FamilyMembers
                .FirstOrDefaultAsync(fm => fm.FamilyId == family.Id && fm.UserId == userId);

            if (existingMember != null)
            {
                if (existingMember.IsActive)
                {
                    throw new InvalidOperationException($"User {userId} is already a member of family {family.Id}");
                }
                else
                {
                    // Reactivate the member
                    existingMember.IsActive = true;
                    await _dbContext.SaveChangesAsync();
                    return existingMember;
                }
            }

            var familyMember = new FamilyMember
            {
                FamilyId = family.Id,
                UserId = userId,
                Role = "Member", // Default role for joining with invite code
                Permissions = "{}", // Default empty permissions
                JoinedAt = DateTime.UtcNow,
                IsActive = true
            };

            _dbContext.FamilyMembers.Add(familyMember);
            await _dbContext.SaveChangesAsync();

            return familyMember;
        }

        public async Task<bool> RemoveMemberAsync(int familyId, int userId)
        {
            var familyMember = await _dbContext.FamilyMembers
                .FirstOrDefaultAsync(fm => fm.FamilyId == familyId && fm.UserId == userId);

            if (familyMember == null)
            {
                throw new KeyNotFoundException($"User {userId} is not a member of family {familyId}");
            }

            _dbContext.FamilyMembers.Remove(familyMember);
            await _dbContext.SaveChangesAsync();

            return true;
        }

        public async Task<bool> LeaveFamilyAsync(int familyId, int userId)
        {
            var familyMember = await _dbContext.FamilyMembers
                .FirstOrDefaultAsync(fm => fm.FamilyId == familyId && fm.UserId == userId);

            if (familyMember == null)
            {
                throw new KeyNotFoundException($"User {userId} is not a member of family {familyId}");
            }

            // Check if this is the last admin
            if (familyMember.Role == "Admin")
            {
                var adminCount = await _dbContext.FamilyMembers
                    .CountAsync(fm => fm.FamilyId == familyId && fm.Role == "Admin" && fm.IsActive);

                if (adminCount <= 1)
                {
                    throw new InvalidOperationException("Cannot leave family: you are the last admin. Please assign another admin or delete the family.");
                }
            }

            // Set as inactive instead of removing
            familyMember.IsActive = false;
            await _dbContext.SaveChangesAsync();

            return true;
        }

        public async Task<IEnumerable<FamilyMember>> GetFamilyMembersAsync(int familyId)
        {
            var family = await _dbContext.Families.FindAsync(familyId);
            if (family == null)
            {
                throw new KeyNotFoundException($"Family with ID {familyId} not found");
            }

            var members = await _dbContext.FamilyMembers
                .Include(fm => fm.User)
                .Where(fm => fm.FamilyId == familyId && fm.IsActive)
                .ToListAsync();

            return members;
        }

        public async Task<bool> UpdateMemberRoleAsync(int familyId, int userId, string newRole)
        {
            var familyMember = await _dbContext.FamilyMembers
                .FirstOrDefaultAsync(fm => fm.FamilyId == familyId && fm.UserId == userId && fm.IsActive);

            if (familyMember == null)
            {
                throw new KeyNotFoundException($"Active user {userId} is not a member of family {familyId}");
            }

            // Check if this is the last admin and we're changing the role from Admin
            if (familyMember.Role == "Admin" && newRole != "Admin")
            {
                var adminCount = await _dbContext.FamilyMembers
                    .CountAsync(fm => fm.FamilyId == familyId && fm.Role == "Admin" && fm.IsActive);

                if (adminCount <= 1)
                {
                    throw new InvalidOperationException("Cannot change role: this is the last admin. Please assign another admin first.");
                }
            }

            familyMember.Role = newRole;

            // If the new role is Admin, assign FamilyAdmin role to the user if they don't already have it
            if (newRole == "Admin")
            {
                var familyAdminRole = await _roleService.GetRoleByNameAsync("FamilyAdmin");
                if (familyAdminRole != null)
                {
                    var userHasRole = await _dbContext.UserRoles
                        .AnyAsync(ur => ur.UserId == userId && ur.RoleId == familyAdminRole.Id);

                    if (!userHasRole)
                    {
                        await _roleService.AssignRoleToUserAsync(userId, familyAdminRole.Id);
                        _logger.LogInformation($"User {userId} assigned FamilyAdmin role");
                    }
                }
            }

            await _dbContext.SaveChangesAsync();

            return true;
        }

        public async Task<bool> IsFamilyAdminAsync(int familyId, int userId)
        {
            return await _dbContext.FamilyMembers
                .AnyAsync(fm => fm.FamilyId == familyId && fm.UserId == userId && fm.Role == "Admin" && fm.IsActive);
        }

        public async Task<bool> IsFamilyMemberAsync(int familyId, int userId)
        {
            return await _dbContext.FamilyMembers
                .AnyAsync(fm => fm.FamilyId == familyId && fm.UserId == userId && fm.IsActive);
        }

        public async Task<bool> FamilyExistsAsync(int familyId)
        {
            return await _dbContext.Families.AnyAsync(f => f.Id == familyId);
        }

        public async Task<bool> IsValidInviteCodeAsync(string inviteCode)
        {
            return await _dbContext.Families.AnyAsync(f => f.InviteCode == inviteCode);
        }

        private string GenerateRandomCode(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var random = new Random();
            var code = new StringBuilder(length);

            for (int i = 0; i < length; i++)
            {
                code.Append(chars[random.Next(chars.Length)]);
            }

            return code.ToString();
        }
    }
}
