using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Tesseract;

namespace HisabKitab.Infrastructure.Services
{
    public class OCRService : IOCRService
    {
        private readonly ILogger<OCRService> _logger;
        private readonly IFileStorageService _fileStorageService;
        private readonly string _tessdataPath;

        public OCRService(
            ILogger<OCRService> logger,
            IFileStorageService fileStorageService,
            IConfiguration configuration)
        {
            _logger = logger;
            _fileStorageService = fileStorageService;

            // Get tessdata path from configuration or use default
            _tessdataPath = configuration["OCR:TessdataPath"] ??
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "tessdata");

            // Create directory if it doesn't exist
            if (!Directory.Exists(_tessdataPath))
            {
                Directory.CreateDirectory(_tessdataPath);
                _logger.LogWarning($"Tessdata directory created at {_tessdataPath}. Please download language files from https://github.com/tesseract-ocr/tessdata and place them in this directory.");
            }

            // Check if language files exist
            var defaultLanguage = configuration["OCR:DefaultLanguage"] ?? "eng";
            var languageFile = Path.Combine(_tessdataPath, $"{defaultLanguage}.traineddata");

            if (!File.Exists(languageFile))
            {
                _logger.LogWarning($"Tessdata language file for '{defaultLanguage}' not found at {languageFile}. OCR functionality will not work correctly.");
                _logger.LogInformation("Please download language files from https://github.com/tesseract-ocr/tessdata and place them in the tessdata directory.");
            }
        }

        public async Task<string> ExtractTextFromImageAsync(string imagePath, string language = "eng")
        {
            try
            {
                // Get the image data
                byte[] imageData = await _fileStorageService.GetFileAsync(imagePath);

                using (var engine = new TesseractEngine(_tessdataPath, language, EngineMode.Default))
                {
                    using (var img = Pix.LoadFromMemory(imageData))
                    {
                        using (var page = engine.Process(img))
                        {
                            var text = page.GetText();
                            _logger.LogInformation($"OCR Mean confidence: {page.GetMeanConfidence()}");

                            return text;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error extracting text from image: {imagePath}");
                throw;
            }
        }

        public async Task<string> ExtractTextFromBase64Async(string base64Image, string language = "eng")
        {
            try
            {
                // Convert base64 to byte array
                byte[] imageData = Convert.FromBase64String(base64Image);

                // Use Task.Run to make this CPU-bound operation run asynchronously
                return await Task.Run(() => {
                    using var engine = new TesseractEngine(_tessdataPath, language, EngineMode.Default);
                    using var img = Pix.LoadFromMemory(imageData);
                    using var page = engine.Process(img);

                    var text = page.GetText();
                    _logger.LogInformation("OCR Mean confidence: {Confidence}", page.GetMeanConfidence());

                    return text;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting text from base64 image");
                throw;
            }
        }

        public async Task<string> ParseReceiptTextAsync(string text, string language = "eng")
        {
            try
            {
                // Use Task.Run to make this CPU-bound operation run asynchronously
                return await Task.Run(() => {
                    // Parse the receipt text to extract structured data
                    var receiptItems = new List<ReceiptItem>();
                    var merchantName = ExtractMerchantName(text);
                    var date = ExtractDate(text);
                    var totalAmount = ExtractTotalAmount(text);

                    // Extract items
                    var items = ExtractItems(text);

                    // Create receipt data object
                    var receiptData = new
                    {
                        MerchantName = merchantName,
                        Date = date,
                        TotalAmount = totalAmount,
                        Items = items
                    };

                    // Convert to JSON
                    return JsonSerializer.Serialize(receiptData);
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing receipt text");
                throw;
            }
        }

        public async Task<ReceiptData> ProcessReceiptImageAsync(string imagePath, string language = "eng")
        {
            try
            {
                // Extract text from image
                var text = await ExtractTextFromImageAsync(imagePath, language);

                // Parse the text
                var parsedData = await ParseReceiptTextAsync(text, language);

                // Create receipt data object
                var receiptData = new ReceiptData
                {
                    ImagePath = imagePath,
                    OcrText = text,
                    ParsedData = parsedData,
                    Language = language,
                    IsProcessed = true,
                    ScanDate = DateTime.UtcNow
                };

                // Parse the JSON to get additional data
                var parsedJson = JsonSerializer.Deserialize<dynamic>(parsedData);
                if (parsedJson != null)
                {
                    if (parsedJson.TryGetProperty("MerchantName", out JsonElement merchantNameProp))
                        receiptData.MerchantName = merchantNameProp.GetString() ?? "Unknown Merchant";

                    if (parsedJson.TryGetProperty("Date", out JsonElement dateProp))
                    {
                        string? dateStr = dateProp.GetString();
                        if (!string.IsNullOrEmpty(dateStr))
                        {
                            receiptData.ReceiptDate = DateTime.Parse(dateStr);
                        }
                    }

                    if (parsedJson.TryGetProperty("TotalAmount", out JsonElement totalAmountProp))
                        receiptData.TotalAmount = totalAmountProp.GetDecimal();
                }

                return receiptData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing receipt image: {ImagePath}", imagePath);
                throw;
            }
        }

        public async Task<ReceiptData> ProcessReceiptBase64Async(string base64Image, int? transactionId = null, string language = "eng")
        {
            try
            {
                // Save the image
                var imagePath = await _fileStorageService.SaveBase64FileAsync(
                    base64Image,
                    "receipts",
                    $"receipt_{Guid.NewGuid()}.jpg");

                // Extract text from image
                var text = await ExtractTextFromBase64Async(base64Image, language);

                // Parse the text
                var parsedData = await ParseReceiptTextAsync(text, language);

                // Create receipt data object
                var receiptData = new ReceiptData
                {
                    TransactionId = transactionId ?? 0,
                    ImagePath = imagePath,
                    OcrText = text,
                    ParsedData = parsedData,
                    Language = language,
                    IsProcessed = true,
                    ScanDate = DateTime.UtcNow
                };

                // Parse the JSON to get additional data
                var parsedJson = JsonSerializer.Deserialize<dynamic>(parsedData);
                if (parsedJson != null)
                {
                    if (parsedJson.TryGetProperty("MerchantName", out JsonElement merchantNameProp))
                        receiptData.MerchantName = merchantNameProp.GetString() ?? "Unknown Merchant";

                    if (parsedJson.TryGetProperty("Date", out JsonElement dateProp))
                    {
                        string? dateStr = dateProp.GetString();
                        if (!string.IsNullOrEmpty(dateStr))
                        {
                            receiptData.ReceiptDate = DateTime.Parse(dateStr);
                        }
                    }

                    if (parsedJson.TryGetProperty("TotalAmount", out JsonElement totalAmountProp))
                        receiptData.TotalAmount = totalAmountProp.GetDecimal();
                }

                return receiptData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing receipt from base64 image");
                throw;
            }
        }

        #region Helper Methods

        private static string ExtractMerchantName(string text)
        {
            // Simple heuristic: first line is often the merchant name
            var lines = text.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            if (lines.Length > 0)
            {
                return lines[0].Trim();
            }

            return "Unknown Merchant";
        }

        private static string ExtractDate(string text)
        {
            // Look for date patterns
            var dateRegex = new Regex(@"(?:date|dt)[:\s]+(\d{1,2}[-/\.]\d{1,2}[-/\.]\d{2,4})", RegexOptions.IgnoreCase);
            var match = dateRegex.Match(text);

            if (match.Success)
            {
                return match.Groups[1].Value;
            }

            // Try another pattern (MM/DD/YYYY or DD/MM/YYYY)
            dateRegex = new Regex(@"(\d{1,2}[-/\.]\d{1,2}[-/\.]\d{2,4})", RegexOptions.IgnoreCase);
            match = dateRegex.Match(text);

            if (match.Success)
            {
                return match.Groups[1].Value;
            }

            return DateTime.UtcNow.ToString("yyyy-MM-dd");
        }

        private static decimal ExtractTotalAmount(string text)
        {
            // Look for total amount patterns
            var totalRegex = new Regex(@"(?:total|amount|sum|tot)[:\s]+(?:rs\.?|npr\.?|₹)?[\s]*(\d+(?:[.,]\d+)?)", RegexOptions.IgnoreCase);
            var match = totalRegex.Match(text);

            if (match.Success)
            {
                if (decimal.TryParse(match.Groups[1].Value.Replace(",", "."), out decimal amount))
                {
                    return amount;
                }
            }

            // Try another pattern (just look for numbers with currency symbols)
            totalRegex = new Regex(@"(?:rs\.?|npr\.?|₹)[\s]*(\d+(?:[.,]\d+)?)", RegexOptions.IgnoreCase);
            match = totalRegex.Match(text);

            if (match.Success)
            {
                if (decimal.TryParse(match.Groups[1].Value.Replace(",", "."), out decimal amount))
                {
                    return amount;
                }
            }

            return 0;
        }

        private static List<dynamic> ExtractItems(string text)
        {
            var items = new List<dynamic>();
            var lines = text.Split('\n', StringSplitOptions.RemoveEmptyEntries);

            // Skip first few lines (usually header) and last few lines (usually footer)
            var contentLines = lines.Skip(2).Take(lines.Length - 4).ToList();

            foreach (var line in contentLines)
            {
                // Look for item patterns: name followed by price
                var itemRegex = new Regex(@"(.+?)(?:rs\.?|npr\.?|₹)?[\s]*(\d+(?:[.,]\d+)?)", RegexOptions.IgnoreCase);
                var match = itemRegex.Match(line);

                if (match.Success)
                {
                    var name = match.Groups[1].Value.Trim();
                    var priceStr = match.Groups[2].Value.Replace(",", ".");

                    if (decimal.TryParse(priceStr, out decimal price) && !string.IsNullOrWhiteSpace(name))
                    {
                        items.Add(new
                        {
                            Name = name,
                            Amount = price,
                            Quantity = 1
                        });
                    }
                }
            }

            return items;
        }

        #endregion
    }
}
