using System;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Helpers;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HisabKitab.Infrastructure.Services
{
    public class UserService : IUserService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IRoleService _roleService;
        private readonly ILogger<UserService> _logger;

        public UserService(
            ApplicationDbContext dbContext,
            IRoleService roleService,
            ILogger<UserService> logger)
        {
            _dbContext = dbContext;
            _roleService = roleService;
            _logger = logger;
        }

        public async Task<User> RegisterAsync(string username, string email, string password, string firstName, string lastName, string? phoneNumber = null, string language = "en")
        {
            if (await UsernameExistsAsync(username))
                throw new ArgumentException($"Username '{username}' is already taken.");

            if (await EmailExistsAsync(email))
                throw new ArgumentException($"Email '{email}' is already registered.");

            var passwordHash = HashPassword(password);

            var user = new User
            {
                Username = username,
                Email = email,
                PasswordHash = passwordHash,
                FirstName = firstName,
                LastName = lastName,
                PhoneNumber = phoneNumber ?? string.Empty,
                Language = language,
                ProfilePicture = "", // Set default empty string for ProfilePicture
                NotificationPreferences = "{}", // Set default empty JSON object for NotificationPreferences
                CreatedAt = DateTime.UtcNow
            };

            // Ensure all string properties are initialized to prevent not-null constraint violations
            user = EntityHelper.EnsureStringPropertiesInitialized(user);

            _dbContext.Users.Add(user);
            await _dbContext.SaveChangesAsync();

            try
            {
                // Check if this is the first user in the system
                bool isFirstUser = await _dbContext.Users.CountAsync() == 1; // Count is 1 because we just added this user

                if (isFirstUser)
                {
                    // First user gets PlatformAdmin role
                    var adminRole = await _roleService.GetRoleByNameAsync("PlatformAdmin");
                    if (adminRole != null)
                    {
                        await _roleService.AssignRoleToUserAsync(user.Id, adminRole.Id);
                        _logger.LogInformation($"First user '{username}' assigned PlatformAdmin role");
                    }
                    else
                    {
                        _logger.LogWarning($"Could not assign PlatformAdmin role to first user {username} - role not found");
                    }
                }
                else
                {
                    // Regular users get User role
                    var userRole = await _roleService.GetRoleByNameAsync("User");
                    if (userRole != null)
                    {
                        await _roleService.AssignRoleToUserAsync(user.Id, userRole.Id);
                        _logger.LogInformation($"Default 'User' role assigned to user {username}");
                    }
                    else
                    {
                        _logger.LogWarning($"Could not assign default 'User' role to user {username} - role not found");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error assigning role to user {username}");
                // We don't throw here to avoid failing the registration process
            }

            return user;
        }

        public async Task<User> AuthenticateAsync(string username, string password)
        {
            var user = await _dbContext.Users
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .SingleOrDefaultAsync(u => u.Username == username);

            if (user == null)
                return null;

            if (!VerifyPasswordHash(password, user.PasswordHash))
                return null;

            // Update last login
            user.LastLoginAt = DateTime.UtcNow;
            await _dbContext.SaveChangesAsync();

            return user;
        }

        public async Task<User?> GetByIdAsync(int id)
        {
            return await _dbContext.Users
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur != null ? ur.Role : null)
                .SingleOrDefaultAsync(u => u.Id == id);
        }

        public async Task<User?> GetByUsernameAsync(string username)
        {
            return await _dbContext.Users
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur != null ? ur.Role : null)
                .SingleOrDefaultAsync(u => u.Username == username);
        }

        public async Task<User?> GetByEmailAsync(string email)
        {
            return await _dbContext.Users
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur != null ? ur.Role : null)
                .SingleOrDefaultAsync(u => u.Email == email);
        }

        public async Task<bool> UsernameExistsAsync(string username)
        {
            return await _dbContext.Users.AnyAsync(u => u.Username == username);
        }

        public async Task<bool> EmailExistsAsync(string email)
        {
            return await _dbContext.Users.AnyAsync(u => u.Email == email);
        }

        public async Task<User> UpdateProfileAsync(int userId, string firstName, string lastName, string phoneNumber, string language)
        {
            var user = await _dbContext.Users.FindAsync(userId);
            if (user == null)
                throw new KeyNotFoundException($"User with ID {userId} not found.");

            user.FirstName = firstName ?? user.FirstName;
            user.LastName = lastName ?? user.LastName;
            user.PhoneNumber = phoneNumber ?? user.PhoneNumber;
            user.Language = language ?? user.Language;
            user.UpdatedAt = DateTime.UtcNow;

            _dbContext.Users.Update(user);
            await _dbContext.SaveChangesAsync();

            return user;
        }

        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            var user = await _dbContext.Users.FindAsync(userId);
            if (user == null)
                throw new KeyNotFoundException($"User with ID {userId} not found.");

            if (!VerifyPasswordHash(currentPassword, user.PasswordHash))
                return false;

            user.PasswordHash = HashPassword(newPassword);
            user.UpdatedAt = DateTime.UtcNow;

            _dbContext.Users.Update(user);
            await _dbContext.SaveChangesAsync();

            return true;
        }

        public async Task<bool> ChangeEmailAsync(int userId, string newEmail, string password)
        {
            var user = await _dbContext.Users.FindAsync(userId);
            if (user == null)
                throw new KeyNotFoundException($"User with ID {userId} not found.");

            if (!VerifyPasswordHash(password, user.PasswordHash))
                return false;

            if (await EmailExistsAsync(newEmail))
                throw new ArgumentException($"Email '{newEmail}' is already registered.");

            user.Email = newEmail;
            user.UpdatedAt = DateTime.UtcNow;

            _dbContext.Users.Update(user);
            await _dbContext.SaveChangesAsync();

            return true;
        }

        public async Task<bool> UpdateProfilePictureAsync(int userId, string profilePicture)
        {
            var user = await _dbContext.Users.FindAsync(userId);
            if (user == null)
                throw new KeyNotFoundException($"User with ID {userId} not found.");

            user.ProfilePicture = profilePicture;
            user.UpdatedAt = DateTime.UtcNow;

            _dbContext.Users.Update(user);
            await _dbContext.SaveChangesAsync();

            return true;
        }

        private string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            return Convert.ToBase64String(hashedBytes);
        }

        private bool VerifyPasswordHash(string password, string storedHash)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            var hash = Convert.ToBase64String(hashedBytes);
            return hash == storedHash;
        }
    }
}
