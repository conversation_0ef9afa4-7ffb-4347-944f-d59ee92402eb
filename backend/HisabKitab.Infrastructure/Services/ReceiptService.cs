using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HisabKitab.Infrastructure.Services
{
    public class ReceiptService : IReceiptService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<ReceiptService> _logger;
        private readonly IOCRService _ocrService;
        private readonly IFileStorageService _fileStorageService;

        public ReceiptService(
            ApplicationDbContext dbContext,
            ILogger<ReceiptService> logger,
            IOCRService ocrService,
            IFileStorageService fileStorageService)
        {
            _dbContext = dbContext;
            _logger = logger;
            _ocrService = ocrService;
            _fileStorageService = fileStorageService;
        }

        public async Task<ReceiptData> CreateReceiptAsync(
            int transactionId,
            string imagePath,
            string ocrText,
            string parsedData,
            DateTime scanDate,
            string language = "eng",
            bool isProcessed = false)
        {
            try
            {
                // Validate transaction exists
                var transaction = await _dbContext.Transactions.FindAsync(transactionId);
                if (transaction == null)
                {
                    throw new KeyNotFoundException($"Transaction with ID {transactionId} not found");
                }

                // Create receipt data
                var receiptData = new ReceiptData
                {
                    TransactionId = transactionId,
                    ImagePath = imagePath,
                    OcrText = ocrText,
                    ParsedData = parsedData,
                    ScanDate = scanDate,
                    Language = language,
                    IsProcessed = isProcessed,
                    LastUpdatedAt = DateTime.UtcNow
                };

                // Parse the JSON to get additional data
                if (!string.IsNullOrEmpty(parsedData))
                {
                    try
                    {
                        var parsedJson = JsonSerializer.Deserialize<JsonElement>(parsedData);

                        if (parsedJson.TryGetProperty("MerchantName", out var merchantNameProp))
                            receiptData.MerchantName = merchantNameProp.GetString() ?? "";

                        if (parsedJson.TryGetProperty("Date", out var dateProp) && dateProp.ValueKind == JsonValueKind.String)
                        {
                            var dateStr = dateProp.GetString();
                            if (!string.IsNullOrEmpty(dateStr))
                                receiptData.ReceiptDate = DateTime.Parse(dateStr);
                        }

                        if (parsedJson.TryGetProperty("TotalAmount", out var totalAmountProp))
                            receiptData.TotalAmount = totalAmountProp.GetDecimal();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error parsing receipt data JSON");
                    }
                }

                // Add to database
                _dbContext.ReceiptData.Add(receiptData);
                await _dbContext.SaveChangesAsync();

                return receiptData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating receipt for transaction {transactionId}");
                throw;
            }
        }

        public async Task<ReceiptData> GetReceiptByIdAsync(int receiptId)
        {
            try
            {
                var receipt = await _dbContext.ReceiptData
                    .Include(r => r.Transaction)
                    .FirstOrDefaultAsync(r => r.Id == receiptId);

                if (receipt == null)
                {
                    throw new KeyNotFoundException($"Receipt with ID {receiptId} not found");
                }

                return receipt;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting receipt {receiptId}");
                throw;
            }
        }

        public async Task<IEnumerable<ReceiptData>> GetReceiptsByTransactionIdAsync(int transactionId)
        {
            try
            {
                return await _dbContext.ReceiptData
                    .Where(r => r.TransactionId == transactionId)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting receipts for transaction {transactionId}");
                throw;
            }
        }

        public async Task<IEnumerable<ReceiptData>> GetReceiptHistoryAsync(
            int userId,
            DateTime? startDate = null,
            DateTime? endDate = null,
            decimal? minAmount = null,
            decimal? maxAmount = null,
            string? merchantName = null,
            bool? isProcessed = null)
        {
            try
            {
                // Get transactions for the user
                var userTransactionIds = await _dbContext.Transactions
                    .Where(t => t.UserId == userId)
                    .Select(t => t.Id)
                    .ToListAsync();

                // Query receipts
                var query = _dbContext.ReceiptData
                    .Where(r => userTransactionIds.Contains(r.TransactionId));

                // Apply filters
                if (startDate.HasValue)
                {
                    query = query.Where(r => r.ScanDate >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    query = query.Where(r => r.ScanDate <= endDate.Value);
                }

                if (minAmount.HasValue)
                {
                    query = query.Where(r => r.TotalAmount >= minAmount.Value);
                }

                if (maxAmount.HasValue)
                {
                    query = query.Where(r => r.TotalAmount <= maxAmount.Value);
                }

                if (!string.IsNullOrEmpty(merchantName))
                {
                    query = query.Where(r => r.MerchantName.Contains(merchantName));
                }

                if (isProcessed.HasValue)
                {
                    query = query.Where(r => r.IsProcessed == isProcessed.Value);
                }

                // Get results
                return await query
                    .Include(r => r.Transaction)
                    .OrderByDescending(r => r.ScanDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting receipt history for user {userId}");
                throw;
            }
        }

        public async Task<ReceiptData> UpdateReceiptAsync(
            int receiptId,
            string? ocrText = null,
            string? parsedData = null,
            bool? isProcessed = null)
        {
            try
            {
                var receipt = await _dbContext.ReceiptData.FindAsync(receiptId);
                if (receipt == null)
                {
                    throw new KeyNotFoundException($"Receipt with ID {receiptId} not found");
                }

                // Update properties
                if (ocrText != null)
                {
                    receipt.OcrText = ocrText;
                }

                if (parsedData != null)
                {
                    receipt.ParsedData = parsedData;

                    // Parse the JSON to update additional data
                    try
                    {
                        var parsedJson = JsonSerializer.Deserialize<JsonElement>(parsedData);

                        if (parsedJson.TryGetProperty("MerchantName", out var merchantNameProp))
                            receipt.MerchantName = merchantNameProp.GetString() ?? "";

                        if (parsedJson.TryGetProperty("Date", out var dateProp) && dateProp.ValueKind == JsonValueKind.String)
                        {
                            var dateStr = dateProp.GetString();
                            if (!string.IsNullOrEmpty(dateStr))
                                receipt.ReceiptDate = DateTime.Parse(dateStr);
                        }

                        if (parsedJson.TryGetProperty("TotalAmount", out var totalAmountProp))
                            receipt.TotalAmount = totalAmountProp.GetDecimal();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error parsing updated receipt data JSON");
                    }
                }

                if (isProcessed.HasValue)
                {
                    receipt.IsProcessed = isProcessed.Value;
                }

                receipt.LastUpdatedAt = DateTime.UtcNow;

                // Save changes
                await _dbContext.SaveChangesAsync();

                return receipt;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating receipt {receiptId}");
                throw;
            }
        }

        public async Task<bool> DeleteReceiptAsync(int receiptId)
        {
            try
            {
                var receipt = await _dbContext.ReceiptData.FindAsync(receiptId);
                if (receipt == null)
                {
                    return false;
                }

                // Delete the image file
                if (!string.IsNullOrEmpty(receipt.ImagePath))
                {
                    await _fileStorageService.DeleteFileAsync(receipt.ImagePath);
                }

                // Remove from database
                _dbContext.ReceiptData.Remove(receipt);
                await _dbContext.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting receipt {receiptId}");
                throw;
            }
        }

        public async Task<ReceiptData> ProcessAndSaveReceiptAsync(
            string base64Image,
            int? transactionId = null,
            string language = "eng")
        {
            try
            {
                // Process the receipt with OCR
                var receiptData = await _ocrService.ProcessReceiptBase64Async(base64Image, transactionId, language);

                // Save to database
                if (transactionId.HasValue)
                {
                    receiptData.TransactionId = transactionId.Value;
                }

                _dbContext.ReceiptData.Add(receiptData);
                await _dbContext.SaveChangesAsync();

                return receiptData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing and saving receipt");
                throw;
            }
        }

        public async Task<bool> UserCanAccessReceiptAsync(int receiptId, int userId)
        {
            try
            {
                var receipt = await _dbContext.ReceiptData
                    .Include(r => r.Transaction)
                    .FirstOrDefaultAsync(r => r.Id == receiptId);

                if (receipt == null)
                {
                    return false;
                }

                // Check if user owns the transaction
                var transaction = await _dbContext.Transactions
                    .FirstOrDefaultAsync(t => t.Id == receipt.TransactionId);

                if (transaction == null)
                {
                    return false;
                }

                // Direct ownership
                if (transaction.UserId == userId)
                {
                    return true;
                }

                // Check account sharing
                var account = await _dbContext.Accounts
                    .FirstOrDefaultAsync(a => a.Id == transaction.AccountId);

                if (account == null)
                {
                    return false;
                }

                // User owns the account
                if (account.UserId == userId)
                {
                    return true;
                }

                // Check if account is shared with user
                var isShared = await _dbContext.AccountShares
                    .AnyAsync(s => s.AccountId == account.Id && s.FamilyMemberId == userId);

                return isShared;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking if user {userId} can access receipt {receiptId}");
                throw;
            }
        }

        public async Task<IEnumerable<ReceiptData>> BatchCreateReceiptsAsync(IEnumerable<ReceiptData> receipts)
        {
            try
            {
                // Add all receipts to database
                await _dbContext.ReceiptData.AddRangeAsync(receipts);
                await _dbContext.SaveChangesAsync();

                return receipts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch creating receipts");
                throw;
            }
        }

        public async Task<IEnumerable<ReceiptData>> BatchUpdateReceiptsAsync(IEnumerable<ReceiptData> receipts)
        {
            try
            {
                foreach (var receipt in receipts)
                {
                    var existingReceipt = await _dbContext.ReceiptData.FindAsync(receipt.Id);
                    if (existingReceipt != null)
                    {
                        // Update properties
                        _dbContext.Entry(existingReceipt).CurrentValues.SetValues(receipt);
                        existingReceipt.LastUpdatedAt = DateTime.UtcNow;
                    }
                }

                await _dbContext.SaveChangesAsync();

                return receipts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch updating receipts");
                throw;
            }
        }

        public async Task<bool> BatchDeleteReceiptsAsync(IEnumerable<int> receiptIds)
        {
            try
            {
                var receipts = await _dbContext.ReceiptData
                    .Where(r => receiptIds.Contains(r.Id))
                    .ToListAsync();

                // Delete image files
                foreach (var receipt in receipts)
                {
                    if (!string.IsNullOrEmpty(receipt.ImagePath))
                    {
                        await _fileStorageService.DeleteFileAsync(receipt.ImagePath);
                    }
                }

                // Remove from database
                _dbContext.ReceiptData.RemoveRange(receipts);
                await _dbContext.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch deleting receipts");
                throw;
            }
        }
    }
}
