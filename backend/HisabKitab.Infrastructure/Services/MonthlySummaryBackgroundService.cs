using System;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using Microsoft.EntityFrameworkCore;
using HisabKitab.Core.Interfaces;

namespace HisabKitab.Infrastructure.Services
{
    public class MonthlySummaryBackgroundService : IHostedService, IDisposable
    {
        private Task? _executingTask;
        private readonly CancellationTokenSource _stoppingCts = new();
        private readonly ILogger<MonthlySummaryBackgroundService> _logger;
        private readonly IServiceProvider _serviceProvider;
        private Timer? _timer;

        public MonthlySummaryBackgroundService(
            ILogger<MonthlySummaryBackgroundService> logger,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Monthly Summary Background Service is starting.");

            // Calculate time until next run (first day of next month at 2 AM)
            var now = DateTime.UtcNow;
            var nextMonth = now.AddMonths(1);
            var firstDayOfNextMonth = new DateTime(nextMonth.Year, nextMonth.Month, 1, 2, 0, 0, DateTimeKind.Utc);
            var timeUntilNextRun = firstDayOfNextMonth - now;

            // If it's already past 2 AM on the first day of the month, run today
            if (now.Day == 1 && now.Hour < 2)
            {
                timeUntilNextRun = new DateTime(now.Year, now.Month, 1, 2, 0, 0, DateTimeKind.Utc) - now;
            }

            _logger.LogInformation("Monthly Summary Background Service will run in {TimeUntilNextRun} (at {NextRunTime})",
                timeUntilNextRun, DateTime.UtcNow.Add(timeUntilNextRun));

            // Create a timer that triggers once at the calculated time, then monthly
            _timer = new Timer(ExecuteTask, null, timeUntilNextRun, TimeSpan.FromDays(30));

            return Task.CompletedTask;
        }

        private void ExecuteTask(object? state)
        {
            _logger.LogInformation("Monthly Summary Background Service is executing.");

            // Store the task we're executing
            _executingTask = GenerateMonthlySummariesAsync(_stoppingCts.Token);
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            // Stop the timer
            _timer?.Change(Timeout.Infinite, 0);

            // Stop called without start
            if (_executingTask == null)
            {
                return;
            }

            // Signal cancellation to the executing method
            _stoppingCts.Cancel();

            // Wait until the task completes or the stop token triggers
            await Task.WhenAny(_executingTask, Task.Delay(-1, cancellationToken));

            _logger.LogInformation("Monthly Summary Background Service has stopped.");
        }

        public void Dispose()
        {
            _timer?.Dispose();
            _stoppingCts.Cancel();
            _stoppingCts.Dispose();
            GC.SuppressFinalize(this);
        }

        private async Task GenerateMonthlySummariesAsync(CancellationToken stoppingToken)
        {
            try
            {
                // Get previous month
                var now = DateTime.UtcNow;
                var previousMonth = now.AddMonths(-1);
                var month = previousMonth.Month;
                var year = previousMonth.Year;

                _logger.LogInformation("Generating monthly summaries for {Month}/{Year}", month, year);

                using var scope = _serviceProvider.CreateScope();
                var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();

                // Generate summaries for all users
                var success = await notificationService.GenerateAllMonthlySummariesAsync(month, year);

                if (success)
                {
                    _logger.LogInformation("Successfully generated monthly summaries for {Month}/{Year}", month, year);

                    // Send notifications to users about their monthly summaries
                    await SendMonthlySummaryNotifications(month, year, stoppingToken);
                }
                else
                {
                    _logger.LogWarning("Failed to generate monthly summaries for {Month}/{Year}", month, year);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating monthly summaries");
            }
        }

        private async Task SendMonthlySummaryNotifications(int month, int year, CancellationToken stoppingToken)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();
                var dbContext = scope.ServiceProvider.GetRequiredService<Data.ApplicationDbContext>();

                // Get all monthly summaries for the specified month and year
                var summaries = await dbContext.MonthlySummaries
                    .Where(s => s.Month == month && s.Year == year && !s.IsNotified)
                    .ToListAsync(stoppingToken);

                foreach (var summary in summaries)
                {
                    if (stoppingToken.IsCancellationRequested)
                        break;

                    try
                    {
                        // Format month name
                        var monthName = new DateTime(year, month, 1).ToString("MMMM");

                        // Create notification title and message
                        var title = $"{monthName} {year} Financial Summary";
                        var message = $"Your {monthName} summary is ready! Income: {summary.TotalIncome:C}, Expenses: {summary.TotalExpense:C}, Savings: {summary.NetSavings:C} ({summary.SavingsRate:F1}%)";

                        // Create notification
                        await notificationService.CreateNotificationAsync(
                            summary.UserId,
                            title,
                            message,
                            "MonthlySummary",
                            summary.Id);

                        // Send push notification
                        await notificationService.SendPushNotificationAsync(
                            summary.UserId,
                            title,
                            message,
                            new Dictionary<string, string>
                            {
                                { "type", "MonthlySummary" },
                                { "month", month.ToString() },
                                { "year", year.ToString() },
                                { "summaryId", summary.Id.ToString() },
                                { "actionUrl", "/analytics/monthly-summary" }
                            });

                        // Mark summary as notified
                        summary.IsNotified = true;
                        summary.NotifiedAt = DateTime.UtcNow;
                        summary.UpdatedAt = DateTime.UtcNow;

                        _logger.LogInformation("Monthly summary notification sent to user ID {UserId} for {Month}/{Year}",
                            summary.UserId, month, year);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error sending monthly summary notification to user ID {UserId}",
                            summary.UserId);
                    }
                }

                await dbContext.SaveChangesAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending monthly summary notifications");
            }
        }
    }
}
