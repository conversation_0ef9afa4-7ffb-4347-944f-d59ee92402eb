using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Helpers;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HisabKitab.Infrastructure.Services
{
    public class TransactionService : ITransactionService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<TransactionService> _logger;

        public TransactionService(ApplicationDbContext dbContext, ILogger<TransactionService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        // Helper method to apply filters to transaction queries
        private IQueryable<Transaction> ApplyFilters(
            IQueryable<Transaction> query,
            DateTime? startDate = null,
            DateTime? endDate = null,
            TransactionType? type = null,
            int? accountId = null,
            int? categoryId = null,
            int? priorityId = null,
            string? status = null,
            string? searchTerm = null)
        {
            // Apply date filters
            if (startDate.HasValue)
            {
                query = query.Where(t => t.Date >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(t => t.Date <= endDate.Value);
            }

            // Apply transaction type filter
            if (type.HasValue)
            {
                query = query.Where(t => t.Type == type.Value);
            }

            // Apply account filter
            if (accountId.HasValue)
            {
                query = query.Where(t => t.AccountId == accountId.Value || t.ToAccountId == accountId.Value);
            }

            // Apply category filter
            if (categoryId.HasValue)
            {
                query = query.Where(t => t.CategoryId == categoryId.Value || t.Items.Any(i => i.CategoryId == categoryId.Value));
            }

            // Apply priority filter
            if (priorityId.HasValue)
            {
                query = query.Where(t => t.PriorityId == priorityId.Value);
            }

            // Apply status filter
            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(t => t.Status == status);
            }

            // Apply search term filter
            if (!string.IsNullOrEmpty(searchTerm))
            {
                searchTerm = searchTerm.ToLower();
                query = query.Where(t =>
                    t.Description.ToLower().Contains(searchTerm) ||
                    t.Tags.ToLower().Contains(searchTerm) ||
                    t.Location.ToLower().Contains(searchTerm) ||
                    t.Items.Any(i => i.Name.ToLower().Contains(searchTerm) || i.Notes.ToLower().Contains(searchTerm))
                );
            }

            return query;
        }

        // Helper method to apply sorting to transaction queries
        private IQueryable<Transaction> ApplySorting(
            IQueryable<Transaction> query,
            string? sortBy = null,
            bool ascending = true)
        {
            // Default sort by date descending
            if (string.IsNullOrEmpty(sortBy))
            {
                return ascending ? query.OrderBy(t => t.Date) : query.OrderByDescending(t => t.Date);
            }

            // Apply sorting based on sortBy parameter
            switch (sortBy.ToLower())
            {
                case "amount":
                    return ascending ? query.OrderBy(t => t.Amount) : query.OrderByDescending(t => t.Amount);
                case "description":
                    return ascending ? query.OrderBy(t => t.Description) : query.OrderByDescending(t => t.Description);
                case "category":
                    return ascending ? query.OrderBy(t => t.Category != null ? t.Category.Name : string.Empty) : query.OrderByDescending(t => t.Category != null ? t.Category.Name : string.Empty);
                case "account":
                    return ascending ? query.OrderBy(t => t.Account != null ? t.Account.Name : string.Empty) : query.OrderByDescending(t => t.Account != null ? t.Account.Name : string.Empty);
                case "status":
                    return ascending ? query.OrderBy(t => t.Status) : query.OrderByDescending(t => t.Status);
                case "createdat":
                    return ascending ? query.OrderBy(t => t.CreatedAt) : query.OrderByDescending(t => t.CreatedAt);
                case "updatedat":
                    return ascending ? query.OrderBy(t => t.UpdatedAt) : query.OrderByDescending(t => t.UpdatedAt);
                default:
                    return ascending ? query.OrderBy(t => t.Date) : query.OrderByDescending(t => t.Date);
            }
        }

        public async Task<Transaction> CreateTransactionAsync(
            decimal amount,
            string description,
            DateTime date,
            TransactionType type,
            int accountId,
            int? toAccountId,
            int categoryId,
            int? priorityId,
            int userId,
            string status = "Completed",
            int? approvedByUserId = null,
            string? receiptImage = null,
            string? tags = null,
            string? location = null,
            decimal? exchangeRate = null,
            int? recurringTransactionId = null,
            bool isSynced = true,
            SyncStatus syncStatus = SyncStatus.Synced,
            IEnumerable<TransactionItem>? items = null)
        {
            // Validate account exists
            var account = await _dbContext.Accounts.FindAsync(accountId);
            if (account == null)
            {
                throw new KeyNotFoundException($"Account with ID {accountId} not found");
            }

            // Validate toAccount exists if provided
            if (toAccountId.HasValue)
            {
                var toAccount = await _dbContext.Accounts.FindAsync(toAccountId.Value);
                if (toAccount == null)
                {
                    throw new KeyNotFoundException($"To Account with ID {toAccountId.Value} not found");
                }
            }

            // Validate category exists
            var category = await _dbContext.Categories.FindAsync(categoryId);
            if (category == null)
            {
                throw new KeyNotFoundException($"Category with ID {categoryId} not found");
            }

            // Validate priority exists if provided
            if (priorityId.HasValue)
            {
                var priority = await _dbContext.Priorities.FindAsync(priorityId.Value);
                if (priority == null)
                {
                    throw new KeyNotFoundException($"Priority with ID {priorityId.Value} not found");
                }
            }

            // Create transaction
            var transaction = new Transaction
            {
                Amount = amount,
                Description = description,
                Date = date,
                Type = type,
                AccountId = accountId,
                ToAccountId = toAccountId,
                CategoryId = categoryId,
                PriorityId = priorityId,
                UserId = userId,
                Status = status,
                ApprovedByUserId = approvedByUserId,
                ReceiptImage = receiptImage ?? string.Empty,
                Tags = tags ?? string.Empty,
                Location = location ?? string.Empty,
                ExchangeRate = exchangeRate,
                RecurringTransactionId = recurringTransactionId,
                IsSynced = isSynced,
                SyncStatus = syncStatus,
                LastUpdatedAt = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow
            };

            // Ensure all string properties are initialized to prevent not-null constraint violations
            transaction = EntityHelper.EnsureStringPropertiesInitialized(transaction);

            // Begin transaction
            using var dbTransaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                // Add transaction
                _dbContext.Transactions.Add(transaction);
                await _dbContext.SaveChangesAsync();

                // Update account balance
                if (type == TransactionType.Income)
                {
                    account.Balance += amount;
                }
                else if (type == TransactionType.Expense)
                {
                    account.Balance -= amount;
                }
                else if (type == TransactionType.Transfer && toAccountId.HasValue)
                {
                    account.Balance -= amount;
                    var toAccount = await _dbContext.Accounts.FindAsync(toAccountId.Value);
                    if (toAccount != null)
                    {
                        toAccount.Balance += amount;
                        toAccount.LastUpdatedAt = DateTime.UtcNow;
                    }
                    else
                    {
                        throw new KeyNotFoundException($"To Account with ID {toAccountId.Value} not found");
                    }
                }

                account.LastUpdatedAt = DateTime.UtcNow;
                await _dbContext.SaveChangesAsync();

                // Add transaction items if provided
                if (items != null && items.Any())
                {
                    foreach (var item in items)
                    {
                        item.TransactionId = transaction.Id;
                        item.CreatedAt = DateTime.UtcNow;

                        // Ensure all string properties are initialized
                        var initializedItem = EntityHelper.EnsureStringPropertiesInitialized(item);
                        _dbContext.TransactionItems.Add(initializedItem);
                    }
                    await _dbContext.SaveChangesAsync();
                }

                // Commit transaction
                await dbTransaction.CommitAsync();

                _logger.LogInformation($"Transaction created: {transaction.Id} - {transaction.Amount} - {transaction.Type}");

                return transaction;
            }
            catch (Exception ex)
            {
                // Rollback transaction on error
                await dbTransaction.RollbackAsync();
                _logger.LogError(ex, "Error creating transaction");
                throw;
            }
        }

        public async Task<Transaction> GetTransactionByIdAsync(int transactionId)
        {
            var transaction = await _dbContext.Transactions
                .Include(t => t.Account)
                .Include(t => t.ToAccount)
                .Include(t => t.Category)
                .Include(t => t.Priority)
                .Include(t => t.User)
                .Include(t => t.ApprovedByUser)
                .Include(t => t.Items)
                .ThenInclude(i => i.Category)
                .FirstOrDefaultAsync(t => t.Id == transactionId);

            if (transaction == null)
            {
                throw new KeyNotFoundException($"Transaction with ID {transactionId} not found");
            }

            return transaction;
        }

        public async Task<IEnumerable<Transaction>> GetTransactionsByUserIdAsync(
            int userId,
            DateTime? startDate = null,
            DateTime? endDate = null,
            TransactionType? type = null,
            int? accountId = null,
            int? categoryId = null,
            int? priorityId = null,
            string? status = null,
            string? searchTerm = null,
            string? sortBy = null,
            bool ascending = true,
            int? skip = null,
            int? take = null)
        {
            var query = _dbContext.Transactions
                .Include(t => t.Account)
                .Include(t => t.ToAccount)
                .Include(t => t.Category)
                .Include(t => t.Priority)
                .Include(t => t.Items)
                .ThenInclude(i => i.Category)
                .Where(t => t.UserId == userId);

            // Apply filters
            query = ApplyFilters(query, startDate, endDate, type, accountId, categoryId, priorityId, status, searchTerm);

            // Apply sorting
            query = ApplySorting(query, sortBy, ascending);

            // Apply pagination
            if (skip.HasValue)
            {
                query = query.Skip(skip.Value);
            }

            if (take.HasValue)
            {
                query = query.Take(take.Value);
            }

            return await query.ToListAsync();
        }

        public async Task<IEnumerable<Transaction>> GetTransactionsByAccountIdAsync(
            int accountId,
            DateTime? startDate = null,
            DateTime? endDate = null,
            TransactionType? type = null)
        {
            var query = _dbContext.Transactions
                .Include(t => t.Account)
                .Include(t => t.ToAccount)
                .Include(t => t.Category)
                .Include(t => t.Priority)
                .Include(t => t.Items)
                .ThenInclude(i => i.Category)
                .Where(t => t.AccountId == accountId || t.ToAccountId == accountId);

            // Apply date filters
            if (startDate.HasValue)
            {
                query = query.Where(t => t.Date >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(t => t.Date <= endDate.Value);
            }

            // Apply transaction type filter
            if (type.HasValue)
            {
                query = query.Where(t => t.Type == type.Value);
            }

            // Sort by date descending by default
            query = query.OrderByDescending(t => t.Date);

            return await query.ToListAsync();
        }

        public async Task<IEnumerable<Transaction>> GetTransactionsByFamilyIdAsync(
            int familyId,
            DateTime? startDate = null,
            DateTime? endDate = null,
            TransactionType? type = null,
            int? accountId = null,
            int? categoryId = null,
            int? priorityId = null,
            string? status = null,
            string? searchTerm = null,
            string? sortBy = null,
            bool ascending = true,
            int? skip = null,
            int? take = null)
        {
            // Get all family accounts
            var familyAccounts = await _dbContext.Accounts
                .Where(a => a.FamilyId == familyId)
                .Select(a => a.Id)
                .ToListAsync();

            if (!familyAccounts.Any())
            {
                return new List<Transaction>();
            }

            var query = _dbContext.Transactions
                .Include(t => t.Account)
                .Include(t => t.ToAccount)
                .Include(t => t.Category)
                .Include(t => t.Priority)
                .Include(t => t.Items)
                .ThenInclude(i => i.Category)
                .Where(t => familyAccounts.Contains(t.AccountId) ||
                           (t.ToAccountId.HasValue && familyAccounts.Contains(t.ToAccountId.Value)));

            // Apply filters
            query = ApplyFilters(query, startDate, endDate, type, accountId, categoryId, priorityId, status, searchTerm);

            // Apply sorting
            query = ApplySorting(query, sortBy, ascending);

            // Apply pagination
            if (skip.HasValue)
            {
                query = query.Skip(skip.Value);
            }

            if (take.HasValue)
            {
                query = query.Take(take.Value);
            }

            return await query.ToListAsync();
        }

        public async Task<Transaction> UpdateTransactionAsync(
            int transactionId,
            decimal? amount = null,
            string? description = null,
            DateTime? date = null,
            TransactionType? type = null,
            int? accountId = null,
            int? toAccountId = null,
            int? categoryId = null,
            int? priorityId = null,
            string? status = null,
            int? approvedByUserId = null,
            string? receiptImage = null,
            string? tags = null,
            string? location = null,
            decimal? exchangeRate = null,
            int? recurringTransactionId = null,
            bool? isSynced = null,
            SyncStatus? syncStatus = null)
        {
            var transaction = await _dbContext.Transactions.FindAsync(transactionId);
            if (transaction == null)
            {
                throw new KeyNotFoundException($"Transaction with ID {transactionId} not found");
            }

            // Begin database transaction
            using var dbTransaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                // Store original values for account balance updates
                var originalAmount = transaction.Amount;
                var originalType = transaction.Type;
                var originalAccountId = transaction.AccountId;
                var originalToAccountId = transaction.ToAccountId;

                // Update transaction properties if provided
                if (amount.HasValue)
                {
                    transaction.Amount = amount.Value;
                }

                if (description != null)
                {
                    transaction.Description = description;
                }

                if (date.HasValue)
                {
                    transaction.Date = date.Value;
                }

                if (type.HasValue)
                {
                    transaction.Type = type.Value;
                }

                if (accountId.HasValue)
                {
                    // Validate account exists
                    var account = await _dbContext.Accounts.FindAsync(accountId.Value);
                    if (account == null)
                    {
                        throw new KeyNotFoundException($"Account with ID {accountId.Value} not found");
                    }
                    transaction.AccountId = accountId.Value;
                }

                if (toAccountId.HasValue)
                {
                    // Validate toAccount exists if provided
                    if (toAccountId.Value > 0)
                    {
                        var toAccount = await _dbContext.Accounts.FindAsync(toAccountId.Value);
                        if (toAccount == null)
                        {
                            throw new KeyNotFoundException($"To Account with ID {toAccountId.Value} not found");
                        }
                        transaction.ToAccountId = toAccountId.Value;
                    }
                    else
                    {
                        transaction.ToAccountId = null;
                    }
                }

                if (categoryId.HasValue)
                {
                    // Validate category exists
                    var category = await _dbContext.Categories.FindAsync(categoryId.Value);
                    if (category == null)
                    {
                        throw new KeyNotFoundException($"Category with ID {categoryId.Value} not found");
                    }
                    transaction.CategoryId = categoryId.Value;
                }

                if (priorityId.HasValue)
                {
                    // Validate priority exists if provided
                    if (priorityId.Value > 0)
                    {
                        var priority = await _dbContext.Priorities.FindAsync(priorityId.Value);
                        if (priority == null)
                        {
                            throw new KeyNotFoundException($"Priority with ID {priorityId.Value} not found");
                        }
                        transaction.PriorityId = priorityId.Value;
                    }
                    else
                    {
                        transaction.PriorityId = null;
                    }
                }

                if (status != null)
                {
                    transaction.Status = status;
                }

                if (approvedByUserId.HasValue)
                {
                    transaction.ApprovedByUserId = approvedByUserId;
                }

                if (receiptImage != null)
                {
                    transaction.ReceiptImage = receiptImage;
                }

                if (tags != null)
                {
                    transaction.Tags = tags;
                }

                if (location != null)
                {
                    transaction.Location = location;
                }

                if (exchangeRate.HasValue)
                {
                    transaction.ExchangeRate = exchangeRate;
                }

                if (recurringTransactionId.HasValue)
                {
                    transaction.RecurringTransactionId = recurringTransactionId;
                }

                if (isSynced.HasValue)
                {
                    transaction.IsSynced = isSynced.Value;
                }

                if (syncStatus.HasValue)
                {
                    transaction.SyncStatus = syncStatus.Value;
                }

                transaction.LastUpdatedAt = DateTime.UtcNow;
                transaction.UpdatedAt = DateTime.UtcNow;

                // Ensure all string properties are initialized
                transaction = EntityHelper.EnsureStringPropertiesInitialized(transaction);

                // Update transaction
                _dbContext.Transactions.Update(transaction);
                await _dbContext.SaveChangesAsync();

                // Update account balances if necessary
                if (amount.HasValue || type.HasValue || accountId.HasValue || toAccountId.HasValue)
                {
                    // Revert original transaction effect on accounts
                    if (originalType == TransactionType.Income)
                    {
                        var originalAccount = await _dbContext.Accounts.FindAsync(originalAccountId);
                        if (originalAccount != null)
                        {
                            originalAccount.Balance -= originalAmount;
                            originalAccount.LastUpdatedAt = DateTime.UtcNow;
                        }
                    }
                    else if (originalType == TransactionType.Expense)
                    {
                        var originalAccount = await _dbContext.Accounts.FindAsync(originalAccountId);
                        if (originalAccount != null)
                        {
                            originalAccount.Balance += originalAmount;
                            originalAccount.LastUpdatedAt = DateTime.UtcNow;
                        }
                    }
                    else if (originalType == TransactionType.Transfer && originalToAccountId.HasValue)
                    {
                        var originalAccount = await _dbContext.Accounts.FindAsync(originalAccountId);
                        var originalToAccount = originalToAccountId.HasValue ?
                            await _dbContext.Accounts.FindAsync(originalToAccountId.Value) : null;

                        if (originalAccount != null)
                        {
                            originalAccount.Balance += originalAmount;
                            originalAccount.LastUpdatedAt = DateTime.UtcNow;
                        }

                        if (originalToAccount != null)
                        {
                            originalToAccount.Balance -= originalAmount;
                            originalToAccount.LastUpdatedAt = DateTime.UtcNow;
                        }
                    }

                    // Apply new transaction effect on accounts
                    if (transaction.Type == TransactionType.Income)
                    {
                        var account = await _dbContext.Accounts.FindAsync(transaction.AccountId);
                        if (account != null)
                        {
                            account.Balance += transaction.Amount;
                            account.LastUpdatedAt = DateTime.UtcNow;
                        }
                    }
                    else if (transaction.Type == TransactionType.Expense)
                    {
                        var account = await _dbContext.Accounts.FindAsync(transaction.AccountId);
                        if (account != null)
                        {
                            account.Balance -= transaction.Amount;
                            account.LastUpdatedAt = DateTime.UtcNow;
                        }
                    }
                    else if (transaction.Type == TransactionType.Transfer && transaction.ToAccountId.HasValue)
                    {
                        var account = await _dbContext.Accounts.FindAsync(transaction.AccountId);
                        var toAccount = transaction.ToAccountId.HasValue ?
                            await _dbContext.Accounts.FindAsync(transaction.ToAccountId.Value) : null;

                        if (account != null)
                        {
                            account.Balance -= transaction.Amount;
                            account.LastUpdatedAt = DateTime.UtcNow;
                        }

                        if (toAccount != null)
                        {
                            toAccount.Balance += transaction.Amount;
                            toAccount.LastUpdatedAt = DateTime.UtcNow;
                        }
                    }

                    await _dbContext.SaveChangesAsync();
                }

                // Commit transaction
                await dbTransaction.CommitAsync();

                _logger.LogInformation($"Transaction updated: {transaction.Id}");

                return transaction;
            }
            catch (Exception ex)
            {
                // Rollback transaction on error
                await dbTransaction.RollbackAsync();
                _logger.LogError(ex, "Error updating transaction");
                throw;
            }
        }

        public async Task<bool> DeleteTransactionAsync(int transactionId)
        {
            var transaction = await _dbContext.Transactions
                .Include(t => t.Items)
                .FirstOrDefaultAsync(t => t.Id == transactionId);

            if (transaction == null)
            {
                throw new KeyNotFoundException($"Transaction with ID {transactionId} not found");
            }

            // Begin database transaction
            using var dbTransaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                // Revert transaction effect on account balances
                if (transaction.Type == TransactionType.Income)
                {
                    var account = await _dbContext.Accounts.FindAsync(transaction.AccountId);
                    if (account != null)
                    {
                        account.Balance -= transaction.Amount;
                        account.LastUpdatedAt = DateTime.UtcNow;
                    }
                }
                else if (transaction.Type == TransactionType.Expense)
                {
                    var account = await _dbContext.Accounts.FindAsync(transaction.AccountId);
                    if (account != null)
                    {
                        account.Balance += transaction.Amount;
                        account.LastUpdatedAt = DateTime.UtcNow;
                    }
                }
                else if (transaction.Type == TransactionType.Transfer && transaction.ToAccountId.HasValue)
                {
                    var account = await _dbContext.Accounts.FindAsync(transaction.AccountId);
                    var toAccount = transaction.ToAccountId.HasValue ?
                        await _dbContext.Accounts.FindAsync(transaction.ToAccountId.Value) : null;

                    if (account != null)
                    {
                        account.Balance += transaction.Amount;
                        account.LastUpdatedAt = DateTime.UtcNow;
                    }

                    if (toAccount != null)
                    {
                        toAccount.Balance -= transaction.Amount;
                        toAccount.LastUpdatedAt = DateTime.UtcNow;
                    }
                }

                await _dbContext.SaveChangesAsync();

                // Remove transaction items
                if (transaction.Items != null && transaction.Items.Any())
                {
                    _dbContext.TransactionItems.RemoveRange(transaction.Items);
                    await _dbContext.SaveChangesAsync();
                }

                // Remove transaction
                _dbContext.Transactions.Remove(transaction);
                await _dbContext.SaveChangesAsync();

                // Commit transaction
                await dbTransaction.CommitAsync();

                _logger.LogInformation($"Transaction deleted: {transactionId}");

                return true;
            }
            catch (Exception ex)
            {
                // Rollback transaction on error
                await dbTransaction.RollbackAsync();
                _logger.LogError(ex, "Error deleting transaction");
                throw;
            }
        }

        public async Task<TransactionItem> AddTransactionItemAsync(
            int transactionId,
            string name,
            decimal amount,
            int quantity = 1,
            int? categoryId = null,
            string? notes = null)
        {
            // Validate transaction exists
            var transaction = await _dbContext.Transactions.FindAsync(transactionId);
            if (transaction == null)
            {
                throw new KeyNotFoundException($"Transaction with ID {transactionId} not found");
            }

            // Validate category exists if provided
            if (categoryId.HasValue)
            {
                var category = await _dbContext.Categories.FindAsync(categoryId.Value);
                if (category == null)
                {
                    throw new KeyNotFoundException($"Category with ID {categoryId.Value} not found");
                }
            }

            // Create transaction item
            var transactionItem = new TransactionItem
            {
                TransactionId = transactionId,
                Name = name,
                Amount = amount,
                Quantity = quantity,
                CategoryId = categoryId,
                Notes = notes ?? string.Empty,
                CreatedAt = DateTime.UtcNow
            };

            // Ensure all string properties are initialized
            transactionItem = EntityHelper.EnsureStringPropertiesInitialized(transactionItem);

            _dbContext.TransactionItems.Add(transactionItem);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Transaction item created: {transactionItem.Id} for transaction {transactionId}");

            return transactionItem;
        }

        public async Task<TransactionItem> UpdateTransactionItemAsync(
            int transactionItemId,
            string? name = null,
            decimal? amount = null,
            int? quantity = null,
            int? categoryId = null,
            string? notes = null)
        {
            var transactionItem = await _dbContext.TransactionItems.FindAsync(transactionItemId);
            if (transactionItem == null)
            {
                throw new KeyNotFoundException($"Transaction item with ID {transactionItemId} not found");
            }

            // Validate category exists if provided
            if (categoryId.HasValue && categoryId.Value > 0)
            {
                var category = await _dbContext.Categories.FindAsync(categoryId.Value);
                if (category == null)
                {
                    throw new KeyNotFoundException($"Category with ID {categoryId.Value} not found");
                }
                transactionItem.CategoryId = categoryId.Value;
            }
            else if (categoryId.HasValue && categoryId.Value <= 0)
            {
                transactionItem.CategoryId = null;
            }

            // Update transaction item properties if provided
            if (name != null)
            {
                transactionItem.Name = name;
            }

            if (amount.HasValue)
            {
                transactionItem.Amount = amount.Value;
            }

            if (quantity.HasValue)
            {
                transactionItem.Quantity = quantity.Value;
            }

            if (notes != null)
            {
                transactionItem.Notes = notes;
            }

            transactionItem.UpdatedAt = DateTime.UtcNow;

            // Ensure all string properties are initialized
            transactionItem = EntityHelper.EnsureStringPropertiesInitialized(transactionItem);

            _dbContext.TransactionItems.Update(transactionItem);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Transaction item updated: {transactionItem.Id}");

            return transactionItem;
        }

        public async Task<bool> DeleteTransactionItemAsync(int transactionItemId)
        {
            var transactionItem = await _dbContext.TransactionItems.FindAsync(transactionItemId);
            if (transactionItem == null)
            {
                throw new KeyNotFoundException($"Transaction item with ID {transactionItemId} not found");
            }

            _dbContext.TransactionItems.Remove(transactionItem);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Transaction item deleted: {transactionItemId}");

            return true;
        }

        public async Task<IEnumerable<Transaction>> BatchCreateTransactionsAsync(IEnumerable<Transaction> transactions)
        {
            if (transactions == null || !transactions.Any())
            {
                return new List<Transaction>();
            }

            // Begin database transaction
            using var dbTransaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var createdTransactions = new List<Transaction>();

                foreach (var transaction in transactions)
                {
                    // Validate account exists
                    var account = await _dbContext.Accounts.FindAsync(transaction.AccountId);
                    if (account == null)
                    {
                        throw new KeyNotFoundException($"Account with ID {transaction.AccountId} not found");
                    }

                    // Validate toAccount exists if provided
                    if (transaction.ToAccountId.HasValue)
                    {
                        var toAccount = await _dbContext.Accounts.FindAsync(transaction.ToAccountId.Value);
                        if (toAccount == null)
                        {
                            throw new KeyNotFoundException($"To Account with ID {transaction.ToAccountId.Value} not found");
                        }
                    }

                    // Validate category exists
                    var category = await _dbContext.Categories.FindAsync(transaction.CategoryId);
                    if (category == null)
                    {
                        throw new KeyNotFoundException($"Category with ID {transaction.CategoryId} not found");
                    }

                    // Validate priority exists if provided
                    if (transaction.PriorityId.HasValue)
                    {
                        var priority = await _dbContext.Priorities.FindAsync(transaction.PriorityId.Value);
                        if (priority == null)
                        {
                            throw new KeyNotFoundException($"Priority with ID {transaction.PriorityId.Value} not found");
                        }
                    }

                    // Set creation date and last updated date
                    transaction.CreatedAt = DateTime.UtcNow;
                    transaction.LastUpdatedAt = DateTime.UtcNow;

                    // Ensure all string properties are initialized
                    EntityHelper.EnsureStringPropertiesInitialized(transaction);

                    // Add transaction
                    _dbContext.Transactions.Add(transaction);
                    await _dbContext.SaveChangesAsync();

                    // Update account balance
                    if (transaction.Type == TransactionType.Income)
                    {
                        account.Balance += transaction.Amount;
                    }
                    else if (transaction.Type == TransactionType.Expense)
                    {
                        account.Balance -= transaction.Amount;
                    }
                    else if (transaction.Type == TransactionType.Transfer && transaction.ToAccountId.HasValue)
                    {
                        account.Balance -= transaction.Amount;
                        var toAccount = await _dbContext.Accounts.FindAsync(transaction.ToAccountId.Value);
                        if (toAccount != null)
                        {
                            toAccount.Balance += transaction.Amount;
                        }
                        if (toAccount != null)
                        {
                            toAccount.LastUpdatedAt = DateTime.UtcNow;
                        }
                    }

                    account.LastUpdatedAt = DateTime.UtcNow;
                    await _dbContext.SaveChangesAsync();

                    createdTransactions.Add(transaction);
                }

                // Commit transaction
                await dbTransaction.CommitAsync();

                _logger.LogInformation($"Batch created {createdTransactions.Count} transactions");

                return createdTransactions;
            }
            catch (Exception ex)
            {
                // Rollback transaction on error
                await dbTransaction.RollbackAsync();
                _logger.LogError(ex, "Error batch creating transactions");
                throw;
            }
        }

        public async Task<IEnumerable<Transaction>> BatchUpdateTransactionsAsync(IEnumerable<Transaction> transactions)
        {
            if (transactions == null || !transactions.Any())
            {
                return new List<Transaction>();
            }

            // Begin database transaction
            using var dbTransaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var updatedTransactions = new List<Transaction>();

                foreach (var transaction in transactions)
                {
                    // Validate transaction exists
                    var existingTransaction = await _dbContext.Transactions.FindAsync(transaction.Id);
                    if (existingTransaction == null)
                    {
                        throw new KeyNotFoundException($"Transaction with ID {transaction.Id} not found");
                    }

                    // Store original values for account balance updates
                    var originalAmount = existingTransaction.Amount;
                    var originalType = existingTransaction.Type;
                    var originalAccountId = existingTransaction.AccountId;
                    var originalToAccountId = existingTransaction.ToAccountId;

                    // Update transaction properties
                    existingTransaction.Amount = transaction.Amount;
                    existingTransaction.Description = transaction.Description;
                    existingTransaction.Date = transaction.Date;
                    existingTransaction.Type = transaction.Type;
                    existingTransaction.CategoryId = transaction.CategoryId;
                    existingTransaction.PriorityId = transaction.PriorityId;
                    existingTransaction.Status = transaction.Status;
                    existingTransaction.Tags = transaction.Tags;
                    existingTransaction.Location = transaction.Location;
                    existingTransaction.ExchangeRate = transaction.ExchangeRate;
                    existingTransaction.IsSynced = transaction.IsSynced;
                    existingTransaction.SyncStatus = transaction.SyncStatus;
                    existingTransaction.LastUpdatedAt = DateTime.UtcNow;
                    existingTransaction.UpdatedAt = DateTime.UtcNow;

                    // Validate account exists if changed
                    if (existingTransaction.AccountId != originalAccountId)
                    {
                        var account = await _dbContext.Accounts.FindAsync(existingTransaction.AccountId);
                        if (account == null)
                        {
                            throw new KeyNotFoundException($"Account with ID {existingTransaction.AccountId} not found");
                        }
                    }

                    // Validate toAccount exists if provided and changed
                    if (existingTransaction.ToAccountId.HasValue && existingTransaction.ToAccountId != originalToAccountId)
                    {
                        var toAccount = await _dbContext.Accounts.FindAsync(existingTransaction.ToAccountId.Value);
                        if (toAccount == null)
                        {
                            throw new KeyNotFoundException($"To Account with ID {existingTransaction.ToAccountId.Value} not found");
                        }
                    }

                    // Validate category exists
                    var category = await _dbContext.Categories.FindAsync(existingTransaction.CategoryId);
                    if (category == null)
                    {
                        throw new KeyNotFoundException($"Category with ID {existingTransaction.CategoryId} not found");
                    }

                    // Validate priority exists if provided
                    if (existingTransaction.PriorityId.HasValue)
                    {
                        var priority = await _dbContext.Priorities.FindAsync(existingTransaction.PriorityId.Value);
                        if (priority == null)
                        {
                            throw new KeyNotFoundException($"Priority with ID {existingTransaction.PriorityId.Value} not found");
                        }
                    }

                    // Ensure all string properties are initialized
                    existingTransaction = EntityHelper.EnsureStringPropertiesInitialized(existingTransaction);

                    // Update transaction
                    _dbContext.Transactions.Update(existingTransaction);
                    await _dbContext.SaveChangesAsync();

                    // Update account balances if necessary
                    if (existingTransaction.Amount != originalAmount ||
                        existingTransaction.Type != originalType ||
                        existingTransaction.AccountId != originalAccountId ||
                        existingTransaction.ToAccountId != originalToAccountId)
                    {
                        // Revert original transaction effect on accounts
                        if (originalType == TransactionType.Income)
                        {
                            var originalAccount = await _dbContext.Accounts.FindAsync(originalAccountId);
                            if (originalAccount != null)
                            {
                                originalAccount.Balance -= originalAmount;
                                originalAccount.LastUpdatedAt = DateTime.UtcNow;
                            }
                        }
                        else if (originalType == TransactionType.Expense)
                        {
                            var originalAccount = await _dbContext.Accounts.FindAsync(originalAccountId);
                            if (originalAccount != null)
                            {
                                originalAccount.Balance += originalAmount;
                                originalAccount.LastUpdatedAt = DateTime.UtcNow;
                            }
                        }
                        else if (originalType == TransactionType.Transfer && originalToAccountId.HasValue)
                        {
                            var originalAccount = await _dbContext.Accounts.FindAsync(originalAccountId);
                            var originalToAccount = originalToAccountId.HasValue ?
                                await _dbContext.Accounts.FindAsync(originalToAccountId.Value) : null;

                            if (originalAccount != null)
                            {
                                originalAccount.Balance += originalAmount;
                                originalAccount.LastUpdatedAt = DateTime.UtcNow;
                            }

                            if (originalToAccount != null)
                            {
                                originalToAccount.Balance -= originalAmount;
                                originalToAccount.LastUpdatedAt = DateTime.UtcNow;
                            }
                        }

                        // Apply new transaction effect on accounts
                        if (existingTransaction.Type == TransactionType.Income)
                        {
                            var account = await _dbContext.Accounts.FindAsync(existingTransaction.AccountId);
                            if (account != null)
                            {
                                account.Balance += existingTransaction.Amount;
                                account.LastUpdatedAt = DateTime.UtcNow;
                            }
                        }
                        else if (existingTransaction.Type == TransactionType.Expense)
                        {
                            var account = await _dbContext.Accounts.FindAsync(existingTransaction.AccountId);
                            if (account != null)
                            {
                                account.Balance -= existingTransaction.Amount;
                                account.LastUpdatedAt = DateTime.UtcNow;
                            }
                        }
                        else if (existingTransaction.Type == TransactionType.Transfer && existingTransaction.ToAccountId.HasValue)
                        {
                            var account = await _dbContext.Accounts.FindAsync(existingTransaction.AccountId);
                            var toAccount = existingTransaction.ToAccountId.HasValue ?
                                await _dbContext.Accounts.FindAsync(existingTransaction.ToAccountId.Value) : null;

                            if (account != null)
                            {
                                account.Balance -= existingTransaction.Amount;
                                account.LastUpdatedAt = DateTime.UtcNow;
                            }

                            if (toAccount != null)
                            {
                                toAccount.Balance += existingTransaction.Amount;
                                toAccount.LastUpdatedAt = DateTime.UtcNow;
                            }
                        }

                        await _dbContext.SaveChangesAsync();
                    }

                    updatedTransactions.Add(existingTransaction);
                }

                // Commit transaction
                await dbTransaction.CommitAsync();

                _logger.LogInformation($"Batch updated {updatedTransactions.Count} transactions");

                return updatedTransactions;
            }
            catch (Exception ex)
            {
                // Rollback transaction on error
                await dbTransaction.RollbackAsync();
                _logger.LogError(ex, "Error batch updating transactions");
                throw;
            }
        }

        public async Task<bool> BatchDeleteTransactionsAsync(IEnumerable<int> transactionIds)
        {
            if (transactionIds == null || !transactionIds.Any())
            {
                return true;
            }

            // Begin database transaction
            using var dbTransaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                foreach (var transactionId in transactionIds)
                {
                    var transaction = await _dbContext.Transactions
                        .Include(t => t.Items)
                        .FirstOrDefaultAsync(t => t.Id == transactionId);

                    if (transaction == null)
                    {
                        // Skip if transaction doesn't exist
                        continue;
                    }

                    // Revert transaction effect on account balances
                    if (transaction.Type == TransactionType.Income)
                    {
                        var account = await _dbContext.Accounts.FindAsync(transaction.AccountId);
                        if (account != null)
                        {
                            account.Balance -= transaction.Amount;
                            account.LastUpdatedAt = DateTime.UtcNow;
                        }
                    }
                    else if (transaction.Type == TransactionType.Expense)
                    {
                        var account = await _dbContext.Accounts.FindAsync(transaction.AccountId);
                        if (account != null)
                        {
                            account.Balance += transaction.Amount;
                            account.LastUpdatedAt = DateTime.UtcNow;
                        }
                    }
                    else if (transaction.Type == TransactionType.Transfer && transaction.ToAccountId.HasValue)
                    {
                        var account = await _dbContext.Accounts.FindAsync(transaction.AccountId);
                        var toAccount = transaction.ToAccountId.HasValue ?
                            await _dbContext.Accounts.FindAsync(transaction.ToAccountId.Value) : null;

                        if (account != null)
                        {
                            account.Balance += transaction.Amount;
                            account.LastUpdatedAt = DateTime.UtcNow;
                        }

                        if (toAccount != null)
                        {
                            toAccount.Balance -= transaction.Amount;
                            toAccount.LastUpdatedAt = DateTime.UtcNow;
                        }
                    }

                    await _dbContext.SaveChangesAsync();

                    // Remove transaction items
                    if (transaction.Items != null && transaction.Items.Any())
                    {
                        _dbContext.TransactionItems.RemoveRange(transaction.Items);
                        await _dbContext.SaveChangesAsync();
                    }

                    // Remove transaction
                    _dbContext.Transactions.Remove(transaction);
                    await _dbContext.SaveChangesAsync();
                }

                // Commit transaction
                await dbTransaction.CommitAsync();

                _logger.LogInformation($"Batch deleted {transactionIds.Count()} transactions");

                return true;
            }
            catch (Exception ex)
            {
                // Rollback transaction on error
                await dbTransaction.RollbackAsync();
                _logger.LogError(ex, "Error batch deleting transactions");
                throw;
            }
        }

        public async Task<bool> TransactionExistsAsync(int transactionId)
        {
            return await _dbContext.Transactions.AnyAsync(t => t.Id == transactionId);
        }

        public async Task<bool> UserCanAccessTransactionAsync(int transactionId, int userId)
        {
            var transaction = await _dbContext.Transactions
                .Include(t => t.Account)
                .FirstOrDefaultAsync(t => t.Id == transactionId);

            if (transaction == null)
            {
                return false;
            }

            // User can access if they created the transaction
            if (transaction.UserId == userId)
            {
                return true;
            }

            // User can access if the transaction is for a shared account
            var accountShares = await _dbContext.AccountShares
                .Where(a => a.AccountId == transaction.AccountId)
                .Include(a => a.FamilyMember)
                .ToListAsync();

            return accountShares.Any(a => a.FamilyMember != null && a.FamilyMember.UserId == userId);
        }

        public async Task<bool> UserCanModifyTransactionAsync(int transactionId, int userId)
        {
            var transaction = await _dbContext.Transactions
                .Include(t => t.Account)
                .FirstOrDefaultAsync(t => t.Id == transactionId);

            if (transaction == null)
            {
                return false;
            }

            // User can modify if they created the transaction
            if (transaction.UserId == userId)
            {
                return true;
            }

            // User can modify if they have edit permissions on the account
            var accountShares = await _dbContext.AccountShares
                .Where(a => a.AccountId == transaction.AccountId)
                .Include(a => a.FamilyMember)
                .ToListAsync();

            return accountShares.Any(a => a.FamilyMember != null && a.FamilyMember.UserId == userId &&
                                         (a.Permissions == "Edit" || a.Permissions == "Delete"));
        }

        public async Task<decimal> GetTotalIncomeAsync(int userId, DateTime startDate, DateTime endDate, int? accountId = null)
        {
            var query = _dbContext.Transactions
                .Where(t => t.UserId == userId && t.Type == TransactionType.Income && t.Date >= startDate && t.Date <= endDate);

            if (accountId.HasValue)
            {
                query = query.Where(t => t.AccountId == accountId.Value);
            }

            return await query.SumAsync(t => t.Amount);
        }

        public async Task<decimal> GetTotalExpenseAsync(int userId, DateTime startDate, DateTime endDate, int? accountId = null)
        {
            var query = _dbContext.Transactions
                .Where(t => t.UserId == userId && t.Type == TransactionType.Expense && t.Date >= startDate && t.Date <= endDate);

            if (accountId.HasValue)
            {
                query = query.Where(t => t.AccountId == accountId.Value);
            }

            return await query.SumAsync(t => t.Amount);
        }

        public async Task<decimal> GetBalanceAsync(int userId, DateTime startDate, DateTime endDate, int? accountId = null)
        {
            var income = await GetTotalIncomeAsync(userId, startDate, endDate, accountId);
            var expense = await GetTotalExpenseAsync(userId, startDate, endDate, accountId);

            return income - expense;
        }
    }
}
