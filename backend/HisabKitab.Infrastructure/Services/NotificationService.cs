using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using HisabKitab.Core.Entities;
using HisabKitab.Core.Enums;
using HisabKitab.Core.Interfaces;
using HisabKitab.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HisabKitab.Infrastructure.Services
{
    public class NotificationService : INotificationService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<NotificationService> _logger;

        public NotificationService(
            ApplicationDbContext dbContext,
            ILogger<NotificationService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        #region Immediate Notifications

        public async Task<Notification> CreateNotificationAsync(
            int userId,
            string title,
            string message,
            string type,
            int? referenceId = null)
        {
            // Validate user exists
            if (!await _dbContext.Users.AnyAsync(u => u.Id == userId))
            {
                throw new KeyNotFoundException($"User with ID {userId} not found");
            }

            // Parse the notification type
            var notificationType = NotificationType.Info; // Default to Info
            if (Enum.TryParse<NotificationType>(type, true, out var parsedType))
            {
                notificationType = parsedType;
            }

            // Create notification
            var notification = new Notification
            {
                UserId = userId,
                Title = title,
                Message = message,
                Type = notificationType,
                RelatedEntityType = type,
                RelatedEntityId = referenceId,
                IsRead = false,
                ReadAt = null,
                CreatedAt = DateTime.UtcNow
            };

            // Ensure all string properties are initialized to prevent not-null constraint violations
            // Initialize any null string properties with empty strings
            if (notification.Title == null) notification.Title = string.Empty;
            if (notification.Message == null) notification.Message = string.Empty;
            if (notification.RelatedEntityType == null) notification.RelatedEntityType = string.Empty;

            _dbContext.Notifications.Add(notification);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Notification created for user ID {userId}: {title}");

            return notification;
        }

        public async Task<IEnumerable<Notification>> GetNotificationsByUserIdAsync(
            int userId,
            bool includeRead = false)
        {
            var query = _dbContext.Notifications
                .Where(n => n.UserId == userId);

            if (!includeRead)
            {
                query = query.Where(n => !n.IsRead);
            }

            return await query
                .OrderByDescending(n => n.CreatedAt)
                .ToListAsync();
        }

        public async Task<Notification> GetNotificationByIdAsync(int notificationId)
        {
            var notification = await _dbContext.Notifications
                .FirstOrDefaultAsync(n => n.Id == notificationId);

            if (notification == null)
            {
                throw new KeyNotFoundException($"Notification with ID {notificationId} not found");
            }

            return notification;
        }

        public async Task<bool> MarkNotificationAsReadAsync(int notificationId)
        {
            var notification = await _dbContext.Notifications.FindAsync(notificationId);
            if (notification == null)
            {
                throw new KeyNotFoundException($"Notification with ID {notificationId} not found");
            }

            notification.IsRead = true;
            notification.ReadAt = DateTime.UtcNow;
            notification.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Notification marked as read: ID {notificationId}");

            return true;
        }

        public async Task<bool> MarkAllNotificationsAsReadAsync(int userId)
        {
            var notifications = await _dbContext.Notifications
                .Where(n => n.UserId == userId && !n.IsRead)
                .ToListAsync();

            if (!notifications.Any())
            {
                return true; // No unread notifications
            }

            foreach (var notification in notifications)
            {
                notification.IsRead = true;
                notification.ReadAt = DateTime.UtcNow;
                notification.UpdatedAt = DateTime.UtcNow;
            }

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"All notifications marked as read for user ID {userId}");

            return true;
        }

        public async Task<bool> DeleteNotificationAsync(int notificationId)
        {
            var notification = await _dbContext.Notifications.FindAsync(notificationId);
            if (notification == null)
            {
                throw new KeyNotFoundException($"Notification with ID {notificationId} not found");
            }

            _dbContext.Notifications.Remove(notification);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Notification deleted: ID {notificationId}");

            return true;
        }
        #endregion

        #region Scheduled Notifications

        public async Task<NotificationSchedule> ScheduleNotificationAsync(
            int userId,
            string title,
            string message,
            DateTime scheduledFor,
            NotificationType type = NotificationType.Info,
            string? relatedEntityType = null,
            int? relatedEntityId = null,
            string? actionUrl = null)
        {
            // Validate user exists
            if (!await _dbContext.Users.AnyAsync(u => u.Id == userId))
            {
                throw new KeyNotFoundException($"User with ID {userId} not found");
            }

            // Create scheduled notification
            var schedule = new NotificationSchedule
            {
                UserId = userId,
                Title = title ?? string.Empty,
                Message = message ?? string.Empty,
                Type = type,
                ScheduledFor = scheduledFor,
                RelatedEntityType = relatedEntityType ?? string.Empty,
                RelatedEntityId = relatedEntityId,
                ActionUrl = actionUrl ?? string.Empty,
                IsActive = true,
                IsSent = false,
                SentAt = null,
                CreatedAt = DateTime.UtcNow
            };

            _dbContext.NotificationSchedules.Add(schedule);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Notification scheduled for user ID {userId} at {scheduledFor}: {title}");

            return schedule;
        }

        public async Task<IEnumerable<NotificationSchedule>> GetScheduledNotificationsByUserIdAsync(
            int userId,
            bool includeSent = false)
        {
            var query = _dbContext.NotificationSchedules
                .Where(n => n.UserId == userId);

            if (!includeSent)
            {
                query = query.Where(n => !n.IsSent);
            }

            return await query
                .OrderBy(n => n.ScheduledFor)
                .ToListAsync();
        }

        public async Task<NotificationSchedule> GetScheduledNotificationByIdAsync(int scheduleId)
        {
            var schedule = await _dbContext.NotificationSchedules
                .FirstOrDefaultAsync(n => n.Id == scheduleId);

            if (schedule == null)
            {
                throw new KeyNotFoundException($"Scheduled notification with ID {scheduleId} not found");
            }

            return schedule;
        }

        public async Task<NotificationSchedule> UpdateScheduledNotificationAsync(
            int scheduleId,
            string? title = null,
            string? message = null,
            DateTime? scheduledFor = null,
            bool? isActive = null)
        {
            var schedule = await _dbContext.NotificationSchedules.FindAsync(scheduleId);
            if (schedule == null)
            {
                throw new KeyNotFoundException($"Scheduled notification with ID {scheduleId} not found");
            }

            // Update properties if provided
            if (title != null)
                schedule.Title = title;

            if (message != null)
                schedule.Message = message;

            if (scheduledFor.HasValue)
                schedule.ScheduledFor = scheduledFor.Value;

            if (isActive.HasValue)
                schedule.IsActive = isActive.Value;

            schedule.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Scheduled notification updated: ID {scheduleId}");

            return schedule;
        }

        public async Task<bool> DeleteScheduledNotificationAsync(int scheduleId)
        {
            var schedule = await _dbContext.NotificationSchedules.FindAsync(scheduleId);
            if (schedule == null)
            {
                throw new KeyNotFoundException($"Scheduled notification with ID {scheduleId} not found");
            }

            _dbContext.NotificationSchedules.Remove(schedule);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Scheduled notification deleted: ID {scheduleId}");

            return true;
        }

        public async Task<IEnumerable<NotificationSchedule>> GetDueScheduledNotificationsAsync(DateTime? beforeDate = null)
        {
            var now = beforeDate ?? DateTime.UtcNow;

            return await _dbContext.NotificationSchedules
                .Where(n => n.IsActive && !n.IsSent && n.ScheduledFor <= now)
                .OrderBy(n => n.ScheduledFor)
                .ToListAsync();
        }

        public async Task<bool> MarkScheduledNotificationAsSentAsync(int scheduleId)
        {
            var schedule = await _dbContext.NotificationSchedules.FindAsync(scheduleId);
            if (schedule == null)
            {
                throw new KeyNotFoundException($"Scheduled notification with ID {scheduleId} not found");
            }

            schedule.IsSent = true;
            schedule.SentAt = DateTime.UtcNow;
            schedule.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Scheduled notification marked as sent: ID {scheduleId}");

            return true;
        }

        #endregion

        #region Recurring Notifications

        public async Task<RecurringNotification> CreateRecurringNotificationAsync(
            int userId,
            string title,
            string message,
            RecurrenceType recurrenceType,
            int recurrenceInterval,
            DateTime startDate,
            DateTime? endDate = null,
            NotificationType type = NotificationType.Info,
            string? relatedEntityType = null,
            int? relatedEntityId = null,
            string? actionUrl = null,
            string? recurrenceData = null)
        {
            // Validate user exists
            if (!await _dbContext.Users.AnyAsync(u => u.Id == userId))
            {
                throw new KeyNotFoundException($"User with ID {userId} not found");
            }

            // Calculate next run time
            var nextRunAt = CalculateNextRunTime(startDate, recurrenceType, recurrenceInterval, recurrenceData ?? string.Empty);

            // Create recurring notification
            var recurring = new RecurringNotification
            {
                UserId = userId,
                Title = title ?? string.Empty,
                Message = message ?? string.Empty,
                Type = type,
                RecurrenceType = recurrenceType,
                RecurrenceInterval = recurrenceInterval,
                StartDate = startDate,
                EndDate = endDate,
                RecurrenceData = recurrenceData ?? string.Empty,
                RelatedEntityType = relatedEntityType ?? string.Empty,
                RelatedEntityId = relatedEntityId,
                ActionUrl = actionUrl ?? string.Empty,
                IsActive = true,
                LastRunAt = DateTime.MinValue,
                NextRunAt = nextRunAt,
                CreatedAt = DateTime.UtcNow
            };

            _dbContext.RecurringNotifications.Add(recurring);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Recurring notification created for user ID {userId}: {title}");

            return recurring;
        }

        public async Task<IEnumerable<RecurringNotification>> GetRecurringNotificationsByUserIdAsync(
            int userId,
            bool includeInactive = false)
        {
            var query = _dbContext.RecurringNotifications
                .Where(n => n.UserId == userId);

            if (!includeInactive)
            {
                query = query.Where(n => n.IsActive);
            }

            return await query
                .OrderBy(n => n.NextRunAt)
                .ToListAsync();
        }

        public async Task<RecurringNotification> GetRecurringNotificationByIdAsync(int recurringId)
        {
            var recurring = await _dbContext.RecurringNotifications
                .FirstOrDefaultAsync(n => n.Id == recurringId);

            if (recurring == null)
            {
                throw new KeyNotFoundException($"Recurring notification with ID {recurringId} not found");
            }

            return recurring;
        }

        public async Task<RecurringNotification> UpdateRecurringNotificationAsync(
            int recurringId,
            string? title = null,
            string? message = null,
            RecurrenceType? recurrenceType = null,
            int? recurrenceInterval = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            bool? isActive = null,
            string? recurrenceData = null)
        {
            var recurring = await _dbContext.RecurringNotifications.FindAsync(recurringId);
            if (recurring == null)
            {
                throw new KeyNotFoundException($"Recurring notification with ID {recurringId} not found");
            }

            // Update properties if provided
            if (title != null)
                recurring.Title = title;

            if (message != null)
                recurring.Message = message;

            bool recurrenceChanged = false;

            if (recurrenceType.HasValue)
            {
                recurring.RecurrenceType = recurrenceType.Value;
                recurrenceChanged = true;
            }

            if (recurrenceInterval.HasValue)
            {
                recurring.RecurrenceInterval = recurrenceInterval.Value;
                recurrenceChanged = true;
            }

            if (startDate.HasValue)
            {
                recurring.StartDate = startDate.Value;
                recurrenceChanged = true;
            }

            if (endDate.HasValue)
                recurring.EndDate = endDate;

            if (recurrenceData != null)
            {
                recurring.RecurrenceData = recurrenceData;
                recurrenceChanged = true;
            }

            if (isActive.HasValue)
                recurring.IsActive = isActive.Value;

            // Recalculate next run time if recurrence parameters changed
            if (recurrenceChanged)
            {
                recurring.NextRunAt = CalculateNextRunTime(
                    recurring.StartDate,
                    recurring.RecurrenceType,
                    recurring.RecurrenceInterval,
                    recurring.RecurrenceData);
            }

            recurring.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Recurring notification updated: ID {recurringId}");

            return recurring;
        }

        public async Task<bool> DeleteRecurringNotificationAsync(int recurringId)
        {
            var recurring = await _dbContext.RecurringNotifications.FindAsync(recurringId);
            if (recurring == null)
            {
                throw new KeyNotFoundException($"Recurring notification with ID {recurringId} not found");
            }

            _dbContext.RecurringNotifications.Remove(recurring);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Recurring notification deleted: ID {recurringId}");

            return true;
        }

        public async Task<IEnumerable<RecurringNotification>> GetDueRecurringNotificationsAsync(DateTime? beforeDate = null)
        {
            var now = beforeDate ?? DateTime.UtcNow;

            return await _dbContext.RecurringNotifications
                .Where(n => n.IsActive && n.NextRunAt <= now && (n.EndDate == null || n.EndDate >= now))
                .OrderBy(n => n.NextRunAt)
                .ToListAsync();
        }

        public async Task<bool> UpdateRecurringNotificationNextRunTimeAsync(int recurringId)
        {
            var recurring = await _dbContext.RecurringNotifications.FindAsync(recurringId);
            if (recurring == null)
            {
                throw new KeyNotFoundException($"Recurring notification with ID {recurringId} not found");
            }

            // Update last run time
            recurring.LastRunAt = DateTime.UtcNow;

            // Calculate next run time
            recurring.NextRunAt = CalculateNextRunTime(
                recurring.StartDate,
                recurring.RecurrenceType,
                recurring.RecurrenceInterval,
                recurring.RecurrenceData,
                recurring.LastRunAt);

            recurring.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Recurring notification next run time updated: ID {recurringId}, Next: {recurring.NextRunAt}");

            return true;
        }

        private DateTime CalculateNextRunTime(
            DateTime startDate,
            RecurrenceType recurrenceType,
            int interval,
            string recurrenceData,
            DateTime? lastRunAt = null)
        {
            var baseDate = lastRunAt ?? DateTime.UtcNow;

            // If the start date is in the future, use that as the next run time
            if (startDate > baseDate)
            {
                return startDate;
            }

            // Calculate next run time based on recurrence type
            switch (recurrenceType)
            {
                case RecurrenceType.Daily:
                    return baseDate.AddDays(interval);

                case RecurrenceType.Weekly:
                    return baseDate.AddDays(interval * 7);

                case RecurrenceType.Monthly:
                    return baseDate.AddMonths(interval);

                case RecurrenceType.Yearly:
                    return baseDate.AddYears(interval);

                case RecurrenceType.Custom:
                    // Parse custom recurrence data (JSON)
                    if (!string.IsNullOrEmpty(recurrenceData))
                    {
                        try
                        {
                            var customData = JsonSerializer.Deserialize<Dictionary<string, object>>(recurrenceData);
                            // Implement custom recurrence logic based on the data
                            // For now, default to daily
                            return baseDate.AddDays(interval);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error parsing custom recurrence data");
                        }
                    }
                    return baseDate.AddDays(interval);

                default:
                    return baseDate.AddDays(interval);
            }
        }

        #endregion

        #region Push Notifications

        public async Task<bool> SendPushNotificationAsync(
            int userId,
            string title,
            string message,
            Dictionary<string, string>? data = null)
        {
            // Get user's active devices
            var devices = await _dbContext.UserDevices
                .Where(d => d.UserId == userId && d.IsActive && !string.IsNullOrEmpty(d.PushToken))
                .ToListAsync();

            if (!devices.Any())
            {
                _logger.LogInformation($"No active devices found for user ID {userId}");
                return false;
            }

            bool success = true;

            // Send push notification to each device
            foreach (var device in devices)
            {
                try
                {
                    // In a real implementation, this would call a push notification service
                    // For now, just log the notification
                    _logger.LogInformation($"Push notification sent to device {device.DeviceId} for user ID {userId}: {title}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error sending push notification to device {device.DeviceId} for user ID {userId}");
                    success = false;
                }
            }

            return success;
        }

        public async Task<UserDevice> RegisterDeviceAsync(
            int userId,
            string deviceId,
            string pushToken,
            string? deviceName = null,
            string? deviceType = null)
        {
            // Check if device already exists
            var existingDevice = await _dbContext.UserDevices
                .FirstOrDefaultAsync(d => d.UserId == userId && d.DeviceId == deviceId);

            if (existingDevice != null)
            {
                // Update existing device
                existingDevice.PushToken = pushToken;
                existingDevice.DeviceName = deviceName ?? existingDevice.DeviceName;
                existingDevice.DeviceType = deviceType ?? existingDevice.DeviceType;
                existingDevice.LastActiveAt = DateTime.UtcNow;
                existingDevice.IsActive = true;
                existingDevice.UpdatedAt = DateTime.UtcNow;

                await _dbContext.SaveChangesAsync();

                _logger.LogInformation($"Device updated for user ID {userId}: {deviceId}");

                return existingDevice;
            }

            // Create new device
            var device = new UserDevice
            {
                UserId = userId,
                DeviceId = deviceId,
                DeviceName = deviceName ?? "Unknown Device",
                DeviceType = deviceType ?? "Unknown",
                PushToken = pushToken,
                LastActiveAt = DateTime.UtcNow,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            _dbContext.UserDevices.Add(device);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Device registered for user ID {userId}: {deviceId}");

            return device;
        }

        public async Task<UserDevice> UpdateDeviceTokenAsync(
            int userId,
            string deviceId,
            string pushToken)
        {
            var device = await _dbContext.UserDevices
                .FirstOrDefaultAsync(d => d.UserId == userId && d.DeviceId == deviceId);

            if (device == null)
            {
                throw new KeyNotFoundException($"Device with ID {deviceId} not found for user ID {userId}");
            }

            device.PushToken = pushToken;
            device.LastActiveAt = DateTime.UtcNow;
            device.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Device token updated for user ID {userId}: {deviceId}");

            return device;
        }

        public async Task<bool> UnregisterDeviceAsync(
            int userId,
            string deviceId)
        {
            var device = await _dbContext.UserDevices
                .FirstOrDefaultAsync(d => d.UserId == userId && d.DeviceId == deviceId);

            if (device == null)
            {
                throw new KeyNotFoundException($"Device with ID {deviceId} not found for user ID {userId}");
            }

            device.IsActive = false;
            device.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation($"Device unregistered for user ID {userId}: {deviceId}");

            return true;
        }

        #endregion

        #region Monthly Summary

        public async Task<bool> GenerateMonthlyUserSummaryAsync(
            int userId,
            int month,
            int year)
        {
            // Validate user exists
            if (!await _dbContext.Users.AnyAsync(u => u.Id == userId))
            {
                throw new KeyNotFoundException($"User with ID {userId} not found");
            }

            // Check if summary already exists
            var existingSummary = await _dbContext.MonthlySummaries
                .FirstOrDefaultAsync(s => s.UserId == userId && s.Month == month && s.Year == year);

            if (existingSummary != null)
            {
                _logger.LogInformation($"Monthly summary already exists for user ID {userId} for {month}/{year}");
                return true;
            }

            try
            {
                // Calculate start and end dates for the month
                var startDate = new DateTime(year, month, 1);
                var endDate = startDate.AddMonths(1).AddDays(-1);

                // Get all transactions for the user in the specified month
                var transactions = await _dbContext.Transactions
                    .Include(t => t.Category)
                    .Where(t => t.UserId == userId && t.Date >= startDate && t.Date <= endDate)
                    .ToListAsync();

                // Calculate summary data
                decimal totalIncome = transactions
                    .Where(t => t.Type == TransactionType.Income)
                    .Sum(t => t.Amount);

                decimal totalExpense = transactions
                    .Where(t => t.Type == TransactionType.Expense)
                    .Sum(t => t.Amount);

                decimal netSavings = totalIncome - totalExpense;
                decimal savingsRate = totalIncome > 0 ? (netSavings / totalIncome) * 100 : 0;

                int transactionCount = transactions.Count;

                var expenseTransactions = transactions
                    .Where(t => t.Type == TransactionType.Expense)
                    .ToList();

                decimal averageExpense = expenseTransactions.Any()
                    ? expenseTransactions.Average(t => t.Amount)
                    : 0;

                decimal largestExpense = expenseTransactions.Any()
                    ? expenseTransactions.Max(t => t.Amount)
                    : 0;

                // Get top expense category
                var expensesByCategory = expenseTransactions
                    .GroupBy(t => t.Category?.Name ?? "Uncategorized")
                    .Select(g => new { Category = g.Key, Amount = g.Sum(t => t.Amount) })
                    .OrderByDescending(x => x.Amount)
                    .ToList();

                string topExpenseCategory = expensesByCategory.Any()
                    ? expensesByCategory.First().Category
                    : "None";

                // Get top income source
                var incomeBySource = transactions
                    .Where(t => t.Type == TransactionType.Income)
                    .GroupBy(t => t.Category?.Name ?? "Uncategorized")
                    .Select(g => new { Source = g.Key, Amount = g.Sum(t => t.Amount) })
                    .OrderByDescending(x => x.Amount)
                    .ToList();

                string topIncomeSource = incomeBySource.Any()
                    ? incomeBySource.First().Source
                    : "None";

                // Create expenses by category JSON
                var expensesByCategoryJson = JsonSerializer.Serialize(
                    expensesByCategory.Select(x => new { category = x.Category, amount = x.Amount }));

                // Create daily trend JSON
                var dailyTrend = new List<object>();
                for (var date = startDate; date <= endDate; date = date.AddDays(1))
                {
                    var dayTransactions = transactions.Where(t => t.Date.Date == date.Date).ToList();
                    var dayIncome = dayTransactions.Where(t => t.Type == TransactionType.Income).Sum(t => t.Amount);
                    var dayExpense = dayTransactions.Where(t => t.Type == TransactionType.Expense).Sum(t => t.Amount);

                    dailyTrend.Add(new
                    {
                        date = date.ToString("yyyy-MM-dd"),
                        income = dayIncome,
                        expense = dayExpense
                    });
                }
                var dailyTrendJson = JsonSerializer.Serialize(dailyTrend);

                // Create monthly summary
                var summary = new MonthlySummary
                {
                    UserId = userId,
                    Month = month,
                    Year = year,
                    TotalIncome = totalIncome,
                    TotalExpense = totalExpense,
                    NetSavings = netSavings,
                    SavingsRate = savingsRate,
                    TransactionCount = transactionCount,
                    AverageExpense = averageExpense,
                    LargestExpense = largestExpense,
                    TopExpenseCategory = topExpenseCategory,
                    TopIncomeSource = topIncomeSource,
                    ExpensesByCategory = expensesByCategoryJson,
                    DailyTrend = dailyTrendJson,
                    IsNotified = false,
                    CreatedAt = DateTime.UtcNow
                };

                _dbContext.MonthlySummaries.Add(summary);
                await _dbContext.SaveChangesAsync();

                _logger.LogInformation($"Monthly summary generated for user ID {userId} for {month}/{year}");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error generating monthly summary for user ID {userId} for {month}/{year}");
                return false;
            }
        }

        public async Task<bool> GenerateAllMonthlySummariesAsync(
            int month,
            int year)
        {
            try
            {
                // Get all active users
                var users = await _dbContext.Users.ToListAsync();

                foreach (var user in users)
                {
                    try
                    {
                        await GenerateMonthlyUserSummaryAsync(user.Id, month, year);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Error generating monthly summary for user ID {user.Id}");
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error generating monthly summaries for {month}/{year}");
                return false;
            }
        }

        #endregion
    }
}
