using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using HisabKitab.Core.Interfaces;

namespace HisabKitab.Infrastructure.Services
{
    public class NotificationBackgroundService : IHostedService, IDisposable
    {
        private Task? _executingTask;
        private readonly CancellationTokenSource _stoppingCts = new();
        private readonly ILogger<NotificationBackgroundService> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(5); // Check every 5 minutes

        public NotificationBackgroundService(
            ILogger<NotificationBackgroundService> logger,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Notification Background Service is starting.");

            // Store the task we're executing
            _executingTask = ExecuteAsync(_stoppingCts.Token);

            // If the task is completed then return it, otherwise it's running
            return _executingTask.IsCompleted ? _executingTask : Task.CompletedTask;
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            // Stop called without start
            if (_executingTask == null)
            {
                return;
            }

            // Signal cancellation to the executing method
            _stoppingCts.Cancel();

            // Wait until the task completes or the stop token triggers
            await Task.WhenAny(_executingTask, Task.Delay(-1, cancellationToken));

            _logger.LogInformation("Notification Background Service has stopped.");
        }

        public void Dispose()
        {
            _stoppingCts.Cancel();
            _stoppingCts.Dispose();
            GC.SuppressFinalize(this);
        }

        private async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Notification Background Service is starting.");

            while (!stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("Notification Background Service is checking for due notifications.");

                try
                {
                    await ProcessScheduledNotifications(stoppingToken);
                    await ProcessRecurringNotifications(stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while processing notifications.");
                }

                _logger.LogInformation("Notification Background Service is sleeping for {Interval} minutes.", _checkInterval.TotalMinutes);
                await Task.Delay(_checkInterval, stoppingToken);
            }

            _logger.LogInformation("Notification Background Service is stopping.");
        }

        private async Task ProcessScheduledNotifications(CancellationToken stoppingToken)
        {
            using var scope = _serviceProvider.CreateScope();
            var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();

            // Get all active scheduled notifications that are due
            var dueNotifications = await notificationService.GetDueScheduledNotificationsAsync(DateTime.UtcNow);

            foreach (var notification in dueNotifications)
            {
                if (stoppingToken.IsCancellationRequested)
                    break;

                try
                {
                    _logger.LogInformation("Processing scheduled notification ID {NotificationId} for user ID {UserId}",
                        notification.Id, notification.UserId);

                    // Create immediate notification
                    await notificationService.CreateNotificationAsync(
                        notification.UserId,
                        notification.Title,
                        notification.Message,
                        notification.Type.ToString(),
                        notification.RelatedEntityId);

                    // Send push notification if applicable
                    await notificationService.SendPushNotificationAsync(
                        notification.UserId,
                        notification.Title,
                        notification.Message,
                        new Dictionary<string, string>
                        {
                            { "type", notification.Type.ToString() },
                            { "entityType", notification.RelatedEntityType ?? string.Empty },
                            { "entityId", notification.RelatedEntityId?.ToString() ?? string.Empty },
                            { "actionUrl", notification.ActionUrl ?? string.Empty }
                        });

                    // Mark scheduled notification as sent
                    await notificationService.MarkScheduledNotificationAsSentAsync(notification.Id);
                    _logger.LogInformation("Scheduled notification ID {NotificationId} processed and marked as sent", notification.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing scheduled notification ID {NotificationId}", notification.Id);
                }
            }
        }

        private async Task ProcessRecurringNotifications(CancellationToken stoppingToken)
        {
            using var scope = _serviceProvider.CreateScope();
            var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();

            // Get all active recurring notifications that are due
            var dueRecurringNotifications = await notificationService.GetDueRecurringNotificationsAsync(DateTime.UtcNow);

            foreach (var recurring in dueRecurringNotifications)
            {
                if (stoppingToken.IsCancellationRequested)
                    break;

                try
                {
                    _logger.LogInformation("Processing recurring notification ID {RecurringId} for user ID {UserId}",
                        recurring.Id, recurring.UserId);

                    // Create immediate notification
                    await notificationService.CreateNotificationAsync(
                        recurring.UserId,
                        recurring.Title,
                        recurring.Message,
                        recurring.Type.ToString(),
                        recurring.RelatedEntityId);

                    // Send push notification if applicable
                    await notificationService.SendPushNotificationAsync(
                        recurring.UserId,
                        recurring.Title,
                        recurring.Message,
                        new Dictionary<string, string>
                        {
                            { "type", recurring.Type.ToString() },
                            { "entityType", recurring.RelatedEntityType ?? string.Empty },
                            { "entityId", recurring.RelatedEntityId?.ToString() ?? string.Empty },
                            { "actionUrl", recurring.ActionUrl ?? string.Empty }
                        });

                    // Update next run time
                    await notificationService.UpdateRecurringNotificationNextRunTimeAsync(recurring.Id);
                    _logger.LogInformation("Recurring notification ID {RecurringId} processed and next run time updated", recurring.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing recurring notification ID {RecurringId}", recurring.Id);
                }
            }
        }
    }
}
