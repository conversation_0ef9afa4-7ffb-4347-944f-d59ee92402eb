using Microsoft.EntityFrameworkCore;
using HisabKitab.Core.Entities;

namespace HisabKitab.Infrastructure.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<RefreshToken> RefreshTokens { get; set; }
        public DbSet<Family> Families { get; set; }
        public DbSet<FamilyMember> FamilyMembers { get; set; }
        public DbSet<Account> Accounts { get; set; }
        public DbSet<AccountShare> AccountShares { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Transaction> Transactions { get; set; }
        public DbSet<TransactionItem> TransactionItems { get; set; }
        public DbSet<TransactionTag> TransactionTags { get; set; }
        public DbSet<Tag> Tags { get; set; }
        public DbSet<Priority> Priorities { get; set; }
        public DbSet<BudgetLimit> BudgetLimits { get; set; }
        public DbSet<Currency> Currencies { get; set; }
        public DbSet<ExchangeRate> ExchangeRates { get; set; }
        public DbSet<Loan> Loans { get; set; }
        public DbSet<LoanPayment> LoanPayments { get; set; }
        public DbSet<LoanReminder> LoanReminders { get; set; }
        public DbSet<SavingsGoal> SavingsGoals { get; set; }
        public DbSet<SavingsContribution> SavingsContributions { get; set; }
        public DbSet<WishlistItem> WishlistItems { get; set; }
        public DbSet<RecurringTransaction> RecurringTransactions { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<NotificationSchedule> NotificationSchedules { get; set; }
        public DbSet<RecurringNotification> RecurringNotifications { get; set; }
        public DbSet<MonthlySummary> MonthlySummaries { get; set; }
        public DbSet<ExpenseApprovalRequest> ExpenseApprovalRequests { get; set; }
        public DbSet<SyncQueue> SyncQueue { get; set; }
        public DbSet<Report> Reports { get; set; }
        public DbSet<ReportData> ReportData { get; set; }
        public DbSet<AnalyticsMetric> AnalyticsMetrics { get; set; }
        public DbSet<AnomalyDetection> AnomalyDetections { get; set; }
        public DbSet<SpendingLimit> SpendingLimits { get; set; }
        public DbSet<UserDevice> UserDevices { get; set; }
        public DbSet<ImportHistory> ImportHistory { get; set; }
        public DbSet<ExportHistory> ExportHistory { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }
        public DbSet<FeatureFlag> FeatureFlags { get; set; }
        public DbSet<ReceiptData> ReceiptData { get; set; }
        public DbSet<UserSetting> UserSettings { get; set; }
        public DbSet<SyncConflict> SyncConflicts { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure composite key for FamilyMember
            modelBuilder.Entity<FamilyMember>()
                .HasKey(fm => new { fm.FamilyId, fm.UserId });

            // Configure composite key for TransactionTag
            modelBuilder.Entity<TransactionTag>()
                .HasKey(tt => new { tt.TransactionId, tt.TagId });

            // Configure composite key for UserRole
            modelBuilder.Entity<UserRole>()
                .HasKey(ur => new { ur.UserId, ur.RoleId });

            // Configure relationships for User
            modelBuilder.Entity<User>()
                .HasMany(u => u.LoansGiven)
                .WithOne(l => l.LenderUser)
                .HasForeignKey(l => l.LenderUserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<User>()
                .HasMany(u => u.LoansReceived)
                .WithOne(l => l.BorrowerUser)
                .HasForeignKey(l => l.BorrowerUserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<User>()
                .HasMany(u => u.RequestedApprovals)
                .WithOne(ea => ea.RequestedByUser)
                .HasForeignKey(ea => ea.RequestedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<User>()
                .HasMany(u => u.ApprovedApprovals)
                .WithOne(ea => ea.ApprovedByUser)
                .HasForeignKey(ea => ea.ApprovedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure relationships for Transaction
            modelBuilder.Entity<Transaction>()
                .HasOne(t => t.Account)
                .WithMany(a => a.Transactions)
                .HasForeignKey(t => t.AccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Transaction>()
                .HasOne(t => t.ToAccount)
                .WithMany(a => a.TransfersTo)
                .HasForeignKey(t => t.ToAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Transaction>()
                .HasOne(t => t.ApprovedByUser)
                .WithMany()
                .HasForeignKey(t => t.ApprovedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure relationships for Currency
            modelBuilder.Entity<Currency>()
                .HasMany(c => c.FromExchangeRates)
                .WithOne(er => er.FromCurrency)
                .HasForeignKey(er => er.FromCurrencyId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Currency>()
                .HasMany(c => c.ToExchangeRates)
                .WithOne(er => er.ToCurrency)
                .HasForeignKey(er => er.ToCurrencyId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure relationships for Category
            modelBuilder.Entity<Category>()
                .HasOne(c => c.ParentCategory)
                .WithMany(c => c.Subcategories)
                .HasForeignKey(c => c.ParentCategoryId)
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
}
