# Hisab-Kitab Development Guide

This document provides a step-by-step guide for developing the Hisab-Kitab personal finance application, organized by phases and features.

## Phase 1: Authentication & Core Setup

### Backend Development

1. **Database Setup**
   - Configure PostgreSQL connection in `appsettings.json`
   - Create initial migration: `dotnet ef migrations add InitialCreate`
   - Apply migration: `dotnet ef database update`

2. **User & Role Models**
   - Implement `User.cs` and `Role.cs` in Core/Entities
   - Create `UserRole.cs` for many-to-many relationship
   - Add `RefreshToken.cs` for token-based auth

3. **Authentication DTOs**
   - Create `AuthDTOs.cs` with:
     - `RegisterDto`
     - `LoginDto`
     - `TokenResponseDto`
     - `RefreshTokenDto`

4. **Auth Services**
   - Implement `IJwtService.cs` interface
   - Create `JwtService.cs` implementation
   - Implement `IUserService.cs` interface
   - Create `UserService.cs` implementation
   - Implement `IRoleService.cs` interface
   - Create `RoleService.cs` implementation

5. **Auth Controller**
   - Create `AuthController.cs` with endpoints:
     - `POST /api/auth/register`
     - `POST /api/auth/login`
     - `POST /api/auth/refresh-token`
     - `POST /api/auth/logout`

6. **Role-Based Authorization**
   - Implement `RequireRoleAttribute.cs` for role-based access
   - Configure JWT authentication in `Program.cs`
   - Add automatic PlatformAdmin role assignment for first user

### Frontend Development

1. **Project Setup**
   - Initialize React Native project with Expo
   - Configure folder structure
   - Set up i18n for English and Nepali

2. **API Service**
   - Create `api.ts` with base API configuration
   - Implement auth endpoints in API service

3. **Auth Context**
   - Create `AuthContext.tsx` with:
     - User state management
     - Login/logout functions
     - Role checking functions

4. **Auth Screens**
   - Implement `LoginScreen.tsx`
   - Implement `RegisterScreen.tsx`

5. **Navigation**
   - Set up `AppNavigator.tsx` with auth flow
   - Create protected routes based on authentication

## Phase 2: Family Management

### Backend Development

1. **Family Domain Models**
   - Implement `Family.cs` entity
   - Create `FamilyMember.cs` entity with roles

2. **Family DTOs**
   - Create `FamilyDTOs.cs` with:
     - `FamilyDto`
     - `CreateFamilyDto`
     - `UpdateFamilyDto`
     - `FamilyMemberDto`
     - `InviteCodeDto`

3. **Family Services**
   - Implement `IFamilyService.cs` interface
   - Create `FamilyService.cs` implementation

4. **Family Controllers**
   - Create `FamilyController.cs` with endpoints:
     - `GET /api/family` - List families
     - `GET /api/family/{id}` - Get family details
     - `POST /api/family` - Create family
     - `PUT /api/family/{id}` - Update family
     - `DELETE /api/family/{id}` - Delete family
   - Create `FamilyMemberController.cs` with endpoints:
     - `GET /api/family/{id}/members` - List members
     - `POST /api/family/{id}/invite` - Generate invite code
     - `POST /api/family/join` - Join with invite code
     - `PUT /api/family/{id}/members/{memberId}` - Update member role
     - `DELETE /api/family/{id}/members/{memberId}` - Remove member

### Frontend Development

1. **Family Models**
   - Create `Family.ts` model
   - Create `FamilyMember.ts` model

2. **Family API Service**
   - Add family endpoints to API service

3. **Family Context**
   - Create `FamilyContext.tsx` for state management

4. **Family Screens**
   - Implement `FamiliesScreen.tsx` - List families
   - Implement `FamilyDetailsScreen.tsx` - View family details
   - Implement `CreateFamilyScreen.tsx` - Create new family
   - Implement `JoinFamilyScreen.tsx` - Join with invite code
   - Implement `EditFamilyScreen.tsx` - Edit family details
   - Implement `FamilyMembersScreen.tsx` - Manage members

## Phase 3: Accounts & Categories

### Backend Development

1. **Account Domain Models**
   - Implement `Account.cs` entity
   - Create `AccountShare.cs` for shared accounts

2. **Category Domain Models**
   - Implement `Category.cs` entity
   - Create `Priority.cs` entity for priority system

3. **Account & Category DTOs**
   - Create `AccountDTOs.cs` with:
     - `AccountDto`
     - `CreateAccountDto`
     - `UpdateAccountDto`
     - `AccountShareDto`
   - Create `CategoryDTOs.cs` with:
     - `CategoryDto`
     - `CreateCategoryDto`
     - `UpdateCategoryDto`
   - Create `PriorityDTOs.cs`

4. **Account & Category Services**
   - Implement `IAccountService.cs` interface
   - Create `AccountService.cs` implementation
   - Implement `ICategoryService.cs` interface
   - Create `CategoryService.cs` implementation
   - Implement `IPriorityService.cs` interface
   - Create `PriorityService.cs` implementation

5. **Account & Category Controllers**
   - Create `AccountController.cs` with endpoints:
     - `GET /api/account` - List accounts
     - `GET /api/account/{id}` - Get account details
     - `POST /api/account` - Create account
     - `PUT /api/account/{id}` - Update account
     - `DELETE /api/account/{id}` - Delete account
     - `POST /api/account/{id}/share` - Share account
   - Create `CategoryController.cs` with endpoints:
     - `GET /api/category` - List categories
     - `GET /api/category/{id}` - Get category details
     - `POST /api/category` - Create category
     - `PUT /api/category/{id}` - Update category
     - `DELETE /api/category/{id}` - Delete category
   - Create `PriorityController.cs` with CRUD endpoints

### Frontend Development

1. **Account & Category Models**
   - Create `Account.ts` model
   - Create `Category.ts` model
   - Create `Priority.ts` model

2. **Account & Category API Service**
   - Add account endpoints to API service
   - Add category endpoints to API service

3. **Account & Category Contexts**
   - Create `AccountContext.tsx` for state management
   - Create `CategoryContext.tsx` for state management

4. **Account Screens**
   - Implement `AccountsScreen.tsx` - List accounts
   - Implement `AccountDetailsScreen.tsx` - View account details
   - Implement `CreateAccountScreen.tsx` - Create new account
   - Implement `EditAccountScreen.tsx` - Edit account details
   - Implement `ShareAccountScreen.tsx` - Share account with family

5. **Category Screens**
   - Implement `CategoriesScreen.tsx` - List categories
   - Implement `CategoryDetailsScreen.tsx` - View category details
   - Implement `CreateCategoryScreen.tsx` - Create new category
   - Implement `EditCategoryScreen.tsx` - Edit category details

6. **Budget Limit Screens**
   - Implement `BudgetLimitsScreen.tsx` - List budget limits
   - Implement `AddBudgetLimitScreen.tsx` - Create budget limit
   - Implement `EditBudgetLimitScreen.tsx` - Edit budget limit

## Phase 4: Transactions & Items

### Backend Development

1. **Transaction Domain Models**
   - Implement `Transaction.cs` entity
   - Create `TransactionItem.cs` entity for items
   - Create `TransactionTag.cs` for tagging
   - Create `Tag.cs` entity

2. **Transaction DTOs**
   - Create `TransactionDTOs.cs` with:
     - `TransactionDto`
     - `CreateTransactionDto`
     - `UpdateTransactionDto`
     - `TransactionItemDto`
     - `TransactionHistoryDto`
     - `TransactionFilterDto`

3. **Transaction Services**
   - Implement `ITransactionService.cs` interface
   - Create `TransactionService.cs` implementation
   - Implement `ITagService.cs` interface
   - Create `TagService.cs` implementation

4. **Transaction Controllers**
   - Create `TransactionController.cs` with endpoints:
     - `GET /api/transaction` - List transactions
     - `GET /api/transaction/{id}` - Get transaction details
     - `POST /api/transaction` - Create transaction
     - `PUT /api/transaction/{id}` - Update transaction
     - `DELETE /api/transaction/{id}` - Delete transaction
     - `GET /api/transaction/history` - Get transaction history
     - `POST /api/transaction/batch` - Batch operations
   - Create `TagController.cs` with CRUD endpoints

### Frontend Development

1. **Transaction Models**
   - Create `Transaction.ts` model
   - Create `TransactionItem.ts` model
   - Create `Tag.ts` model

2. **Transaction API Service**
   - Add transaction endpoints to API service
   - Add tag endpoints to API service

3. **Transaction Context**
   - Create `TransactionContext.tsx` for state management

4. **Transaction Screens**
   - Implement `TransactionListScreen.tsx` - List transactions
   - Implement `TransactionDetailsScreen.tsx` - View transaction details
   - Implement `AddTransactionScreen.tsx` - Create new transaction
   - Implement `EditTransactionScreen.tsx` - Edit transaction details
   - Implement `TransactionHistoryScreen.tsx` - View transaction history
   - Implement `TransactionFilterScreen.tsx` - Filter transactions

## Phase 5: Loans & EMIs

### Backend Development

1. **Loan Domain Models**
   - Implement `Loan.cs` entity
   - Create `LoanPayment.cs` entity
   - Create `LoanReminder.cs` entity

2. **Loan DTOs**
   - Create `LoanDTOs.cs` with:
     - `LoanDto`
     - `CreateLoanDto`
     - `UpdateLoanDto`
     - `LoanPaymentDto`
     - `LoanReminderDto`
     - `LoanTimelineDto`

3. **Loan Services**
   - Implement `ILoanService.cs` interface
   - Create `LoanService.cs` implementation
   - Implement EMI calculation algorithms:
     - Flat interest
     - Reducing balance
     - Compound interest

4. **Loan Controllers**
   - Create `LoanController.cs` with endpoints:
     - `GET /api/loan` - List loans
     - `GET /api/loan/{id}` - Get loan details
     - `POST /api/loan` - Create loan
     - `PUT /api/loan/{id}` - Update loan
     - `DELETE /api/loan/{id}` - Delete loan
     - `GET /api/loan/{id}/timeline` - Get loan timeline
     - `GET /api/loan/{id}/payments` - Get loan payments
     - `POST /api/loan/{id}/payment` - Add payment
     - `PUT /api/loan/{id}/payment/{paymentId}` - Update payment
     - `DELETE /api/loan/{id}/payment/{paymentId}` - Delete payment
     - `POST /api/loan/{id}/reminder` - Create reminder

### Frontend Development

1. **Loan Models**
   - Create `Loan.ts` model
   - Create `LoanPayment.ts` model
   - Create `LoanReminder.ts` model

2. **Loan API Service**
   - Add loan endpoints to API service

3. **Loan Context**
   - Create `LoanContext.tsx` for state management

4. **Loan Screens**
   - Implement `LoansScreen.tsx` - List loans
   - Implement `LoanDetailsScreen.tsx` - View loan details
   - Implement `CreateLoanScreen.tsx` - Create new loan
   - Implement `EditLoanScreen.tsx` - Edit loan details
   - Implement `LoanTimelineScreen.tsx` - View loan timeline
   - Implement `AddPaymentScreen.tsx` - Add loan payment
   - Implement `EditPaymentScreen.tsx` - Edit loan payment
   - Implement `LoanReminderScreen.tsx` - Set up reminders

## Phase 6: Savings Goals & Wishlist

### Backend Development

1. **Savings Domain Models**
   - Implement `SavingsGoal.cs` entity
   - Create `SavingsContribution.cs` entity
   - Create `WishlistItem.cs` entity

2. **Savings DTOs**
   - Create `SavingsDTOs.cs` with:
     - `SavingsGoalDto`
     - `CreateSavingsGoalDto`
     - `UpdateSavingsGoalDto`
     - `SavingsContributionDto`
   - Create `WishlistDTOs.cs` with:
     - `WishlistItemDto`
     - `CreateWishlistItemDto`
     - `UpdateWishlistItemDto`

3. **Savings Services**
   - Implement `ISavingsService.cs` interface
   - Create `SavingsService.cs` implementation
   - Implement `IWishlistService.cs` interface
   - Create `WishlistService.cs` implementation

4. **Savings Controllers**
   - Create `SavingsGoalController.cs` with endpoints:
     - `GET /api/savings` - List savings goals
     - `GET /api/savings/{id}` - Get savings goal details
     - `POST /api/savings` - Create savings goal
     - `PUT /api/savings/{id}` - Update savings goal
     - `DELETE /api/savings/{id}` - Delete savings goal
     - `GET /api/savings/{id}/contributions` - Get contributions
     - `POST /api/savings/{id}/contribution` - Add contribution
   - Create `WishlistController.cs` with CRUD endpoints

### Frontend Development

1. **Savings Models**
   - Create `SavingsGoal.ts` model
   - Create `SavingsContribution.ts` model
   - Create `WishlistItem.ts` model

2. **Savings API Service**
   - Add savings endpoints to API service
   - Add wishlist endpoints to API service

3. **Savings Context**
   - Create `SavingsContext.tsx` for state management

4. **Savings Screens**
   - Implement `SavingsGoalsScreen.tsx` - List savings goals
   - Implement `SavingsGoalDetailsScreen.tsx` - View goal details
   - Implement `CreateSavingsGoalScreen.tsx` - Create new goal
   - Implement `EditSavingsGoalScreen.tsx` - Edit goal details
   - Implement `AddContributionScreen.tsx` - Add contribution
   - Implement `WishlistScreen.tsx` - Manage wishlist items

## Phase 7: Analytics & Reports

### Backend Development

1. **Analytics Domain Models**
   - Implement `AnalyticsMetric.cs` entity
   - Create `Report.cs` entity
   - Create `ReportData.cs` entity
   - Create `AnomalyDetection.cs` entity

2. **Analytics DTOs**
   - Create `AnalyticsDTOs.cs` with:
     - `AnalyticsMetricDto`
     - `AnalyticsRequestDto`
     - `CashflowAnalysisDto`
   - Create `ReportDTOs.cs` with:
     - `ReportDto`
     - `CreateReportDto`
     - `UpdateReportDto`
     - `ReportDataDto`

3. **Analytics Services**
   - Implement `IAnalyticsService.cs` interface
   - Create `AnalyticsService.cs` implementation
   - Implement `IReportService.cs` interface
   - Create `ReportService.cs` implementation
   - Create data aggregation algorithms

4. **Analytics Controllers**
   - Create `AnalyticsController.cs` with endpoints:
     - `GET /api/analytics/metrics` - Get metrics
     - `GET /api/analytics/cashflow` - Get cashflow analysis
     - `GET /api/analytics/spending` - Get spending analysis
     - `GET /api/analytics/anomalies` - Get anomaly detections
   - Create `ReportController.cs` with endpoints:
     - `GET /api/report` - List reports
     - `GET /api/report/{id}` - Get report details
     - `POST /api/report` - Create report
     - `PUT /api/report/{id}` - Update report
     - `DELETE /api/report/{id}` - Delete report
     - `GET /api/report/{id}/export` - Export report

### Frontend Development

1. **Analytics Models**
   - Create `AnalyticsMetric.ts` model
   - Create `Report.ts` model

2. **Analytics API Service**
   - Add analytics endpoints to API service
   - Add report endpoints to API service

3. **Analytics Context**
   - Create `AnalyticsContext.tsx` for state management

4. **Analytics Screens**
   - Implement `AnalyticsDashboardScreen.tsx` - Main analytics dashboard
   - Implement `CashflowAnalysisScreen.tsx` - Cashflow analysis
   - Implement `SpendingAnalysisScreen.tsx` - Spending analysis
   - Implement `ReportsScreen.tsx` - List reports
   - Implement `ReportDetailsScreen.tsx` - View report details
   - Implement `CreateReportScreen.tsx` - Create new report
   - Implement `ExportReportScreen.tsx` - Export report

## Phase 8: Budget Governance & Controls

### Backend Development

1. **Budget Control Domain Models**
   - Implement `BudgetLimit.cs` entity
   - Create `SpendingLimit.cs` entity
   - Create `ExpenseApprovalRequest.cs` entity

2. **Budget Control DTOs**
   - Create `BudgetControlDTOs.cs` with:
     - `BudgetLimitDto`
     - `CreateBudgetLimitDto`
     - `UpdateBudgetLimitDto`
     - `SpendingLimitDto`
   - Create `ApprovalDTOs.cs` with:
     - `ExpenseApprovalRequestDto`
     - `CreateApprovalRequestDto`
     - `UpdateApprovalRequestDto`

3. **Budget Control Services**
   - Implement `IBudgetControlService.cs` interface
   - Create `BudgetControlService.cs` implementation
   - Implement `IApprovalService.cs` interface
   - Create `ApprovalService.cs` implementation

4. **Budget Control Controllers**
   - Create `BudgetLimitController.cs` with CRUD endpoints
   - Create `SpendingLimitController.cs` with CRUD endpoints
   - Create `ApprovalController.cs` with endpoints:
     - `GET /api/approval` - List approval requests
     - `GET /api/approval/{id}` - Get approval request details
     - `POST /api/approval` - Create approval request
     - `PUT /api/approval/{id}` - Update approval request
     - `POST /api/approval/{id}/approve` - Approve request
     - `POST /api/approval/{id}/reject` - Reject request

### Frontend Development

1. **Budget Control Models**
   - Create `BudgetLimit.ts` model
   - Create `SpendingLimit.ts` model
   - Create `ApprovalRequest.ts` model

2. **Budget Control API Service**
   - Add budget control endpoints to API service
   - Add approval endpoints to API service

3. **Budget Control Context**
   - Create `BudgetControlContext.tsx` for state management
   - Create `ApprovalContext.tsx` for state management

4. **Budget Control Screens**
   - Implement `BudgetLimitsScreen.tsx` - List budget limits
   - Implement `SpendingLimitsScreen.tsx` - List spending limits
   - Implement `ApprovalRequestsScreen.tsx` - List approval requests
   - Implement `ApprovalDetailsScreen.tsx` - View approval details
   - Implement `CreateApprovalRequestScreen.tsx` - Create approval request

## Phase 9: OCR & Receipt Scanning

### Backend Development

1. **Receipt Domain Models**
   - Implement `ReceiptData.cs` entity

2. **Receipt DTOs**
   - Create `ReceiptDTOs.cs` with:
     - `ReceiptDataDto`
     - `UploadReceiptDto`
     - `ProcessReceiptDto`
     - `ReceiptResultDto`

3. **Receipt Services**
   - Implement `IReceiptService.cs` interface
   - Create `ReceiptService.cs` implementation
   - Implement OCR processing with Tesseract

4. **Receipt Controllers**
   - Create `ReceiptController.cs` with endpoints:
     - `POST /api/receipt/upload` - Upload receipt image
     - `POST /api/receipt/process` - Process receipt with OCR
     - `GET /api/receipt/{id}` - Get receipt data
     - `GET /api/receipt/transaction/{transactionId}` - Get receipts by transaction
     - `PUT /api/receipt/{id}` - Update receipt data
     - `DELETE /api/receipt/{id}` - Delete receipt

### Frontend Development

1. **Receipt Models**
   - Create `Receipt.ts` model

2. **Receipt API Service**
   - Add receipt endpoints to API service

3. **OCR Context**
   - Create `OCRContext.tsx` for state management

4. **Receipt Screens**
   - Implement `ScanReceiptScreen.tsx` - Scan receipt with camera
   - Implement `ReceiptGalleryScreen.tsx` - Select from gallery
   - Implement `ReceiptDetailsScreen.tsx` - View receipt details
   - Implement `ReceiptEditScreen.tsx` - Edit extracted data
   - Implement `ReceiptHistoryScreen.tsx` - View receipt history

## Phase 10: Offline-First Experience

### Backend Development

1. **Sync Domain Models**
   - Implement `SyncQueue.cs` entity
   - Create `ImportHistory.cs` entity
   - Create `ExportHistory.cs` entity

2. **Sync DTOs**
   - Create `SyncDTOs.cs` with:
     - `SyncQueueDto`
     - `SyncBatchDto`
     - `SyncResultDto`
     - `ConflictResolutionDto`

3. **Sync Services**
   - Implement `ISyncService.cs` interface
   - Create `SyncService.cs` implementation
   - Implement conflict resolution algorithms

4. **Sync Controllers**
   - Create `SyncController.cs` with endpoints:
     - `POST /api/sync/push` - Push local changes
     - `GET /api/sync/pull` - Pull remote changes
     - `POST /api/sync/resolve` - Resolve conflicts
     - `GET /api/sync/status` - Get sync status

### Frontend Development

1. **Sync Models**
   - Create `SyncQueue.ts` model
   - Create `SyncConflict.ts` model

2. **Sync API Service**
   - Add sync endpoints to API service

3. **Sync Context**
   - Create `SyncContext.tsx` for state management

4. **Offline Storage**
   - Implement `db-dexie.ts` with IndexedDB schema
   - Create background sync queue
   - Implement conflict resolution UI

5. **Sync Screens**
   - Implement `SyncStatusScreen.tsx` - View sync status
   - Implement `ConflictResolutionScreen.tsx` - Resolve conflicts
   - Implement `SyncSettingsScreen.tsx` - Configure sync settings

## Phase 11: Notifications & Reminders

### Backend Development

1. **Notification Domain Models**
   - Implement `Notification.cs` entity
   - Create `NotificationSchedule.cs` entity
   - Create `RecurringNotification.cs` entity

2. **Notification DTOs**
   - Create `NotificationDTOs.cs` with:
     - `NotificationDto`
     - `CreateNotificationDto`
     - `NotificationScheduleDto`
     - `RecurringNotificationDto`

3. **Notification Services**
   - Implement `INotificationService.cs` interface
   - Create `NotificationService.cs` implementation
   - Create background services:
     - `NotificationBackgroundService.cs`
     - `ReminderBackgroundService.cs`
     - `MonthlySummaryBackgroundService.cs`

4. **Notification Controllers**
   - Create `NotificationController.cs` with endpoints:
     - `GET /api/notification` - List notifications
     - `GET /api/notification/{id}` - Get notification details
     - `POST /api/notification` - Create notification
     - `PUT /api/notification/{id}` - Update notification
     - `DELETE /api/notification/{id}` - Delete notification
     - `POST /api/notification/schedule` - Schedule notification
     - `POST /api/notification/device` - Register device for push

### Frontend Development

1. **Notification Models**
   - Create `Notification.ts` model
   - Create `NotificationSchedule.ts` model

2. **Notification API Service**
   - Add notification endpoints to API service

3. **Notification Context**
   - Create `NotificationContext.tsx` for state management

4. **Push Notifications**
   - Implement `pushNotifications.ts` service
   - Set up Expo notifications

5. **Notification Screens**
   - Implement `NotificationScreen.tsx` - List notifications
   - Implement `NotificationDetailsScreen.tsx` - View notification details
   - Implement `CreateScheduledNotificationScreen.tsx` - Schedule notification
   - Implement `EditScheduledNotificationScreen.tsx` - Edit scheduled notification
   - Implement `NotificationSettingsScreen.tsx` - Configure notification settings

## Phase 12: Multilingual Support Enhancement

### Backend Development

1. **Localization Services**
   - Implement `ILocalizationService.cs` interface
   - Create `LocalizationService.cs` implementation
   - Add language preference to `UserSetting.cs`

2. **API Localization**
   - Add localization middleware
   - Create resource files for API responses
   - Implement language detection from headers

3. **Controllers Enhancement**
   - Update controllers to support localized responses
   - Add language preference endpoints to `UserController.cs`

### Frontend Development

1. **i18n Enhancement**
   - Expand translation files for English and Nepali
   - Implement RTL support for UI components
   - Create language switcher component

2. **Localized Components**
   - Update all UI components to support RTL layouts
   - Create localized date/time formatters
   - Implement localized number formatting

3. **Language Settings**
   - Implement `LanguageSettingsScreen.tsx`
   - Create language selection component
   - Add language preference to user settings

## Phase 13: Feature Management

### Backend Development

1. **Feature Flag Domain Models**
   - Implement `FeatureFlag.cs` entity
   - Create `UserSetting.cs` entity for user preferences

2. **Feature Flag DTOs**
   - Create `FeatureFlagDTOs.cs` with:
     - `FeatureFlagDto`
     - `CreateFeatureFlagDto`
     - `UpdateFeatureFlagDto`
     - `UserSettingDto`

3. **Feature Flag Services**
   - Implement `IFeatureFlagService.cs` interface
   - Create `FeatureFlagService.cs` implementation
   - Implement `IUserSettingService.cs` interface
   - Create `UserSettingService.cs` implementation

4. **Feature Flag Controllers**
   - Create `FeatureFlagController.cs` with endpoints:
     - `GET /api/feature` - List feature flags
     - `GET /api/feature/{id}` - Get feature flag details
     - `POST /api/feature` - Create feature flag
     - `PUT /api/feature/{id}` - Update feature flag
     - `DELETE /api/feature/{id}` - Delete feature flag
   - Create `UserSettingController.cs` with endpoints:
     - `GET /api/user/settings` - Get user settings
     - `PUT /api/user/settings` - Update user settings

### Frontend Development

1. **Feature Flag Models**
   - Create `FeatureFlag.ts` model
   - Create `UserSetting.ts` model

2. **Feature Flag API Service**
   - Add feature flag endpoints to API service
   - Add user settings endpoints to API service

3. **Feature Flag Context**
   - Create `FeatureFlagContext.tsx` for state management
   - Implement conditional rendering based on flags

4. **Feature Flag Screens**
   - Implement `FeatureFlagsScreen.tsx` - Admin control panel
   - Update settings screen with feature toggles
   - Implement feature announcement components

## Development Workflow & Testing

### Backend Testing

1. **Unit Tests**
   - Create test projects for each layer
   - Implement service tests with mocked repositories
   - Create controller tests with mocked services

2. **Integration Tests**
   - Set up test database
   - Create API integration tests
   - Implement end-to-end test scenarios

3. **Performance Testing**
   - Create benchmarks for critical operations
   - Test sync performance with large datasets
   - Optimize database queries

### Frontend Testing

1. **Component Tests**
   - Set up Jest for component testing
   - Create snapshot tests for UI components
   - Test form validation logic

2. **E2E Testing**
   - Set up Detox for E2E testing
   - Create test scenarios for critical flows
   - Test offline functionality

3. **Performance Optimization**
   - Implement React.memo for expensive components
   - Optimize list rendering with virtualization
   - Test and optimize app startup time

### Deployment

1. **Backend Deployment**
   - Set up CI/CD pipeline
   - Configure production database
   - Set up monitoring and logging

2. **Frontend Deployment**
   - Configure app signing
   - Set up app store deployment
   - Create release management process
